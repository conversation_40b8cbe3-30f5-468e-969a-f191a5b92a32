<?php

namespace Tests\Browser\Internal;

use Illuminate\Support\Str;
use Tests\Support\BrowserTestCase;
use Tests\Support\Browser;

class SecuritySettingsTest extends BrowserTestCase
{

  public function testChangePassword()
  {
    $strongPassword = $this->faker->password . Str::random(6);

    $this->launchBrowser(function (Browser $browser) use ($strongPassword) {
      $browser
        ->loginPage()->login($this->dataCreator)
        ->dashboardPage()
        ->openMainMenuDropdown()
        ->securitySettings()
        ->changePassword($this->dataCreatorParameters->getUserPassword(), $strongPassword)
        ->alert()->assertSuccess();
    });
  }

  public function testChangeAuthMethodToTotp()
  {
    $this->launchBrowser(function (Browser $browser) {
      $browser
        ->loginPage()->login($this->dataCreator)
        ->dashboardPage()
        ->openMainMenuDropdown()
        ->securitySettings()
        ->changeAuthMethodToTotp($this->dataCreator->getUser())
        ->alert()->assertSuccess();
    });
  }

  public function testChangeAuthMethodToSms()
  {
    $this->launchBrowser(function (Browser $browser) {
      $browser
        ->loginPage()->login($this->dataCreator)
        ->dashboardPage()
        ->openMainMenuDropdown()
        ->securitySettings()
        ->changeAuthMethodToSmsTotp($this->dataCreator->getUser())
        ->alert()->assertSuccess();
    });
  }
}
