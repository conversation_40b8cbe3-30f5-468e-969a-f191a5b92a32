#!/usr/bin/env node
const fs = require('fs-extra');
const path = require('path');
const pkg = require('./package.json');
const JsZip = require('jszip');
const crypto = require('crypto');
const argv = require('yargs').
  alias('h', 'help').
  usage('Usage: $0 <distDir> [options]').
  example('$0 --target=chrome').
  showHelpOnFail(false, 'Specify --help for available options').
  options({
    t: {
      alias: 'target',
      describe: 'defines target browser',
      type: 'string',
      nargs: 1,
      demand: 'target is required',
    },
  }).argv;

function getFileName(target, version) {
  switch (target) {
    case 'chrome': {
      return `hix_browser_ext-${version}-chrome.zip`;
    }
    case 'firefox': {
      return `hix_browser_ext-${version}-ff.xpi`;
    }
    default:
      throw new Error('unsupported target');
  }
}

class CreateZipService {
  async createZipFile(sourceDir, filename) {

    let zip = new JsZip();
    this.buildZipFromDirectory(sourceDir, zip, sourceDir);

    /** generate zip file content */
    const zipContent = await zip.generateAsync({
      type: 'nodebuffer',
      compression: 'DEFLATE',
      compressionOptions: {
        level: 9,
      },
    });

    fs.mkdirsSync(path.dirname(filename))

    /** create zip file */
    fs.writeFileSync(filename, zipContent);
  }

  // returns a flat array of absolute paths of all files recursively contained in the dir
  buildZipFromDirectory(dir, zip, root) {
    const list = fs.readdirSync(dir);

    for (let file of list) {
      file = path.resolve(dir, file);
      let stat = fs.statSync(file);
      if (stat && stat.isDirectory()) {
        this.buildZipFromDirectory(file, zip, root);
      } else {
        const filedata = fs.readFileSync(file);
        const extension = path.extname(file);

        // Exclude sourcemap files for creating the actual extension zip
        // They are getting used by Sentry
        if (extension === '.map')
          continue;

        zip.file(path.relative(root, file), filedata);
      }
    }
  }
}

const version = pkg.version.replace('-', '.');
const sourceDir = path.resolve(process.cwd(), `dist/${argv.target}`);
const filePath = path.resolve(process.cwd(),
  `bin/${argv.target}/${getFileName(argv.target, version)}`);

console.log(`Generating archive: ${filePath}`);

let zipService = new CreateZipService();

zipService.createZipFile(sourceDir, filePath).then(() => {
  if (process.env.IN_PIPELINE === 'true') {
    const latestFilePath = path.resolve(process.cwd(), `bin/latest/${argv.target}/`);
    fs.mkdirsSync(latestFilePath)
    fs.copyFile(filePath, latestFilePath + getFileName(argv.target, version))
  }

  console.log('Completed!');
}).catch((e) => console.error('Error', e));

