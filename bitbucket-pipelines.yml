image: node:17-slim

options:
    max-time: 10 # maximum time a step can execute for (in minutes)

clone:
    depth: full # large depth required by SonarCloud

definitions:
    services:
        docker:
            memory: 4096

    build: &build
        size: 2x
        name: "Build binaries & run SonarCloud analysis"
        trigger: automatic
        caches:
            - node
            - docker
        script:
            - apt update
            - apt-get install -y openjdk-17-jre
            - npm install
            - npm run archive
            -   pipe: sonarsource/sonarcloud-scan:2.0.0
                variables:
                    SONAR_TOKEN: ****************************************
        artifacts:
            - bin/**

    firefox-review: &firefox-review
        name: "Firefox review zip"
        trigger: manual
        script:
            - apt-get update -y
            - apt-get install -y zip openjdk-17-jre
            - zip -r mozilla-firefox-review.zip . -x '/.git/*' -x '/node_modules/*' -x '/bin/*' -x '/dist/*' -x '/.idea/*' -x '/bitbucket-pipelines.yml' -x '/sonar-project.properties'
        artifacts:
            - mozilla-firefox-review.zip

    build-staging: &build-staging
        name: "Testing archive"
        trigger: manual
        caches:
            - node
        script:
            - apt-get update -y
            - apt-get install -y ca-certificates zip
            - npm install
            - export IN_PIPELINE=true
            - export NODE_ENV=production
            - npm run archive
            - zip hix_extension_staging.zip bin/latest/*.zip bin/latest/*.xpi
        artifacts:
            - hix_extension_staging.zip
            - bin/latest/**

    build-prod: &build-prod
        name: "Release archive"
        trigger: manual
        deployment: production
        caches:
            - node
        script:
            - apt-get update -y
            - apt-get install -y ca-certificates zip
            - npm install
            - export IN_PIPELINE=true
            - export NODE_ENV=production
            - npm run archive
            - zip hix_extension_production.zip bin/latest/*.zip bin/latest/*.xpi
        artifacts:
            - hix_extension_production.zip
            - bin/latest/**

pipelines:
    default:
        -   parallel:
            - step: *build
            - step: *build-staging

    branches:
        master:
            - parallel:
                - step: *build
                - step: *firefox-review
                - step: *build-staging
                - step: *build-prod
