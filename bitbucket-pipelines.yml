image: securelogin/php-ci:R2024-20

options:
    max-time: 60 # maximum time a step can execute for (in minutes)

clone:
    depth: full # large depth required by SonarCloud

definitions:
    services:
        docker:
            memory: 3072
        redis:
            image: redis
            memory: 256
        mongo:
            image: mongo
            memory: 256
        mysql:
            image: securelogin/mysql:8.0.35
            memory: 512
            variables:
                MYSQL_DATABASE: 'securelogin'
                MYSQL_ROOT_PASSWORD: 'ftw'
                MYSQL_USER: 'test_user'
                MYSQL_PASSWORD: 'test_user_password'
        meilisearch:
          image: 'getmeili/meilisearch:v1.13.0'
          memory: 512
          variables:
              MEILI_MASTER_KEY: '1ZZTkY_C2iX1kwbXdnkV1jopU0JfKE8CTSdjdfAyNqg'
          ports:
            - '7700:7700'
          volumes:
            - 'meili_data:/meili_data'

    run-code-style-check: &run-code-style-check
        name: 'Check PSR-12 code-style'
        trigger: automatic
        script:
            - source ./bitbucket-pipelines/run-psr-12-check.sh

    compile-frontend: &compile-frontend
        size: 2x
        name: 'Compile frontend for deployment'
        trigger: automatic
        image: node:20-alpine
        caches:
            - node
        script:
            - apk --no-cache add git -q
            - yarn install --non-interactive
            - yarn run prod
        artifacts:
            - public/**

    run-sonar: &run-sonar
        size: 2x
        name: "Run SonarCloud Analysis"
        trigger: automatic
        services:
            - redis
            - mysql
            - mongo
        script:
            - source ./bitbucket-pipelines/set_environment_variables.sh
            - source ./bitbucket-pipelines/run-phpunit-merger.sh
            -   pipe: sonarsource/sonarcloud-scan:2.0.0
                variables:
                    SONAR_TOKEN: ****************************************
                    SONAR_SCANNER_OPTS: '-Xmx2048m'
                    EXTRA_ARGS: $SONAR_ARGS
                    DEBUG: "false"
        artifacts:
            - test-reports/**

    run-aikido: &run-aikido
      size: 2x
      name: "Run Aikido Security Scan"
      trigger: automatic
      script:
         - pipe: aikido-production/bitbucket-pipe:1.0.7
           variables:
             AIKIDO_API_KEY: $AIKIDO_API_KEY
             MINIMUM_SEVERITY: "CRITICAL"
             FAIL_ON_TIMEOUT: "true"
             FAIL_ON_DEPENDENCY_SCAN: "false"
             FAIL_ON_SAST_SCAN: "false"
             FAIL_ON_IAC_SCAN: "false"
             FAIL_ON_SECRETS_SCAN: "false"

    prepare-test-dependencies: &prepare-test-dependencies
        name: "Prepare test dependencies"
        trigger: automatic
        services:
            - redis
            - mysql
            - mongo
        caches:
            - node
            - composer
        script:
            - source ./bitbucket-pipelines/set_environment_variables.sh
            - source ./bitbucket-pipelines/prepare-test-dependencies.sh
        artifacts:
            - vendor/**
            - .env
            - public/**

    run-unit-tests: &run-unit-tests
        size: 2x
        name: "Run unit tests"
        trigger: automatic
        services:
            - redis
            - mysql
            - mongo
        caches:
            - node
            - composer
        script:
            - source ./bitbucket-pipelines/prepare-database-for-tests.sh
            - source ./bitbucket-pipelines/run-unit-tests.sh
        artifacts:
            - test-reports/**

    run-feature-tests: &run-feature-tests
        size: 2x
        name: "Run feature tests"
        trigger: automatic
        services:
            - redis
            - mysql
            - mongo
        caches:
            - node
            - composer
        script:
            - source ./bitbucket-pipelines/prepare-database-for-tests.sh
            - source ./bitbucket-pipelines/run-feature-tests.sh
        artifacts:
            - test-reports/**

    run-feature-declarations-tests: &run-feature-declarations-tests
        size: 2x
        name: "Run feature declarations tests"
        trigger: automatic
        services:
            - redis
            - mysql
            - mongo
        caches:
            - node
            - composer
        script:
            - source ./bitbucket-pipelines/prepare-database-for-tests.sh
            - source ./bitbucket-pipelines/run-feature-declarations-tests.sh
        artifacts:
            - test-reports/**

    run-feature-services-tests: &run-feature-services-tests
        size: 2x
        name: "Run feature services other tests"
        trigger: automatic
        services:
            - redis
            - mysql
            - mongo
        caches:
            - node
            - composer
        script:
            - source ./bitbucket-pipelines/prepare-database-for-tests.sh
            - source ./bitbucket-pipelines/run-feature-services-tests.sh
        artifacts:
            - test-reports/**

    run-feature-services-crm-tests: &run-feature-services-crm-tests
        size: 2x
        name: "Run feature services CRM tests"
        trigger: automatic
        services:
            - redis
            - mysql
            - mongo
        caches:
            - node
            - composer
        script:
            - source ./bitbucket-pipelines/prepare-database-for-tests.sh
            - source ./bitbucket-pipelines/run-feature-services-crm-tests.sh
        artifacts:
            - test-reports/**

    run-feature-services-dms-tests: &run-feature-services-dms-tests
        size: 2x
        name: "Run feature services DMS tests"
        trigger: automatic
        services:
            - redis
            - mysql
            - mongo
        caches:
            - node
            - composer
        script:
            - source ./bitbucket-pipelines/prepare-database-for-tests.sh
            - source ./bitbucket-pipelines/run-feature-services-dms-tests.sh
        artifacts:
            - test-reports/**

    run-feature-services-open-questions-tests: &run-feature-services-open-questions-tests
        size: 2x
        name: "Run feature services Open Questions tests"
        trigger: automatic
        services:
            - redis
            - mysql
            - mongo
        caches:
            - node
            - composer
        script:
            - source ./bitbucket-pipelines/prepare-database-for-tests.sh
            - source ./bitbucket-pipelines/run-feature-services-open-questions-tests.sh
        artifacts:
            - test-reports/**

    run-feature-services-whatsapp-tests: &run-feature-services-whatsapp-tests
        size: 2x
        name: "Run feature services Whatsapp tests"
        trigger: automatic
        services:
            - redis
            - mysql
            - mongo
        caches:
            - node
            - composer
        script:
            - source ./bitbucket-pipelines/prepare-database-for-tests.sh
            - source ./bitbucket-pipelines/run-feature-services-whatsapp-tests.sh
        artifacts:
            - test-reports/**

    run-feature-commands-tests: &run-feature-commands-tests
        size: 2x
        name: "Run feature commands tests"
        trigger: automatic
        services:
            - redis
            - mysql
            - mongo
        caches:
            - node
            - composer
        script:
            - source ./bitbucket-pipelines/prepare-database-for-tests.sh
            - source ./bitbucket-pipelines/run-feature-commands-tests.sh
        artifacts:
            - test-reports/**

    run-feature-api-tests: &run-feature-api-tests
        size: 2x
        name: "Run feature API tests"
        trigger: automatic
        services:
            - redis
            - mysql
            - mongo
        caches:
            - node
            - composer
        script:
            - source ./bitbucket-pipelines/prepare-database-for-tests.sh
            - source ./bitbucket-pipelines/run-feature-api-tests.sh
        artifacts:
            - test-reports/**
    run-meilisearch-tests: &run-meilisearch-tests
      size: 2x
      name: "Run meilisearch tests"
      trigger: automatic
      services:
        - redis
        - mysql
        - mongo
        - meilisearch
      caches:
        - node
        - composer
      script:
        - source ./bitbucket-pipelines/prepare-database-for-tests.sh
        - source ./bitbucket-pipelines/run-meilisearch-tests.sh
      artifacts:
        - test-reports/**

    artifacts:
        # for debugging purposes
        - '**'

    deploy: &deploy
        name: "Deploy to Staging"
        trigger: manual
        caches:
            - composer
            - node
        script:
            - composer --version
            - echo 'Deploying to Blue ...'
            - source ./bitbucket-pipelines/set_environment_variables.sh
            - composer install --no-scripts --no-interaction --optimize-autoloader --prefer-dist --no-dev
            # update envoy script first
            - rsync -avhzc --exclude-from 'pipelines-rsync-exclude' --delete Envoy.blade.php "${DEPLOY_USER_WITH_SERVER}:${DEPLOY_BLUE_PATH}"
            # run deployment scripts
            - ssh "${DEPLOY_USER_WITH_SERVER}" "cd ${DEPLOY_BLUE_PATH}; ${DEPLOY_ENVOY_PATH} run pre-blue-deploy --env=${DEPLOY_ENVIRONMENT} --basePath=${DEPLOY_BASE_PATH} --userHome=${USER_HOME} --tag=${BITBUCKET_TAG}"
            - rsync -avhzc --exclude-from 'pipelines-rsync-exclude' --delete . "${DEPLOY_USER_WITH_SERVER}:${DEPLOY_BLUE_PATH}"
            - ssh "${DEPLOY_USER_WITH_SERVER}" "cd ${DEPLOY_BLUE_PATH}; ${DEPLOY_ENVOY_PATH} run post-blue-deploy --env=${DEPLOY_ENVIRONMENT} --basePath=${DEPLOY_BASE_PATH} --userHome=${USER_HOME} --tag=${BITBUCKET_TAG}"
            #       deploy green
            - echo 'Backing up and synchronizing Blue to Green ...'
            - ssh "${DEPLOY_USER_WITH_SERVER}" "cd ${DEPLOY_BLUE_PATH}; ${DEPLOY_ENVOY_PATH} run backup-green --env=${DEPLOY_ENVIRONMENT} --basePath=${DEPLOY_BASE_PATH} --userHome=${USER_HOME} --tag=${BITBUCKET_TAG}"
            - ssh "${DEPLOY_USER_WITH_SERVER}" "cd ${DEPLOY_BLUE_PATH}; ${DEPLOY_ENVOY_PATH} run swap-blue-green --env=${DEPLOY_ENVIRONMENT} --basePath=${DEPLOY_BASE_PATH} --userHome=${USER_HOME} --tag=${BITBUCKET_TAG}"
            - echo 'Creating symlink ...'
            - ssh "${DEPLOY_USER_WITH_SERVER}" "ln -s ${DEPLOY_UI_BASE_PATH}/active/public ${DEPLOY_BASE_PATH}/active/public/app"

    deploy-sso: &deploy-sso
        name: "Staging Blue"
        trigger: manual
        script:
            - echo "Deploying SSO's to Blue ..."
            - source ./bitbucket-pipelines/set_environment_variables.sh
            # update envoy script first
            - mkdir -p ./public/images/widgets/access/
            - cp -rv ./resources/images/widgets/access/* ./public/images/widgets/access/
            - rsync -avhzc --exclude-from 'pipelines-rsync-exclude' --delete Envoy.blade.php "${DEPLOY_USER_WITH_SERVER}:${DEPLOY_BLUE_PATH}"
            #      # run deployment scripts
            - ssh "${DEPLOY_USER_WITH_SERVER}" "cd ${DEPLOY_BLUE_PATH}; ${DEPLOY_ENVOY_PATH} run pre-blue-deploy --env=${DEPLOY_ENVIRONMENT} --basePath=${DEPLOY_BASE_PATH} --userHome=${USER_HOME}"
            - rsync -avhzc --delete ./database/seeders/GenericWidgetSeeder.php "${DEPLOY_USER_WITH_SERVER}:${DEPLOY_BLUE_PATH}/database/seeders/"
            - rsync -avhzc --delete ./database/seeders/GenericWidgetCategorySeeder.php "${DEPLOY_USER_WITH_SERVER}:${DEPLOY_BLUE_PATH}/database/seeders/"
            - rsync -avhzc --delete ./public/images/widgets/access/ "${DEPLOY_USER_WITH_SERVER}:${DEPLOY_BLUE_PATH}/public/images/widgets/access/"
            - rsync -avhzc --delete ./database/seeders/widgets/sso/ "${DEPLOY_USER_WITH_SERVER}:${DEPLOY_BLUE_PATH}/database/seeders/widgets/sso/"
            - ssh "${DEPLOY_USER_WITH_SERVER}" "cd ${DEPLOY_BLUE_PATH}; ${DEPLOY_ENVOY_PATH} run post-blue-deploy-sso --env=${DEPLOY_ENVIRONMENT} --basePath=${DEPLOY_BASE_PATH} --userHome=${USER_HOME}"
            #       deploy to green
            - echo 'Backing up and synchronizing Blue to Green ...'
            - ssh "${DEPLOY_USER_WITH_SERVER}" "cd ${DEPLOY_BLUE_PATH}; ${DEPLOY_ENVOY_PATH} run backup-green --env=${DEPLOY_ENVIRONMENT} --basePath=${DEPLOY_BASE_PATH} --userHome=${USER_HOME}"
            - ssh "${DEPLOY_USER_WITH_SERVER}" "cd ${DEPLOY_BLUE_PATH}; ${DEPLOY_ENVOY_PATH} run swap-blue-green-sso --env=${DEPLOY_ENVIRONMENT} --basePath=${DEPLOY_BASE_PATH} --userHome=${USER_HOME}"

    rollback: &rollback
        name: "Rollback"
        trigger: manual
        script:
            - echo 'Rolling Back ...'
            - source ./bitbucket-pipelines/set_environment_variables.sh
            - ssh "${DEPLOY_USER_WITH_SERVER}" "cd ${DEPLOY_RED_PATH}; ${DEPLOY_ENVOY_PATH} run rollback-to-red --env=${DEPLOY_ENVIRONMENT} --basePath=${DEPLOY_BASE_PATH} --userHome=${USER_HOME} --tag=${BITBUCKET_TAG}"

pipelines:
    default:
        -   step: *prepare-test-dependencies
        -   parallel:
                -   step: *compile-frontend
                -   step: *run-code-style-check
                -   step:
                        <<: *run-unit-tests
                        trigger: manual
                -   step:
                        <<: *run-feature-tests
                        trigger: manual
                -   step:
                        <<: *run-feature-commands-tests
                        trigger: manual
                -   step:
                        <<: *run-feature-declarations-tests
                        trigger: manual
                -   step:
                        <<: *run-feature-services-crm-tests
                        trigger: manual
                -   step:
                        <<: *run-feature-services-dms-tests
                        trigger: manual
                -   step:
                        <<: *run-feature-services-open-questions-tests
                        trigger: manual
                -   step:
                        <<: *run-feature-services-whatsapp-tests
                        trigger: manual
                -   step:
                        <<: *run-feature-services-tests
                        trigger: manual
                -   step:
                        <<: *run-feature-api-tests
                        trigger: manual
                -   step:
                        <<: *run-meilisearch-tests
                        trigger: manual
        -   step: *run-sonar
        -   step:
                <<: *deploy
                deployment: staging
        -   step: *rollback

    pull-requests:
        '**':
            -   step: *prepare-test-dependencies
            -   parallel:
                    -   step: *compile-frontend
                    -   step: *run-aikido
                    -   step: *run-code-style-check
                    -   step: *run-unit-tests
                    -   step: *run-feature-tests
                    -   step: *run-feature-commands-tests
                    -   step: *run-feature-declarations-tests
                    -   step: *run-feature-services-tests
                    -   step: *run-feature-services-crm-tests
                    -   step: *run-feature-services-dms-tests
                    -   step: *run-feature-services-open-questions-tests
                    -   step: *run-feature-services-whatsapp-tests
                    -   step: *run-feature-api-tests
                    -   step: *run-meilisearch-tests
            -   step: *run-sonar
            -   step:
                    <<: *deploy
                    deployment: staging
            -   step: *rollback

    branches:
        develop:
            -   step: *prepare-test-dependencies
            -   parallel:
                    -   step: *compile-frontend
                    -   step: *run-code-style-check
                    -   step: *run-unit-tests
                    -   step: *run-feature-tests
                    -   step: *run-feature-commands-tests
                    -   step: *run-feature-declarations-tests
                    -   step: *run-feature-services-tests
                    -   step: *run-feature-services-crm-tests
                    -   step: *run-feature-services-dms-tests
                    -   step: *run-feature-services-open-questions-tests
                    -   step: *run-feature-services-whatsapp-tests
                    -   step: *run-feature-api-tests
                    -   step: *run-meilisearch-tests
            -   step: *run-sonar
            -   step:
                    <<: *deploy
                    deployment: staging
                    trigger: automatic
            -   step: *rollback

        release/*:
            -   step: *compile-frontend
            -   stage:
                  name: "Deploy to PreProd"
                  deployment: preproduction
                  steps:
                    - step:
                        <<: *deploy
                        name: "Deploy to PreProd"
                        trigger: automatic
            -   step: *prepare-test-dependencies
            -   parallel:
                    -   step: *run-code-style-check
                    -   step: *run-unit-tests
                    -   step: *run-feature-tests
                    -   step: *run-feature-commands-tests
                    -   step: *run-feature-declarations-tests
                    -   step: *run-feature-services-tests
                    -   step: *run-feature-services-crm-tests
                    -   step: *run-feature-services-dms-tests
                    -   step: *run-feature-services-open-questions-tests
                    -   step: *run-feature-services-whatsapp-tests
                    -   step: *run-feature-api-tests
                    -   step: *run-meilisearch-tests
            -   step: *rollback

    tags:
        'R*-*':
            -   step: *compile-frontend
            -   step:
                    <<: *deploy
                    name: "Deploy to Production"
                    deployment: production
            -   step: *rollback

        'SSO-R*-*':
            -   step:
                    <<: *deploy-sso
                    name: "Deploy SSO's Staging"
                    deployment: staging
                    trigger: automatic
            -   step:
                    <<: *deploy-sso
                    name: "Deploy SSO's Production"
                    deployment: production
                    trigger: manual
            -   step: *rollback