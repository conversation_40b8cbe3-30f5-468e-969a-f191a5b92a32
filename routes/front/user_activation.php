<?php

use App\Http\Controllers\Front\UserActivationController;

Route::middleware(['bindings'])->prefix('front/user')->namespace('Front')->group(function () {
    Route::get('activate_account/{token_model}', [UserActivationController::class, 'initActivateAccount'])
        ->name('front.user_activation.init_activate_account');

    Route::post('activate_account/{token_model}', [UserActivationController::class, 'activateAccount'])
        ->name('front.user_activation.activate_account');

    Route::get('activate_user/{token_model}', [UserActivationController::class, 'initActivateUser'])
        ->name('front.user_activation.init_activate_user');

    Route::post('activate_user/{token_model}', [UserActivationController::class, 'activateUser'])
        ->name('front.user_activation.activate_user');

    Route::get('reactivate/{token_model}', [UserActivationController::class, 'initReactivate'])
        ->name('front.user_activation.reactivate');

    Route::post('verify_masterkey/{token_model}', [UserActivationController::class, 'verifyMasterkey'])
        ->name('front.user_activation.verify_master_key');

    Route::get('setup_password/{token_model}', [UserActivationController::class, 'initSetupPassword'])
        ->name('front.user_activation.init_setup_password');

    Route::post('setup_password/{token_model}', [UserActivationController::class, 'setupPassword'])
        ->name('front.user_activation.setup_password');

    Route::get('setup_totp/{token_model}', [UserActivationController::class, 'initSetupTotp'])
        ->name('front.user_activation.init_setup_totp');

    Route::post('setup_totp/{token_model}', [UserActivationController::class, 'setupTotp'])
        ->name('front.user_activation.setup_totp');

    Route::get('setup_otp/{token_model}', [UserActivationController::class, 'initSetupOtp'])
        ->name('front.user_activation.init_setup_otp');

    Route::post('send_sms_otp/{token_model}', [UserActivationController::class, 'sendSmsOtpFromToken'])
        ->name('front.user_activation.send_sms_otp');

    Route::post('setup_otp/{token_model}', [UserActivationController::class, 'setupOtp'])
        ->name('front.user_activation.setup_otp');
});
