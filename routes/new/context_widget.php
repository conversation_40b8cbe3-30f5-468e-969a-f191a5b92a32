<?php

use App\Http\Controllers\Widgets\VueContextWidgetController;

Route::prefix('context_widget')->group(function () {
    // Context widget
    Route::post('{context_widget}/submitForm/{form_id}', [VueContextWidgetController::class, 'submitForm'])
        ->name('new.context_widget.submitForm');

    // ---------------------------- Routes for Managers ----------------------------
    Route::middleware(['hasLicense:new_ui_manager', 'isManagerOfHostAccount'])->group(function () {
        Route::get('available_account_widgets', [VueContextWidgetController::class, 'availableAccountWidgets'])
            ->name('new.context_widget.available_account_widgets');

        Route::post('{context_widget}/update', [VueContextWidgetController::class, 'update'])
            ->name('new.context_widget.update');

        Route::post('{context_widget}/delete', [VueContextWidgetController::class, 'delete'])
            ->name('new.context_widget.delete');

        Route::post('store', [VueContextWidgetController::class, 'store'])
            ->name('new.context_widget.store');

        Route::post('delete_from_context', [VueContextWidgetController::class, 'deleteFromContext'])
            ->name('new.context_widget.delete_from_context');

        Route::get('{context_widget}/settings', [VueContextWidgetController::class, 'settings'])
            ->name('context_widget.settings');
    });
});
