<?php

use App\Http\Controllers\Admin\System\TaskToolsController;

Route::prefix('task_tools')->group(function () {
    Route::get('search_task', [TaskToolsController::class, 'searchTaskByID'])
        ->name('new.system.task_tools.search_task_by_id');

    Route::post('delete_task', [TaskToolsController::class, 'deleteTask'])
        ->name('new.system.task_tools.delete_task');

    Route::post('update_task_status', [TaskToolsController::class, 'updateTaskStatus'])
        ->name('new.system.task_tools.update_task_status');

    Route::post('update_company_id', [TaskToolsController::class, 'updateCompanyId'])
        ->name('new.system.task_tools.update_company_id');

    Route::get('search_task_file', [TaskToolsController::class, 'searchTaskFileById'])
        ->name('new.system.task_tools.search_task_file_by_id');

    Route::post('delete_task_file', [TaskToolsController::class, 'deleteTaskFile'])
        ->name('new.system.task_tools.delete_task_file');

    Route::post('create_afas_task', [TaskToolsController::class, 'createAfasTask'])
        ->name('new.system.task_tools.create_afas_task');
});
