<?php

use App\Http\Controllers\Service\OpenQuestions\OpticalCharacterRecognitionController;

Route::prefix('ocr')->group(function () {
    Route::post('/manual_question', [OpticalCharacterRecognitionController::class, 'createManualQuestion'])
        ->name('open_questions.create.ocr.manual_question');

    Route::post('question/create', [OpticalCharacterRecognitionController::class, 'createQuestion'])
        ->name('open_questions.ocr.create.question');

    Route::get( // @deprecated keep for browser extension 1.5.8
        'company/{company_id}/index',
        [OpticalCharacterRecognitionController::class, 'getQuestionsForExternalCompany']
    )->name('open_questions.ocr.index');

    Route::get('company/questions', [OpticalCharacterRecognitionController::class, 'getQuestionsForExternalCompany'])
        ->name('open_questions.ocr.questions');
});
