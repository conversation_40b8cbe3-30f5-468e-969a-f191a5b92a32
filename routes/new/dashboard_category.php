<?php

use App\Http\Controllers\VueUserController;

Route::prefix('dashboard_category')->group(function () {
    //Dashboard Categories
    Route::get('index', [VueUserController::class, 'indexDashboardCategories'])
        ->name('new.dashboard_category.index');

    Route::post('store', [VueUserController::class, 'storeDashboardCategory'])
        ->name('new.dashboard_category.store');

    Route::post('edit', [VueUserController::class, 'editDashboardCategory'])
        ->name('new.dashboard_category.edit');

    Route::post('delete', [VueUserController::class, 'deleteDashboardCategory'])
        ->name('new.dashboard_category.delete');

    Route::post('move', [VueUserController::class, 'moveDashboardCategory'])
        ->name('new.dashboard_category.move');

    Route::post('favorite', [VueUserController::class, 'setFavoriteDashboardCategory'])
        ->name('new.dashboard_category.favorite');

    Route::post('add_user_widgets', [VueUserController::class, 'addWidgetsToDashboardCategory'])
        ->name('new.dashboard_category.add_user_widgets');

    Route::post('remove_user_widgets', [VueUserController::class, 'removeWidgetsFromDashboardCategory'])
        ->name('new.dashboard_category.remove_user_widgets');
});
