<?php

/**
 * |----------------------------------------------------------------------------
 * | API APP ROUTES
 * |----------------------------------------------------------------------------
 * | Routes used by the app UI
 * | Prefix:  /api/app
 * |----------------------------------------------------------------------------
 */

use App\Http\Controllers\Api\App\SessionController;
use App\Http\Middleware\VerifyCsrfToken;

Route::withoutMiddleware([VerifyCsrfToken::class])->group(function () {
    require('app/auth.php');

    Route::prefix('session')->group(function () {
        Route::get('account', [SessionController::class, 'getAccount'])
            ->name('api.app.session.account');

        Route::prefix('user')->group(function () {
            Route::get('portal', [SessionController::class, 'getPortalUser'])
                ->name('api.app.session.user.portal');

            Route::get('front', [SessionController::class, 'getFrontUser'])
                ->middleware('frontAuth')
                ->name('api.app.session.user.front');
        });
    });

    Route::middleware('auth')->group(function () {
        require('app/templates/template_answers.php');
        require('app/templates/template_entries.php');
        require('app/questions/questions.php');
        require('app/search.php');
        require('app/portal.php');
    });

    require('app/account2.php');
    require('app/front.php');

    // Client page accessed without a user session.
    require('app/client_question.php');
});
