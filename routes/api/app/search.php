<?php

use App\Http\Controllers\Api\App\SearchController;
use App\License;

Route::prefix('search')->middleware('hasLicense:' . License::MEILISEARCH)->group(function () {
    Route::get('multi', [SearchController::class, 'multiSearch'])
        ->name('api.app.search.multi');

    Route::get('tasks', [SearchController::class, 'taskSearch'])
        ->name('api.app.search.tasks');

    Route::get('questions', [SearchController::class, 'questionSearch'])
        ->name('api.app.search.questions');
});
