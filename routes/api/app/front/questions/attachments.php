<?php

use App\Http\Controllers\Api\App\Question\AttachmentController;

Route::prefix('attachments')->group(function () {
    Route::get('{attachment_id}/{file_name}', [AttachmentController::class, 'attachment'])
        ->name('api.app.front.question.attachment');

    Route::get('{file_name}', [AttachmentController::class, 'downloadAttachments'])
        ->where('open_question_id', '[0-9]+')
        ->name('api.app.front.question.attachments.download');

    Route::post('delete', [AttachmentController::class, 'deleteAttachments'])
        ->name('api.app.front.question.attachments.delete');

    Route::post('save', [AttachmentController::class, 'saveUpload'])
        ->name('api.app.front.question.attachments.save');
});
