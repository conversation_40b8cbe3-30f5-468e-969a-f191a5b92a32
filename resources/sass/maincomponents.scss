.title {
  font-weight: bold;
}

code {
  display: block;
}

form {

  .form-field {
    margin-bottom: 8px;

    .message {
      font-size: 12px;
      background: transparent;
      padding-left: 9px;
      padding-top: 4px;
      padding-bottom: 10px;
      color: $grey-dark;
      min-height: 32px;

    }

    &.error .message {
      color: $red;
    }

    &.warning .message {
      color: $orange;
    }

    &.ok .message {
      color: $green;
    }

  }

  .input {
    height: 40px;
    border: 1px solid #BABCBF;
    border-radius: 4px;
    box-shadow: none;
    font-size: 14px;
  }
}

.nav-back {
  cursor: pointer;
  opacity: 0.5;
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  /* webpackIgnore: true */
  background-image: url('/images/icons/noun_Left_1037791.svg');
  background-size: cover;

  &:hover {
    opacity: 1;
  }
}

.button {
  height: 46px;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1px;

  &.is-info.spinner,
  &.spinner {
    text-align: center;
    background-repeat: no-repeat;
    background-position: center center;
    background-image: url('/images/spinning-loader.svg');
    background-size: 24px 24px;
    cursor: wait;
    color: transparent;
  }
}
.has-pointer {
  cursor: pointer;
}

.is-vertical-aligned {
  vertical-align: middle;
}

.main-title {
  font-size: 25px;
  font-weight: bold;
  margin-bottom: 30px;
}

.modal-content {
  border-radius: 4px;
}