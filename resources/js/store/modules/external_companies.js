import axios from "axios";

const state = {
    loading: false,
    externalCompanies: [],
    selectedExternalCompany: null
};

const getters = {};

const actions = {
    loadAllCompanyInfo({commit, dispatch}, account_service_id) {
        dispatch('loadExternalCompanies', account_service_id);
    },
    loadExternalCompanies({commit, state}, [account_service_id, q]) {
        if (state.loading) {
            console.warn('Ignored refresh request because we are still waiting for the previous request.');
        } else {
            let url = '/service/' + account_service_id + '/external_companies';
            if (q !== '') {
                url += '?q=' + encodeURIComponent(q);
            }
            commit('setLoadingState', true);
            return axios.get(url).then((response) => {
                commit('setExternalCompanies', response.data.data);
                commit('setLoadingState', false);
            }).catch((error) => {
                commit('setLoadingState', false);
            });
        }
    },
    resetExternalCompanies({commit}) {
        commit('setLoadingState', true);
        commit('setExternalCompanies', []);
    },
    selectExternalCompany({commit}, company) {
        commit('selectExternalCompany', company);
    }
};

const mutations = {
    setExternalCompanies(state, data) {
        state.externalCompanies = data
    },
    setLoadingState(state, value) {
        state.loading = value;
    },
    selectExternalCompany(state, company) {
        state.selectedExternalCompany = company;
    }
};

export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations
}