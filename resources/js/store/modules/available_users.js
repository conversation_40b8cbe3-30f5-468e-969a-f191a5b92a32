import axios from "axios";

const state = {
    all: [],
    companyIds: [],
    internalUsers: {
        all: [],
        companyIds: [],
    },
}

/*
 This store can only be used right now on the Member Portal, since we don't change the
 external users on that side.
 */
const getters = {
    getByCompanyId: (state) => (id) => {
        const index = state.companyIds.indexOf(id);
        if (index === -1) {
            return null;
        }

        return state.all[index];
    },
    hasExternalUsers: (state, getters) => (id) => {
        const externalUsers = getters.getByCompanyId(id);

        return externalUsers !== null && externalUsers.length > 0
    },
    getInternalUsers: (state) => (id) => {
        const index = state.internalUsers.companyIds.indexOf(id);
        if (index === -1) {
            return null;
        }

        return state.internalUsers.all[index];
    },
    hasInternalUsers: (state, getters) => (id) => {
        const internalUsers = getters.getInternalUsers(id);

        return internalUsers !== null && internalUsers.length > 0
    },
}

const actions = {
    loadExternalByCompanyId({state, commit, getters}, id) {
        const externalUsers = getters.getByCompanyId(id);
        if (externalUsers === null) {
            // While the axios requests completes, some other requests might happen,
            // to prevent further axios calls, we will set already the id and empty.
            commit('addExternalUsers', [id, []])

            return axios.get('/company/' + id + '/users/external')
                .then((response) => {
                    commit('addExternalUsers', [id, response.data.data]);
                })
        }
    },

    loadInternalByCompanyId({state, commit, getters}, id) {
        const internalUser = getters.getInternalUsers(id);
        if (internalUser === null) {
            // While the axios requests completes, some other requests might happen,
            // to prevent further axios calls, we will set already the id and empty.
            commit('addInternalUsers', [id, []])

            return axios.get('/company/' + id + '/users/internal')
                .then((response) => {
                    commit('addInternalUsers', [id, response.data.data]);
                })
        }
    },
};

const mutations = {
    addExternalUsers(state, [companyId, data],) {
        let index = state.companyIds.indexOf(companyId);

        if (index === -1) {
            state.all.push(data);
            state.companyIds.push(companyId);
        } else {
            state.all[index] = data;
        }
    },
    addInternalUsers(state, [companyId, data],) {
        let index = state.internalUsers.companyIds.indexOf(companyId);

        if (index === -1) {
            state.internalUsers.all.push(data);
            state.internalUsers.companyIds.push(companyId);
        } else {
            state.internalUsers.all[index] = data;
        }
    },
}

export default {
    namespaced: true,
        state,
        getters,
        actions,
        mutations
    }