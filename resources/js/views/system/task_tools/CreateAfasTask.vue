<template>
    <div class="create-afas-task content-panel">

        <ContentHeader :image="logoPath" :title="title" />

        <div class="inner-content-panel">

            <form method="post" @submit="submit" ref="form">

                <fieldset>
                    <label>Account ID: <input type="number" name="account_id" value="309" maxlength="11" min="1" max="9999999" /></label>
                </fieldset>

                <fieldset>
                    <label>Company ID: <input type="number" name="company_id" maxlength="11" min="1" max="9999999" /></label>
                </fieldset>

                <fieldset>
                    <label><input type="radio" name="declaration_type" value="IB" checked="checked" /> IB</label>

                    <label><input type="radio" name="declaration_type" value="VPB" /> VPB</label>
                </fieldset>

                <fieldset>
                    <label><input type="checkbox" name="xbrl" value="1" checked="checked" /> XBRL</label>
                    <label><input type="checkbox" name="pdf" value="1" checked="checked" /> PDF</label>
                </fieldset>

                <input type="submit" class="button is-info" :value="$t('system.task_tools.create_afas_task.create_button')" />

            </form>
        </div>
    </div>
</template>

<script>
import Input from "../../../components/Forms/Input";
import ContentHeader from "../../../components/ContentHeader";
import TaskInfo from "./TaskInfo";
import Loader from "../../../components/Loader";
import ActionBar from "../../../components/SelectBar";
import Textarea from "../../../components/Forms/Textarea";
import Label from "../../../components/Forms/Label";

export default {
    name: 'CreateAfasTask',
    components: {
        Label,
        ContentHeader,
        Textarea,
        ActionBar,
        Loader,
        Input,
        TaskInfo
    },
    data() {
        return {
            logoPath: '/images/services/afas.png',
            title: this.$t('system.titles.create_afas_task')
        }
    },
    computed: {
        deleteButtonDisabled() {
            if (this.reason) {
                let trimmedReason = this.reason.trim();
                if (trimmedReason.length >= 2) {
                    return false;
                }
            }
            return true;
        }
    },
    methods: {
        submit(event) {
            event.preventDefault();
            let form = this.$refs.form;
            this.axios.post('/system/task_tools/create_afas_task', new FormData(form));
        }
    }
}

</script>

<style lang="scss" scoped>
@import "../../../../sass/initial-variables";

.create-afas-task {

    form {
        padding: 40px;

        fieldset {
            margin-bottom: 20px;

            label {
                margin-right: 20px;
            }
        }
    }
}
</style>