<template>
    <div class="company-overview content-panel">
        <ContentHeader :show-back="true" :image="company.image" :logo-icon="company.icon" :logo-text="company.title"
                       :logo-background-color="company.color" :title="company.title" :subtitle="company.subtitle"
                       :subtitle-url="company.subtitleUrl"/>

        <div class="row">
            <div class="column is-two-thirds">
                <h1>{{ $t('menu.settings') }}</h1>
                <br>

                <Loader :is-loading="isLoading">
                    <Label :text="$t('open_questions.frequency')"/>
                    <MultipleSelect @input="updateFrequency"
                                    label="value"
                                    :value="frequency"
                                    :options="frequencyOptions"
                                    track-by="key"
                                    :multiple="false"
                                    :close-on-select="true"
                                    :allow-empty="false"
                    />
                    <Label :text="$t('open_questions.day')"/>
                    <MultipleSelect @input="updateDay"
                                    label="value"
                                    :value="day"
                                    :options="dayOptions"
                                    track-by="key"
                                    :multiple="false"
                                    :close-on-select="true"
                                    :allow-empty="false"
                                    :disabled="frequency === 'manually' || (accountPreferences.frequency === 'manually' && frequency === null)"
                    />
                    <Label :text="$t('open_questions.hour')"/>
                    <MultipleSelect @input="updateHour"
                                    label="value"
                                    :value="hour"
                                    :options="hourOptions"
                                    track-by="key"
                                    :multiple="false"
                                    :close-on-select="true"
                                    :allow-empty="false"
                                    :disabled="frequency === 'manually' || (accountPreferences.frequency === 'manually' && frequency === null)"
                    />
                    <br>

                    <table class="table is-fullwidth user-notifier-settings-table" v-if="company">
                        <thead>
                        <tr>
                            <td colspan="2"><b>{{ $t('tasks.table_headers.client_users') }}</b></td>
                            <td class="has-text-centered"><b>{{ $t('open_questions.categories.bookkeeping') }}</b></td>
                            <td class="has-text-centered"><b>{{ $t('open_questions.categories.wage') }}</b></td>
                            <td class="has-text-centered"><b>{{ $t('open_questions.categories.ocr') }}</b></td>
                            <td class="has-text-centered"><b>{{ $t('open_questions.categories.fiscal') }}</b></td>
                            <td class="has-text-centered"><b>{{ $t('open_questions.categories.yearwork') }}</b></td>
                            <td class="has-text-centered"><b>{{ $t('open_questions.categories.other') }}</b></td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(external_user, i) in external_users(company.id)">
                            <td class="user-info" colspan="2">
                                <Logo :width="24" :height="24"
                                      :background-color="external_user.color" :text="external_user.name"/>
                                <div class="text">
                                    <div class="name" :title="external_user.name">
                                        <span>{{ external_user.name }}</span>
                                    </div>
                                    <div class="email" :title="external_user.email">{{ external_user.email }}</div>
                                    <div class="mobile" :title="external_user.mobile">{{ external_user.mobile }}</div>
                                </div>
                            </td>

                            <td v-if="external_user.warning" class="has-text-centered" colspan="3">
                                <div class="warning-notification">
                                    <span><i class="sl-icon-warning has-text-warning"/></span>
                                    <span>{{ external_user.warning }}</span>
                                </div>
                            </td>
                            <template v-else>
                                <td class="has-text-centered">
                                    <Checkbox
                                        :name="'bookkeeping-'+ external_user.id"
                                        @updateCheck="updateSettings(external_user.id, 'bookkeeping')"
                                        :checked="checkSettings(external_user.id, 'bookkeeping')"
                                    />
                                </td>
                                <td class="has-text-centered">
                                    <Checkbox
                                        :name="'wages-'+ external_user.id"
                                        @updateCheck="updateSettings(external_user.id, 'wage')"
                                        :checked="checkSettings(external_user.id, 'wage')"
                                    />
                                </td>

                                <td class="has-text-centered">
                                    <Checkbox
                                        :name="'ocr-'+ external_user.id"
                                        @updateCheck="updateSettings(external_user.id, 'ocr')"
                                        :checked="checkSettings(external_user.id, 'ocr')"
                                    />
                                </td>

                                <td class="has-text-centered">
                                    <Checkbox
                                        :name="'fiscal-'+ external_user.id"
                                        @updateCheck="updateSettings(external_user.id, 'fiscal')"
                                        :checked="checkSettings(external_user.id, 'fiscal')"
                                    />
                                </td>

                                <td class="has-text-centered">
                                    <Checkbox
                                        :name="'yearwork-'+ external_user.id"
                                        @change="updateSettings(external_user.id, 'yearwork')"
                                        :checked="checkSettings(external_user.id, 'yearwork')"

                                    />
                                </td>

                                <td class="has-text-centered">
                                    <Checkbox
                                        :name="'others-'+ external_user.id"
                                        @updateCheck="updateSettings(external_user.id, 'other')"
                                        :checked="checkSettings(external_user.id, 'other')"
                                    />
                                </td>
                            </template>
                        </tr>
                        </tbody>
                    </table>

                    <button type="button" class="button is-info" @click="save()">{{ $t('common.save') }}</button>
                    <button type="button" class="button is-text" @click="cancel()">{{ $t('common.cancel') }}</button>
                </Loader>
            </div>
        </div>
    </div>
</template>

<script>
import ContentHeader from "../../../components/ContentHeader";
import {mapActions, mapGetters, mapMutations} from "vuex";
import Logo from "../../../components/Logo";
import SingleSelect from "../../../components/Forms/SingleSelect";
import MultipleSelect from "../../../components/Forms/MultipleSelect";
import Label from "../../../components/Forms/Label";
import OpenQuestionsApi from "../../../api/OpenQuestionsApi";
import Loader from "../../../components/Loader";
import SingleRadioButton from "../../../components/Forms/SingleRadioButton";
import Checkbox from "../../../components/Checkbox";

export default {
    name: "OpenQuestionsSettings",
    components: {
        SingleRadioButton,
        Loader,
        Label,
        MultipleSelect,
        SingleSelect,
        Logo,
        ContentHeader,
        Checkbox
    },
    data() {
        return {
            isLoading: true,
            frequency: null,
            day: null,
            hour: null,
            accountPreferences: null,
            userSettings: {},
            defaultFrequencyOptions: [
                {'key': null, 'value': this.$t('service.open_questions.fields.account_settings')},
                {'key': 'weekly', 'value': this.$t('service.open_questions.fields.frequency_options.weekly')},
                {'key': 'biweekly', 'value': this.$t('service.open_questions.fields.frequency_options.biweekly')},
                {'key': 'monthly', 'value': this.$t('service.open_questions.fields.frequency_options.monthly')},
                {'key': 'manually', 'value': this.$t('service.open_questions.fields.frequency_options.manually')},
            ],
            defaultDayOptions: [
                {'key': null, 'value': this.$t('service.open_questions.fields.account_settings')},
                {'key': 'monday', 'value': this.$t('service.open_questions.fields.day_options.monday')},
                {'key': 'tuesday', 'value': this.$t('service.open_questions.fields.day_options.tuesday')},
                {'key': 'wednesday', 'value': this.$t('service.open_questions.fields.day_options.wednesday')},
                {'key': 'thursday', 'value': this.$t('service.open_questions.fields.day_options.thursday')},
                {'key': 'friday', 'value': this.$t('service.open_questions.fields.day_options.friday')},
                {'key': 'saturday', 'value': this.$t('service.open_questions.fields.day_options.saturday')},
                {'key': 'sunday', 'value': this.$t('service.open_questions.fields.day_options.sunday')},
            ],
            defaultHourOptions: [
                {'key': null, 'value': this.$t('service.open_questions.fields.account_settings')},
                {'key': 0, 'value': '00:00'},
                {'key': 1, 'value': '01:00'},
                {'key': 2, 'value': '02:00'},
                {'key': 3, 'value': '03:00'},
                {'key': 4, 'value': '04:00'},
                {'key': 5, 'value': '05:00'},
                {'key': 6, 'value': '06:00'},
                {'key': 7, 'value': '07:00'},
                {'key': 8, 'value': '08:00'},
                {'key': 9, 'value': '09:00'},
                {'key': 10, 'value': '10:00'},
                {'key': 11, 'value': '11:00'},
                {'key': 12, 'value': '12:00'},
                {'key': 13, 'value': '13:00'},
                {'key': 14, 'value': '14:00'},
                {'key': 15, 'value': '15:00'},
                {'key': 16, 'value': '16:00'},
                {'key': 17, 'value': '17:00'},
                {'key': 18, 'value': '18:00'},
                {'key': 19, 'value': '19:00'},
                {'key': 20, 'value': '20:00'},
                {'key': 21, 'value': '21:00'},
                {'key': 22, 'value': '22:00'},
                {'key': 23, 'value': '23:00'},
            ]
        }
    },
    computed: {
        ...mapGetters({
            company: 'open_questions/selectedCompany',
            external_users: 'available_users/getByCompanyId',
        }),
        frequencyOptions() {
            let frequencyOptions = this.defaultFrequencyOptions;
            if (this.accountPreferences) {
                frequencyOptions.find(option => option.key === null).value += ': ' + frequencyOptions.find(option => option.key === this.accountPreferences.frequency).value
            }
            return frequencyOptions;
        },
        dayOptions() {
            let dayOptions = this.defaultDayOptions;
            if (this.accountPreferences) {
                dayOptions.find(option => option.key === null).value += ': ' + dayOptions.find(option => option.key === this.accountPreferences.day).value
            }
            return dayOptions;
        },
        hourOptions() {
            let hourOptions = this.defaultHourOptions;
            if (this.accountPreferences) {
                hourOptions.find(option => option.key === null).value += ': ' + hourOptions.find(option => option.key === this.accountPreferences.hour).value
            }
            return hourOptions;
        },
    },
    created() {
        if (this.company) {
            this.loadExternalUsers(this.company.id).then(() => {
                OpenQuestionsApi.getUserNotifierSettings(this.company.id).then(response => {
                    let data = response.data;
                    data.forEach((user) => {
                        this.external_users(this.company.id).forEach((element, index) => {
                            if (element.id === user.user_id) {
                                this.userSettings[user.user_id] = user.category.split(',');
                            }
                        });
                    });

                    this.isLoading = false;
                })
            });
            OpenQuestionsApi.notifierCompanySettings(this.company.id).then(response => {
                this.frequency = response.data.data.frequency ? response.data.data.frequency : null;
                this.day = response.data.data.day ? response.data.data.day : null;
                this.hour = response.data.data.hour ? response.data.data.hour : null;
                this.accountPreferences = response.data.data.account_preferences;
            });
        }
    },
    methods: {
        ...mapMutations({
            updateCompany: 'open_questions/updateCompany',
        }),
        ...mapActions({
            loadExternalUsers: 'available_users/loadExternalByCompanyId',
            reloadCompanies: 'open_questions/reloadCompanies'
        }),
        cancel() {
            this.$router.back();
        },
        updateSettings(userId, category) {
            if (!this.userSettings[userId]) {
                this.userSettings[userId] = [];
            }

            let index = this.userSettings[userId].indexOf(category);
            if (index < 0) {
                this.userSettings[userId].push(category);
            } else {
                this.userSettings[userId].splice(index, 1);
            }
        },
        checkSettings(userId, category) {
            let userSettings = this.userSettings[userId];
            if (userSettings) {
                return this.userSettings[userId].includes(category)
            }
            return false;
        },
        save() {
            OpenQuestionsApi.notifierCompanyUpdateSettings(
                this.company.id,
                {
                    frequency: this.frequency,
                    day: this.day,
                    hour: this.hour,
                    user_notifier_settings: this.userSettings
                }
            ).then((res) => {
                this.updateCompany(res.data.data);
            });
        },
        inputEmail(index) {
            this.users.email[index] = true;
        },
        inputNothing(index) {
            this.users.email[index] = false;
        },
        updateFrequency(frequency) {
            this.frequency = frequency;
        },
        updateDay(day) {
            this.day = day;
        },
        updateHour(hour) {
            this.hour = hour;
        }
    }
}
</script>

<style lang="scss" scoped>
@import '../../../../sass/initial-variables';

.row {
    display: flex;
    flex-wrap: wrap;
    padding: 30px 50px;
}

.button.is-text {
    color: $link;
    text-decoration: none;
}

.checkbox {
    margin: auto;
    width: 14px;
}

table {
    width: 100%;
    color: $grey-darker;
    table-layout: fixed;

    thead td {
        border-bottom: 1px solid $black-10;
        padding: 10px;

        &.has-text-centered {
            padding-left: 5px;
            padding-right: 5px;
        }
    }

    tbody {

        td {
            vertical-align: middle;
            height: 50px;
            border-bottom: 1px solid #f8f8f8;

            .warning-notification {
                display: flex;
                align-items: center;
                justify-content: center;

                .sl-icon-warning {
                    font-size: 18px;
                    vertical-align: sub;
                    padding-right: 5px;
                }
            }

        }

        tr:last-child {
            td {
                border-bottom: none;
            }
        }

        time {
            display: block;
            font-size: 10px;
            line-height: 14px;
            color: $black-40;
        }

        .user-info {
            position: relative;
            min-width: 140px;

            .text {
                position: absolute;
                left: 54px;
                right: 0;
                top: 8px;
                bottom: 8px;

                a {
                    vertical-align: middle;
                    &.disabled {
                        color: $black-10;
                        pointer-events: none;
                    }
                }

                .name,
                .email,
                .mobile {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .email,
                .mobile {
                    font-size: 10px;
                    line-height: 11px;
                    color: $black-40;
                }
            }
        }
    }
}
</style>