<template>
    <div class="open-questions-manual">
        <div class="manual-container">
            <div class="header-container">
                <form method="post" class="manual-form">
                    <h2 class="title is-4">{{ $t('open_questions.manual_question.create_question') }}:
                        {{ $t('open_questions.categories.' + manualQuestionCategory) }}</h2>
                    <div class="form-field-grid">
                        <Input
                            v-model:value="formData['name']"
                            :type="'text'"
                            :placeholder="$t('open_questions.manual_question.fields.name')"
                            :maxlength="350"
                        />
                        <div class="field-grid">
                            <span class="euro">&euro;</span>
                            <Input
                                v-model:value="formData['amount']"
                                :type="'text'"
                                :placeholder="$t('open_questions.manual_question.fields.amount')"
                            />
                        </div>
                        <DatePicker
                            @input="value => formData['transaction_date'] = value.split('T')[0]"
                            :placeholder="$t('open_questions.manual_question.fields.transaction_date')"
                        />
                        <Input
                            v-model:value="formData['invoice_number']"
                            :type="'text'"
                            :placeholder="$t('open_questions.manual_question.fields.invoice_number')"
                        />
                    </div>
                    <SingleSelect
                        v-model:value="formData['type_id']"
                        class="form-field"
                        :label="$t('open_questions.manual_question.fields.type')"
                        name="type_id"
                        :options="this.types"
                        @input="handleMissingInvoice"
                    />
                    <SingleSelect
                        v-if="showMissingInvoicesTypes"
                        class="form-field"
                        :label="$t('open_questions.manual_question.fields.missing_invoice_type')"
                        name="missing_invoice_type"
                        value="purchase"
                        :options="missingInvoicesTypes"
                    />
                    <Textarea v-model:value="formData['internal_note']"
                              :label="$t('open_questions.manual_question.fields.internal_note')"
                              :fixed-size="true"
                              :minlength="0"
                              :maxlength="1000"
                              :disabled="formData['type'] === 'private_or_business'"
                              :fixedSize="true"
                    />
                    <Switch
                        :label="$t('open_questions.manual_question.fields.attachment_needed')"
                        :disabled="attachmentSwitchDisabled"
                        :checked="formData['attachment_needed']"
                        id="attachment_needed"
                        name="attachment_needed"
                        @input="value => formData['attachment_needed'] = value"
                    />
                    <div class="label">{{ $t('open_questions.manual_question.fields.attach_file') }}</div>
                    <div class="file-container">
                        <div class="button-wrap" v-if="formData['files'].length < 5">
                            <UploadButton @upload="uploadFile" :multiple="true"/>
                        </div>
                        <div v-for="(file, index) in formData['files']">
                            <div v-if="file.name" class="file-label">
                                <i class="sl-icon-file"/>{{ file.name }}
                            </div>
                            <div class="file-delete">
                                <i class="sl-icon-close-cross delete-icon" @click="removeFile(index)"/>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="actions-bar">
            <div class="action-button" @click="save">
                <a>
                    <span>{{ $t('common.create') }}</span>
                </a>
            </div>
            <div class="action-button" @click="closeCreate">
                <a>
                    <span>{{ $t('common.close') }}</span>
                </a>
            </div>
        </div>
    </div>
</template>

<script>
import FormSubmitHandler from "../../../../mixins/FormSubmitHandler";
import FormGenerator from "../../../../components/Forms/FormGenerator";
import {mapMutations, mapActions, mapGetters} from "vuex";
import Input from "../../../../components/Forms/Input";
import DatePicker from "../../../../components/Forms/DatePicker";
import SingleSelect from "../../../../components/Forms/SingleSelect";
import MultiSelect from "../../../../components/Forms/MultipleSelect";
import Switch from "../../../../components/Forms/Switch";
import Textarea from "../../../../components/Forms/Textarea";
import UploadButton from "../../../../components/Forms/UploadButton.vue";

export default {
    name: "BookkeepingManualQuestion",
    mixins: [FormSubmitHandler],
    components: {UploadButton, Switch, SingleSelect, DatePicker, Input, FormGenerator, Textarea, MultiSelect},
    props: {
        company: {
            required: true,
            type: Object,
        },
    },
    data() {
        return {
            formData: {
                name: null,
                amount: null,
                transaction_date: null,
                invoice_number: null,
                type_id: null,
                missing_invoice_type: null,
                internal_note: null,
                attachment_needed: false,
                files: []
            },
            types: null,
            typesComplete: null,
            attachmentSwitchDisabled: false
        }
    },
    created() {
        this.axios.get('/open_questions/question/types', {
            params: {
                'service_name': 'manual_questions',
                'category': this.manualQuestionCategory
            }
        }).then((response) => {
            this.typesComplete = response.data;
            let values = {};
            for (let i = 0; i < response.data.length; i++) {
                values[response.data[i].id] = response.data[i].name;
            }
            this.types = values;
        });
    },
    computed: {
        ...mapGetters({
            manualQuestionCategory: 'open_questions/manualQuestionCategory',
        }),
        showMissingInvoicesTypes() {
            if (!this.typesComplete) {
                return false;
            }
            let missingInvoiceType = this.typesComplete.find((t) => t.key === 'missing_invoice');
            return parseInt(this.formData.type_id) === parseInt(missingInvoiceType.id);
        },
        missingInvoicesTypes() {
            return {
                "purchase": "Purchase",
                "sale": "Sale"
            };
        }
    },
    methods: {
        ...mapMutations({
            setIsCreatingManualQuestion: 'open_questions/setIsCreatingManualQuestion',
        }),
        ...mapActions({
            createManualQuestion: 'open_questions/createManualQuestion',
        }),
        submit(event) {
            event.preventDefault();
            let form = this.$refs.form;
            if (form.validate()) {
                this.save(event);
            }
        },
        save(event) {
            this.formData['company_id'] = this.company.id;
            let formData = new FormData();
            for (let key in this.formData) {
                if (this.formData[key] && key !== 'files') {
                    formData.append(key, this.formData[key]);
                }
            }

            for (let file in this.formData['files']) {
                formData.append('files[]', this.formData['files'][file]);
            }

            this.checkSubmit(event, () => this.createManualQuestion(formData));
        },
        closeCreate() {
            this.setIsCreatingManualQuestion(false);
        },
        uploadFile(file) {
            if (this.formData['files'].length >= 5) {
                return;
            }

            this.formData['files'].push(file);
        },
        removeFile(index) {
            this.formData['files'].splice(index, 1);
        },
        handleMissingInvoice(value) {
            let type = this.typesComplete.find(t => t.id === parseInt(value));
            this.formData.attachment_needed = type.preferences.file_upload_required;
            if (type.key === 'missing_invoice') {
                this.formData.missing_invoice_type = 'purchase';
            } else {
                this.formData.missing_invoice_type = null;
            }
            this.attachmentSwitchDisabled = type.key === 'private_or_business' || type.key === 'missing_invoice';
        }
    }
}
</script>

<style lang="scss" scoped>
@import '../../../../../sass/initial-variables';

.open-questions-manual {
    min-height: 560px;
    padding-bottom: 60px;

    .manual-container {
        border: solid 1px #d8d8d8;
        border-radius: 4px 4px 0 0;
        overflow: hidden;

        .manual-form {
            padding: 30px;

            .form-field-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-gap: 10px;
            }

            .field-grid {
                display: flex;
            }

            .euro {
                padding-right: 8px;
                line-height: 40px;
            }
        }
    }

    .actions-bar {
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        border: solid 1px #d8d8d8;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        border-top: none;
        height: 50px;

        a {
            color: #00AFF7;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            justify-content: center;

            &:hover {
                color: #363636;
            }

            span {
                padding-left: 5px;
                line-height: 14px;
                font-size: 14px;
            }
        }

        .action-button {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-around;
            align-items: center;
            cursor: pointer;

            &:hover {
                background-color: rgba($blue, 0.05);

                a {
                    color: black;
                }
            }
        }
    }

    .file-container {
        display: block;
        align-items: center;

        .button-wrap {
            padding-bottom: 10px;
        }

        .upload-file {
            width: 100px;
            min-width: 100px;
        }

        .file-label {
            display: inline-block;
            align-items: center;
            cursor: default;
            text-overflow: ellipsis;
            max-width: 280px;
            white-space: nowrap;

            i {
                margin-right: 5px;
            }
        }

        .file-delete {
            display: inline-block;
            position: relative;
            top: -5px;
        }

        .delete-icon {
            color: $red;
            padding: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;

            &:hover {
                color: $black;
            }
        }
    }

    .button {
        &.is-success {
            height: 40px;
            width: 110px;
        }
    }
}
</style>