<template>
    <div class="open-questions-history" v-if="selected">
        <div class="history-container" :class="{'has-actions': ['open', 'pending'].includes(selected.status)}">
            <div class="header-container">
                <div class="question-header">
                    <div class="title-container">
                        <div class="title">{{ cellValue(selected.title) }}</div>
                        <div class="sub-text">{{ getSubText(selected) }}</div>
                    </div>
                    <div class="title">{{ selected.currency }} {{ selected.amount }}</div>
                </div>
                <div class="separator">
                    <div class="title">{{ $t('service.open_questions.history') }}</div>
                </div>
            </div>

            <div class="messages-container" ref="messagesContainer">
                <div class="messages-sub-container">
                    <div v-for="auditLog in auditLogsSelected">
                        <QuestionMessage :auditLog="auditLog"/>
                    </div>
                </div>

            </div>
        </div>
        <template v-if="['open', 'pending', 'completed'].includes(selected.status.key) && !readOnly">
            <div class="reply-bar bars">
                <div class="reply-block">
                    <Input v-model:value="message" :maxlength="500" class="reply-input" @keyup.enter="sendReply"/>
                    <button class="button send-button" @click="sendReply">
                    <span class="icon">
                      <i class="sl-icon-send-message"/>
                    </span>
                    </button>
                </div>
            </div>
            <div class="actions-bar bars" v-if="selected.category !== 'client'">
                <div class="action-button" @click="deleteQuestion(selected.id)">
                    <a>
                        <i class="sl-icon-trash-can"/>
                        <span>{{ $t('open_questions.actions.remove') }}</span>
                    </a>
                </div>
                <div v-if="hasCloseButton" class="action-button" @click="closeQuestion(selected.id)">
                    <a>
                        <i class="sl-icon-check-mark"/>
                        <span>{{ $t('open_questions.actions.close') }}</span>
                    </a>
                </div>
            </div>
        </template>
    </div>

</template>

<script>
import QuestionMessage from './OpenQuestionMessage';
import Input from '../../../components/Forms/Input';
import {mapActions, mapState} from 'vuex';

export default {
    name: 'QuestionHistory',
    components: {QuestionMessage, Input},
    props: {
        readOnly: {
            type: Boolean,
            value: false,
        },
    },
    data() {
        return {
            message: null,
        };
    },
    mounted() {
        setTimeout(() => {
            this.scrollToBottom();
        }, 1000);
    },
    computed: {
        ...mapState({
            selected: state => state.open_questions.selected,
            auditLogsSelected: state => state.open_questions.auditLogsSelected,
        }),
        hasCloseButton() {
            return ((this.selected.type && this.selected.type.key) !== 'template' || this.selected.status.key === 'pending') && this.selected.status.key !== 'completed';
        },
    },
    methods: {
        ...mapActions({
            deleteQuestion: 'open_questions/deleteQuestion',
            closeQuestion: 'open_questions/closeQuestion',
            replyQuestion: 'open_questions/replyQuestion',
        }),
        sendReply: _.debounce(function() {
            if (this.message && this.message.trim() !== '') {
                if (this.selected.status.key === 'open' || this.selected.category === 'client') {
                    this.replyQuestion({
                        questionId: this.selected.id,
                        message: this.message.trim(),
                        shouldReopen: false,
                    }).then(() => {
                        this.message = null;
                    }).finally(() => {
                        this.scrollToBottom();
                    });
                } else {
                    this.$swal({
                        title: this.$t('open_questions.reply.reopen_title'),
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonColor: '#0F9BF3',
                        cancelButtonColor: '#0F9BF3',
                        cancelButtonText: this.$t('common.no'),
                        confirmButtonText: this.$t('common.yes'),
                        allowOutsideClick: false,
                    }).then((result) => {
                        this.replyQuestion({
                            questionId: this.selected.id,
                            message: this.message.trim(),
                            shouldReopen: result.isConfirmed,
                        }).then(() => {
                            this.message = null;
                        });
                    });
                }
            }
        }, 200),
        scrollToBottom() {
            let messagesContainer = this.$refs.messagesContainer;
            if (messagesContainer) {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        },
        cellValue(value) {
            if (value && value.hasOwnProperty('name')) {
                return value.name;
            }
            return value;
        },
        getSubText(selected) {
            if (selected.category === 'wage') {
                return selected.subtitle;
            }

            if (selected.type) {
                return selected.type.name;
            }

            return '';
        },
    },
    watch: {
        selected() {
            this.scrollToBottom();
        },
        auditLogsSelected() {
            this.scrollToBottom();
        },
    },
};
</script>

<style lang="scss" scoped>
@import '../../../../sass/initial-variables';

.open-questions-history {
    height: 560px;
    margin-bottom: 90px;

    .history-container {
        border: solid 1px #d8d8d8;
        border-radius: 4px;
        height: 500px;
        overflow: hidden;

        &.has-actions {
            border-radius: 4px 4px 0 0;
        }

        .header-container {
            padding: 30px 30px 0;
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .title-container {
            max-width: 250px;
        }

        .title {
            margin: 0;
            font-size: 14px;
            font-weight: bold;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }

        .sub-text {
            font-size: 10px;
            color: $black-40;
            margin: 0;
            padding-top: 5px;
        }

        .separator {
            display: flex;
            align-items: center;
            text-align: center;
        }

        .separator::before, .separator::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid $black-10;
        }

        .separator::before {
            margin-right: .5em;
        }

        .separator::after {
            margin-left: .5em;
        }

        .messages-container {
            overflow-y: auto;
            padding-top: 10px;
            height: 420px;

            .messages-sub-container {
                padding: 0 30px 0 30px;
            }
        }
    }

    .bars {
        background: white;
        display: flex;
        align-items: center;
        justify-content: space-around;
        border: solid 1px #d8d8d8;
        border-top: 0;

        &:last-of-type {
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }
    }

    .reply-bar {
        height: 60px;
    }

    .actions-bar {
        height: 50px;

        a {
            color: #00AFF7;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            justify-content: center;

            &:hover {
                color: #363636;
            }

            i {
                height: 16px;
            }

            span {
                padding-left: 5px;
                line-height: 14px;
                font-size: 14px;
            }
        }

        .action-button {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-around;
            align-items: center;
            cursor: pointer;

            &:hover {
                background-color: rgba($blue, 0.05);

                a {
                    color: black;
                }
            }
        }
    }

    .reply-block {
        width: 100%;
        display: flex;
        padding: 10px;
        justify-content: space-around;
        align-items: center;

        .reply-input {
            width: 80%;
        }

        .button:focus {
            outline: 0;
            border: none;
        }

        .send-button {
            border-radius: 50%;
            padding: 0;
            height: 35px;
            width: 35px;
        }
    }
}
</style>