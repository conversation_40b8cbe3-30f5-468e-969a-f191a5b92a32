<template>
    <div class="app-main-panel" :class="{'new-ui-menu': account.licenses.includes('new_ui_2024')}">
        <ListColumn
            :title="$t('company.title')"
            :add-route="'/manage/companies/new'"
            :add-button-visible="canManage"
            :add-button-title="$t('company.add_company')"
            :items="companies"
            :selected-item="company"
            :title-action="titleAction"
            @select-item="selectCompany"
            @title-action="redirectAdvancedActions"
            :loading="loading"
            :infinite-scroll="true"
            :loadNextItems="loadNextItems"
            :search="search"
            @searchChange="searchChange"
        />

        <div v-if="active" class="content-container">
            <Loader :is-loading="loading" :text="$t('common.loading')" />
            <RouterView v-show="!loading" v-slot="{ Component }" :company="company" :key="company && company.id + '_manage_companies_view'"/>
        </div>
    </div>
</template>

<script>

import ListColumn from '../components/ListColumn';
import {mapState, mapGetters, mapActions, mapMutations} from 'vuex';
import store from '../store/index';
import Loader from '../components/Loader';

export default {
    store: store,
    name: 'ManageCompaniesView',
    components: {
        ListColumn,
        Loader
    },
    computed: {
        ...mapState({
            items: state => state.managed_companies.all,
            page: state => state.managed_companies.page,
            canManage: state => state.user.profile.can.manage_companies,
            search: state => state.managed_companies.search,
            account: state => state.account.current_account,
            user: state => state.user.profile
        }),
        ...mapGetters({
            company: 'managed_companies/selected'
        }),
        'companies'() {
            return this.items.map(company => {
                if (company.users) {
                    company.members_count = _.size(company.users);
                }
                return company;
            })
        },
        titleAction() {
            if (this.items) {
                return this.$t('common.advanced_actions');
            }
        },
    },
    data() {
        return {
            active: true,
            initialId: null,
            loading: true,
        };
    },
    created() {
        let managedCompanyId = this.$route.params.managed_company_id;
        if (managedCompanyId && !isNaN(managedCompanyId)) {
            this.setInitialId(
                parseInt(managedCompanyId)
            );
        }
    },
    mounted() {
        this.loadNextItems().then(() => {
            if (this.initialId) {
                let company = this.getById(this.initialId);
                this.searchChange(company.title, this.initialId).finally(() => {
                    this.loading = false;
                });
            } else {
                if (this.items.length > 0) {
                    this.selectFirstCompany();
                } else {
                    this.$router.replace({name: 'companies-intro'});
                }
            }
        }).finally(() => {
            this.loading = false;
        });
    },
    methods: {
        ...mapActions({
            selectById: 'managed_companies/selectById',
            getById: 'managed_companies/getById',
            loadData: 'managed_companies/loadData'
        }),
        selectCompany(company = null, initial = false, redirectToOverview = true) {
            if (company && !initial) {
                this.selectCompanyById(company.id, redirectToOverview);
            }
        },
        selectCompanyById(id = null, redirectToOverview = true) {
            if (id === null) {
                return;
            }

            this.selectById(id);

            if (!this.companies || this.companies.length === 0) { // Prevents default company selection by returning when company is null.
                return;
            }

            if (this.company === null) {
                return this.selectCompany(this.companies[0], false, true);
            }

            if (redirectToOverview) {
                this.$router.push({name: 'manager-company-overview', params: {managed_company_id: id}});
            }
        },
        selectFirstCompany() {
            if (this.items.length) {
                this.selectCompanyById(this.items[0].id);
            }
        },
        redirectAdvancedActions() {
            this.$router.push({name: 'company-manager-advanced-actions'});
        },
        async searchChange(name = null, id = null) {
            await this.loadData({search: name, id: id}).then(() => {
                this.selectFirstCompany();
            });
        },
        async loadNextItems() {
            await this.loadData({search: null, id: null}).then(() => {
                this.selectFirstCompany();
            });
        },
        setInitialId(id) {
            this.initialId = id;
            this.selectCompanyById(id, false);
        },
    },
    activated() {
        this.active = true;
    },
    deactivated() {
        this.active = false;
    },
    beforeRouteEnter(to, from, next) {
        next(vm => {
            if(!vm.user || (!vm.user.can.use_declarations && !vm.user.can.use_open_questions && !vm.user.can.has_companies) || !vm.user.can.manage_companies || !vm.account.licenses.includes('companies')) {
                if (!vm.user || !vm.user.can.is_admin) {
                    vm.$router.push('start-user');
                } else {
                    vm.$router.push('manage-groups');
                }
            } else if (to.params.managed_company_id && !isNaN(to.params.managed_company_id)) {
                vm.setInitialId(parseInt(to.params.managed_company_id));
            }
        });
    },
};

</script>

<style lang="scss">

@import '../../sass/initial-variables';

table.company-information {
    td.key {
        width: 110px;
        min-width: 110px;
        padding-right: 10px;
    }

    tbody tr:first-child td {
        padding-top: 20px;
    }
}

</style>
