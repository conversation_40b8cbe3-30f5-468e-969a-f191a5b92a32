<template>
    <div class="account-edit content-panel">
        <div class="inner-edit-panel">
            <form method="post" @submit="submit">
                <FormGenerator v-model:schema="schema" v-model:value="formData" ref="form"/>
                <br>
                <button class="button is-info" type="submit">{{ $t('common.save') }}</button>
                <button v-if="edit" @click="deleteOption" class="button is-danger pull-right" type="button">{{$t('common.delete')}}</button>
            </form>
        </div>
    </div>
</template>

<script>

import ContentHeader from '../../../../components/ContentHeader';
import FormGenerator from "../../../../components/Forms/FormGenerator";
import FormSubmitHandler from '../../../../mixins/FormSubmitHandler';
import Name from "../../../../mixins/Account/ExternalLogin/Fields/Name";
import NameIdFormat from "../../../../mixins/Account/ExternalLogin/Fields/NameIdFormat";
import AuthId from "../../../../mixins/Account/ExternalLogin/Fields/AuthId";
import AuthSecret from "../../../../mixins/Account/ExternalLogin/Fields/AuthSecret";
import SpecificExternalLogin from "../../../../mixins/Account/ExternalLogin/Fields/SpecificExternalLogin";
import TwoStepAuthentication from "../../../../mixins/Account/ExternalLogin/Fields/TwoStepAuthentication";
import IdentityLinking from "../../../../mixins/Account/ExternalLogin/Fields/IdentityLinking";
import NotifyOnCreate from "../../../../mixins/Account/ExternalLogin/Fields/NotifyOnCreate";
import TenantName from "../../../../mixins/Account/ExternalLogin/Fields/Tenant";
import ProvisioningId from "../../../../mixins/Account/ExternalLogin/Fields/ProvisioningId";
import LoginButton from "../../../../mixins/Account/ExternalLogin/Fields/LoginButton";
import SelectGenericWidget from "../../../../mixins/Account/ExternalLogin/Fields/SelectGenericWidget";
import SelectGroup from "../../../../mixins/Account/ExternalLogin/Fields/SelectGroup";
import LogoutButton from "../../../../mixins/Account/ExternalLogin/Fields/LogoutButton";
import LoginEndpoints from "../../../../mixins/Account/ExternalLogin/Fields/LoginEndpoints";
import LogoutEndpoints from "../../../../mixins/Account/ExternalLogin/Fields/LogoutEndpoints";
import store from "../../../../store";
import vueJsonEditor from "vue-json-editor";
import {mapActions, mapState, mapGetters} from "vuex";

export default {
    name: 'OpenIDConnect',
    mixins: [
        // All of the mixins are loaded inverted
        SpecificExternalLogin,
        TwoStepAuthentication,
        SelectGroup,
        SelectGenericWidget,
        IdentityLinking,
        NotifyOnCreate,
        LoginButton,
        LogoutEndpoints,
        LoginEndpoints,
        AuthSecret,
        AuthId,
        NameIdFormat,
        Name,
        FormSubmitHandler,
    ],
    components: {
        FormGenerator,
        ContentHeader,
        vueJsonEditor
    },
    props: {
        consumer: null,
        groups: null,
        widgets: null,
        edit: false
    },
    data() {
        return {
            formData: {},
            schema: {}
        }
    },
    methods: {
        submit(event) {
            event.preventDefault();
            let form = this.$refs.form;
            if (form.validate()) {
                this.$emit('submit', this.formData);
            }
        },
        deleteOption(event) {
            event.preventDefault();
            this.$emit('delete', { consumer_id: this.consumer.id });
        },
    },
}

</script>