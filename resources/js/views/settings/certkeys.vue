<template>
    <div class="content-panel">
        <div class="inner-content-panel">
            <div class="inner-edit-panel">
                <Notification
                    v-if="account.cert_status === 'nearly_expired'"
                    type="warning"
                    :text="$t('settings.certkeys.expiration_warning')"
                />
                <h1>{{$t('settings.titles.certkeys')}}</h1>

                <br><br>
                {{$t('settings.certkeys.intro')}}
                <br><br>

                <form method="post" @submit="submit">
                    <FormGenerator v-model:schema="schema" v-model:value="formData" ref="form" />
                    <br><br>
                    <button class="button is-info">{{$t('common.save')}}</button>
                </form>
                <br>
                <hr>
                <table class="cert-table">
                    <thead>
                    <tr>
                        <th>{{$t('settings.certkeys.name')}}</th>
                        <th class="date">{{$t('settings.certkeys.modified')}}</th>
                        <th class="date">{{$t('settings.certkeys.expiration_date')}}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="key in certkeys" class="row">
                        <td class="name">{{key.filename}}</td>
                        <td class="date">{{key.modified}}</td>
                        <td class="date">{{key.valid_to}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>

<script>

import FormSubmitHandler from '../../mixins/FormSubmitHandler';
import Notification from "../../components/Forms/Notification";
import {mapState} from 'vuex';

export default {
    mixins: [FormSubmitHandler],
    name: 'CertKeysSettings',
    components: {
        Notification
    },
    computed: {
        ...mapState({
            account: state => state.account.current_account,
        }),
    },
    data() {
        return {
            status: null,
            certkeys: [],
            formData: {},
            schema: {
                certfile: {
                    fieldType: 'FileInput',
                    label: this.$t('settings.certkeys.file_label'),
                    name: 'certfile',
                    visibility: true,
                },
                password: {
                    fieldType: 'Input',
                    type: 'password_text',
                    name: 'password',
                    label: this.$t('settings.certkeys.password_label'),
                    required: false,
                    visibility: true
                },
            }
        }
    },
    created() {
        let url = '/settings/data/certkeys';

        this.axios.get(url).then((response) => {
            this.certkeys = response.data.data;
        });
    },
    methods: {
        submit(event) {
            event.preventDefault();
            const data = new FormData();
            if (this.formData.certfile !== undefined) {
                let certfile = this.formData.certfile[0];
                data.append('certfile', certfile);
            }

            if (this.formData.password !== undefined) {
                data.append('password', this.formData.password);
            }

            this.checkSubmit(event, () => this.axios.post('/settings/certkeys/add', data))
                .then((response) => {
                    this.certkeys = response.data.data;
                });
        }
    }
}

</script>

<style lang="scss" scoped>

    form {
        max-width: 280px;
    }

    .cert-table {
        width: 100%;

        .row {

            td {
                min-height: 60px;
            }
        }

        .date {
            text-align: center;
        }
    }

    .button{
        height: 50px;
        border-radius: 4px;
        padding: 17px;
        font-size: 14px;
        line-height: 1px;
    }
</style>