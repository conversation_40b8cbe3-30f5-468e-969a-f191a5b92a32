<template>
    <div id="sso-dashboard">
        <div class="dashboard-background scroll-cover"></div>
        <div class="scroll-border"></div>
        <div id="dashboard" class="dashboard">
            <div class="scroll-spacer"></div>
            <div class="widget-container">
                <div class="account-logo-container">
                    <div class="account-logo"></div>
                </div>
                <div class="navbar-container">
                    <div class="navbar-cover dashboard-background is-unselectable">
                        <nav class="navbar" role="navigation" aria-label="navigation">
                            <div class="navbar-menu">
                                <div class="navbar-start">
                                    <div class="navbar-item" @mouseenter="hoverOnShowAll(true)"
                                         @mouseleave="hoverOnShowAll(false)">
                                        <div v-show="hoverShowAll" class="popover-actions">
                                            <div class="popover-action" @click="favoriteCategory(null)"><i
                                                :class="[{'sl-icon-star': !showAllIsFavorite, 'sl-icon-star-filled': showAllIsFavorite}]"/>
                                            </div>
                                        </div>
                                        <a class="navbar-item-content" @click="switchToCategory(null)"
                                           :class="{'active-item': !activeCategory && !showTools}">
                                            {{ $t('common.show_all') }}
                                        </a>
                                    </div>

                                    <div class="navbar-item" v-if="toolsAvailable">
                                        <a class="navbar-item-content" @click="selectTools"
                                           :class="{'active-item': showTools === true}">
                                            {{ $t('menu.communication_widgets') }}
                                        </a>

                                    </div>
                                    <div
                                        v-for="(category, index) in dashboard_categories"
                                        class="navbar-item"
                                        :class="{'category-drop': drag && (!activeCategory || activeCategory.id !== category.id)}"
                                        @mouseenter="setDragCategory(category, $event)"
                                        @mouseleave="setHoverCategory(null)"
                                    >
                                        <div v-if="editCategory === category">
                                            <input
                                                class="input edit-dashboard-category"
                                                type="text"
                                                :id="'dashboard_category_'+index"
                                                v-model="category.name"
                                                :placeholder="$t('dashboard_category.add_category_placeholder')"
                                                @keyup="changeCategoryName($event, category)"
                                                @blur="changeCategoryName($event, category)"
                                                maxlength="20"
                                            />
                                        </div>
                                        <div v-else class="drag-container category-drop-zone"
                                             @mouseenter="setDragCategory(category, $event)">
                                            <div v-if="category && hoverCategory && category.id === hoverCategory.id"
                                                 class="popover-actions">
                                                <div v-if="index > 0" class="popover-action"
                                                     @click="moveCategory('left', category)"><i
                                                    class="sl-icon-arrow-left"/></div>
                                                <div class="popover-action" @click="favoriteCategory(category)"><i
                                                    :class="[{'sl-icon-star': !category.favorite, 'sl-icon-star-filled': category.favorite}]"/>
                                                </div>
                                                <div class="popover-action" @click="enableCategoryEdit(category)"><i
                                                    class="sl-icon-pencil"/></div>
                                                <div class="popover-action" @click="deleteCategory(category)"><i
                                                    class="sl-icon-trash-can"/></div>
                                                <div v-if="index < dashboard_categories.length-1" class="popover-action"
                                                     @click="moveCategory('right', category)"><i
                                                    class="sl-icon-arrow-right"/></div>
                                            </div>
                                            <a class="navbar-item-content is-inline-block"
                                               @click="switchToCategory(category)"
                                               :class="{'active-item': (activeCategory && activeCategory.id === category.id)}">
                                                {{ category.name }}
                                            </a>
                                            <i v-if="checkedWidgets.length > 0 && !(activeCategory && activeCategory.id === category.id)"
                                               class="sl-icon-undo is-inline-block drop-icon"
                                               :title="$t('dashboard_category.move_button_tooltip', {category: category.name})"
                                               @click="moveSelectedToCategory(category)"/>
                                        </div>
                                    </div>
                                    <div class="navbar-item" v-if="dashboard_categories.length < 5"
                                         @click="showAddCategory">
                                        <a class="navbar-item-content control has-icons-left"
                                           :class="{show_underline: addIsExpanded}">
                                            <div class="">
                                                <input
                                                    class="input"
                                                    id="add-category-name"
                                                    type="text"
                                                    @focusin="addIsExpanded = true"
                                                    :placeholder="$t('dashboard_category.add_category_placeholder')"
                                                    @keyup="addCategory($event)"
                                                    @blur="addCategory($event)"
                                                    maxlength="20"
                                                    :class="{expanded: addIsExpanded}"
                                                />
                                                <span class="add-category-name-icon icon is-small is-left">
                                                    <i class="sl-icon-plus"
                                                       :title="$t('dashboard_category.category_add_button_tooltip')"/>
                                                </span>
                                            </div>
                                        </a>
                                    </div>
                                </div>

                                <div class="navbar-end">
                                    <div class="navbar-item">
                                        <ExpandableSearchBar
                                            class="expandable-search"
                                            :value="searchValue"
                                            @searchChange="setSearch"
                                            :always-expanded="true"
                                            :use-debounce="false"
                                            @submit="startSingleWidget"
                                            ref="searchbar"
                                        />
                                    </div>
                                    <div class="navbar-item">
                                        <a>
                                            <router-link :to="{name: 'user-widget-new'}">
                                                {{$t('common.add_widget') }}
                                            </router-link>
                                        </a>
                                    </div>
                                    <div class="navbar-item" style="font-size: medium">
                                        <a>
                                            <router-link :to="{name: 'user-widget-overview'}">
                                                <span class="add-category-name-icon icon is-small is-left">
                                                    <i class="sl-icon-gear" :title="$t('menu.manage_widgets')"/>
                                                </span>
                                            </router-link>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </nav>
                    </div>
                </div>

                <div class="widget-grid" v-if="loading">
                    <Loader :is-loading="loading" :text="$t('common.loading')"></Loader>
                </div>

                <div class="widget-grid" v-else>
                    <div id="CommunicationWidgetsView" style="padding-left: 81px;" v-if="showTools">
                        <CommunicationWidgetsView/>
                    </div>
                    <div v-else>
                        <SlickList
                            :shouldCancelStart="() => {return isMobile}" axis="xy" :list="widgets"
                            :distance="10"
                            class="widgets-list columns is-multiline is-desktop"
                            @sort-start="dragStart"
                            @sort-move="onDrag"
                            @sort-end="dragEnd"
                            @update:list="sortWidgets"
                        >
                            <SlickItem v-for="(widget, index) in widgets" :index="index" :key="index"
                                       class="column is-12-mobile is-one-third-desktop is-one-quarter-widescreen is-one-quarter-fullhd is-unselectable is-paddingless">
                                <Widget
                                    :key="widget.id"
                                    @selected="appendCheckedWidgets"
                                    :widget="widget"
                                    :id="'widget'+index"
                                    :checked="checkedWidgets && checkedWidgets.find(w => w === widget.id)"
                                />
                            </SlickItem>
                            <div v-if="widgets.length < 16 && !loading && !activeCategory"
                                 class="column is-3 widget-height">
                                <router-link :to="{name: 'user-widget-new'}">
                                    <div class="box add-new-widget has-text-centered">
                                        <div class="add-widget-text">
                                            <i class="fa fa-plus-thin"/>
                                            {{ $t('common.add_widget') }}
                                        </div>
                                    </div>
                                </router-link>
                            </div>
                        </SlickList>
                        <SelectBar :show="checkedWidgets.length > 0" @cancel="clearSelection" class="widget-select-bar">
                            <template v-slot:navbar-start>
                                <div class="navbar-item selected-count">
                                    {{ $t('common.bootstrap_select_amount_selected', [checkedWidgets.length]) }}
                                </div>
                            </template>
                            <template v-slot:navbar-end>
                                <div class="navbar-item" v-if="activeCategory">
                                    <button class="button is-danger" @click="deleteFromCategory">
                                        {{ $t('widget.delete_from_category') }}
                                    </button>
                                </div>
                                <div class="navbar-item" v-if="!activeCategory">
                                    <button class="button is-danger" @click="deleteWidgets">
                                        {{ $t('widget.delete_from_dashboard') }}
                                    </button>
                                </div>
                            </template>
                        </SelectBar>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import Widget from '../components/Widget';
import ExpandableSearchBar from '../components/ExpandableSearchBar';
import {mapState, mapActions, mapMutations} from 'vuex';
import {SlickList, SlickItem} from 'vue-slicksort';
import Loader from '../components/Loader';
import DashboardHeaderScroll from '../mixins/DashboardHeaderScroll';
import SelectBar from '../components/SelectBar';
import CommunicationWidgetsView from './CommunicationWidgetsView';

export default {
    name: 'StartViewUser',
    mixins: [DashboardHeaderScroll],
    components: {
        Widget,
        ExpandableSearchBar,
        SlickItem,
        SlickList,
        Loader,
        SelectBar,
        CommunicationWidgetsView,
    },
    data() {
        return {
            unfilteredWidgets: [],
            searchValue: '',
            limitPosition: 132,
            newItem: '',
            dragging: -1,
            checkedWidgets: [],
            newIndex: 0,
            items: [],
            activeCategory: null,
            showTools: false,
            drag: false,
            widgetDragged: null,
            isCategory: false,
            dragCategory: null,
            hoverCategory: null,
            addIsExpanded: false,
            popOverTimeout: null,
            editCategory: null,
            hoverShowAll: false,
            dragOptions: {
                animation: 200,
                group: 'widgets',
                disabled: false,
                ghostClass: 'ghost',
            },
        };
    },
    computed: {
        ...mapState({
            toolsAvailable: state => state.user.profile.can.use_tools,
            widgets: state => state.user_widget.all,
            dashboard_categories: state => state.user.dashboard_categories,
            account: state => state.account.current_account,
            loading: state => state.user_widget.loading,
            isMobile: state => state.isMobile,
        }),
        showAllIsFavorite() {
            if (this.dashboard_categories) {
                return !this.dashboard_categories.find(x => x.favorite);
            } else {
                return false;
            }
        },
    },
    created() {
        this.loadDashboardCategories().then(() => {
            this.selectFavoriteCategory();
            if (this.activeCategory) {
                this.setLoading(true);
                this.loadWidgets().then(() => {
                    this.setWidgetData(this.filterWidgets());
                }).finally(() => {
                    this.setLoading(false);
                });
            } else {
                this.loadWidgets();
            }
        });
    },
    mounted() {
        this.$refs.searchbar.focus();
    },
    methods: {
        ...mapActions({
            loadDashboardCategories: 'user/loadDashboardCategories',
            favoriteDashboardCategory: 'user/favoriteDashboardCategory',
            moveDashboardCategory: 'user/moveDashboardCategory',
            addWidgetsToCategory: 'user/addWidgetsToCategory',
            editDashboardCategory: 'user/editDashboardCategory',
            sortCategoryWidgets: 'user/sortCategoryWidgets',
            addDashboardCategory: 'user/addDashboardCategory',
            deleteDashboardCategory: 'user/deleteCategory',
            deleteWidgetsFromCategory: 'user/deleteWidgetsFromCategory',
            setLoading: 'user_widget/setLoading',
            loadData: 'user_widget/loadData',
            deleteWidget: 'user_widget/deleteWidget',
        }),
        ...mapMutations({
            saveSortWidget: 'user_widget/sortWidgets',
            setWidgetData: 'user_widget/setData',
        }),
        loadWidgets() {
            this.setLoading(true);
            return this.loadData().finally(() => {
                this.setLoading(false);
            });
        },
        setSearch(value) {
            this.searchValue = value;
            if (this.searchValue === '') {
                this.loadWidgets().then(() => {
                    this.setWidgetData(this.filterWidgets());
                }).finally(() => {
                    this.setLoading(false);
                });
                return;
            }
            this.setLoading(true);
            this.setWidgetData(this.filterWidgets());
            this.setLoading(false);
        },
        filterWidgets() {
            let searchValue = this.searchValue;
            let return_value = [];
            let category = this.activeCategory;

            if (this.widgets) {
                if (category) {
                    let self = this;

                    category.user_widgets.filter(function (el) {
                        self.widgets.filter((user_widget) => {
                            if (user_widget.id === el.id) {
                                user_widget['category_widget_id'] = el.category_widget_id;
                                return_value.push(user_widget);
                            }
                        });
                    });
                } else {
                    return_value = this.widgets;
                }

                if (!this.unfilteredWidgets.length) {
                    this.unfilteredWidgets = return_value;
                }

                if (searchValue) {
                    return_value = this.unfilteredWidgets.filter(function (el) {
                        return el.label.toLowerCase().indexOf(searchValue.toLowerCase()) !== -1
                            || (el.description && el.description.toLowerCase().indexOf(searchValue.toLowerCase()) !== -1);
                    });
                } else {
                    return_value = return_value.filter(function (el) {
                        return el.visible || typeof el.visible === 'undefined';
                    });
                }
            }
            return return_value;
        },
        hoverOnShowAll: _.debounce(function (value) {
            if (this.checkedWidgets.length === 0) {
                this.hoverShowAll = value;
            }
        }, 500),
        changeCategoryName(event, category) {
            if ((event.keyCode === 13 || event.type === 'blur') && event.target.value) {
                this.editDashboardCategory([category.id, event.target.value]);
            }
        },
        enableCategoryEdit(category) {
            this.editCategory = category;
            this.$nextTick(() => {
                let index = this.dashboard_categories.findIndex(x => x.id === category.id);
                let element = document.getElementById('dashboard_category_' + index);
                element.focus();
            });
        },
        selectFavoriteCategory() {
            let favorite_category = this.dashboard_categories.find(x => x.favorite);
            if (favorite_category) {
                this.activeCategory = favorite_category;
            }
        },
        favoriteCategory(category) {
            let categoryId = category ? category.id : null;
            this.favoriteDashboardCategory(categoryId);
        },
        moveCategory(direction, category) {
            let index = this.dashboard_categories.findIndex(x => x.id === category.id);
            let newIndex = index;
            if ((direction === 'left' && index > 0) || (direction === 'right' && index < 4)) {
                if (direction === 'left') {
                    newIndex = index - 1;
                } else {
                    newIndex = index + 1;
                }
                this.dashboard_categories.splice(index, 1);
                this.dashboard_categories.splice(newIndex, 0, category);
                this.moveDashboardCategory(this.dashboard_categories);
            }
        },
        setHoverCategory: _.debounce(function (category) {
            if (this.checkedWidgets.length === 0) {
                this.hoverCategory = category;
            }
        }, 500),
        startSingleWidget(event) {
            if (this.widgets.length === 1) {
                document.querySelector('#widget0 .sso-widget').click();
            }
        },
        setDragCategory(category) {
            this.setHoverCategory(category);
            if (this.drag) {
                this.dragCategory = category;
            }
        },
        dragStart(element) {
            this.drag = true;
            this.widgetDragged = this.widgets[element.index];
            this.clearSelection();
        },
        onDrag(event) {
            this.isCategory = event.event.target.classList.contains('category-drop-zone') ||
                event.event.target.parentNode.classList.contains('category-drop-zone') ||
                (event.event.target.childNodes[0] &&
                    event.event.target.childNodes[0].classList &&
                    event.event.target.childNodes[0].classList.contains('category-drop-zone')
                );
        },
        dragEnd(event) {
            this.drag = false;
            if (!this.isCategory) {
                this.newIndex = event.newIndex;
            } else {
                event.event.target.parentNode.parentNode.classList.remove('category-drop');
                this.dropOnCategory(this.dragCategory);
            }
        },
        dropOnCategory(category) {
            if (category && (!this.activeCategory || category.id !== this.activeCategory.id)) {
                let self = this;
                this.$swal({
                    title: this.$t('common.are_you_sure'),
                    text: this.$t('dashboard_category.add_single_swal_message', {
                        category: category.name,
                        widget: self.widgetDragged.label,
                    }),
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#0F9BF3',
                    cancelButtonColor: '#999999',
                    confirmButtonText: this.$t('common.add'),
                    cancelButtonText: this.$t('common.cancel'),
                }).then((result) => {
                    if (result.value) {
                        self.addWidgetsToCategory([[self.widgetDragged.id], category]);
                    }
                }).finally(() => {
                    self.isCategory = false;
                    self.widgetDragged = null;
                });
            }
        },
        sortWidgets(event) {
            if (!this.isCategory) {
                if (!this.activeCategory) {
                    let newWidget = event[this.newIndex];
                    this.saveSortWidget([newWidget, this.newIndex]);
                } else {
                    let newWidget = event[this.newIndex];
                    let index = this.activeCategory.user_widgets.findIndex(w => w.id === newWidget.id);
                    this.activeCategory.user_widgets.splice(index, 1);
                    this.activeCategory.user_widgets.splice(this.newIndex, 0, newWidget);
                    this.sortCategoryWidgets([newWidget, this.newIndex]);
                    this.setWidgetData(this.filterWidgets());
                }
            }
        },
        appendCheckedWidgets(value) {
            if (!this.checkedWidgets.includes(value)) {
                this.checkedWidgets.push(value);
            } else {
                let index = this.checkedWidgets.indexOf(value);
                if (index > -1) {
                    this.checkedWidgets.splice(index, 1);
                }
            }
        },
        deleteWidgets() {
            let self = this;
            this.$swal({
                title: this.$t('widget.delete_from_dashboard'),
                html: this.$t('widget.delete_from_dashboard_text'),
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#F30F2A',
                cancelButtonColor: '#999999',
                confirmButtonText: this.$t('common.delete'),
                cancelButtonText: this.$t('common.cancel'),
            }).then((result) => {
                if (result.value) {
                    self.deleteWidget(self.checkedWidgets).then((response) => {
                        self.checkedWidgets = [];
                    });
                }
            });
        },
        deleteFromCategory() {
            let self = this;
            this.$swal({
                title: this.$t('widget.delete_from_category'),
                html: this.$t('widget.delete_from_category_text'),
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#F30F2A',
                cancelButtonColor: '#999999',
                confirmButtonText: this.$t('common.delete'),
                cancelButtonText: this.$t('common.cancel'),
            }).then((result) => {
                if (result.value) {
                    self.deleteWidgetsFromCategory([self.checkedWidgets, self.activeCategory]).then((response) => {
                        self.activeCategory.user_widgets = response;
                        self.setWidgetData(self.filterWidgets());
                        self.clearSelection();
                    });
                }
            });
        },
        showAddCategory() {
            let element = document.getElementById('add-category-name');
            element.focus();
        },
        addCategory(event) {
            if ((event.keyCode === 13 || event.type === 'blur') && event.target.value && this.dashboard_categories.length < 5) {
                this.addDashboardCategory(event.target.value).then(() => {
                    event.target.value = '';
                    event.target.blur();
                });
                this.addIsExpanded = false;
            } else if (event.type === 'blur' && !event.target.value) {
                this.addIsExpanded = false;
            }
        },
        deleteCategory(category) {
            let self = this;
            this.$swal({
                title: this.$t('common.are_you_sure'),
                text: this.$t('dashboard_category.delete_swal_message', {category: category.name}),
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#F30F2A',
                cancelButtonColor: '#999999',
                confirmButtonText: this.$t('common.delete'),
                cancelButtonText: this.$t('common.cancel'),
            }).then((result) => {
                if (result.value) {
                    self.deleteDashboardCategory(category).then(() => {
                        self.activeCategory = null;
                        self.loadWidgets();
                    });
                }
            });
        },
        moveSelectedToCategory(category) {
            let self = this;
            this.$swal({
                title: this.$t('common.are_you_sure'),
                text: this.$t('dashboard_category.add_multiple_swal_message', {category: category.name}),
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#0F9BF3',
                cancelButtonColor: '#999999',
                confirmButtonText: this.$t('common.add'),
                cancelButtonText: this.$t('common.cancel'),
            }).then((result) => {
                if (result.value) {
                    self.addWidgetsToCategory([self.checkedWidgets, category]).then(() => {
                        self.clearSelection();
                    });
                }
            });
        },
        switchToCategory(category) {
            if (this.activeCategory !== category) {
                this.unfilteredWidgets = [];
                this.clearSelection();
                this.activeCategory = category;
                this.setLoading(true);
                this.loadWidgets().then(() => {
                    this.setWidgetData(this.filterWidgets());
                }).finally(() => {
                    this.setLoading(false);
                });
            }
            this.showTools = false;
        },
        selectTools() {
            this.activeCategory = null;
            this.showTools = true;
        },
        clearSelection() {
            this.showTools = false;
            this.widgets.filter(x => x.isChecked === true).forEach((widget) => {
                widget.isChecked = false;
            });
            this.checkedWidgets = [];
        },
    },
    beforeRouteEnter(to, from, next) {
        next(vm => {
            if (vm.account.licenses.includes('new_ui_2024')) {
                window.location.replace('/app/portal/hix_login')
            }
        });
    },
    beforeRouteLeave(to, from, next) {
        this.clearSelection();
        next();
    },
};
</script>

<style lang="scss">
@import "../../sass/initial-variables";

body.state-sidebar-menu-collapsed #dashboard .widget-grid {
    padding-left: 60px;
}

body.state-sidebar-menu-collapsed #dashboard .widget-container .navbar-container .navbar .navbar-menu,
body.state-sidebar-menu-collapsed #dashboard .widget-container .deletebar-container .deletebar-cover .deletebar .deletebar-content {
    padding-left: 141px;
}

body.state-sidebar-menu-collapsed #dashboard .widget-container .account-logo-container {
    padding-left: 60px;
}

.animation-duration-0-3s {
    animation-duration: 0.3s;
}

body.fixed-header1 {

    #dashboard .widget-container .account-logo-container {
        position: fixed;
        top: 5px;
        left: 0;
        right: 0;

        .account-logo {
            transform: scale(0.67);
        }
    }
}

body.fixed-header2 {

    .navbar-cover {
        position: fixed;
        top: 90px;
        width: 100%;
        height: 45px;
        left: 0;
        right: 0;
        overflow: hidden;
        transition: left 0.3s;
    }

    .scroll-border {
        opacity: 1;
    }

    .navbar {
        background-color: rgba(255, 255, 255, 0.5);
    }
}

.app-main-panel {
    background: transparent;
}

.scroll-cover {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 90px;
    z-index: 990;
}

.scroll-border {
    position: fixed;
    top: 135px;
    left: 0;
    right: 0;
    height: 8px;
    z-index: 993;
    pointer-events: none;
    transition: opacity 0.15s;
    opacity: 0;
    /* webpackIgnore: true */
    background-image: url('/images/top-border-shadow.png');
    background-repeat: repeat-x;
}

#dashboard {
    position: relative;
    width: 100%;

    .widget-container {
        position: relative;
        background-color: rgba(255, 255, 255, 0.9);
        padding-bottom: 80px;
        min-height: calc(100vh - 220px);

        .account-logo-container {
            position: absolute;
            top: -80px;
            height: 112px;
            left: 0;
            right: 0;
            padding-left: 180px;
            z-index: 995;
            text-align: center;
            transition: padding-left 0.3s;
            pointer-events: none;

            /* background image is loaded in app.blade.php */
            .account-logo {
                display: inline-block;
                height: 112px;
                width: 112px;
                box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.3);
                border-radius: 50%;
                background-origin: content-box;
                background-size: cover;
                transform: scale(1.0);
                transition: transform 0.3s ease-out;
            }

        }

        .navbar-container {
            position: relative;
            height: 90px; /* needed so that the scrolling bar keeps its height when the content is moved to static position */
            padding-top: 37px;

            .navbar-cover {
                z-index: 992;
            }

            .navbar {
                background-color: rgba(255, 255, 255, 0.9);
                transition: opacity 0.15s, background-color 0.3s;
                margin-bottom: 0;

                .navbar-menu {
                    padding-left: 269px;
                    padding-right: 81px;
                    transition: padding-left 0.3s;

                    .navbar-item {
                        padding: 0 20px;
                        color: $black-40;
                        font-size: 12px;
                        box-sizing: border-box;

                        .edit-dashboard-category {
                            height: 24px;
                        }

                        .navbar-item-content {
                            color: $black-40;
                            font-size: 12px;

                            &:hover, &.active-item {
                                color: $blue;
                            }

                            .icon {
                                height: 24px !important;
                                width: 24px !important;
                                color: $black-40 !important;
                            }
                        }

                        &:hover {
                            .delete-icon {
                                text-shadow: 0 0 2px $red;
                            }
                        }
                    }

                    .delete-icon {
                        color: $red;
                        font-size: 16px;
                    }

                }
            }
        }
    }

    .widget-grid {
        margin-top: 20px;
        padding-left: 180px;
        transition: padding-left 0.3s;
    }

    .widgets-list {
        padding: 1px 81px;
        align-content: flex-start;
    }

    .fa-plus-thin:before {
        content: "\f067";
        font-weight: 100;
        color: $black-40;
    }

    .box {
        position: relative;
        cursor: pointer;
        border: 1px solid $black-10;
        border-radius: 8px;
        background-color: $white;
        box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.05);
        vertical-align: bottom;
        -webkit-transition: all 0.5s ease;
        transition: all 0.5s ease;

        &.add-new-widget {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        &:hover {
            border: 1px solid #F8F8F8;
            border-radius: 8px;
            box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.13);
        }

        .add-widget-text {
            line-height: 45px;
        }
    }

    .scroll-spacer {
        height: 220px;
    }
}

#add-category-name {
    height: 24px;
    width: 0;
    margin-left: 24px;
    font-size: 12px;
    background-color: transparent;
    border: none;
    box-shadow: none;
    line-height: 16px;
    padding: 0;
    -webkit-transition: width 0.4s ease-in-out;
    transition: width 0.4s ease-in-out;
    border-radius: 0;
    color: $black-40;

    &:focus, &:not(:placeholder-shown) {
        width: 180px !important;
    }

    &.expanded {
        width: 180px !important;
    }
}

.add-category-name-icon {
    margin: 0;
}

.drop-icon {
    padding: 0 5px;
    display: block !important;
    position: absolute;
    top: 0;
    left: calc(50% - 13px);
    color: $blue;
    font-size: 16px;
    font-weight: bold;

    &:hover {
        text-shadow: 0 0 5px $blue;
        cursor: pointer;
    }
}

.category-trash-can {
    color: $red;
    margin-left: 5px;
    position: absolute;
    left: calc(100% - 20px);
    top: 15px;

    &:hover {
        text-shadow: 0 0 3px $red;
        cursor: pointer;
    }
}

.active-button {
    color: $blue;
    text-shadow: 0 0 3px $blue;
}

.category-edit {

    &:hover {
        color: $blue;
        text-shadow: 0 0 3px $blue;
    }
}

.category-drop {
    outline: $black-25 1px dashed;

    &:hover {
        background-color: $black-03;
        outline: $blue 1px solid;
    }

}

.trash-can {
    color: $red;
    font-size: 16px;
    font-weight: bold;
}

.widget-height {
    height: 135px;
}

.widget-rescale {
    transform: scale(0.5);
    transition: 1s;
}

.drag-container {
    /*display: contents;*/
}

.show_underline {
    border-bottom: 1px solid #BBBBBB;
}

.widget-select-bar {
    padding: 0 70px
}

.popover-actions {
    display: flex;
    border: 1px solid $black-10;
    background-color: $white;
    top: -20px;
    position: absolute;
    border-radius: 5px;
    left: 50%;
    margin-right: -50%;
    transform: translate(-50%, -50%);

    .popover-action {
        min-width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        color: $black-40;
        cursor: pointer;

        &:hover {
            background-color: adjust_color($blue, $alpha: -0.9);
            color: $blue;
        }
    }
}

body.state-sidebar-menu-collapsed.is-mobile,
body.is-mobile {

    .scroll-cover {
        display: none;
    }

    #sso-dashboard {

        #dashboard {

            .widget-select-bar {
                padding-left: 0;
                padding-right: 0;

                .navbar-item {
                    padding: 0;
                }

                .selected-count {
                    padding: 0;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    line-height: 50px;
                    height: 50px;
                    padding-right: 20px;
                }
            }

            .widget-container {
                padding: 0;

                .navbar-container,
                .navbar-cover {
                    display: none;
                }

                .account-logo-container,
                .widget-grid {
                    margin-top: 0;
                    padding-left: 0;

                    .widgets-list {
                        padding-top: 40px;
                        padding-left: 20px;
                        padding-right: 20px;
                        margin: 0;
                    }
                }
            }
        }
    }
}

</style>