<template>
    <div class="unknown-preview">
        <div class="header">
            <span>{{title}}</span>
            <div class="actions-container" v-if="url">
                <a :href="url" download :title="$t('common.download')">
                    <i class="sl-icon-download"/>
                </a>
            </div>
        </div>
        <div class="preview-container">
            {{ $t('dossier.preview_not_available') }}
            <br>
            <br>
            <a :href="url" download>
                {{ $t('common.download') }}
            </a>
        </div>
    </div>
</template>

<script>
export default {
    name: "UnknownPreview",
    props: {
        url: {
            type: String
        },
        title: {
            type: String
        }
    }
}
</script>

<style lang="scss" scoped>

@import '../../../../sass/initial-variables';

.unknown-preview {
    border: 1px solid $black-10;
    border-radius: 4px;
    min-height: 600px;
    height: 70vh;
    display: flex;
    flex-direction: column;

    .header {
        padding-left: 10px;
        font-weight: bold;
        border-bottom: 1px solid $black-10;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .actions-container {
            display: flex;
            justify-content: flex-end;
            width: 120px;

            a {
                display: inline-block;
                width: 40px;
                height: 40px;
                line-height: 40px;
                text-align: center;

            }
        }
    }

    .preview-container {
        text-align: center;
        padding: 40px;
        overflow-y: auto;
        max-height: 50%;

        img {
            max-width: 100%;
            height: auto;
        }
    }
}

</style>