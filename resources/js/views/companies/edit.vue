<template>
    <div>
        <ContentHeader logo-icon="sl-icon-globe" logo-background-color="#999999" :title="companyName" :show-back="true" />
        <div class="columns content-columns">
            <div class="column is-two-thirds">
                <form method="post" @submit="submit($event)">
                    <FormGenerator v-model:schema="schema" v-model:value="formData" ref="form" />
                    <br>
                    <button class="button is-info" type="submit">{{$t('common.save')}}</button>
                    <router-link to="overview"><button class="button is-text">{{$t('common.cancel')}}</button></router-link>
                    <button v-if="company && company.info && company.info.can_be_deleted" @click="remove" class="button is-danger pull-right" type="button">{{$t('common.delete')}}</button>
                    <button v-else @click="archive" class="button is-danger is-inverted pull-right" type="button">{{$t('common.archive')}}</button>
                </form>
            </div>
        </div>
    </div>
</template>

<script>

    import ContentHeader from '../../components/ContentHeader';
    import FormSubmitHandler from '../../mixins/FormSubmitHandler';
    import store from '../../store/index';
    import {mapGetters, mapState, mapActions, mapMutations} from "vuex";
    import CompanyApi from "../../api/CompanyApi";

    export default {
        mixins: [FormSubmitHandler],
        name: 'CompanyEdit',
        store: store,
        components: {
            ContentHeader
        },
        props: {
            company: {
                type: Object,
                default: {
                    id: null,
                    name: '',
                    kvk_number: '',
                    address: '',
                    fiscal_number: '',
                    vat_number: '',
                    wage_tax_number: '',
                    bsn_number: '',
                    internal_client_id: ''
                }
            }
        },
        data() {
            return {
                formData: {},
                schema: {
                    name: {
                        fieldType: 'Input',
                        type: 'text',
                        label: this.$t('company.fields.name'),
                        name: 'name',
                        visibility: true,
                        required: true,
                        maxlength: 60
                    },
                    kvk_number: {
                        fieldType: 'Input',
                        type: 'number',
                        label: this.$t('company.fields.kvk_number'),
                        name: 'kvk_number',
                        visibility: true,
                        maxlength: 250,
                    },
                    kvk_number_help: {
                        fieldType: 'HelpText',
                        text: this.$t('company.fields.kvk_number_help'),
                        visibility: true
                    },
                    address: {
                        fieldType: 'Input',
                        type: 'text',
                        label: this.$t('company.fields.address'),
                        name: 'address',
                        visibility: true,
                        maxlength: 160
                    },
                    fiscal_number: {
                        fieldType: 'Input',
                        type: 'text',
                        label: this.$t('company.fields.fiscal_number'),
                        name: 'fiscal_number',
                        visibility: true,
                        maxlength: 250,
                    },
                    fiscal_number_help: {
                        fieldType: 'HelpText',
                        text: this.$t('company.fields.fiscal_number_help'),
                        visibility: true
                    },
                    vat_number: {
                        fieldType: 'Input',
                        type: 'text',
                        label: this.$t('company.fields.vat_number'),
                        name: 'vat_number',
                        visibility: true,
                        maxlength: 250
                    },
                    vat_number_help: {
                        fieldType: 'HelpText',
                        text: this.$t('company.fields.vat_number_help'),
                        visibility: true
                    },
                    wage_tax_number: {
                        fieldType: 'Input',
                        type: 'text',
                        label: this.$t('company.fields.wage_tax_number'),
                        name: 'wage_tax_number',
                        visibility: true,
                        maxlength: 250
                    },
                    wage_tax_number_help: {
                        fieldType: 'HelpText',
                        text: this.$t('company.fields.wage_tax_number_help'),
                        visibility: true
                    },
                    wage_tax_period: {
                        fieldType: 'SingleSelect',
                        name: 'wage_tax_period',
                        required: true,
                        label: this.$t('company.fields.wage_tax_period'),
                        placeholder: this.$t('company.fields.wage_tax_period_options.month'),
                        options: [{'value': 'M', 'display': this.$t('company.fields.wage_tax_period_options.M')}, {'value': 'W4', 'display': this.$t('company.fields.wage_tax_period_options.W4')}],
                        visibility: false
                    },
                    bsn_number: {
                        fieldType: 'Input',
                        type: 'text',
                        label: this.$t('company.fields.bsn_number'),
                        name: 'bsn_number',
                        visibility: true,
                        maxlength: 250
                    },
                    bsn_number_help: {
                        fieldType: 'HelpText',
                        text: this.$t('company.fields.bsn_number_help'),
                        visibility: true
                    },
                    internal_client_id: {
                        fieldType: 'Input',
                        type: 'text',
                        label: this.$t('company.fields.internal_client_id'),
                        name: 'internal_client_id',
                        visibility: true,
                        maxlength: 80
                    },
                    ocr_email_purchase: {
                        fieldType: 'Input',
                        type: 'text',
                        label: this.$t('company.fields.ocr_email_purchase'),
                        name: 'ocr_email_purchase',
                        visibility: true,
                        maxlength: 100
                    },
                    ocr_email_sale: {
                        fieldType: 'Input',
                        type: 'text',
                        label: this.$t('company.fields.ocr_email_sale'),
                        name: 'ocr_email_sale',
                        visibility: true,
                        maxlength: 100
                    },
                }
            }
        },
        created() {
            this.fillData();
        },
        computed: {
            ...mapState({
                companies: state => state.managed_companies.all
            }),
            ...mapGetters({
                identifiersPerType: 'managed_companies/identifiersPerType'
            }),
            companyName() {
                if (this.company) {
                    return this.company.title;
                }
                return '';
            }
        },
        methods: {
            ...mapActions({
                update: 'managed_companies/update',
                archiveCompany: 'managed_companies/archive',
                deleteCompany: 'managed_companies/delete',
                selectById: 'managed_companies/selectById',
            }),
            ...mapMutations({
                updateTitle: 'managed_companies/updateTitle',
            }),
            fillData() {
                if(this.company) {
                    this.schema.name.default = this.company.title;
                    this.schema.kvk_number.default = this.identifiersToString('kvknumber');
                    this.schema.fiscal_number.default = this.identifiersToString('fiscalnumber').toString();
                    this.schema.vat_number.default = this.identifiersToString('vatnumber').toString();
                    this.schema.wage_tax_number.default = this.identifiersToString('lhnumber').toString();
                    this.schema.bsn_number.default = this.identifiersToString('bsnnumber');

                    if (this.company.info) {
                        this.schema.address.default = this.company.info.address;
                        this.schema.internal_client_id.default = this.company.info.internal_client_id;
                        this.schema.ocr_email_purchase.default = this.company.info.ocr_email_purchase;
                        this.schema.ocr_email_sale.default = this.company.info.ocr_email_sale;
                        this.schema.wage_tax_period.default = this.company.info.wage_tax_period;
                    }
                }
            },
            submit(event) {
                let self = this;
                event.preventDefault();
                this.formData.id = this.company.id;
                this.checkSubmit(event, () => CompanyApi.checkDuplicates(this.formData.name, self.company.id).then((response) => {
                    let count = response.data.data;
                    if (count > 0) {
                        self.$swal({
                            title: self.$t('common.are_you_sure'),
                            html: self.$t('company.duplicated_name_warning'),
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#0F9BF3',
                            cancelButtonColor: '#999999',
                            confirmButtonText: self.$t('common.ok'),
                            cancelButtonText: self.$t('common.cancel')
                        }).then((result) => {
                            if (result.value) {
                                self.formData.name += ' (' + count + ')';
                                self.schema.name.default = self.formData.name;
                                CompanyApi.update(self.company.id, self.formData).then((response) => {
                                    self.updateTitle([self.company.id, response.data.data.title])
                                    self.$router.push({name: 'manager-company-overview', params: {managed_company_id: self.company.id}});
                                });
                            }
                        });
                    } else {
                        CompanyApi.update(self.company.id, self.formData).then((response) => {
                            self.updateTitle([self.company.id, response.data.data.title])
                            self.$router.push({name: 'manager-company-overview', params: {managed_company_id: self.company.id}});
                        });
                    }
                }))
            },
            archive() {
                this.$swal({
                    title: this.$t('common.are_you_sure'),
                    text: this.$t('company.archive_confirm_message'),
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#F30F2A',
                    cancelButtonColor: '#999999',
                    confirmButtonText: this.$t('common.archive'),
                    cancelButtonText: this.$t('common.cancel')
                }).then((result) => {
                    if(result.value){
                        this.archiveCompany(this.company.id).then(() => {
                            if (this.companies.length === 0) {
                                this.$router.push({name: 'manage-companies'});
                            } else {
                                this.selectById(this.company.id);
                                this.$router.push({name: 'manager-company-overview', params: {managed_company_id: this.company.id}});
                            }
                        });
                    }
                });
            },
            remove() {
                this.$swal({
                    title: this.$t('common.are_you_sure'),
                    text: this.$t('company.delete_confirm_message'),
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#F30F2A',
                    cancelButtonColor: '#999999',
                    confirmButtonText: this.$t('common.delete'),
                    cancelButtonText: this.$t('common.cancel')
                }).then((result) => {
                    if(result.value){
                        this.deleteCompany(this.company.id).then(() => {
                            if (this.companies.length === 0) {
                                this.$router.push({name: 'manage-companies'});
                            } else {
                                this.selectById(this.companies[0].id);
                                this.$router.push({name: 'manager-company-overview', params: {managed_company_id: this.companies[0].id}});
                            }
                        });
                    }
                });
            },
            identifiersToString(identifierType) {
                let identifiers = [];
                _.forEach(this.identifiersPerType(identifierType), (identifier) => {
                    identifiers.push(identifier.identifier);
                });
                return identifiers.toString();
            }
        },
        watch: {
            'formData.wage_tax_number'() {
                this.schema['wage_tax_period'].visibility = this.formData.wage_tax_number.length > 1;
            },
        }
    }

</script>

<style lang="scss" scoped>

    .button {
        height: 50px;
        border-radius: 4px;
        padding: 17px;
        font-size: 14px;
        line-height: 1px;
    }

    .is-text {
        color: #209cee;
        text-decoration: none;
    }

</style>