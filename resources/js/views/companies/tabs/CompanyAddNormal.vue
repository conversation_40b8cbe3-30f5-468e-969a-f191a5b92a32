<template>
    <div>
        <div v-show="!addUsers" class="columns content-columns">
            <div class="column is-two-thirds">
                <FormGenerator v-model:schema="schema" v-model:value="formData" ref="form"/>
                <br>
                <button @click="next" class="button is-info" type="button">{{ $t('common.next') }}</button>
                <button @click="cancel" class="button is-text" type="button">{{ $t('common.cancel') }}</button>
            </div>

            <transition name="fade">
                <div v-if="showSearchColumn" class="column is-one-third search-column">
                    <Loader :is-loading="searching" :text="$t('company.searching_companies')">
                        <div class="search-results">
                            <h2 class="title is-4">{{ $t('common.suggestions') }}</h2>
                            <div v-for="result in searchResults">
                                <div class="result" @click="fillCompanyData(result)">
                                    <h3 class="title">{{ result.company_name }}</h3>
                                    <span class="subtitle">{{ result.city }}</span>
                                </div>
                            </div>
                        </div>
                    </Loader>
                </div>
            </transition>
        </div>
        <transition name="slide">
            <div v-show="addUsers">
                <div class="notification-container">
                    <Notification :text="$t('company.add_company_notification')" type="warning"/>
                </div>

                <div class="columns content-columns is-multiline">
                    <Tabs :border="false" class="column is-6-fullhd is-6-desktop is-full-tablet panel">
                        <Tab class="half-height" :name="$t('company.tab.user')" code="normal">
                            <div>
                                <Label :text="$t('company.connected_users.add_user')"/>
                                <SearchableSelectList :loading="loadingAvailableUsers"
                                                      :items="filteredAvailableUsers"
                                                      :subtitle-property="'type'"
                                                      @selected="addDeletableUser"/>
                            </div>
                        </Tab>
                        <Tab class="half-height" :name="$t('company.tab.group')" code="mass">
                            <Label :text="$t('company.connected_users.add_group')"/>
                            <SearchableSelectList :loading="loadingAvailableGroups"
                                                  :items="availableGroups"
                                                  :subtitle-property="'subtitle'"
                                                  @selected="addGroups"/>
                        </Tab>
                    </Tabs>
                    <div class="column is-6-fullhd is-6-desktop is-full-tablet panel right-panel">
                        <Label :text="$t('company.connected_users.connected_users')"/>
                        <SearchableSelectList :loading="loadingAddingUsers"
                                              :items="usersToAdd"
                                              :subtitle-property="'type'"
                                              :icon-tooltip-title="$t('common.connected_with')"
                                              :icon-tooltip-property="'contexts'"
                                              @action="deleteUser"
                        />
                    </div>
                </div>

                <div>
                    <button @click="submit" class="button is-info" :disabled="!checkIfAddingColleague">
                        {{ $t('common.save') }}
                    </button>
                    <button @click="back" class="button is-text" type="button">{{ $t('common.back') }}</button>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>

import ContentHeader from '../../../components/ContentHeader';
import FormSubmitHandler from '../../../mixins/FormSubmitHandler';
import Loader from '../../../components/Loader';
import store from '../../../store/index';
import UserManageTable from "../../../components/UserManageTable";
import AddMemberships from "../../../components/AddMemberships";
import Notification from "../../../components/Forms/Notification";
import Label from "../../../components/Forms/Label";
import SearchableSelectList from "../../../components/SearchableSelectList";
import CompanyApi from "../../../api/CompanyApi";
import AccountApi from "../../../api/AccountApi";
import ContextApi from "../../../api/ContextApi";
import {mapActions, mapState} from "vuex";
import KvkApi from "../../../api/KvkApi";
import Tabs from "../../../components/Tabs";
import Tab from "../../../components/Tab";
import _ from "lodash";

export default {
    mixins: [FormSubmitHandler],
    name: 'CompanyAddNormal',
    store: store,
    components: {
        ContentHeader,
        Loader,
        UserManageTable,
        AddMemberships,
        Notification,
        Label,
        SearchableSelectList,
        Tabs,
        Tab
    },
    props: {
        cancel: Function,
    },
    data() {
        return {
            showCsvUpload: false,
            searching: false,
            addUsers: false,
            usersToAdd: [],
            searchResults: [],
            searchEnabled: true,
            delete_membership: {
                text: this.$t('settings.account_manager.delete_button'),
                class: "delete-membership"
            },
            groupsToAdd: [],
            availableGroups: [],
            loadingAddingUsers: false,
            loadingAvailableUsers: true,
            loadingAvailableGroups: true,
            formData: {
                name: '',
                kvk_number: '',
                address: '',
                fiscal_number: '',
                vat_number: '',
                wage_tax_number: '',
                internal_client_id: '',
                ocr_email_purchase: '',
                ocr_email_sale: '',
            },
            schema: {
                name: {
                    fieldType: 'Input',
                    type: 'text',
                    label: this.$t('company.fields.name'),
                    name: 'name',
                    visibility: true,
                    debounce: 500,
                    required: true,
                    maxlength: 60
                },
                kvk_number: {
                    fieldType: 'Input',
                    type: 'text',
                    label: this.$t('company.fields.kvk_number'),
                    name: 'kvk_number',
                    visibility: true,
                    debounce: 500,
                    maxlength: 250,
                },
                kvk_number_help: {
                    fieldType: 'HelpText',
                    text: this.$t('company.fields.kvk_number_help'),
                    visibility: true
                },
                address: {
                    fieldType: 'Input',
                    type: 'text',
                    label: this.$t('company.fields.address'),
                    name: 'address',
                    visibility: true,
                    maxlength: 160
                },
                fiscal_number: {
                    fieldType: 'Input',
                    type: 'text',
                    label: this.$t('company.fields.fiscal_number'),
                    name: 'fiscal_number',
                    visibility: true,
                    maxlength: 250,
                },
                fiscal_number_help: {
                    fieldType: 'HelpText',
                    text: this.$t('company.fields.fiscal_number_help'),
                    visibility: true
                },
                vat_number: {
                    fieldType: 'Input',
                    type: 'text',
                    label: this.$t('company.fields.vat_number'),
                    name: 'vat_number',
                    visibility: true,
                    maxlength: 250
                },
                vat_number_help: {
                    fieldType: 'HelpText',
                    text: this.$t('company.fields.vat_number_help'),
                    visibility: true
                },
                wage_tax_number: {
                    fieldType: 'Input',
                    type: 'text',
                    label: this.$t('company.fields.wage_tax_number'),
                    name: 'wage_tax_number',
                    visibility: true,
                    maxlength: 250
                },
                wage_tax_number_help: {
                    fieldType: 'HelpText',
                    text: this.$t('company.fields.wage_tax_number_help'),
                    visibility: true
                },
                wage_tax_period: {
                    fieldType: 'SingleSelect',
                    name: 'wage_tax_period',
                    required: true,
                    label: this.$t('company.fields.wage_tax_period'),
                    default: 'M',
                    placeholder: this.$t('company.fields.wage_tax_period_options.month'),
                    options: [{
                        'value': 'M',
                        'display': this.$t('company.fields.wage_tax_period_options.M')
                    }, {'value': 'W4', 'display': this.$t('company.fields.wage_tax_period_options.W4')}],
                    visibility: false
                },
                bsn_number: {
                    fieldType: 'Input',
                    type: 'text',
                    label: this.$t('company.fields.bsn_number'),
                    name: 'bsn_number',
                    visibility: true,
                    maxlength: 250
                },
                bsn_number_help: {
                    fieldType: 'HelpText',
                    text: this.$t('company.fields.bsn_number_help'),
                    visibility: true,

                },
                internal_client_id: {
                    fieldType: 'Input',
                    type: 'text',
                    label: this.$t('company.fields.internal_client_id'),
                    name: 'internal_client_id',
                    visibility: true,
                    maxlength: 80
                },
                ocr_email_purchase: {
                    fieldType: 'Input',
                    type: 'text',
                    label: this.$t('company.fields.ocr_email_purchase'),
                    name: 'ocr_email_purchase',
                    visibility: true,
                    maxlength: 100
                },
                ocr_email_sale: {
                    fieldType: 'Input',
                    type: 'text',
                    label: this.$t('company.fields.ocr_email_sale'),
                    name: 'ocr_email_sale',
                    visibility: true,
                    maxlength: 100
                },
            },
            lastFirstKvkNumber: null
        }
    },
    created() {
        this.loadAvailableUsers().then((response) => {
            this.loadingAvailableUsers = false;
        });
        this.loadAvailableGroups().then((response) => {
            this.availableGroups = response.data.data.filter((group) => {
                return group.members_count > 0;
            });

            this.loadingAvailableGroups = false;
        });
    },
    computed: {
        ...mapState({
            availableUsers: state => state.managed_companies.availableUsers,
        }),
        showSearchColumn() {
            return !this.searchEnabled || this.searching || (this.searchResults && this.searchResults.length > 0);
        },
        filteredAvailableUsers() {
            return this.availableUsers.filter((user) => {
                return this.usersToAdd.findIndex(u => u.id === user.id) < 0;
            });
        },
        checkIfAddingColleague() {
            return this.usersToAdd.some((user) => {
                return user.is_external === 0;
            });
        }
    },
    methods: {
        ...mapActions({
            loadAvailableUsers: 'managed_companies/loadAvailableUsers',
            massCreateManagedCompany: 'managed_companies/massCreate',
            createManagedCompany: 'managed_companies/create'
        }),
        load(fn) {
            if (!this.searchEnabled) {
                return
            }
            Promise.resolve()
                .then(() => this.searching = true)
                .then(() =>
                    fn().catch(error => {
                        console.log(error);
                    }))
                .finally(() => this.searching = false);
        },
        fillCompanyData(item) {
            this.searchEnabled = false;
            //add data to both schema (for display) and to formData to make sure it is also submitted.
            this.schema.name.default = item.company_name;
            this.schema.kvk_number.default = item.kvk_number;
            this.schema.address.default = this.formatFullAddress(item.street, item.city);
            this.formData.name = item.company_name;
            this.formData.kvk_number = item.kvk_number;
            this.formData.address = this.formatFullAddress(item.street, item.city);
            KvkApi.getOwnerInfo(item.kvk_number).then(response => {
                this.schema.fiscal_number.default = response.data.rsin;
                this.formData.fiscal_number = response.data.rsin;
            });
        },
        formatFullAddress(street, city) {
            if (!street && !city) {
                return '';
            }
            return street + ', ' + city;
        },
        next(event) {
            this.checkSubmit(event, () => {
                CompanyApi.checkDuplicates(this.formData.name).then((response) => {
                    let count = response.data.data;
                    if (count > 0) {
                        this.$swal({
                            title: this.$t('common.are_you_sure'),
                            html: this.$t('company.duplicated_name_warning'),
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#0F9BF3',
                            cancelButtonColor: '#999999',
                            confirmButtonText: this.$t('common.ok'),
                            cancelButtonText: this.$t('common.cancel')
                        }).then((result) => {
                            if (result.value) {
                                this.formData.name += ' (' + count + ')';
                                this.schema.name.default = this.formData.name;
                                this.addUsers = true;
                            }
                        });
                    } else {
                        this.addUsers = true;
                    }
                })
            });
        },
        back() {
            this.addUsers = false;
        },
        submit(event) {
            event.preventDefault();

            this.formData['user_ids'] = this.usersToAdd.map((user) => user.id);
            this.formData['groups_ids'] = this.groupsToAdd;

            this.checkSubmit(event, () => this.createManagedCompany(this.formData))
                .then((company) => {
                    this.$router.push({
                        name: 'manager-company-overview',
                        params: {managed_company_id: company.id}
                    });
                });
        },
        addDeletableUser(user) {
            user.can_be_deleted = true;
            user.contexts = []; // dont show contexts
            this.addUser(user);
        },
        addUser(user) {
            this.usersToAdd.push({...user});
        },
        deleteUser(user) {
            this.usersToAdd.splice(this.usersToAdd.findIndex(u => u.id === user.id), 1);
            _.forEach(this.availableUsers, (member) => {
                member.can_be_deleted = false;
            });
        },
        addGroup(group) {
            this.groupsToAdd.push(group);
        },
        deleteGroup(group) {
            this.availableGroups.splice(this.availableGroups.findIndex(g => g.id === group.id), 1);
        },
        loadAvailableGroups() {
            return AccountApi.manageContexts();
        },
        addGroups(group) {
            this.$swal({
                title: this.$t('common.warning'),
                html: group.has_only_colleagues
                    ? this.$t('company.connected_users.confirm_messages.add_group')
                    : this.$t('company.connected_users.confirm_messages.add_group_with_client_users'),
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#0F9BF3',
                cancelButtonColor: '#999999',
                cancelButtonText: this.$t('common.cancel'),
                confirmButtonText: this.$t('common.yes')
            }).then((result) => {
                if (result.value) {
                    this.loadingAddingUsers = true;
                    this.deleteGroup(group);
                    ContextApi.getMembers(group.id).then(response => {
                        this.addGroup(group);
                        _.forEach(response.data.data, (member) => {
                            if (this.usersToAdd.findIndex(u => u.id === member.id) < 0) {
                                member.can_be_deleted = false;
                                this.addUser(member);
                            }
                        });
                        this.loadingAddingUsers = false;
                    });

                }
            });
        },
        queryKvk: _.debounce(function (companyName, kvkNumber) {
            KvkApi.search(companyName, kvkNumber).then(response => {
                this.searchResults = response.data
            });
        }, 300),
    },
    watch: {
        'formData.kvk_number'() {
            this.load(() => {
                const firstKvkNumber = this.formData.kvk_number.split(',')[0];

                if (firstKvkNumber !== this.lastFirstKvkNumber) {
                    this.lastFirstKvkNumber = firstKvkNumber;
                    this.queryKvk(null, firstKvkNumber);
                }
            });
        },
        'formData.name'() {
            this.load(() => {
                this.queryKvk(this.formData.name, null);
            });
        },
        'formData.wage_tax_number'() {
            this.schema['wage_tax_period'].visibility = this.formData.wage_tax_number.length > 1;
        }
    }
}

</script>

<style lang="scss" scoped>
.column {
    padding: 0 !important;
}

.columns {
    padding: 0 !important;
}

.label {
    padding-left: 12px;
}

.inner-edit-panel {
    padding: 60px;
}

.search-column {
    padding: 30px !important;
}

.button {
    height: 50px;
    border-radius: 4px;
    padding: 17px;
    font-size: 14px;
    line-height: 1px;
}

.is-text {
    color: #209cee;
    text-decoration: none;
}

.search-results {
    display: flex;
    flex-flow: column;
    height: 100%;

    .result {
        cursor: pointer;
        padding-top: 15px;
        padding-bottom: 12px;
        border-top: solid 1px #ddd;

        .title {
            font-size: 14px;
            margin: 0;
            padding: 0;
        }

        .subtitle {
            font-size: 14px;
        }
    }
}

.notification-container {
    padding: 15px 0;
}

.fade-enter-active, .fade-leave-active {
    transition: opacity .4s
}

.fade-enter, .fade-leave-to {
    opacity: 0
}

.slide-leave-active,
.slide-enter-active {
    transition: 1s;
}

.slide-enter {
    transform: translate(100%, 0);
}

.slide-leave-to {
    transform: translate(-100%, 0);
}

.right-panel {
    margin-top: 43px;
}

</style>