<template>
    <div v-if="company" class="company-overview content-panel">
        <div class="inner-content-panel">
            <ContentHeader
                :image="company.image"
                :logo-icon="company.icon"
                :logo-text="company.title"
                :logo-background-color="company.color"
                :title="company.title"
                :subtitle="company.subtitle"
            />
            <template v-if="!loadingInfo && company">
                <TagsInput
                    v-if="company.info"
                    class="tags-input"
                    :tags="company.info.tags"
                    :placeholder="$t('form.tag_input_placeholder')"
                    @update="updateTags"
                    :search-list="suggestedTags"
                />
                <i v-if="isMobile" @click="onClose" class="fa fa-times-thin fa-3x" aria-hidden="true"/>
                <div v-if="company.info && company.info.disabled" class="content-columns">
                    <div class="content-column column">
                        <Notification
                            :text="$t('company.archived_company_notification')"
                            :action="restore_action"
                            type="is-warning"
                        />
                    </div>
                </div>
                <a class="back-button" v-if="manage_users" @click="back">{{ $t('common.back') }}</a>
                <ManageUsers v-if="manage_users" :company="company"/>
                <div v-if="!manage_users" class="content-columns">
                    <div class="column is-6">
                        <Information
                            class="table-spacing"
                            :items="info_items"
                            :header="$t('company.company_info_title')"
                            :edit_link="company.info && (company.info.disabled !== true) ? edit_link : ''"
                        />
                        <ActivityTable
                            v-if="company"
                            :url="company?.info?.events_url"
                            :urlall="company?.info?.events_url"
                        />
                    </div>
                    <div class="column is-6">
                        <div v-if="identifiers.length > 0">
                            <CompanyIdentifiersTable
                                :identifiers="identifiers"
                                :loading="loading"
                                :show-links="company.info && company.info.disabled !== true"
                            />
                            <br>
                            <br>
                        </div>
                        <div v-if="uploadTypes && uploadTypes.length > 0">
                            <CompanyUploadTypesTable :upload-types="uploadTypes"/>
                            <br>
                            <br>
                        </div>
                        <UserManageTable
                            v-if="company"
                            :loading="companyUsersLoading"
                            :show_buttons="company.info && company.info.disabled !== true"
                            :title="$t('company.connected_users.connected_users')"
                            :top_button="$t('common.manage')"
                            subtitle-property="type"
                            :users="companyUsers"
                            :clickable="true"
                            :empty_message="$t('company.no_connected_users')"
                            :icon-tooltip-title="$t('common.connected_with')"
                            :icon-tooltip-property="'contexts'"
                            @create-membership="switchManage(true)"
                        />
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import ContentHeader from '../../components/ContentHeader';
import Information from '../../components/Information';
import ServicesTable from '../../components/ServicesTable';
import CompanyIdentifiersTable from '../../components/CompanyIdentifiersTable';
import CompanyUploadTypesTable from '../../components/CompanyUploadTypesTable';
import ActivityTable from '../../components/ActivityTable';
import RecursiveAccordion from '../../components/RecursiveAccordion';
import store from '../../store/index';
import UserManageTable from "../../components/UserManageTable";
import ManageUsers from "./manage_users";
import {mapGetters, mapState, mapActions, mapMutations} from "vuex";
import Notification from "../../components/Forms/Notification";
import TagsInput from "../../components/Forms/TagsInput";
import CompanyUploadTypesApi from "../../api/CompanyUploadTypesApi";

export default {
    store: store,
    name: 'CompanyOverview',
    components: {
        CompanyIdentifiersTable,
        CompanyUploadTypesTable,
        TagsInput,
        Notification,
        ContentHeader,
        Information,
        ServicesTable,
        RecursiveAccordion,
        UserManageTable,
        ManageUsers,
        ActivityTable,
    },
    data() {
        return {
            edit_link: 'edit',
            administrators: [],
            info_items: [],
            members: [],
            manage_users: false,
            restore_action: {
                display: 'button',
                label: this.$t('common.restore'),
                callback: this.restore
            },
            uploadTypes: null,
            companyUsersLoading: true,
            companyUsers: []
        }
    },
    computed: {
        ...mapState({
            isMobile: state => state.isMobile,
            loading: state => state.managed_companies.loadingServicesFor,
            loadingInfo: state => state.managed_companies.loadingInfo,
            suggestedTags: state => state.managed_companies.suggestedTags
        }),
        ...mapGetters({
            company: 'managed_companies/selected',
            services: 'managed_companies/servicesForSelected',
            identifiers: 'managed_companies/identifiersForSelected',
            identifiersPerType: 'managed_companies/identifiersPerType'
        }),
        membersRoute() {
            if (typeof this.company !== null) {
                if (this.company.members_count) {
                    return '/manage/users?company=' + this.company.id;
                }
            }
            return null;
        }
    },
    created() {
        if (this.company) {
            this.loadInfo(this.company.id).then(() => {
                this.loadIdentifiers(this.company.id).then(() => {
                    this.fillData();
                });
            })

            this.loadUploadTypes(this.company.id);
            this.reloadUsers();
        }
        this.getTagSuggestions();
    },
    methods: {
        ...mapActions({
            restore: 'managed_companies/restore',
            updateTags: 'managed_companies/updateTags',
            getTagSuggestions: 'managed_companies/getTagSuggestions',
            loadUsers: 'managed_companies/users',
            loadIdentifiers: 'managed_companies/loadIdentifiers',
            loadInfo: 'managed_companies/loadInfo'
        }),
        ...mapMutations({
            setUploadTypes: 'managed_companies/setUploadTypes',
            setServiceData: 'managed_companies/setServiceData'
        }),
        onClose() {
            let listColumnContainer = document.getElementsByClassName('list-column-container');
            listColumnContainer[0].style.display = 'block';

            let contentContainer = document.getElementsByClassName('content-container');
            contentContainer[0].style.display = 'none';

            let closeGroupInfoMobile = document.getElementsByClassName('fa-times-thin');
            closeGroupInfoMobile[0].classList.add('is-hidden');
        },
        switchManage(value) {
            this.manage_users = value;
            this.reloadUsers();
        },
        reloadUsers() {
            this.companyUsersLoading = true;
            this.loadUsers(this.company.id).then(users => {
                this.companyUsers = users;
                this.companyUsersLoading = false;
            });
        },
        fillData() {
            let lhNumber = this.makeIdentifierList('lhnumber');
            this.info_items = [
                {label: this.$t('common.company'), title: this.company.title},
                {label: this.$t('company.fields.kvk_number'), title: this.makeIdentifierList('kvknumber')},
                {label: this.$t('company.fields.address'), title: this.company.info ? this.company.info.address : ''},
                {label: this.$t('company.fields.fiscal_number'), title: this.makeIdentifierList('fiscalnumber')},
                {label: this.$t('company.fields.vat_number'), title: this.makeIdentifierList('vatnumber')},
                {label: this.$t('company.fields.wage_tax_number'), title: lhNumber},
            ];

            if (lhNumber) {
                this.info_items.push(
                    {
                        label: this.$t('company.fields.wage_tax_period'),
                        title: this.$t('company.fields.wage_tax_period_options.' + (this.company.info ? this.company.info.wage_tax_period : ''))
                    }
                );
            }

            this.info_items.push(
                {label: this.$t('company.fields.bsn_number'), title: this.makeIdentifierList('bsnnumber')},
                {
                    label: this.$t('company.fields.internal_client_id'),
                    title: [this.company.info ? this.company.info.internal_client_id : ''].join('<br>')
                },
                {
                    label: this.$t('company.fields.ocr_email_purchase'),
                    title: this.company.info ? this.company.info.ocr_email_purchase : ''
                },
                {
                    label: this.$t('company.fields.ocr_email_sale'),
                    title: this.company.info ? this.company.info.ocr_email_sale : ''
                },);
        },
        back() {
            this.manage_users = false;
        },
        makeIdentifierList(identifierType) {
            let titles = [];
            let identifiersByType = this.identifiersPerType(identifierType);
            if (identifiersByType.length) {
                _.forEach(identifiersByType.filter(i => i.type === identifierType), (identifier) => {
                    titles.push(identifier.identifier)
                });
            }
            return titles.join(', ');
        },
        loadUploadTypes(companyId) {
            CompanyUploadTypesApi.uploadTypes(companyId).then(response => {
                let uploadTypes = response.data.data;
                this.setUploadTypes(uploadTypes);
                this.uploadTypes = uploadTypes;
            });
        },
    },
    watch: {
        company: {
            handler: function (newValue) {
                if (this.company) {
                    this.fillData();
                }
            },
            deep: true
        }
    }
}

</script>

<style lang="scss" scoped>
.tags-input {
    margin-left: 140px;
    display: inline-flex;
}

.is-marginless-bottom {
    margin-bottom: 0;
}

.company-overview .content-panel {
    overflow-y: auto;
}

.company-overview .content-columns:first-of-type {
    padding: 30px 30px 0;
}

table.plain {
    margin-bottom: 0;
}

.company-overview .content-columns:not(:first-of-type) {
    padding: 0 30px;
}

.back-button {
    position: absolute;
    top: 30px;
    left: 80px;
}
</style>