{"multi_select": {"no_items_found": "No items found.", "click_to_select": "Click to select", "click_to_remove": "Click to remove", "selected": "Selected"}, "form": {"single_select_default_placeholder": "Select option...", "multi_select_default_placeholder": "Select options...", "show_all_widgets": "Show all widgets", "file_input_selected": "Selected file: {filename}", "tag_input_placeholder": "Add tag"}, "multiple_file_upload": {"drop_text": "Drag and drop your files here to upload", "single_file_drop_text": "Drag and drop your file here or upload one by clicking on the box", "no_image": "No image", "extension_error": "This type of file is not allowed", "add_files": "Add files", "add_folder": "Add folder", "too_many_files": "Could not add file '{name}', maximum of {max} files reached."}, "validation": {"value_required": "This field requires a value.", "period_required": "Period is required.", "min_char": "The text should be at least {0} characters.", "max_char": "A maximum of {0} characters is allowed."}, "list_builder": {"add_new_item": "Add new item", "remove_item": "Remove item", "remove_last_message": "It is not possible to remove the last item from the list.", "max_items_reached": "You have reached the maximum amount of items."}, "common_endpoint": {"domain_required": "The domain field is required", "path_in_url_required": "The path in the query string is required"}, "filetype": {"permission-select-header": "Approval"}, "update_highlights": {"status_overview_filter_notification": "The filters have been changed. Use the search function and filters to filter results. By deselecting all, all results will be displayed."}}