<template>
    <div class="history-container">
        <div class="messages-container" ref="messagesContainer">
            <div class="messages-subcontainer">
                <div v-for="auditLog in auditLogs">
                    <QuestionMessage :auditLog="auditLog" :url="'/api/app/front/questions/attachments/'"/>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

import QuestionMessage from "../../../../views/open_questions/user_overview/OpenQuestionMessage"

export default {
    name: 'QuestionAuditLogs',
    components: {
        QuestionMessage
    },
    props: {
        auditLogs: null
    }
}

</script>

<style lang="scss">

@import '../../../../../sass/initial-variables';

.history-container {
    &.has-actions {
        border-radius: 4px 4px 0 0;
    }

    .header-container {
        padding: 30px 0;
        padding-top: 20px;
    }

    .question-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .title-container {
        max-width: 250px;
    }

    .sub-text {
        font-size: 10px;
        color: $black-40;
        margin: 0;
    }

    .messages-container {
        overflow-y: auto;

        .messages-subcontainer {
            padding: 0;
        }
    }

    .message-container {
        &.right {
            padding-right: 10px;
        }
    }
}

</style>