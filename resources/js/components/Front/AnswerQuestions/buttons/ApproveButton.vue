<template>
    <div>
        <div class="approve-wrap">
            <div class="approve-button" @click="approve" :title="$t('front.questions.button_tooltips.approve')">
            <span class="icon">
                <i class="sl-icon-check icon-green"/>
            </span>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    name: 'ApproveButton',
    data() {
        return {
            externalUsers: null,
        }
    },
    props: {
        question: null,
        mode: {
            type: String,
            required: false,
            default: 'front'
        },
    },
    computed: {
        companyId() {
            let companyId = this.$route.params.open_questions_company_id;

            if (isNaN(companyId)) {
                companyId = window.location.hash.substring(1);
            }

            return companyId;
        },
    },
    methods: {
        approve() {
            this.$emit('approve', this.question.id);
        }
    },
}
</script>

<style lang="scss" scoped>

@import '../../../../../sass/initial-variables';

.approve-wrap {
    .approve-button {
        border-radius: 100%;
        border: 1px solid #ebebeb;
        cursor: pointer;
        padding: 12px;
        height: 46px;
        width: 46px;
        float: left;

        .icon {
            color: $blue;
            font-size: 18px;
        }

        .icon-green {
            color: $green;
        }

        &:hover {
            background-color: $hoverBg;
        }
    }
}
</style>