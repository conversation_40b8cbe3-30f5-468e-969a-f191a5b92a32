<template>
    <div :class="visible ? '' : 'none'">
        <div v-if="item.showDivider" class="list-item-divider">
            <h4 class="list-item-divider-title">{{item.divider}}</h4>
        </div>
        <div :class="'list-item-container' + (selected ? ' active' : '')" @mousedown="e => onMouseDown(e)">

            <div class="list-item-container-child-left">
                <Logo
                    v-if="showLogo" class="list-item-icon" :image="item.image" :icon="item.icon" :text="item.title"
                    :background-color="item.color" :width="logo_width" :height="logo_height" :dot-count="item[todoField]"
                    :badge-color="item.badge_color"
                />
                <div class="list-item-content">
                    <h2 class="list-item-title" :title="item.title">{{item.title}}</h2>
                    <h3 class="list-item-subtitle" :title="item.subtitle">{{item.subtitle}}</h3>
                    <h3 class="list-item-subtitle" v-if="item.subsubtitle" :title="item.subsubtitle">
                        {{item.subsubtitle}}</h3>
                </div>
            </div>

            <div v-if="item.warning_icon" class="warning-container icon-container" :title="item.warning_icon_title">
                <span class="sl-icon-warning"></span>
            </div>

            <div :title="item.warning_tooltip" class="warning-container">
                <span v-if="item.warning_tooltip" class="sl-icon-warning"></span>
            </div>

            <div v-if="item.icon" class="icon-container" :class="item.iconColor">
                <span :class="item.icon"></span>
            </div>
        </div>
    </div>
</template>

<script>

    import Logo from "../Logo"

    export default {
        name: 'ScrollableListItem',
        components: {
            Logo
        },
        props: ['item', 'selected', 'showLogo', 'visible', 'todoField'],
        data() {
            return {
                logo_height: 44,
                logo_width: 44,
            }
        },
        methods: {
            onMouseDown() {
                this.$emit('select-item', this.item)
            }
        },
        mounted() {
            if (this.selected) {
                this.$emit('select-mount', this.$el.offsetTop);
            }
        }
    }
</script>

<style lang="scss" scoped>

    @import '../../../sass/initial-variables';

    .list-item-container {
        height: 72px;
        position: relative;
        padding: 0 17px;
        align-items: center;
        display: flex;
        justify-content: space-between;

        &:hover {
            background-color: $hoverBg;
            cursor: pointer;
        }

        &.disabled {
            background-color: #f0f0f0;
            opacity: 0.5;
        }

        &.active {
            background-color: $selectedBg;
            color: $selectedFg;

            .list-item-container-child-left {
                .list-item-content {
                    .list-item-subtitle {
                        color: $selectedFg;
                    }
                }
            }
        }

        .list-item-container-child-left {
            display: flex;
            flex-direction: row;
            min-width: 0;
            align-items: center;

            .list-item-icon {
                vertical-align: top;
                margin-right: 17px;
            }

            .list-item-content {
                flex-direction: column;
                min-width: 0;

                h3, h2 {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .list-item-title {
                    font-weight: bold;
                    font-size: 14px;
                    line-height: 24px;
                }

                .list-item-subtitle {
                    font-size: 12px;
                    line-height: 18px;
                    color: #999999;
                }
            }
        }
    }

    .none {
        display: none;
    }

    .list-item-divider {
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);

        .list-item-divider-title {
            font-size: 14px;
            line-height: 40px;
            margin-left: 34px;
        }
    }

    .badge-container {
        color: white;
        font-size: 12px;
        margin: auto 0 auto 10px;
        font-weight: bold;
        text-align: center;
    }

    .badge {
        display: inline-block;
        min-width: 18px;
        padding: 2px 2px 2px 1px;
        border-radius: 50%;
        font-size: 0.8em;
        background: #F30F2A;
    }

    .warning-container {
        display: contents;
        font-size: 18px;
    }

    .warning-color {
        color: $yellow;
    }

    .success-color {
        color: $green;
    }

    .decline-color {
        color: $orange;
    }

    .icon-container {
        font-size: 18px;
        margin: auto 0 auto 10px;
        font-weight: bold;
        text-align: center;
    }

    .sl-icon-warning {
        color: $yellow;
    }
</style>