<template>
    <div v-if="!isMobile" class="segmented-control" :style="[segmentedControlStyle]">
        <div class="segmented-item" :style="[optionSelected == option[value] ? itemSelectedStyle : null, itemStyle]"
             :class="{'is-selected': optionSelected == option[value]}"
             v-for="option in options" @click="handleClick(option)" :id="option.value">
            <i v-if="option.icon" class="fa fa-bell-o"></i>
            <span>{{ option[label] }}</span>

            <span v-if="option.btn_badge" class="badge-container">
                <span class="badge">{{ option.btn_badge }}</span>
            </span>
        </div>
    </div>
</template>

<script>
    import _ from 'lodash';
    import store from '../store/index';
    import {mapState} from "vuex";

    export default {
        store: store,
        props: {
            options: {
                type: Array,
                required: true
            },
            selected: {
                type: String,
                default: null
            },
            label: {
                type: String,
                default: 'label'
            },
            value: {
                type: String,
                default: 'value'
            },
            btn_badge: {
                default: 0
            },
            icon: {
                type: String,
                default: 'value'
            },
            color: {
                type: String,
                default: '#fff'
            },
            activeColor: {
                type: String,
                default: '#f8f8f8'
            }
        },
        data () {
            return {
                optionSelected: null
            }
        },
        computed: {
            ...mapState({
                isMobile: state => state.isMobile
            }),
            segmentedControlStyle () {
                return {
                    color: this.activeColor,
                    border: `solid 1px ${this.activeColor}`
                }
            },
            itemStyle () {
                return {
                    borderRight: `solid 1px ${this.activeColor}`
                }
            },
            itemSelectedStyle () {
                return {
                    background: this.activeColor
                }
            },
        },
        methods: {
            handleClick: _.throttle(function (option) {
                if(this.optionSelected === option.value) {
                    this.optionSelected = null;
                } else {
                    this.optionSelected = option.value;
                }
                this.$emit('select', this.optionSelected);
            }, 300,  {leading: true, trailing: false}),
        },
        watch: {
            selected() {
                this.optionSelected = this.selected;
            }
        }
    }
</script>

<style lang="scss" scoped>
    @import '../../sass/initial-variables';

    .segmented-control {
        position: fixed;
        top: 23px;
        right: 28px;
        display: flex;
        flex-direction: row;
        border-radius: 4px;
        z-index: 1050;
        border-color: $grey-light !important;
        background-color: #fff;

        .segmented-item {
            color: $grey-dark;
            display: inline-block;
            padding: 9px 20px;
            text-align: center;
            user-select: none;
            position: relative;
            border-color: $grey-light !important;
            cursor: pointer;

            &:first-child {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
            }

            &:last-child {
                border: none !important;
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }
        }

        .is-selected {
            background-color: $grey-lighter;
        }

        .badge-container {
            color: white;
            display: inline-flex;
            line-height: 14px;
            font-weight: 500;
            height: 14px;
            width: 14px;
            font-size: 12px;
            margin-left: 5px;
        }

        .badge {
            display: inline-block;
            min-width: 18px;
            padding: 2px 2px 2px 1px;
            border-radius: 50%;
            position: absolute;
            font-size: 0.8em;
            background: #F30F2A;
            font-weight: bold;
            color: white;
            text-align: center;
        }
    }
</style>