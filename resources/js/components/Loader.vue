<template>
    <div class="pos-relative" :class="{'loader-container vld-parent' : isLoading, 'small': isSmall}">
        <loading :active.sync="isLoading"
                 :can-cancel="false"
                 :height="height"
                 :width="width"
                 :opacity="opacity"
                 :backgroundColor="backgroundColor"
                 :is-full-page="fullPage">

            <template v-slot:default>
                <div slot="default" class="loader-slot">
                    <HixLoader10/>
                </div>
            </template>
            <template v-slot:after>
                <div v-if="text" slot="after" class="after-slot" :class="{'light' : lightMode}">
                    {{ text }}
                </div>
            </template>
        </loading>
        <slot v-if="!isLoading"></slot>
    </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import './../../assets/css/vue-loading-overlay.css';
import HixLoader10 from "./Loaders/HixLoader10.vue";

export default {
    components: {
        HixLoader10,
        Loading
    },
    props: {
        isLoading: Boolean,
        text: String,
        color: String,
        isSmall: {
            type: Boolean,
            default: false
        },
        loader: {
            type: String,
            default: 'spinner'
        },
        lightMode: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            fullPage: false,
            height: 64,
            width: 64,
            opacity: 0.8,
            backgroundColor: 'transparent',
        }
    },
}
</script>

<style lang="scss" scoped>

.pos-relative {
    position: relative;
}

.loader-container {
    height: 50vh;
    flex: 1 1 auto; /* loader should grow to available space*/

    &.small {
        height: 200px;
    }

    & .vld-overlay {
        &:focus {
            outline: none
        }
    }
}

.loader-slot {
    text-align: center;
}

.after-slot {
    text-align: center;

    &.light {
        color: white;
    }
}

.fade-enter-active, .fade-leave-active {
    transition: opacity .4s
}

.fade-enter, .fade-leave-to {
    opacity: 0
}

.is-mobile {
    .pos-relative {
        position: static;
        height: auto;
    }
}
</style>
