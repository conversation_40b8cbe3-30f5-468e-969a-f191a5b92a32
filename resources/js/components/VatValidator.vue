<template>
        <span v-if="loading" class="icon" :title="$t('tasks.checking_vat_validity')">
            <i class="sl-icon-refresh fa-spin loading"></i>
        </span>
        <span v-else-if="valid" class="icon" :title="$t('tasks.valid_vat', {company_name: company_name})">
            <i class="sl-icon-solid-checked validated"></i>
        </span>
        <span v-else class="icon" :title="$t('tasks.could_not_validate_vat')">
            <i class="sl-icon-solid-question unvalidated"></i>
        </span>
</template>

<script>
    export default {
        name: 'VatValidator',
        props: {
            vat_number: null,
            country_code: null
        },
        data() {
            return {
                loading: true,
                valid: false,
                company_name: null
            };
        },
        created() {
            if (this.vat_number && this.country_code) {
                let url = '/api/app/front/docs/task/vat/' + this.country_code + '/' + this.vat_number;
                this.axios.get(url).then((response) => {
                    this.valid = response.data.data.valid;
                    this.company_name = response.data.data.company_name;
                }).catch(() => {
                    this.valid = false;
                    this.company_name = null;
                }).finally(() => {
                    this.loading = false;
                });
            }
        }
    }
</script>

<style lang="scss" scoped>
    @import '../../sass/initial-variables';

    .loading {
        color: $blue;
    }

    .validated {
        color: $green;
    }

    .unvalidated {
        color: $yellow;
    }
</style>