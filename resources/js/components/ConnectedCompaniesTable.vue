<template>
    <div class="searchable-connected-list">
        <div class="header-component">
            <div class="list-title" v-if="leftTitle">
                {{leftTitle}}
            </div>
            <div class="list-title" v-if="rightTitle">
                {{rightTitle}}
            </div>
        </div>
        <Loader :is-loading="this.loading" :text="$t('common.loading')"/>
        <div v-if="!loading" class="list-component">
            <div class="top">
                <SearchBar :value="searchValue" @searchChange="setSearch" />
            </div>
            <div class="connected-list" v-if="filteredItems">
                <div class="connected-list-item" v-for="item in filteredItems">
                    <div class="connected-list-item-label">
                        <Logo class="list-item-icon" :image="item['internal'].image" :text="item['internal'].name"
                              :background-color="item['internal'].color" :width="44" :height="44"/>
                        {{item['internal'].name}}
                    </div>
                    <div class="connection-icon">
                        <i class="sl-icon-link-horizontal"/>
                    </div>
                    <div class="connected-list-item-label second-label">
                        <Logo class="list-item-icon" :image="item['external'].image" :text="item['external'].name"
                              :background-color="item['external'].color" :width="44" :height="44"/>
                        <div>
                            <div class="list-item-title">
                                {{item['external'].name}}
                                <i v-if="iconClass && iconTooltipTitle && iconTooltipProperty && item['external'][iconTooltipProperty]" class="title-icon" :class="iconClass" :title="getIconTooltipTitle(item['external'])"/>
                            </div>
                            <div v-if="subtitleProperty" class="list-item-subtitle">
                                {{item['external'][subtitleProperty]}}
                            </div>
                        </div>
                    </div>
                    <div class="delete-button">
                        <i class="sl-icon-trash-can" @click="deleteConnection(item)" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

    import SearchBar from "./SearchBar";
    import SingleSelect from "./Forms/SingleSelect";
    import Logo from "./Logo";
    import Loader from "./Loader";

    export default {
        name: 'ConnectedCompaniesTable',
        components: {
            SearchBar,
            SingleSelect,
            Logo,
            Loader
        },
        props: {
            items: {
                type: Array,
                required: true
            },
            subtitleProperty: {
                type: String
            },
            leftTitle: {
                type: String,
            },
            rightTitle: {
                type: String,
            },
            loading: Boolean,
            iconClass: {
                type: String
            },
            iconTooltipTitle: {
                type: String
            },
            iconTooltipProperty: {
                type: String
            },
        },
        data() {
            return {
                searchValue: '',
                selectedItem: null,
            };
        },
        computed: {
            filteredItems() {
                let items = this.items;

                if(this.searchValue)
                {
                    items = items.filter(i => i['internal'].name.toLowerCase().indexOf(this.searchValue.toLowerCase()) !== -1
                        || i['external'].name.toLowerCase().indexOf(this.searchValue.toLowerCase()) !== -1
                        || i['external'].id.toLowerCase().indexOf(this.searchValue.toLowerCase()) !== -1);
                }

                return _.orderBy(items, item => {
                    return item['internal'].name.toLowerCase();
                });
            }
        },
        methods: {
            setSearch(value) {
                this.searchValue = value;
            },
            deleteConnection(item) {
                this.$emit('delete-item', item);
            },
            getIconTooltipTitle(item) {
                let title = this.iconTooltipTitle;
                if(Array.isArray(item[this.iconTooltipProperty])) {
                    _.forEach(item[this.iconTooltipProperty], function (iconItem) {
                        title += '\n• '+ iconItem;
                    });
                } else if(_.isString(item[this.iconTooltipProperty])) {
                    title += '\n• '+ item[this.iconTooltipProperty];
                }
                return title;
            }
        },
    };

</script>

<style lang="scss" scoped>
    @import '../../sass/initial-variables';

    .searchable-connected-list {
        width: 100%;
        max-height: 500px;

        .title-icon {
            font-size: 20px;
            line-height: 24px;
            position: absolute;
            margin-left: 5px;
        }

        .header-component {
            height: 30px;
            display: flex;

            .list-title {
                line-height: 30px;
                font-size: 14px;
                font-weight: bold;
                width: 50%;
            }
        }

        .list-component {
            border: 1px solid $black-03;
            max-height: 470px;
            border-radius: 3px;

            .top {
                background-color: $white;
                padding: 10px;
                width: 50%;

                .search-bar {
                    padding: 0;
                }
            }

            .connected-list {
                border-top: 1px solid $black-03;
                overflow-y: auto;
                height: 100%;
                max-height: 410px;

                .connected-list-item {
                    background-color: $white;
                    border-bottom: 1px solid $black-03;
                    padding: 6px;
                    display: flex;
                    justify-content: space-between;
                    height: 60px;

                    &:hover {
                        background-color: rgba($blue, 0.1);
                    }

                    .connected-list-item-label {
                        width: 40%;
                        font-size: 14px;
                        line-height: 44px;
                        overflow:hidden;
                        white-space:nowrap;
                        text-overflow: ellipsis;
                        display: flex;

                        .list-item-icon {
                            margin-right: 10px;
                        }

                        &.second-label {
                            margin-left: 6px;
                        }

                        .list-item-title {
                            line-height: 24px;
                            height: 50%;
                        }

                        .list-item-subtitle {
                            line-height: 20px;
                            height: 50%;
                            color: $black-40;
                            font-size: 10px;
                        }
                    }

                    .delete-button, .connection-icon {
                        width: 10%;
                        text-align: right;
                        padding-right: 20px;
                        line-height: 24px;

                        .sl-icon-link-horizontal {
                            font-size: 24px;
                            color: $black-40;
                            line-height: 44px;
                        }
                        .sl-icon-trash-can {
                            font-size: 20px;
                            color: $red;
                            cursor: pointer;
                            line-height: 44px;
                            padding: 12px;
                        }
                    }
                }
            }
        }
    }
</style>