<template>
    <nav class="crumble-path" v-if="items && items.length > 0">
        <span v-for="item in items" class="item">
           &nbsp;/&nbsp;<router-link v-if="item.title" :to="item.url" :title="item.title">{{ item.title }}</router-link>
        </span>
    </nav>
</template>

<script>

export default {
    name: 'CrumblePath',
    props: {
        items: {
            type: Array,
            default: null,
        },
        path: '',
    },
    data() {
        return {
            loading: true,
        };
    },
}
</script>

<style lang="scss">

@import '../../sass/initial-variables';

nav.crumble-path {
    padding-left: 60px;
    padding-top: 33px;
    padding-bottom: 33px;
    height: 90px;
    position: absolute;
    z-index: 2;

    .item {
        color: $grey;
        font-size: 14px;
        line-height: 24px;
        display: inline-block;
        max-width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        transition: 0.3s max-width;
        white-space: nowrap;

        &:hover {
            max-width: 250px;
        }

        a {
            color: $link;
        }
    }
}
</style>