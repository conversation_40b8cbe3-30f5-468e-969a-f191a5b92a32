@import 'variables';

.v-sidebar-menu {
    * {
        box-sizing: border-box;
    }

    .menu-header {
        position: relative;

        .menu-title {
            position: absolute;
            font-size: 18px;
            top: 17px;
            left: 60px;
            right: 10px;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    z-index: 999;
    box-sizing: border-box;
    transition: 0.3s width;

    > .vsm--list {
        width: 100%;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .vsm--dropdown > .vsm--list {
        padding: 5px;
    }

    .vsm--item {
        position: relative;
        display: block;
        height: 34px;
        padding-right: 10px;
        margin-bottom: 5px;
    }

    .vsm--link {
        padding-top: 8px;
        padding-bottom: 8px;
        padding-left: 22px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-radius: 0 6px 6px 0;
        position: relative;
        display: block;
        font-size: $item-font-size;
        font-weight: 400;
        line-height: $item-line-height;
        text-decoration: none;
        z-index: 20;
        transition: 0.3s all;
        margin-right: 10px;
        margin-bottom: 5px;

        &_exact-active,
        &_active {
            font-weight: normal;
        }

        &_disabled {
            opacity: 0.4;
            pointer-events: none;
        }

    }

    .vsm--title {
        display: block;
        white-space: nowrap;
        padding-left: 27px;
    }

    .vsm--icon {
        float: left;
        line-height: $item-line-height;
        margin-right: 10px;
    }

    .vsm--header {
        font-size: 14px;
        font-weight: 600;
        padding: 10px;
        white-space: nowrap;
        text-transform: uppercase;
    }

    .vsm--badge {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);

        &.default {
            background: $badge-color;
            font-size: 12px;
            border-radius: 50%;
            height: 18px;
            width: 18px;
            line-height: 20px;
            font-weight: 600;
            text-transform: uppercase;
            text-align: center;
        }
    }

    .vsm--toggle-btn {
        display: block;
        text-align: left;
        font-style: normal;
        height: 73px;
        cursor: pointer;
        border: none;
        width: 100%;
        outline: none;
        padding-left: 24px;

        &:after {
            content: '\f0c9';
        }

        &_slot:after {
            display: none;
        }
    }

    &.vsm_collapsed {
        & > .vsm--list {
            width: calc(100% + 17px);
            padding-right: 17px;
        }
    }

    &.vsm_rtl {
        right: 0;
        left: inherit;
        text-align: right;

        & > .vsm--list {
            direction: rtl;
        }

        &.vsm_collapsed > .vsm--list {
            padding-right: 0px;
            padding-left: 17px;
            margin-left: -17px;
        }

        & .vsm--icon {
            float: right;
            margin-left: 10px;
            margin-right: 0px;
        }

        & .vsm--arrow {
            left: 10px;
            right: inherit;
        }

        & .vsm--badge {
            left: 10px;
            right: inherit;
        }
    }

    .expand-enter-active,
    .expand-leave-active {
        transition: height 0.35s ease;
        overflow: hidden;
    }

    .expand-enter,
    .expand-leave-to {
        height: 0 !important;
    }

    .slide-animation-enter-active {
        animation: slide-animation 0.2s;
    }

    .slide-animation-leave-active {
        animation: slide-animation 0.2s reverse;
    }

    @keyframes slide-animation {
        0% {
            width: 0%;
        }
        100% {
            width: 100%;
        }
    }
}

/*
@media screen and (max-width: 640px) {
  .v-sidebar-menu > .vsm-expanded{
  }

  .v-sidebar-menu > .vsm-collapsed {
    height: 76px;
    transition: height 500ms;
  }
  .v-sidebar-menu > .vsm-default {
    transition: height 500ms;
  }

  }
}
*/

.footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background-color: transparent;
    padding: 0;

    .switch {
        height: 62px;
        cursor: pointer;
        color: white;
        padding: 20px 22px;
        border-top: 1px solid white;
        white-space: nowrap;
        overflow: hidden;

        .switch-label {
            padding-left: 10px;
        }
    }
}

.is-mobile {

    .v-sidebar-menu {
        width: 100% !important;
        height: 100%;
        overflow: hidden;

        .menu-header {
            position: relative;

            .vsm--toggle-btn {
                height: 60px;
            }

            .menu-title {
                position: absolute;
                font-size: 18px;
                top: 17px;
                left: 60px;
                right: 60px;
                text-align: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: white;
            }
        }
    }
}

.is-mobile.state-sidebar-menu-collapsed {

    nav.main {

        .v-sidebar-menu {
            height: 60px;
        }

        .vsm--list,
        .footer {
            display: none;
        }
    }
}

.hamburger {
    font-size: 20px;
}