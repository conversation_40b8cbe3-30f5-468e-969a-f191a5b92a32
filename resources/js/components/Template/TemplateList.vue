<template>
    <div class="template-list">
        <label class="label">{{ label }}</label>
        <hr class="line">
        <div v-for="template in templates" class="default-template">
            <a @click="$emit('selected', template.id)">{{ template.name }}</a>
            <hr class="line">
        </div>
    </div>
</template>

<script>

export default {
    name: "TemplateList",
    props: {
        label:{
            type: String
        },
        templates: {
            fields: {
                type: Array
            }
        }
    },
}
</script>

<style scoped>
    .template-list {
        margin-bottom: 10px;
        
        .line {
            margin: 6px 0;
        }

        .label {
            padding: 8px 0 0 0;
        }
    }
</style>