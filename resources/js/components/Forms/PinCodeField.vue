<template>
    <div v-if="visibility" class="field" @input="$emit('update:value', pinCode)">
        <Notification v-if="notification && (notification.always_show || notification.value_equals_to === pinCode)"
                      :type="notification.type"
                      :text="notification.translation" />
        <Input :value="value"
               :type="type"
               :name="name" :label="label"
               :disabled="disabled"
               :maxlength="maxlength"
               v-model:value="pinCode"
        />
    </div>
</template>

<script>
import Input from './Input';
import Notification from "./Notification";

export default {
        name: "PinCodeField",
        components: {
            Notification,
            Input
        },
        props: {
            type: String,
            value: String,
            name: String,
            label: String,
            disabled: null,
            maxlength: null,
            notification: Array,
            visibility: {
                default: true,
                type: Boolean
            },
        },
        data () {
            return {
                pinCode: this.value
            }
        }
    }
</script>