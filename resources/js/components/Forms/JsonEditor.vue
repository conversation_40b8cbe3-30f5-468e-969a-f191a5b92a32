<template>
    <VueJsonEditor
        v-model:value="innerValue"
        :show-btns="false"
        :expandedOnStart="true"
        @json-change="emitFormValue($event)"
    />
</template>

<script>

    import VueJsonEditor from "vue-json-editor";

    export default {
        name: "JsonEditor",
        components: {
            VueJsonEditor
        },
        data() {
            return {
                innerValue: null
            }
        },
        props: {
            /**
             * Presets the selected options value.
             * @type {Object||Array||String||Integer}
             */
            value: {
                type: null,
                default() {
                    return []
                }
            },
            /**
             * Label to look for in option Object
             * @default 'label'
             * @type {String}
             */
            label: {
                type: String
            },
            /**
             * Disable the MultiSelect
             */
            disabled: false,
        },
        methods: {
            emitFormValue(value) {
                if(value){
                    if (this.trackBy) {
                        if(Array.isArray(value)){
                            value = value.map(v => v[this.trackBy]);
                        }
                        else{
                            value = value[this.trackBy];
                        }
                    }
                }

                this.$emit("input", value);
            },
            setValue(value){
                if (this.trackBy) {
                    let newVal = [];
                    for(let i in this.options) {
                        if(Array.isArray(value)){
                            if(value.includes(this.options[i][this.trackBy])){
                                let entry = {};
                                entry[this.trackBy] = this.options[i][this.trackBy];
                                entry[this.label] = this.options[i][this.label];
                                newVal.push(entry);
                            }
                        }
                        else{
                            if(value === this.options[i][this.trackBy]){
                                let entry = {};
                                entry[this.trackBy] = this.options[i][this.trackBy];
                                entry[this.label] = this.options[i][this.label];
                                newVal.push(entry);
                            }
                        }
                    }
                    this.innerValue = newVal;
                    this.emitFormValue(newVal);
                } else {
                    this.innerValue = value;
                }
            }
        },
        watch: {
            value: function (newVal, oldVal) {
                this.setValue(newVal);
            }
        },
        mounted() {
            this.setValue(this.value);
        }
    }
</script>

<style>

    .multiselect__tags > span{
        font-size: 14px !important;
    }

    .multiselect__tags > input{
        font-size: 14px !important;
    }

    .multiselect__option--highlight:after {
        background-color: #0F9BF3 !important;
    }
</style>