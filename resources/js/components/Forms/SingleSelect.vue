<template>
    <div v-if="visibility" class="single-select-field" :name="name">
        <label :class="{'input-inline': inline}">
            <div v-if="label" class="label">
                {{ label }}
            </div>
            <Notification
                v-if="notification && (notification.always_show || notification.value_equals_to === value)"
                :type="notification.type"
                :text="notification.translation"
            />
            <Multiselect
                v-model="valueObject"
                :name="name"
                :options="optionsArray"
                :searchable="settings?.searchable"
                :multiple="false"
                :allow-empty="allowEmpty"
                :show-labels="settings?.showLabels"
                :placeholder="placeholder ? placeholder : this.$t('form.single_select_default_placeholder')"
                :label="display"
                :disabled="disabled"
                :validation="validation"
                @input="handleInput"
                @update:modelValue="handleInput"
            />
            <div v-if="message" class="message">
                {{ message }}
            </div>
        </label>
    </div>
</template>

<script>

/**
 * This component wraps the Vue MultiSelect to add the following features:
 * - Clickable label element around select element
 * - Allow JSON/object values to be set to avoid having to convert it to array every time.
 * - Select a value with just the 'key' (string) instead of having to match the entire object.
 */
import Multiselect from 'vue-multiselect'
import Notification from './Notification';
import InputValidationMessage from "../../mixins/InputValidationMessage.vue";

export default {
    name: 'SingleSelect',
    components: {
        Notification,
        Multiselect,
    },
    mixins: [InputValidationMessage],
    data() {
        return {
            optionsArray: [],
            valueObject: null,
        };
    },
    props: {
        name: '',
        value: '',
        settings: {
            type: Object,
            default: {
                searchable: false,
                allowEmpty: false,
                showLabels: false
            }
        },
        trackBy: {
            type: String,
            default: 'value'
        },
        display: {
            type: String,
            default: 'display'
        },
        type: String,
        label: String,
        placeholder: {
            type: String,
            default: ''
        },
        options: null,
        visibility: {
            default: true
        },
        disabled: {
            type: Boolean,
            default: false
        },
        allowEmpty: {
            type: Boolean,
            default: false
        },
        notification: Object,
        validation: Object,
        inline: {
            default: false,
            type: Boolean
        }
    },
    created() {
        this.optionsArray = this.optionsToArray(this.options);
        this.valueToObject();
        // send default value to parent if set
        if (this.valueObject) {
            this.$emit('input', this.valueObject.value);
            this.$emit('update:value', this.valueObject.value);
        }
    },
    methods: {
        valueToObject() {
            if (this.value === null) {
                this.valueObject = null;
            }

            for (let i in this.optionsArray) {
                if (this.optionsArray[i][this.trackBy] == this.value) {
                    this.valueObject = this.optionsArray[i];
                    break;
                }
            }
        },
        // For string array: we return options array of objects containing the same string in {value, display}
        optionsToArray(options) {
            let optionsArray = [];
            if (Array.isArray(options)) {
                options.map((option, index) => {
                    if (_.isString(option)) {
                        optionsArray.push({value: index, display: option});
                    } else if (_.isInteger(option)) {
                        optionsArray.push({value: option, display: option});
                    } else {
                        optionsArray.push(option);
                    }
                })
                return optionsArray;
            }

            for (let i in options) {
                optionsArray.push({value: i, display: options[i]});
            }

            return optionsArray;
        },
        handleInput(valueObject) {
            this.valueObject = valueObject;
            let value = valueObject.value;
            this.$emit('input', value);
            this.$emit('update:value', value);
        }
    },
    watch: {
        value() {
            this.valueToObject();
        },
        //to allow JSON objects as options, convert it to an array to pass to the MultiSelect.
        options() {
            this.optionsArray = this.optionsToArray(this.options);
            this.valueToObject();
        }
    }
}
</script>

<style src="vue-multiselect/dist/vue-multiselect.css"></style>
<style lang="scss" scoped>

@import '../../../sass/initial-variables';

.multiselect__tags > span {
    font-size: 14px !important;
}

.multiselect__tags-wrap > span {
    background: $selectedBg !important;
}

.multiselect__content-wrapper > ul > li > span {
    font-weight: normal !important;
    font-size: 14px !important;
}

.multiselect__option--highlight {
    background: $selectedBg !important;
}

.multiselect__option--selected.multiselect__option--highlight {
    background: $selectedBg !important;
}

.multiselect__option--selected.multiselect__option--highlight:after {
    background: $selectedBg !important;
}

.message {
    background: none;
    color: $red;
}

.input-inline {
    display: flex;
    justify-content: space-between;

    :first-child {
        flex: 1;
    }

    > * {
        flex: 1.5
    }
}
</style>