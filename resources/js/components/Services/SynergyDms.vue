<template>
    <div>
        <div class="columns">
            <div class="column is-half">
                <Loader :is-loading="loading" :text="$t('common.loading')">
                    <h1>{{ $t('service.document_management_system.synergy_dms.configure_title') }}</h1>
                    <br>
                    <div v-if="!loading">
                        <div v-if="!configured">
                            <Notification type="is-warning"
                                          :text="$t('service.document_management_system.synergy_dms.unauthorized_msg')"/>
                        </div>
                        <div v-else>
                            <Notification type="success-notification"
                                          :text="$t('service.document_management_system.synergy_dms.authorized_msg')"/>
                        </div>
                        <form @submit="configure">
                            <br>
                            <div v-if="configuring">
                                <p class="intro"
                                   v-html="$t('service.document_management_system.synergy_dms.configure_intro')"/>
                                <FormGenerator v-model:schema="schema_configuration"
                                               v-model:value="formDataConfiguration"/>
                                <br>
                            </div>
                            <button v-if="configuring" class="is-info button">{{ $t('common.configure') }}</button>
                            <button v-else @click="switchReconfigure($event,true)" class="is-info button">
                                {{ $t('common.reconfigure') }}
                            </button>
                            <button v-if="configured && configuring" @click="switchReconfigure($event,false)"
                                    class="button is-text opacity-background">{{ $t('common.cancel') }}
                            </button>
                            <button class="button is-danger pull-right" @click="deleteService($event)">
                                {{ $t('common.delete') }}
                            </button>
                        </form>
                    </div>
                </Loader>
            </div>
            <div class="column is-half">
                <h2>{{ $t('common.preferences') }}</h2>
                <FormGenerator v-model:schema="preferenceSchema" v-model:value="preferenceData"/>
                <br>
                <button @click="savePreferences" class="button is-info">{{ $t('common.save') }}</button>
            </div>
        </div>
    </div>
</template>
<script>

import Label from '../Forms/Label';
import FormGenerator from '../Forms/FormGenerator';
import Loader from '../Loader';
import Notification from '../Forms/Notification';
import FormSubmitHandler from '../../mixins/FormSubmitHandler';
import {mapActions} from 'vuex';
import Checkbox from '../Forms/Checkbox';
import SynergyDmsApi from '../../api/dms/SynergyDmsApi';
import PreferenceSchemaMixin from './PreferenceSchemaMixin.vue';

export default {
    name: 'SynergyDms',
    mixins: [FormSubmitHandler, PreferenceSchemaMixin],
    components: {
        Notification,
        Loader,
        FormGenerator,
        Label,
        Checkbox,
    },
    props: [
        'account_service',
    ],
    data() {
        return {
            loading: true,
            configured: false,
            reconfigure: false,
            formDataConfiguration: {},
            schema_configuration: {
                api_key: {
                    fieldType: 'Input',
                    type: 'text',
                    placeholder: this.$t('service.document_management_system.synergy_dms.fields.api_key'),
                    label: this.$t('service.document_management_system.synergy_dms.fields.api_key'),
                    required: true,
                    name: 'api_key',
                    visibility: true,
                },
            },
        };
    },
    computed: {
        configuring() {
            return !this.configured || this.reconfigure;
        },
    },
    created() {
        this.checkConnection().then((response) => {
            this.configured = response.data.data;
        }).finally(() => {
            this.loading = false;
        });
    },
    methods: {
        ...mapActions({
            deleteAccountService: 'services/deleteAccountService',
        }),
        configure() {
            this.loading = true;
            SynergyDmsApi.setupConnection(this.account_service.id, this.formDataConfiguration).then((res) => {
                let account_service = res.data.data;
                this.configured = account_service && account_service.properties && account_service.properties.api_key;
            }).finally(() => this.loading = false);
        },
        switchReconfigure(event, value) {
            event.preventDefault();
            this.reconfigure = value;
        },
        checkConnection() {
            return SynergyDmsApi.checkConnection(this.account_service.id);
        },
        deleteService(event) {
            event.preventDefault();
            let self = this;
            this.$swal({
                title: this.$t('service.document_management_system.synergy_dms.delete_title'),
                html: this.$t('service.document_management_system.synergy_dms.delete_message'),
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#F30F2A',
                confirmButtonText: this.$t('common.delete'),
            }).then((result) => {
                if (result.value) {
                    self.deleteAccountService(self.account_service.id);
                }
            });
        },
        savePreferences() {
            this.$emit('update-preferences', this.preferenceData);
        },
    },
};
</script>

<style lang="scss" scoped>
@import '../../../sass/initial-variables';

.intro {
    white-space: pre-line;
}

.success-notification {
    background-color: $green;
    color: $white;
}

.label-border {
    border-bottom: solid 1px $black-03;
    padding-bottom: 20px;
}

h2 {
    font-size: 14px;
    font-weight: bold;
    padding-bottom: 20px;
}

.checkbox-auditlog {
    margin-bottom: 20px;
}
</style>