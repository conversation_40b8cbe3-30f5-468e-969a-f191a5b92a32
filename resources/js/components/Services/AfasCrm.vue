<template>
    <Loader :is-loading="loading" :text="$t('common.loading')">
        <div class="columns afas-tasks-settings">
            <div class="column is-half">
                <div v-if="!loading">
                    <h2>{{ $t('service.customer_relationship_management.' + this.account_service.service.reference_name + '.configure_title') }}</h2>
                    <Notification v-if="configured" type="is-success"
                                  :text="$t('service.customer_relationship_management.' + this.account_service.service.reference_name + '.configured')"/>
                    <Notification v-if="errorConfiguring" type="is-warning"
                                  :text="$t('service.customer_relationship_management.' + this.account_service.service.reference_name + '.error_configuring')"/>
                    <div v-if="editing">
                        <p v-html="$t('service.customer_relationship_management.' + this.account_service.service.reference_name + '.intro').replace(/\n/g, '<br>')"></p>
                        <Input v-model:value="clientId"
                               :label="$t('service.customer_relationship_management.afas_crm.fields.client_id')"/>
                        <Label :text="$t('service.customer_relationship_management.afas_crm.fields.afas_token')"/>
                        <HelpText
                            :text="$t('service.customer_relationship_management.afas_crm.fields.afas_token_example')"/>
                        <Input v-model:value="token"/>
                        <br>
                        <button class="button is-info" @click="configure">{{ $t('common.done') }}</button>
                        <button class="button is-danger pull-right" @click="deleteService">
                            {{ $t('common.delete') }}
                        </button>
                    </div>
                    <button v-else class="button is-info" @click="edit">{{ $t('common.edit') }}</button>
                </div>
            </div>
            <div v-if="configured && !loading" class="column is-half">
                <h2>{{ $t('common.preferences') }}</h2>
                <FormGenerator v-model:schema="preferencesSchema" v-model:value="preferencesRequestData" />
                <br>
                <button class="button is-info" @click="savePreferences">{{ $t('common.save') }}</button>
            </div>
        </div>
    </Loader>
</template>

<script>

import SingleSelect from "../Forms/SingleSelect";
import MultipleSelect from "../Forms/MultipleSelect";
import Input from "../Forms/Input";
import AfasCrmApi from "../../api/crm/AfasCrmApi";
import AccountApi from "../../api/AccountApi";
import AccountServiceApi from "../../api/AccountServiceApi";
import Notification from "../Forms/Notification";
import Loader from "../Loader";
import Label from "../Forms/Label";
import HelpText from "../Forms/HelpText";
import Checkbox from "../Forms/Checkbox";
import FormGenerator from '../Forms/FormGenerator';
import store from "../../store";
import {mapActions} from "vuex";
import CrmMixin from "./Crm/CrmMixin";

export default {
    name: 'AfasCrm',
    components: {
        Checkbox,
        HelpText,
        Label,
        Input,
        MultipleSelect,
        SingleSelect,
        FormGenerator,
        Notification,
        Loader
    },
    mixins: [CrmMixin],
    props: [
        'account_service'
    ],
    data() {
        return {
            loading: true,
            configured: false,
            errorConfiguring: false,
            editing: false,
            preferencesRequestData: {},
            preferencesSchema: {
                sync_users_group: {
                    fieldType: 'SingleSelect',
                    id: 'sync_users_group',
                    name: 'sync_users_group',
                    label: this.$t('service.customer_relationship_management.afas_crm.fields.sync_users'),
                    default: this.account_service.properties.sync_users_group,
                    options: [],
                    visibility: false
                },
                labelContactName: {
                    fieldType: 'Label',
                    text: this.$t('service.customer_relationship_management.afas_crm.fields.client_types.label'),
                    visibility: true,
                },
                client_types: {
                    fieldType: 'MultipleSelect',
                    name: 'client_types',
                    trackBy: "key",
                    label: "name",
                    multiple: true,
                    options: this.clientTypes,
                    default: this.account_service.properties && this.account_service.properties.client_types ? this.account_service.properties.client_types : [],
                    visibility: true,
                    disabled: !store.state.user.profile.can.is_account_manager
                },
                sync_companies_groups_label: {
                    fieldType: 'Label',
                    text: this.$t('service.customer_relationship_management.fields.sync_groups'),
                    visibility: true,
                },
                sync_companies_groups: {
                    fieldType: 'MultipleSelect',
                    id: 'sync_companies_groups',
                    name: 'sync_companies_groups',
                    trackBy: "value",
                    label: 'display',
                    multiple: true,
                    options: [],
                    default: [],
                    visibility: true,
                    disabled: !store.state.user.profile.can.is_account_manager
                },
                auth_id_strategy: {
                    fieldType: 'SingleSelect',
                    id: 'authIdStrategy',
                    name: 'auth_id_strategy',
                    label: this.$t('service.crm.auth_id_strategy.label'),
                    default: this.selectedAuthIdStrategy,
                    options: [],
                    visibility: true
                }
            },
            token: '',
            clientId: ''
        };
    },
    computed: {
        clientTypes() {
            return [
                {'key': 'client_organization', 'name': this.$t('service.customer_relationship_management.afas_crm.fields.client_types.client_organization')},
                {'key': 'client_ib', 'name': this.$t('service.customer_relationship_management.afas_crm.fields.client_types.client_ib')}
            ]
        }
    },
    created() {
        this.fetchConfiguration();
        this.fetchAvailableGroups();
        this.fillData();
    },
    methods: {
        ...mapActions({
            deleteAccountService: 'services/deleteAccountService',
            updatePreferences: 'services/updatePreferences',
        }),
        edit() {
            this.editing = true;
        },
        configure() {
            let data = {
                'client_id': this.clientId,
                'token': this.token
            };
            AfasCrmApi.authorize(this.account_service.id, this.account_service.service.reference_name, data).then((response) => {
                this.editing = false;
                this.errorConfiguring = false;
                this.configured = true;
            });
        },
        deleteService() {
            let self = this;
            this.$swal({
                title: this.$t('service.customer_relationship_management.' + this.account_service.service.reference_name + '.delete_title'),
                html: this.$t('service.customer_relationship_management.' + this.account_service.service.reference_name + '.delete_message'),
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#F30F2A',
                confirmButtonText: this.$t('common.delete'),
            }).then((result) => {
                if (result.value) {
                    self.deleteAccountService(self.account_service.id);
                }
            })
        },
        fetchConfiguration() {
            AccountServiceApi.getConfigurations(this.account_service.id).then((response) => {
                if (response.data !== '') {
                    this.clientId = response.data.client_id;
                    this.token = response.data.token;
                    this.configured = Boolean(this.clientId && this.token);
                } else {
                    this.editing = true;
                    this.errorConfiguring = true;
                }
            }).catch(() => {
                this.errorConfiguring = true;
                this.editing = true;
            })
        },
        fillData() {
            this.preferencesSchema.client_types.options = this.clientTypes;
            this.preferencesSchema.client_types.default = this.account_service.properties && this.account_service.properties.client_types ? this.account_service.properties.client_types : [];
            this.preferencesSchema.auth_id_strategy.options = this.authIdStrategyOptions;
            if (this.account_service.properties.hasOwnProperty('auth_id_strategy')) {
                this.selectedAuthIdStrategy = this.account_service.properties.auth_id_strategy;
                this.preferencesSchema.auth_id_strategy.default = this.account_service.properties.auth_id_strategy;
            }
            this.loading = false;
        }
    },
    watch: {
        'configured'() {
            this.preferencesSchema.sync_users_group.visibility = 'enabled';
        }
    }
}
</script>

<style lang="scss" scoped>
.is-vcentered {
    vertical-align: middle;
}

h2 {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 20px;
    border-bottom: solid 1px #d8d8d8;
    padding-bottom: 20px;
}

.multiselect {
    ::deep(.multiselect__content-wrapper) {
        width: 200px;
    }
}
</style>