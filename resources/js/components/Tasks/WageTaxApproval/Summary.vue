<template>
    <div class="task-summary">
        <div class="header">
            <span>{{$t('common.summary')}} {{$t('tasks.wage_tax_approval.in_currency', {currency: file.currency})}}</span>
            <div class="actions-container">
                <a v-if="file.url" :href="file.url" download class="pull-right"><i class="sl-icon-download" :title="$t('common.download')"></i></a>
                <a v-if="file.url" :href="file.url" target="_blank" class="pull-right"><i class="sl-icon-magnifier" :title="$t('common.enlarge')"></i></a>
            </div>
        </div>
        <div class="table-container">
            <table class="table is-fullwidth">
                <tbody v-for="row in file.summary">
                <tr>
                    <td class="sub-section"><b>{{row[0]}}</b></td>
                    <td class="sub-section">{{row[1]}}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'WageTaxSummary',
        props: {
            file: null
        }
    }
</script>

<style lang="scss" scoped>
    @import '../../../../sass/initial-variables';

    .task-summary {
        border: 1px solid $black-10;
        border-radius: 4px;

        .header {
            padding-left: 10px;
            font-weight: bold;
            border-bottom: 1px solid $black-10;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;

            .actions-container {
                display: flex;
                justify-content: flex-end;
                width: 80px;

                a {
                    display: inline-block;
                    width: 40px;
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                }
            }
        }

        .table-container {
            padding-top: 10px;
            padding-bottom: 5px;
        }

        table {

            tbody {
                .label {
                    min-width: 180px;
                }
            }

            tr:last-child td {
                padding-bottom: 7px;
            }

            tr:nth-of-type(2) td {
                padding-top: 7px;
            }

            td {
                vertical-align: middle !important;
            }

            th {
                border-bottom: 1px solid $black-10 !important;
                vertical-align: middle !important;
            }

            .section {
                border-top: 1px solid $black-10;
                border-bottom: 1px solid $black-10;
            }

            .sub-section {
                padding: 0 10px;
                border: none;
            }
        }
    }

</style>