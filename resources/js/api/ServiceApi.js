import BaseApi from "./BaseApi";

class ServiceApi extends BaseApi {
    async getAllServices(search = null) {
        return await this._get('index', {
            params: {
                search: search
            }
        });
    }

    async getActiveDmsService() {
        return await this._get('active_dms_service');
    }

    /**
     * Get service by name
     * @param name
     * @returns {Promise<AxiosResponse<any>>}
     */
    async getService(name) {
        return await this._get('', {
            params: {
                service_name: name
            }
        });
    }

    checkDuplicatesBeforeImport(companies, accountServiceId) {
        return this._post(`company/check_duplicates`, {
            'companies': companies,
            'account_service_id': accountServiceId,
        });
    }
}

export default new ServiceApi('/service/')