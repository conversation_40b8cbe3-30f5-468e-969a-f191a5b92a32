import BaseApi from "./BaseApi";

class AuthorizationsApi extends BaseApi {
    async getTypes() {
        return await this._get('types');
    }

    async getCompanies(type, page, search) {
        return await this._get('companies', {
            params: {
                authorization_type: type,
                page: page,
                search: search
            }
        });
    }

    async requestAuthorization(data) {
        return this._post('request_authorization', data);
    }
}

export default new AuthorizationsApi('/authorizations/')