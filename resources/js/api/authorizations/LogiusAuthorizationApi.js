import BaseApi from "../BaseApi";

class LogiusAuthorizationApi extends BaseApi {
    emailSettings(identifierId) {
        return this._get('email_settings/identifier_id/' + identifierId)
    }

    update(identifierId, settings) {
        return this._post('email_settings/update', {
            'identifier_id': identifierId,
            'settings': settings
        })
    }
}

export default new LogiusAuthorizationApi('/authorizations/')