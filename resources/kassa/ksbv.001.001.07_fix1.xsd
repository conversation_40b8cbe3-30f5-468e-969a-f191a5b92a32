<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:ksbv.001.001.07" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	targetNamespace="urn:iso:std:iso:20022:tech:xsd:ksbv.001.001.07" elementFormDefault="qualified">
	<xs:element name="Document" type="Document" />
	<xs:complexType name="AccountIdentification4Choice">
		<xs:choice>
			<xs:element name="Othr" type="GenericAccountIdentification1" />
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="Document">
		<xs:sequence>
			<xs:element name="CstmrCdtTrfInitn" type="CustomerCreditTransferInitiationV07" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="ActiveOrHistoricCurrencyAndAmount_SimpleType">
		<xs:restriction base="xs:decimal">
			<xs:fractionDigits value="5" />
			<xs:totalDigits value="18" />
			<xs:minInclusive value="0" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ActiveOrHistoricCurrencyAndAmount">
		<xs:simpleContent>
			<xs:extension base="ActiveOrHistoricCurrencyAndAmount_SimpleType">
				<xs:attribute name="Ccy" type="ActiveOrHistoricCurrencyCode" use="required" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:simpleType name="ActiveOrHistoricCurrencyCode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="EUR" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="AmountType4Choice">
		<xs:choice>
			<xs:element name="InstdAmt" type="ActiveOrHistoricCurrencyAndAmount" />
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="BranchAndFinancialInstitutionIdentification5">
		<xs:sequence>
			<xs:element name="FinInstnId" type="FinancialInstitutionIdentification8" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CashAccount24">
		<xs:sequence>
			<xs:element name="Id" type="AccountIdentification4Choice" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CreditTransferTransaction26">
		<xs:sequence>
			<xs:element name="PmtId" type="PaymentIdentification1" />
			<xs:element name="Amt" type="AmountType4Choice" />
			<xs:element name="RmtInf" type="RemittanceInformation11" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CustomerCreditTransferInitiationV07">
		<xs:sequence>
			<xs:element name="GrpHdr" type="GroupHeader48" />
			<xs:element name="PmtInf" type="PaymentInstruction20" />
			<xs:element name="SplmtryData" type="SupplementaryData1" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="DecimalNumber">
		<xs:restriction base="xs:decimal">
			<xs:fractionDigits value="17" />
			<xs:totalDigits value="18" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExternalLocalInstrument1Code">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="35" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="FinancialInstitutionIdentification8">
		<xs:sequence>
			<xs:element name="Othr" type="GenericFinancialIdentification1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericAccountIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max34Text" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericFinancialIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericOrganisationIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GenericPersonIdentification1">
		<xs:sequence>
			<xs:element name="Id" type="Max35Text" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GroupHeader48">
		<xs:sequence>
			<xs:element name="MsgId" type="Max35Text" />
			<xs:element name="CreDtTm" type="ISODateTime" />
			<xs:element name="NbOfTxs" type="Max15NumericText" />
			<xs:element name="InitgPty" type="PartyIdentificationGH" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="ISODate">
		<xs:restriction base="xs:date" />
	</xs:simpleType>
	<xs:simpleType name="ISODateTime">
		<xs:restriction base="xs:dateTime" />
	</xs:simpleType>
	<xs:complexType name="LocalInstrument2Choice">
		<xs:choice>
			<xs:element name="Cd" type="ExternalLocalInstrument1Code" fixed="IDEAL" />
		</xs:choice>
	</xs:complexType>
	<xs:simpleType name="Max140Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="140" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max15NumericText">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{1,15}" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max34Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="34" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max35Text">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="35" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Max35ANS">
    	<xs:restriction base="xs:string">
        	<xs:pattern value="[a-zA-Z0-9]{1,35}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="OrganisationIdentification8">
		<xs:sequence>
			<xs:element name="Othr" type="GenericOrganisationIdentification1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Party11Choice">
		<xs:choice>
			<xs:element name="OrgId" type="OrganisationIdentification8" />
			<xs:element name="PrvtId" type="PersonIdentification5" />
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="PartyIdentificationDB">
		<xs:sequence>
			<xs:element name="Id" type="Party11Choice" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PartyIdentificationGH">
		<xs:sequence>
			<xs:element name="Nm" type="Max140Text" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PaymentIdentification1">
		<xs:sequence>
			<xs:element name="EndToEndId" type="Max35ANS" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PaymentInstruction20">
		<xs:sequence>
			<xs:element name="PmtInfId" type="Max35ANS" />
			<xs:element name="PmtMtd" type="PaymentMethod3Code" fixed="TRF" />
			<xs:element name="NbOfTxs" type="Max15NumericText" />
			<xs:element name="CtrlSum" type="DecimalNumber" />
			<xs:element name="PmtTpInf" type="PaymentTypeInformation19" />
			<xs:element name="ReqdExctnDt" type="ISODate" />
			<xs:element name="Dbtr" type="PartyIdentificationDB" />
			<xs:element name="DbtrAcct" type="CashAccount24" />
			<xs:element name="DbtrAgt" type="BranchAndFinancialInstitutionIdentification5" />
			<xs:element name="CdtTrfTxInf" type="CreditTransferTransaction26" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="PaymentMethod3Code">
		<xs:restriction base="xs:string">
			<xs:enumeration value="TRF" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PaymentTypeInformation19">
		<xs:sequence>
			<xs:element name="LclInstrm" type="LocalInstrument2Choice" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PersonIdentification5">
		<xs:sequence>
			<xs:element name="Othr" type="GenericPersonIdentification1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RemittanceInformation11">
		<xs:sequence>
			<xs:element name="Ustrd" type="Max140Text" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupplementaryData1">
		<xs:sequence>
			<xs:element name="Envlp" type="SupplementaryDataEnvelope1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupplementaryDataEnvelope1">
		<xs:sequence>
			<xs:element name="Kassa" type="KassaData" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="KassaData">
		<xs:sequence>
			<xs:element name="Language" type="CountryCode" />
			<xs:element name="PortalID" type="xs:string" />
			<xs:element name="Subscription" type="xs:string" />
			<xs:element name="ReturnURL" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CountryCode">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z]{2,2}" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
