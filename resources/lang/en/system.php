<?php

return [
    'herald.file' => 'File',
    'herald.file.settings.last.modified' => 'Settings file last modified at:',
    'herald.file.settings.not.created' => 'Settings file is not created',
    'herald.last.modified' => 'Last modified',
    'herald.application.files' => 'Application files',
    'herald.field.file.required' => 'File is required',
    'herald.field.file.json' => 'JSON file is not valid',
    'herald.field.filename.required' => 'Filename is required',
    'herald.field.save' => 'Save file',
    'herald.field.commit' => 'Commit to all accounts',
    'task_tools' => [
        'manage_service_task' => [
            'no_results' => 'No task found',
            'delete_task_success' => 'Successfully removed task',
            'delete_task_failed' => 'Could not delete task/tasks',
            'delete_task_file_success' => 'Successfully removed file',
            'delete_task_file_failed' => 'Could not delete file',
            'update_status_success' => 'Task status successfully changed',
            'update_status_failed' => 'Changing task status failed',
            'update_company_success' => 'Updated company successfully',
            'update_company_failed' => 'Updating company failed',
            'update_company_wrong_account' => 'Updating company failed because the company is from another account',
        ],
        'manage_task_files' => [
            'no_results' => 'No task file found',
        ]
    ]
];
