<?php

return [
    'users' => 'Users',
    'activate_user_account' => ' Activate your account',
    'reactivate_user_account' => 'Reactivate your user account',
    'already_activated' => 'User :user already activated',
    'send_activation' => 'Send (re)activation link',
    'send_invitation' => 'Send invitation mail',
    'field_name_required' => 'Name is a required field',
    'field_auth_id' => 'Username',
    'field_auth_id_not_unique' => 'The username is already in use',
    'field_auth_secret' => 'Password',
    'field_password' => 'Password',
    'field_auth_method' => 'Authentication method',
    'field_auth_method_note_no_contexts' => 'Please select at least one group first',
    'field_auth_method_edit_note' => 'The configured authentication method can only be changed by the user. However, it is possible to enforce specific authentication methods on group level.',
    'field_auth_method_secret' => 'Password only',
    'field_auth_method_sms_otp' => 'Login + SMS OTP',
    'field_auth_method_totp' => 'Login + Authenticator (time-based OTP)',
    'field_auth_method_reset_totp' => 'Reset authenticator',
    'field_auth_method_totp_reset_attention' => 'After you have scanned the QR code, please do not forget to enter a verification code and save the changes. Otherwise the new Authenticator instance will not be activated.',
    'field_auth_method_none_available' => 'The combination of the selected groups result in the user not being able to have any authentication methods.',
    'field_auth_method_none_available_user' => 'You are unable to select any authentication method, please contact your administrator.',
    'field_otp_response' => 'Verification code',
    'field_totp_response' => 'Verification code',
    'field_sms_otp_response' => 'Verification code',
    'field_remember_me' => 'Remember me',
    'field_email_required' => 'Email address is required',
    'field_mobile_required' => 'Mobile phone number is required when using SMS OTP',
    'field_mobile_invalid' => 'This mobile phone number is invalid',
    'field_contexts' => 'User groups...',
    'field_contexts_required' => 'At least one group should be selected',
    'field_contexts_invalid' => 'Not all of the selected groups can be used. If this error remains, please contact your administrator.',
    'field_language' => 'Default language...',
    'field_image' => 'Image',
    'field_image_hint' => 'Maximum file size is 2MB',
    'field_image_invalid' => 'The image uploaded is invalid or too large',
    'field_image_invalid_mimes' => 'The image uploaded is not supported. Please use JPEG, PNG or GIF files.',
    'field_use_2fa' => 'Use SMS authentication',
    'field_masterkey' => 'Master key',
    'field_masterkey_hint' => 'Please enter your Master key here in order to preserve you widget settings. You received the Master key via mail (on :masterkey_sent). If you leave this field empty, then all your credentials are flushed.',
    'field_masterkey_not_present' => "Enter a valid Master key or confirm that you don't want to use a Master key",
    'field_masterkey_invalid' => 'The Master key is not valid. Please make sure the most recent Master key is entered correctly.',
    'field_last_login' => 'Last login',
    'field_invite' => 'Send invitation to user',
    'field_invite_hint' => 'Via mail, the user is asked to activate within 48 hours',
    'field_session_lifetime' => "Session lifetime (hours)",
    'field_session_lifetime_max' => 'The selected session lifetime exceeds the maximum, please open this window again. If the issue remains please contact your administrator.',
    'field_ip_whitelist_empty' => "List IP addresses is empty",
    'field_ip_whitelist_no_restrictions' => "No IP address restrictions",
    'ip_invalid' => 'IP address is invalid',
    'field_status' => 'Status',
    'field_status_new' => 'New',
    'field_status_active' => 'Active',
    'field_status_blocked' => 'Blocked',
    'field_status_archived' => 'Archived',
    'field_status_verified' => 'Verified',
    'field_last_login_empty' => 'User never logged in yet',
    'field_last_login_at_from' => 'Logged in at :time from :ip',
    'field_previous_login_at_from' => 'Logged in at :time from :ip',
    'field_created_at_by' => 'Created at :time',
    'field_single_session' => 'Allow only 1 active login session',
    'field_usage_types_note' => 'Leave this field empty to use the environment setting.',
    'field_auth_method_required' => 'An authentication method has not been selected.',
    'field_language_required' => 'Language field is required',
    'use_email_as_auth_id' => 'Use email as username',
    'access_widget_settings_incomplete' => 'The settings are not yet complete.',
    'communication_widget_settings_incomplete' => 'This widget is not visible on the dashboard because the settings are not yet complete.',
    'no_memberships' => 'No group memberships for this user',
    'no_memberships_found' => 'No group memberships found',
    'no_user_widgets' => 'No widgets for this user',
    'no_access_widgets' => 'The dashboard of this user doesn\'t contain access widgets.',
    'no_access_widgets_found' => 'No access widgets found',
    'no_communication_widgets' => 'The dashboard of this user doesn\'t contain communication widgets.',
    'no_communication_widgets_found' => 'No communication widgets found',
    'no_changes' => 'No changes have been made',
    'store_user_succeed' => 'User created!',
    'update_profile_succeed' => 'Profile updated!',
    'update_secret_succeed' => 'Password changed!',
    'update_user_succeed' => 'User updated!',
    'update_succeed' => 'User updated!',
    'delete_user_succeed' => 'User deleted!',
    'send_activation_succeed' => 'User activation will be sent to :recipient. <a href=":activation_url" target="_blank">View activation link</a>',
    'send_auth_recovery_succeed' => 'Authentication recovery will be send to :recipient',
    'update_security_settings_succeed' => 'Security settings updated!',
    'enable_all_context_widgets_succeed' => ':count widgets added to the personal dashboard of this user!',
    'create_user_not_authorized_warning' => 'Not authorized to create a user. \nProbably a user group has to be created first.',
    'store_user_not_authorized' => 'Not authorized to store this user',
    'update_secret_user_not_authorized' => 'Not authorized to update your password',
    'update_profile_user_not_authorized' => 'Not authorized to update your profile',
    'update_secret_verification_failed' => 'Incorrect password, passwords are not equal or violate password policies',
    'create_user_not_authorized' => 'Cannot create a user yet, please create a group first',
    'update_user_not_authorized' => 'Not authorized to update the user',
    'delete_user_not_authorized' => 'Not authorized to delete the user',
    'block_user_not_authorized' => 'Not authorized to block the user',
    'unblock_user_not_authorized' => 'Not authorized to unblock the user',
    'show_user_not_authorized' => 'Not authorized to view the user',
    'activation_succeed' => 'Your user account is successfully (re)activated!',
    'activation_failed' => 'Your user account could not be (re)activated',
    'activation_invalid_masterkey' => 'The Master key is not valid',
    'activation_link' => 'Activation link',
    'activation_user_not_found' => 'The user activation could not be initialized because the user account is currently not available. Please contact your administrator.',
    'welcome_user' => 'Welcome',
    'welcome_back_user' => 'Welcome back :user',
    'welcome_account_admin' => 'Welcome :user in your new account. Let\'s start with creating a user group. Next to that you can enable widgets and invite users to start building their personal dashboard.',
    'msg_recipient' => 'addressee',
    'msg_header' => 'Dear :name,',
    'msg_header_informal' => 'Hi :name,',
    'msg_regards' => 'Kind regards,
:sender
:account',
    'msg_regards_with_account' => 'Kind regards,
:account',
    'msg_regards_securelogin' => 'Kind regards,<br>SecureLogin',
    'msg_regards_hix' => 'Kind regards,<br>Team Hix',
    'deleted_user' => 'Deleted user',
    'your_username_label' => 'Your username: ',
    'your_environment_label' => 'Your environment: ',
    'missing_mobile_number' => 'User has no mobile number',
    'is_blocked' => 'This user is blocked',
    'message_update_succeed' => 'User successfully updated',

    'msg_activation_reminder' => [
        'subject' => '[Reminder] Invitation to Hix',
        'intro' => ":days days ago, we have send you an invitation. According to our records your user account has not yet been activated. Please could you assist us with this urgently so that we can provide you with access to your applications.",
        'instruction' => 'You can activate your user account in just two steps. Please click on the button above and then create your password.',
    ],

    'msg_reactivation_request' => [
        'subject' => 'Reactivation request',
        'intro' => 'User :name has requested to reactivate the account without a valid masterkey. Click the button below to check this request and approve or decline it. Be aware the user cannot proceed the reactivation without approval.',
    ],

    'msg_reactivation_request_approved' => [
        'subject' => 'Reactivation: approved',
        'button' => 'Reactivate',
        'intro' => 'You have submitted a request to reactivate without a valid masterkey. Your administrator has approved this request. Click the link below to reactivate.',
    ],

    'msg_reactivation_request_declined' => [
        'subject' => 'Reactivation: declined',
        'intro' => 'You have submitted a request to reactivate without a valid masterkey. Your administrator has declined this request. Contact your environment manager for more information.',
    ],

    'msg_comeback_reminder' => [
        'subject' => 'Your Hix account - reminder',
        'body' => 'You have previously logged into your :environment environment. Within this environment you have access to one central dashboard with all your login credentials. This dashboard allows you to login more easily and safely.<br><br>
      You receive this e-mail because we noticed that you have only logged in once. Using the button below you can log in again.<br><br>
      In case you have any questions about the use of Hix, or if you are curious which applications are available, please contact your manager.',
        'button_text' => 'Log in to your account',
    ],

    'msg_masterkey_new' => [
        'subject' => 'Please store: Hix Master key',
        'intro' => 'Congratulations! Your account has been successfully activated. You hereby receive your Hix Master key. This is a unique key which you need in case you forget your Hix password. <b>Without the Master key it is not possible to fully recover your account.</b><br><br>We advise you to store this e-mail in a secure place, in case you need it in the future.',
    ],

    'msg_masterkey_changed' => [
        'subject' => 'Please store: New Hix Master key',
        'intro' => 'Please see below your new personal Hix Master key. The new Master key replaces all Master keys received in the past for the user account mentioned below. With this unique code you can regain access to your user account if you forget your password. In such an event it is not possible to fully recover your account details without your Master key.',
        'advice' => 'We strongly advise you to store this e-mail in a safe location for future use.',
    ],

    'msg_reactivation' => [
        'subject' => 'Reactivation of your account',
        'intro' => 'A request has been made to reset your password.<br><br>
      To recover your account, you need the Master key that has been send to you on :date by email.<br><br>
      <b>Please be aware:</b> in case you do not enter a Master key, all of your entered credentials are deleted.<br><br>
      Please click on the button below and follow the steps.',
    ],

    'msg_security_changed' => [
        'subject' => 'Your security settings have been changed',
        'body' => 'This e-mail is to inform you that your login method has been changed.<br><br>
      If you did not perform this action, please contact your environment manager at once.<br><br>
      This action was performed from IP address :ip on :datetime with the browser :agent.',
    ],

    'msg_reactivated' => [
        'subject' => 'Your account has been reactivated',
        'body' => 'Your user account has been reactivated. As part of the process your password has been changed.<br><br>
       If you did not perform this action, please contact your environment manager at once.<br><br>
       Action executed from IP address: :ip on :datetime with the browser :agent',
    ],

    'msg_invitation_first_account_owner' => [
        'subject' => 'Your Hix environment is ready',
        'intro' => "Welcome to Hix! With Hix you will have easier and safer access to your often-used online applications.<br><br><b>How to proceed from here? </b><br><br>To get started, we made a <a href=\"https://support.hellohix.com\">support page</a> to explain the core features of our product.<br><br>Please activate your account in just two steps. Click on the button below and choose your password.",
        'your_username' => 'Your username: ',
        'your_environment' => 'Your environment: ',
        'outro' => 'If you have any more questions please visit our <a href="https://support.hellohix.com">support page</a>.<br><br>Our customer success team is happy to help you out.<br>Good luck!',
    ],

    'msg_auth_recovery_subject' => 'Authentication recovery requested',
    'msg_auth_recovery_intro' => 'Your manager has started an authentication recovery procedure. This allows you to login to Hix with only your password. <b>This link is only valid once.</b> After you have signed in it is possible to reset your authentication method.',
    'msg_auth_recovery_forgot_phone' => 'Forgot your phone?',
    'msg_auth_recovery_instruction' => 'You can login once with the link below, with just your password. The next time you login, you will login again with your authenticator.',
    'msg_auth_recovery_new_phone' => 'New phone?',
    'msg_auth_recovery_new_phone_instruction' => 'You can login with the link below once, with just your password. Follow below steps after you logged in:',
    'msg_auth_recovery_new_phone_step1' => 'Click your name top right',
    'msg_auth_recovery_new_phone_step2' => 'Go to security settings',
    'msg_auth_recovery_new_phone_step3' => 'Choose "reset authenticator"',
    'msg_auth_recovery_next_time' => 'The next time you login, you will use the newly configured authenticator.',
    'msg_auth_recovery_button_caption' => 'Start authentication recovery',
    'msg_auth_recovery_only_valid_for' => 'Attention: The authentication recovery is only valid for user :user',

    'created_by_azure' => [
        'subject' => 'Invitation to Hix',
        'intro' => "
            <b>:account_name uses Hix and has invited you.</b>
            <br>
            <br>
            Hix ensures that...
            <ul>
                <li>all your passwords are stored in a safe way;</li>
                <li>other cannot access your credentials;</li>
                <li>shared user accounts with your colleagues are always up-to-date.</li>
            </ul>
            <br>
            Please click the button below to login directly with your Microsoft account.
        ",
        'button' => 'Login',
        'regards' => 'Kind Regards,',
    ],

    'widgets_on_dashboard' => 'Widgets on dashboard',

    'profile_setup_intro' => 'Fill in the information below to complete your profile.',
    'auth_secret_setup_intro' => 'Your current password has expired and you need to set a new password. The password validity for you is :expire_days days.',

    'show_introduction' => "Show introduction",

    'use_days' => 'Has used this application on :use_days individual days',
    'avg_return_days' => 'Returns after an average of :avg_return_days days',

    'auth_secret_change_success' => 'Your password has successfully been changed.',
    'single_session_terminated_previous' => 'Only one active session is allowed for your user. You are now logged out in all other browsers and devices.',

    'dashboard_category' => [
        'field_name' => [
            'required' => 'A name is required.',
            'max' => 'The name cannot be more than 20 characters.',
        ],
        'user_widgets' => [
            'required' => 'No widgets were selected',
        ],
    ],

    'store_dashboard_category' => [
        'succeed' => 'The category has been added',
        'failed' => 'Something went wrong trying to add the category',
    ],

    'delete_dashboard_category' => [
        'succeed' => 'The category was deleted',
        'failed' => 'Something went wrong trying to delete the category',
    ],

    'security_method' => [
        'totp' => 'You are using TOTP with an authenticator as two factor authentication.',
        'sms_otp' => 'You are using SMS with the number :number as two factor authentication.',
        'secret_ask_admin' => 'You are not using two factor authentication. Ask your administrator to enable this.',
        'secret_change' => 'You are not using two factor authentication. Change this now for improved security.'
    ],

    'update_profile' => [
        'succeed' => 'The new profile settings have been saved successfully',
        'failed' => 'Something went wrong while saving the profile settings',
        'fields' => [
            'firstname' => [
                'required' => 'First name is required',
                'max' => 'First name can only contain up to 50 characters',
            ],
            'lastname' => [
                'max' => 'Last name can only contain up to 50 characters',
            ],
            'email' => [
                'required' => 'Email address is required',
                'email' => 'Email address is invalid',
                'max' => 'Email address can only contain up to 80 characters',
                'email_change_cooldown' => 'Email address was recently changed. Your email address can only be changed once every 5 minutes.'
            ],
            'mobile' => [
                'valid_mobile' => 'Mobile number is invalid',
                'max' => 'Mobile number can only contain up to 20 characters',
                'required' => 'Mobile number is required when your user account uses SMS OTP',
            ],
            'image' => [
                'invalid' => 'The uploaded image is invalid or too large',
                'invalid_mimes' => 'The uploaded image type is not supported. Please use JPEG, PNG or GIF files.',
            ],
        ],
    ],
    'edit_security_settings' => [
        'succeed' => 'The new security settings have been saved successfully',
        'failed' => 'Something went wrong while saving the security settings',
        'sms_send' => 'SMS is sent',
        'fields' => [
            'auth_secret' => [
                'required_with' => 'Current password is required when you wish to change your password',
                'hash_check' => 'This is not your current password',
            ],
            'auth_secret_new' => [
                'strong_password' => 'The new password is not strong enough, use uppercase, lowercase, numbers and symbols to make your password stronger.',
                'different' => 'The new password must be different from the current password.',
                'limit_password' => 'The new password cannot be longer than 200 characters.',
            ],
            'auth_secret_verification' => [
                'required_with' => 'Please repeat the new password',
                'same' => 'The repeated password is not the same',
            ],
            'auth_method' => [
                'in' => 'The authentication method is not allowed',
                'required' => 'Authentication method is required',
            ],
            'session_lifetime' => [
                'required' => 'Duration of your session is required',
                'numeric' => 'Duration of your session is invalid, try again',
                'max' => 'The selected session lifetime exceeds the maximum, please open this window again. If the issue remains please contact your administrator.',
            ],
            'totp_response' => [
                'required_if' => 'Please fill in the code from your authenticator app',
                'valid_totp' => 'The authenticator code is invalid',
            ],
            'mobile' => [
                'required' => 'Mobile phone number is required when using SMS OTP',
                'valid_mobile' => 'The mobile number is invalid',
                'max' => 'The mobile number can only contain up to 20 characters',
            ],
            'sms_otp_response' => [
                'required_if' => 'Please fill in the code from your SMS',
                'valid_sms_otp' => 'The SMS code is invalid',
            ],
        ],
    ],
    'vue_mass_store' => [
        'succeed' => 'The new users were successfully created',
        'failed' => 'Something went wrong trying to create the new users',
        'missing_names' => 'First name and last name of users are required',
        'duplicate_entries' => 'Some users already existed, and were not re-created',
    ],
    'vue_store' => [
        'succeed' => 'The new user was successfully created',
        'failed' => 'Something went wrong trying to create the new user',
        'fields' => [
            'member_context_ids' => [
                'required' => 'At least one group should be selected',
                'exists' => 'Not all of the selected groups can be used. If this error remains, please contact your administrator.',
            ],
            'firstname' => [
                'required' => 'First name is required',
                'max' => 'The first name exceeds the maximum length of 50 characters',
            ],
            'lastname' => [
                'max' => 'The last name exceeds the maximum length of 50 characters',
            ],
            'email' => [
                'email' => 'This email address is invalid',
                'required' => 'Email address is required',
                'max' => 'The email address exceeds the maximum length of 80 characters',
                'unique' => 'The email address is already in use',
            ],
            'mobile' => [
                'valid_mobile' => 'The phone number is invalid',
                'required' => 'Mobile phone number is required',
                'max' => 'The phone number exceeds the maximum length of 20 characters',
            ],
            'language' => [
                'in' => 'Invalid input: choose a valid language',
            ],
            'auth_id' => [
                'required' => 'Username is required',
                'max' => 'The username exceeds the limit 80 characters',
                'valid_auth_id' => 'Username is invalid',
                'unique' => 'The username is already in use',
            ],
            'auth_method' => [
                'required' => 'Authentication method is required',
                'in' => 'Invalid input: choose a valid authentication method',
            ],
            'ip_whitelist' => [
                'ip_list' => 'Not a valid list of IP addresses entered',
            ],
            'is_external' => [
                'required' => 'Type of user is required',
            ],
        ],
    ],
    'send_activation_email' => [
        'succeed' => 'The email has been sent to the user.',
        'failed' => 'Your email address has already been verified.'
    ],
    'send_verify_email_request' => [
        'succeed' => 'A mail has been sent to verify your email.',
        'failed' => 'Your email address has already been verified.'
    ],
    'send_auth_recovery_email' => [
        'succeed' => 'Authentication recovery link has been sent.'
    ],
    'update' => [
        'succeed' => 'Saved successfully!',
        'failed' => 'Something went wrong trying to save the changes to this user.',
        'validation' => [
            'firstname' => [
                'required' => 'First name is required',
                'max' => 'First name can contain up to 50 characters',
            ],
            'lastname' => [
                'max' => 'Last name can contain up to 50 characters',
            ],
            'email' => [
                'email' => 'Email address is not valid',
                'required' => 'Email address is required',
                'max' => 'E-mail address can contain up to 80 characters',
                'email_change_cooldown' => 'Email address was recently changed. Your email address can only be changed once every 5 minutes.'
            ],
            'mobile' => [
                'valid_mobile' => 'Mobile number is not valid',
                'max' => 'Mobile number can contain up to 20 characters',
                'required' => 'Mobile number is required when your user account uses SMS OTP',
            ],
            'ip_whitelist' => [
                'ip_list' => 'Invalid list of IP addresses entered',
            ],
            'auth_id' => [
                'required' => 'Username is required',
                'max' => 'Username can contain up to 80 characters',
            ],
            'is_external' => [
                'required' => 'User type is required',
            ],
        ]
    ],
    'delete' => [
        'succeed' => 'User deleted!',
        'failed' => 'Something went wrong trying to delete this user.',
    ],
    'update_companies' => [
        'succeed' => 'User updated successfully',
        'failed' => 'Something went wrong when trying to update user'
    ],
    'block' => [
        'succeed' => 'The user has been blocked',
        'unauthorized' => 'You are not allowed to block the user.'
    ],
    'unblock' => [
        'succeed' => 'The user has been unblocked',
        'unauthorized' => 'You are not allowed to unblock the user.'
    ],
    'login' => [
        'failed' => 'These credentials do not match our records or the user account is not active',
        'field_auth_id' => 'Username',
        'field_auth_secret' => 'Password',
    ],
    'set_task_user_totp' => [
        'succeed' => 'Authenticator was set up successfully',
        'failed' => 'Something went wrong please refresh your page and try again',
        'invalid_code' => 'The supplied code is incorrect',
        'validation' => [
            'totp_code' => [
                'required' => 'Please fill in the code',
                'digits' => 'The code has to be 6 numbers'
            ]
        ]
    ],
    'status' => [
        'new' => 'New',
        'active' => 'Active',
        'verified' => 'Verified',
        'blocked' => 'Blocked',
        'archived' => 'Archived',
        'deactivated' => 'Deactivated'
    ],
    'import' => [
        'processed' => 'User #:id :auth_id processed.',
        'not_found' => 'User #:id does not exist in this account.',
        'validation' => [
            'extensions' => 'Only file with extension .csv or .xlsx are accepted',
            'context_ids' => 'Select a user group'
        ]
    ]
];
