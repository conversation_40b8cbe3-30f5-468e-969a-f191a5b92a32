@import '{{ App::make('url')->to('/css/fonts.css?'.filemtime(public_path('/css/fonts.css'))) }}';

body{
  font-family:verdana;
  font-size:13px;
}

table{
  width:100%;
  box-sizing: border-box;
  border-collapse: collapse;
}

input{
  border-radius:2px;
  border:1px solid #CCC;
  margin:0;
  margin-top:10px;
  font-family:roboto;
  width:100%;
  box-sizing:border-box;
  -moz-box-sizing:border-box;
  padding: 10px;
  font-size:13px;
  border-radius: 5px;
}

i.zmdi {
  font-size: 23px; 
  padding-top: 10px;
}

a {
  display:block;
  margin-top: 10px;
}

.bgm-primary {
  background:{{$account->primary_color}}!important;
  color:{{$account->primary_color_contrast}}!important;
}

.bgm-secondary {
  background:{{$account->secondary_color}}!important;
  color:{{$account->secondary_color_contrast}}!important;
}

.bgm-white .bgm-primary{
  background: {{$account->primary_color_on_light}} !important;
  color: {{$account->primary_color_on_light_contrast}} !important;
}

.bgm-white .bgm-secondary{
  background: {{$account->secondary_color_on_light}} !important;
  color: {{$account->secondary_color_on_light_contrast}} !important;
}

.c-primary {
  color:{{$account->primary_color}}!important;
}

.c-secondary {
  color: {{$account->secondary_color}}!important
}

.bgm-white .c-primary{
  color: {{$account->primary_color_on_light}} !important;
}

.bgm-white .c-secondary{
  color: {{$account->secondary_color_on_light}} !important;
}

.btn-login {
  cursor:pointer;
  border-radius:5px;
  border:none;
  padding:10px;
  margin:0;
  margin-top:10px;
  width:100%;
  font-family:verdana;
  font-size:13px;
}

#overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255,255,255,0.8);
  z-index: 2;
}

#overlay .btn-login {
  width: 70%;
}
