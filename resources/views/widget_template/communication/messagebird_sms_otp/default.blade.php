<div class="card" id="messagebird_sms_otp_{{$widget->id}}">
  <div class="card-header bgm-secondary" style="height:40px; padding:11px 10px;">
    <div class="pull-left col-sm-11 p-0">
      <h2 style="padding-bottom:5px;">
        {{$widget->showLabel()}} {{!empty($widget->properties['recipient'])?'('.$widget->properties['recipient'].')':''}}
      </h2>
    </div>
    <div class="pull-right col-sm-1">
      <i style="font-size:20px;" class="c-primary-contrast zmdi zmdi-refresh pull-right"
         onclick="refreshContainer($('#messagebird_sms_otp_{{$widget->id}}'), '{{route('context_widget.communication_widget.reload',['widget'=>$widget->id])}}',true,undefined,refresh_communication_widgets)">
      </i>
    </div>
  </div>
  <div class="card-body" style="max-height:500px; overflow-y: scroll;">
    @if(!empty($widget->properties['recipient']))
      <div class="list-group">
        @if(!empty($widget->properties['mapped_data']['list']))
          @foreach($widget->properties['mapped_data']['list'] as $sms)
            <div class="list-group-item media">
              <div class="media-body">
                <div class="col-sm-10 p-0">
                  {!! preg_replace('/\b[0-9]{1,6}\b/','<span style="font-weight:bold;cursor: pointer;" onmousedown="copyText(this, event, true)" data-clipboard="$0">$0 <i class="zmdi zmdi-copy"></i></span>',$sms['body']) !!}
                </div>
                <div class="col-sm-2 p-0" style="text-align: right;">
                  {{date("H:i", strtotime($sms['created_at']))}}<br>
                  {{date("d-m-y", strtotime($sms['created_at']))}}<br>
                </div>
              </div>
            </div>
          @endforeach
        @else
          <div class="center p-20">
            <span>{{trans('widget.messagebird_sms_otp.no_results')}}</span>
          </div>
        @endif
      </div>
    @else
      <div class="center p-20">
        <span>{{trans('widget.messagebird_sms_otp.setup_required')}}</span>
      </div>
    @endif
  </div>
</div>

<script>
  function copyText(element, e, should_notify)
  {
    if(e.which === 1)
    {
      new ClipboardJS(element, {
        container: document.getElementById('messagebird_sms_otp_{{$widget->id}}'),
        text: function(element) {
          return $(element).attr('data-clipboard');
        }
      });

      if(should_notify === true)
      {
        notify("{{trans('common.copied_to_clipboard')}}", 'success', 500);
      }
    }
  }
</script>
