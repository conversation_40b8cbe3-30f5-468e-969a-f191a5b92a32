<?php

use App\Company;
use App\Support\Carbon;

?>

@if (\Session::has('success'))
    <div class="alert alert-success">
        {!! \Session::get('success') !!}</li>
    </div>
@endif

<div>
    <form action="/system/open_questions/store" method="POST">
        @csrf
        <fieldset>
            <label>Service</label>
            <select name="service_name">
                <option value="twinfield_open_questions">Twinfield</option>
            </select>
        </fieldset>
        <fieldset>
            <label>Company</label>
            <select name="company_id">
                <?php foreach ($companies as $company) {
                    /** @var Company $company */
                    echo '<option value="' . $company->id . '">' . $company->name . '</option>';
                } ?>
            </select>
        </fieldset>
        <fieldset>
            <label>Title</label>
            <input type="text" name="title" value="GameStop Actions"/>
            <label>Name</label>
            <input type="text" name="name" value="GameStop Actions"/>
        </fieldset>
        <fieldset>
            <label>Amount</label>
            <input type="text" name="amount" step="0,01" value="1.123,33"/>
            <small>DB problems if format will be incorrect</small>
        </fieldset>
        <fieldset>
            <label>Type</label>
            <select name="type">
                <option value="private_or_business">Private or business</option>
                <option value="missing_invoice">Missing Invoice</option>
                <option value="other">Other</option>
            </select>
        </fieldset>
        <fieldset>
            <label>Currency</label>
            <select name="currency">
                <option value="EUR">€</option>
                <option value="GBP">£</option>
                <option value="USD">$</option>
            </select>
        </fieldset>
        <fieldset>
            <label>Transaction Date</label>
            <input type="datetime-local" name="transaction_date" value="<?php echo Carbon::now()->format('Y-m-d\TH:i') ?>"/>
        </fieldset>
        <fieldset>
            <label>Attachment Needed</label>
            <input type="checkbox" name="attachment_needed" value="1"/>
        </fieldset>
        <fieldset>
            <label>Note to client</label>
            <textarea type="text" name="internal_note"></textarea>
        </fieldset>
        <button type="submit">Create</button>
    </form>
</div>

<style type="text/css">
    fieldset {
        border: none;
    }
</style>