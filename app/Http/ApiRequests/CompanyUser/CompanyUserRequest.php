<?php

namespace App\Http\ApiRequests\CompanyUser;

use App\Http\Requests\Request;
use App\Traits\Requests\AuthorizeSanctumApiRequest;

/**
 * @Class CompanyUserRequest
 * @property int $company_id
 */
class CompanyUserRequest extends Request
{
    use AuthorizeSanctumApiRequest;

    public function authorize(): bool
    {
        return (!empty($this->user()) && $this->user()->canManageCompanies()) || $this->sanctumApiAuthorize();
    }

    public function rules(): array
    {
        return [
            'page' => 'numeric',
            'limit' => 'numeric'
        ];
    }

    public function messages(): array
    {
        return [
            'page.numeric' => trans('validation.numeric', ['attribute' => 'page']),
            'page.limit' => trans('validation.numeric', ['attribute' => 'limit']),
        ];
    }
}
