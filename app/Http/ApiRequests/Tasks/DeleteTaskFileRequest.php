<?php

namespace App\Http\ApiRequests\Tasks;

use App\Http\VueRequests\PostRequest;
use App\Traits\Requests\AuthorizeSanctumApiRequest;

/**
 * @property int $task_file_id
 */
class DeleteTaskFileRequest extends PostRequest
{
    use AuthorizeSanctumApiRequest;

    public function authorize(): bool
    {
        if (!empty($this->user())) {
            return $this->user()->isManagerOfHostAccount() || $this->user()->isAdminUser();
        }

        return $this->sanctumApiAuthorize();
    }

    public function rules(): array
    {
        return [];
    }

    public function messages(): array
    {
        return [];
    }
}
