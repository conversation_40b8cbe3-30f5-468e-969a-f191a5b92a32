<?php

namespace App\Http\ApiRequests\User;

use App\Http\VueRequests\GetRequest;
use App\Traits\Requests\AuthorizeSanctumApiRequest;

class ExternalUserRequest extends GetRequest
{
    use AuthorizeSanctumApiRequest;

    public function authorize(): bool
    {
        return (!empty($this->user()) && $this->user()->isInternal()) || $this->sanctumApiAuthorize();
    }

    public function rules(): array
    {
        return [];
    }

    public function messages(): array
    {
        return [];
    }
}
