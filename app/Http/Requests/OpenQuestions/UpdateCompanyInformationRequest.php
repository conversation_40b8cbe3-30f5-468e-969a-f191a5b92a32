<?php

namespace App\Http\Requests\OpenQuestions;

use App\Http\VueRequests\Request;

/**
 * @property array $data
 */
class UpdateCompanyInformationRequest extends Request
{
    public function authorize(): bool
    {
        return $this->user()->canManageCompanies();
    }

    public function rules(): array
    {
        return [
            'data.externalId' => 'required|string',
            'data.externalName' => 'required|string|no_url',
            'data.serviceName' => 'required|string',
            'data.blocked' => 'required|bool'
        ];
    }

    public function externalId(): string
    {
        return $this->data['externalId'];
    }

    public function serviceName(): string
    {
        return $this->data['serviceName'];
    }

    public function getData(): array
    {
        return $this->data;
    }
}
