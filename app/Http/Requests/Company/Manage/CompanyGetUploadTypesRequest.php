<?php

namespace App\Http\Requests\Company\Manage;

use App\Http\VueRequests\GetRequest;

class CompanyGetUploadTypesRequest extends GetRequest
{
    public function authorize(): bool
    {
        return $this->user()->canManageCompanies();
    }

    public function rules(): array
    {
        return [];
    }

    public function getCompanyId()
    {
        return $this->route('company');
    }
}
