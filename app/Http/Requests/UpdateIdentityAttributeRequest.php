<?php

namespace App\Http\Requests;

use App\Http\VueRequests\PostRequest;

/**
 * @property int $identity_attribute_id
 * @property string $value
 */
class UpdateIdentityAttributeRequest extends PostRequest
{
    public function authorize(): bool
    {
        return $this->user()->isAdminUser();
    }

    public function rules(): array
    {
        return [
            'value' => 'required|string|max:255',
        ];
    }
}
