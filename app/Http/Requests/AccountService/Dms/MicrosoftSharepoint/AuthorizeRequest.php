<?php

namespace App\Http\Requests\AccountService\Dms\MicrosoftSharepoint;

use App\Http\VueRequests\Request;
use App\ValueObject\Services\Dms\MicrosoftSharepoint\Configuration;

/**
 * Class AuthorizeRequest.
 *
 * @property string $client_id
 * @property string $client_secret
 * @property string $tenant_id
 *
 * @package App\Http\Requests\AccountService\Dms\MicrosoftSharepoint
 */
class AuthorizeRequest extends Request
{
    public function authorize(): bool
    {
        return $this->user() !== null && $this->user()->isManagerOfHostAccount();
    }

    public function rules(): array
    {
        return [
            'client_id' => 'required|string|min:3',
            'client_secret' => 'required|string|min:3',
            'tenant_id' => 'required|string|min:3',
        ];
    }

    public function attributes(): array
    {
        return [
            'client_id' => trans('service.microsoft_sharepoint_dms.fields.client_id'),
            'client_secret' => trans('service.microsoft_sharepoint_dms.fields.client_secret'),
            'tenant_id' => trans('service.microsoft_sharepoint_dms.fields.tenant_id'),
        ];
    }

    public function accountServiceId(): int
    {
        return $this->route('account_service_id');
    }

    public function configurations(): Configuration
    {
        return new Configuration(
            $this->client_id,
            $this->client_secret,
            $this->tenant_id
        );
    }
}
