<?php

namespace App\Http\Requests;

use App\Account;
use App\Http\TokenRequest;
use App\Token;
use App\User;

class NewEmailVerificationRequest extends TokenRequest
{
    protected $model_name = 'User';
    protected $action = 'emailChange';
    protected $token_type = 'emailChange';

    protected function setModelObject(): void
    {
        $this->model_object = User::query()
            ->where('account_id', Account::getHostAccount()->id)
            ->where('id', $this->route('token_model')->user_id)
            ->first();
    }

    public function authorize()
    {
        return !empty($this->getModelObject()) && !$this->model_object->isBlocked();
    }

    public function isTokenDataValid(Token $token): bool
    {
        return Account::getHostAccount()->users()->where('id', $token->user_id)->exists();
    }
}
