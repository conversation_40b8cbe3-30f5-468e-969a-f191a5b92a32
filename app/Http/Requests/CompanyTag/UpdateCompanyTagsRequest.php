<?php

namespace App\Http\Requests\CompanyTag;

use App\Company;
use App\Http\VueRequests\PostRequest;

class UpdateCompanyTagsRequest extends PostRequest
{
    public function authorize(): bool
    {
        return $this->user()->canManageCompanies();
    }

    public function company(): Company
    {
        return Company::findOrFail($this->company_id);
    }

    public function tags(): array
    {
        return array_map('trim', $this->tags);
    }

    public function rules(): array
    {
        return [
            'company_id' => 'required|int',
            'tags' => 'array|max:5',
            'tags.*' => 'min:1|max:20'
        ];
    }
}
