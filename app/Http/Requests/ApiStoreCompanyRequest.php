<?php

namespace App\Http\Requests;

use App\Http\VueRequests\GetRequest;
use App\Traits\Requests\AuthorizeSanctumApiRequest;

class ApiStoreCompanyRequest extends GetRequest
{
    use AuthorizeSanctumApiRequest;

    public function authorize(): bool
    {
        return (!empty($this->user()) && $this->user()->isManagerOfHostAccount() && $this->user()->isManager())
            || $this->sanctumApiAuthorize();
    }

    public function rules(): array
    {
        return [
            'name' => 'required|max:80',
            'address' => 'max:160',
            'kvk_number' => 'max:250',
            'internal_client_id' => 'max:80',
            'vat_number' => 'max:250',
            'fiscal_number' => 'max:250',
            'wage_tax_number' => 'max:250',
            'bsn_number' => 'max:250',
            'ocr_email' => 'email:filter,rfc|max:100',
            'ocr_email_purchase' => 'email:filter,rfc|max:100',
            'ocr_email_sale' => 'email:filter,rfc|max:100',
            'external_id' => 'string|max:100'
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => trans('validation.required', ['attribute' => 'name']),
            'name.max' => trans('validation.max.string', ['attribute' => 'name', 'max' => '80']),
            'kvk_number.max' => trans('validation.max.string', ['attribute' => 'kvk_number', 'max' => '80']),
            // phpcs:ignore
            'internal_client_id.max' => trans(
                'validation.max.string',
                ['attribute' => 'internal_client_id', 'max' => '80']
            ),
            // phpcs:ignore
            'vat_number.max' => trans('validation.max.string', ['attribute' => 'vat_number', 'max' => '250']),
            'fiscal_number.max' => trans('validation.max.string', ['attribute' => 'fiscal_number', 'max' => '250']),
            'wage_tax_number.max' => trans('validation.max.string', ['attribute' => 'wage_tax_number', 'max' => '250']),
            // phpcs:ignore
            'bsn_number.max' => trans('validation.max.string', ['attribute' => 'bsn_number', 'max' => '250']),
            // phpcs:ignore
            'ocr_email.email' => trans('validation.email', ['attribute' => 'ocr_email']),
            'ocr_email.max' => trans('validation.max.string', ['attribute' => 'ocr_email', 'max' => '100']),
            'ocr_email_purchase.email' => trans('validation.email', ['attribute' => 'ocr_email_purchase']),
            'ocr_email_purchase.max' => trans(
                'validation.max.string',
                ['attribute' => 'ocr_email_purchase', 'max' => '100']
            ),
            'ocr_email_sale.email' => trans('validation.email', ['attribute' => 'ocr_email_sale']),
            'ocr_email_sale.max' => trans('validation.max.string', ['attribute' => 'ocr_email_sale', 'max' => '100']),
            'external_id.max' => trans('validation.max.string', ['attribute' => 'external_id', 'max' => '100']),
        ];
    }
}
