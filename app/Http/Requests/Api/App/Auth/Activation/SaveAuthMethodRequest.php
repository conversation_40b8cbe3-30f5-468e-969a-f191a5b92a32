<?php

namespace App\Http\Requests\Api\App\Auth\Activation;

use App\Http\Requests\Token\AbstractTokenPostRequest;
use App\Token;

/**
 * @property string $auth_method
 * @property string $token
 */
class SaveAuthMethodRequest extends AbstractTokenPostRequest
{
    public function rules(): array
    {
        return [
            'token' => 'required',
            'auth_method' => 'required|in:totp,sms_otp,secret',
        ];
    }

    public function messages(): array
    {
        return [
            'token.required' => $this->getCommonErrorTranslation(),
            'auth_method.required' => trans('auth.activation.save_auth_method.required'),
            'auth_method.in' => $this->getCommonErrorTranslation(),
        ];
    }

    public function getTokenType(): string
    {
        return Token::TYPE_ACTIVATE_USER;
    }
}
