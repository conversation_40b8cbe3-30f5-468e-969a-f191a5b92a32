<?php

namespace App\Http\Requests\Api\App\Auth;

use App\Http\VueRequests\PostRequest;

/**
 * @property string $auth_id
 * @property string $auth_secret
 * @property string $scenario_key
 */
class LoginRequest extends PostRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'auth_id' => 'required',
            'auth_secret' => 'required',
            'scenario_key' => 'nullable|string'
        ];
    }

    public function messages(): array
    {
        return [
            'auth_id.required' => 'Username is required',
            'auth_secret.required' => 'Password is required'
        ];
    }
}
