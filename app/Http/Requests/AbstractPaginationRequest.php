<?php

namespace App\Http\Requests;

use App\Http\VueRequests\GetRequest;
use App\ValueObject\Pagination\PaginationFilters;

abstract class AbstractPaginationRequest extends GetRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'page' => 'required|integer|min:1',
        ];
    }

    /**
     * Returns the filters asked.
     *
     * @return PaginationFilters
     */
    abstract public function filters(): PaginationFilters;
}
