<?php

namespace App\Http\Requests;

use App\Http\VueRequests\GetRequest;
use App\Traits\Requests\AuthorizeSanctumApiRequest;

class ApiAccountUsersRequest extends GetRequest
{
    use AuthorizeSanctumApiRequest;

    public function authorize(): bool
    {
        return (!empty($this->user()) && $this->user()->isManagerOfOwnAccount())
            || $this->sanctumApiAuthorize();
    }

    public function rules(): array
    {
        return [
            'page' => 'numeric'
        ];
    }

    public function messages(): array
    {
        return [
            'page.numeric' => trans('validation.numeric', ['attribute' => 'page']),
        ];
    }
}
