<?php

namespace App\Http\Controllers\Api\V2;

use App\Builders\QueryBuilder\OpenQuestionConfig;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V2\ApiV2GetRequest;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use Illuminate\Pagination\LengthAwarePaginator;
use Spatie\QueryBuilder\QueryBuilder;

class OpenQuestionController extends Controller
{
    public function index(ApiV2GetRequest $request): LengthAwarePaginator|array
    {
        $query = OpenQuestion::query()
            ->whereIn(OpenQuestion::COMPANY_ID, $request->user()->activeCompanyIds());

        $qbuilder = QueryBuilder::for($query)
            ->select(OpenQuestionConfig::getSelect())
            ->join('companies', 'open_questions.company_id', '=', 'companies.id')
            ->allowedFields(OpenQuestionConfig::getAllowedColumns())
            ->allowedIncludes(OpenQuestionConfig::getAllowedRelations())
            ->allowedFilters(OpenQuestionConfig::getAllFilters())
            ->defaultSort(OpenQuestionConfig::getDefaultSort())
            ->allowedSorts(OpenQuestionConfig::getAllowedSorts());

        if ($request->has('count') && $request->count == 1) {
            return ['count' => $qbuilder->count()];
        }

        $qbuilder->withTotalAmount();

        if ($request->has('sort')) {
            $aggregate = OpenQuestionConfig::getAggregate(ltrim($request->get('sort'), '-'));
            if ($aggregate) {
                $qbuilder->withAggregate($aggregate[0], $aggregate[1]);
            }
        }

        return $qbuilder->paginate($request->limit)
            ->appends(request()->query());
    }
}
