<?php

namespace App\Http\Controllers\Api\V1;

use App\Helpers\ApiHelper;
use App\Http\ApiRequests\Templates\AddTemplateToCompanyRequest;
use App\Http\ApiRequests\Templates\GetTemplateRequest;
use App\Http\Controllers\Controller;
use App\Http\Resources\Template\ApiResource;
use App\Http\Responses\Response;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Repositories\CompanyRepository;
use App\Repositories\Services\AccountServiceRepository;
use App\Repositories\Templates\TemplateRepository;
use App\Service;
use App\Services\OpenQuestions\OpenQuestionService;
use App\Services\OpenQuestions\OpenQuestionTypeService;
use App\Services\OpenQuestions\Templates\TemplateEntryService;
use App\Services\OpenQuestions\Templates\TemplateFieldService;
use App\Services\OpenQuestions\Templates\TemplateService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class TemplateController extends Controller
{
    public function __construct(
        private readonly TemplateService $templateService,
        private readonly TemplateRepository $templateRepository,
        private readonly CompanyRepository $companyRepository,
        private readonly AccountServiceRepository $accountServiceRepository,
        private readonly OpenQuestionService $openQuestionService,
        private readonly TemplateEntryService $templateEntryService,
        private readonly OpenQuestionTypeService $openQuestionTypeService,
        private readonly TemplateFieldService $templateFieldService,
    ) {
    }

    /**
     * @OA\Get(
     *     path="/api/v1/templates",
     *     summary="Get list of templates.",
     *     tags={"Templates"},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     ),
     * )
     */
    public function getTemplates(GetTemplateRequest $request): AnonymousResourceCollection
    {
        $templates = $this->templateService->getByAccountId($request->account());
        return ApiResource::collection($templates);
    }

    /**
     * @OA\Post(
     *      path="/api/v1/templates/add_to_company",
     *      summary="Add a template to a company",
     *      tags={"Templates"},
     *      description="Add an template to a company.",
     *      @OA\RequestBody(
     *          @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  required={"company_id", "template_id"},
     *                  @OA\Property(
     *                      property="company_id",
     *                      type="integer",
     *                      example="123",
     *                      description="ID of an existing company"
     *                  ),
     *                  @OA\Property(
     *                      property="template_id",
     *                      type="integer",
     *                      example="123",
     *                      description="ID of an existing template"
     *                  )
     *              ),
     *              @OA\Examples(
     *                  example="example-request",
     *                  summary="Example request",
     *                  value={
     *                      "company_id": "123",
     *                      "template_id": "123"
     *                  }
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation"
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Company or Template not found",
     *          @OA\JsonContent(ref="#/components/schemas/ErrorModel"),
     *      ),
     *  )
     */
    public function addToCompany(AddTemplateToCompanyRequest $request): JsonResponse
    {
        try {
            $template = $this->templateRepository->getById($request->template_id);
            if ($template->account_id !== $request->account()->id) {
                throw new ModelNotFoundException('Could not find Template with ID #' . $request->template_id);
            }
        } catch (ModelNotFoundException) {
            return ApiHelper::errorResponse(
                ['Template #' . $request->template_id . ' not found'],
                Response::HTTP_NOT_FOUND
            );
        }

        try {
            $company = $this->companyRepository->getById($request->company_id);
            if ($company->account_id !== $request->account()->id) {
                throw new ModelNotFoundException('Could not find Company with ID #' . $request->company_id);
            }
        } catch (ModelNotFoundException) {
            return ApiHelper::errorResponse(
                ['Company #' . $request->company_id . ' not found'],
                Response::HTTP_NOT_FOUND
            );
        }

        $manualOpenQuestionService = $this->accountServiceRepository->firstEnabledByServiceName(
            $request->account(),
            Service::MANUAL_QUESTIONS_SERVICE,
        );

        if (is_null($manualOpenQuestionService)) {
            return ApiHelper::errorResponse(
                ['Manual Question Service is not installed'],
                Response::HTTP_BAD_REQUEST
            );
        }

        $type = $this->openQuestionTypeService->getTypeByKey(
            $manualOpenQuestionService->id,
            OpenQuestion::TYPE_TEMPLATE
        );

        try {
            \DB::beginTransaction();

            $openQuestion = $this->openQuestionService->createApiTemplateQuestion(
                $company,
                $request->genericAttributes($template, $company, $type),
                Service::MANUAL_QUESTIONS_SERVICE,
                $request->user(),
                $request->agentData()
            );

            $this->templateEntryService->createOld(
                $openQuestion,
                ['template_id' => $template->id],
                $template,
                $this->templateFieldService->getIdsByTemplate($template),
                $request->user()
            );

            \DB::commit();

            return ApiHelper::successResponse(['message' => 'Templated successfully added to company']);
        } catch (\Throwable $e) {
            \DB::rollBack();

            return ApiHelper::errorResponse(['message' => $e->getMessage()], Response::HTTP_BAD_REQUEST);
        }
    }
}
