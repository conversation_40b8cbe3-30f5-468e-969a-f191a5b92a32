<?php

namespace App\Http\Controllers\Api\Priv;

use App\Account;
use App\Exceptions\Api\ConflictException;
use App\Exceptions\Api\NotFoundException;
use App\Exceptions\Api\PreconditionFailedException;
use App\Exceptions\Api\UnauthorizedException;
use App\Helpers\MemoryHelper;
use App\Helpers\SharedFileAuditLogHelper;
use App\Http\ApiRequests\SharedFiles\SendRequest;
use App\Logging\Channels\ServiceLog;
use App\Service;
use App\Services\AccountServiceService;
use App\Services\FrontAuthService;
use App\Services\SharedFiles\SharedFileBundleService;
use App\Services\SharedFiles\SharedFileBundleUserService;
use App\Services\SharedFiles\SharedFileService;
use App\Services\UserService;
use App\User;
use DB;
use Illuminate\Support\Collection;
use Throwable;

readonly class SharedFilesController
{
    public function __construct(
        private SharedFileBundleService $sharedFileBundleService,
        private SharedFileService $sharedFileService,
        private UserService $userService,
        private FrontAuthService $frontAuthService,
        private SharedFileBundleUserService $sharedFileBundleUserService,
        private AccountServiceService $accountServiceService
    ) {
    }

    /**
     * @param SendRequest $request
     * @return array
     * @throws ConflictException
     * @throws NotFoundException
     * @throws PreconditionFailedException
     * @throws Throwable
     */
    public function send(SendRequest $request): array
    {
        // Because we allow a maximum of 10 files with 30MB each, if they are loaded in memory, 256MB is not enough
        MemoryHelper::setMemoryLimit('512M');
        $currentUser = $request->user();
        $account = $currentUser->account;
        $secureShareService = $this->accountServiceService->getByService(
            $account,
            $currentUser,
            Service::SECURE_SHARE_SERVICE
        );
        if ($secureShareService === null) {
            throw new PreconditionFailedException('SecureShare service is not installed in your account');
        }

        $usersFound = new Collection();
        if ($request->has('recipients')) {
            $usersFound = $usersFound->merge($this->processRecipients($account, $request->recipients));
        }
        if ($request->has('user_ids')) {
            $usersFound = $usersFound->merge($this->processUserIds($account, $request->user_ids));
        }

        try {
            DB::beginTransaction();
            $bundle = $this->sharedFileBundleService->store($account, $request->title, $currentUser);
            $this->sharedFileService->storeInBundle($bundle, $request->attachments);

            $result = [];
            foreach ($usersFound as $user) {
                $token = $this->frontAuthService->createForSharedBundle($user, $bundle);
                $this->sharedFileBundleUserService->store($bundle, $user);
                $result[] = [$user->email, $account->route('front.auth.start', ['token' => $token])];
            }

            SharedFileAuditLogHelper::linkCreated($bundle, $request->user());
            DB::commit();
            return $result;
        } catch (Throwable $e) {
            ServiceLog::error('Failed to create shared files bundle for user #' . $currentUser->id);
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * @param Account $account
     * @param array $recipients
     * @return Collection
     * @throws ConflictException
     * @throws NotFoundException
     * @throws PreconditionFailedException
     */
    private function processRecipients(Account $account, array $recipients): Collection
    {
        $usersFound = new Collection();
        $recipientsNotFound = [];

        foreach ($recipients as $recipient) {
            $users = $this->userService->getByEmail($account, $recipient, includeSecureShareRecipient: true);
            if ($users->count() > 1) {
                throw new ConflictException('Multiple users found with email ' . $recipient);
            }
            if ($users->isEmpty()) {
                $recipientsNotFound[] = $recipient;
                continue;
            }
            /** @var User $user */
            $user = $users->first();

            if ($user->isBlocked()) {
                throw new ConflictException('User with email ' . $recipient . ' is blocked');
            }
            $usersFound->push($user);
        }

        if (!empty($recipientsNotFound)) {
            throw new NotFoundException(
                'The following recipients were not found: ' . implode(', ', $recipientsNotFound)
            );
        }

        return $usersFound;
    }

    /**
     * @param Account $account
     * @param array $userIds
     * @return Collection
     * @throws ConflictException
     * @throws PreconditionFailedException
     * @throws UnauthorizedException
     */
    private function processUserIds(Account $account, array $userIds): Collection
    {
        $usersFound = new Collection();

        foreach ($userIds as $userId) {
            $user = $this->userService->getById($userId, $account);

            if ($user->isBlocked()) {
                throw new ConflictException('User with ID ' . $userId . ' is blocked');
            }

            $usersFound->push($user);
        }

        return $usersFound;
    }
}
