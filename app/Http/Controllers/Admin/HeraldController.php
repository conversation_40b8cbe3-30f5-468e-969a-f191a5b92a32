<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\VueRequests\System\HeraldFileEditRequest;
use App\Http\VueRequests\System\HeraldFileRequest;
use App\Http\VueRequests\System\HeraldRequest;
use App\Services\System\HeraldService;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;

class HeraldController extends Controller
{
    private HeraldService $heraldService;

    public function __construct(
        HeraldService $heraldService
    ) {
        $this->heraldService = $heraldService;
    }

    /**
     * @return Application|Factory|View
     */
    public function index(HeraldRequest $request)
    {
        return view('system.pages.herald.index', [
            'settings' => $this->heraldService->getSettingsFileProperties($request->account()),
            'files' => $this->heraldService->getTheMostRecentApplicationFiles($request->account())
        ]);
    }

    /**
     * @throws FileNotFoundException
     */
    public function edit(HeraldFileRequest $request)
    {
        return view('system.pages.herald.edit', [
            'filename' => $request->get('file'),
            'file' => $this->heraldService->getFile(
                $request->account(),
                $request->get('file')
            )
        ]);
    }

    /**
     * @param HeraldFileEditRequest $request
     * @return Application|RedirectResponse|Redirector
     */
    public function editFile(HeraldFileEditRequest $request)
    {
        $this->heraldService->editFile(
            $request->account(),
            $request->get('filename'),
            $request->get('file')
        );

        return redirect(route('system.herald'));
    }

    /**
     * @param HeraldFileEditRequest $request
     * @return Application|RedirectResponse|Redirector
     * @throws FileNotFoundException
     */
    public function commitFile(HeraldFileEditRequest $request)
    {
        $this->heraldService->commitFile(
            $request->account(),
            $request->get('filename'),
            $request->get('file')
        );

        return redirect(route('system.herald'));
    }
}
