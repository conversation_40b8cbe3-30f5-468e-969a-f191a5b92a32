<?php

namespace App\Http\Controllers\Admin\System;

use App\Http\Controllers\Controller;
use App\Http\VueRequests\System\Statistics\GetStatisticsRequest;
use App\Services\Statistics\StatisticsService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;

class StatisticsController extends Controller
{
    public function __construct(
        private readonly StatisticsService $statisticsService
    ) {
    }

    public function getStatistics(GetStatisticsRequest $request): Response|ResponseFactory
    {
        return $request->ok($this->statisticsService->getStatistics());
    }
}
