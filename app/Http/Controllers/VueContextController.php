<?php

namespace App\Http\Controllers;

use App\Account;
use App\Context;
use App\Event;
use App\Events\ContextSecuritySettingsChangedEvent;
use App\Exceptions\Context\CompaniesLockedException;
use App\Exceptions\Context\ContextNotFoundException;
use App\Exceptions\UnauthorizedException;
use App\Factories\Services\User\CsvExportFactory;
use App\Helpers\Http\CsvExportHelper;
use App\Http\Requests\User\UserPaginationRequest;
use App\Http\Resources\Account\AvailableUsersResource;
use App\Http\Resources\Account\SettingsSecurityResource;
use App\Http\Resources\Company\CompanyResource;
use App\Http\Resources\Context\AvailableCompanyResource;
use App\Http\Resources\Context\AvailableParentResource;
use App\Http\Resources\Context\EventResource;
use App\Http\Resources\Context\IndexResource;
use App\Http\Resources\ContextWidgetSettingsResource;
use App\Http\Resources\Member\IndexResource as UserIndexResource;
use App\Http\VueRequests\Context\DeleteRequest;
use App\Http\VueRequests\Context\ExportCsvRequest;
use App\Http\VueRequests\Context\ExportUsersRequest;
use App\Http\VueRequests\Context\GetAvailableParentsRequest;
use App\Http\VueRequests\Context\GetCompaniesRequest;
use App\Http\VueRequests\Context\IndexContextAccessWidgetsRequest;
use App\Http\VueRequests\Context\IndexContextMembershipsRequest;
use App\Http\VueRequests\Context\IndexMembersRequest;
use App\Http\VueRequests\Context\RelationInfoRequest;
use App\Http\VueRequests\Context\IndexNotMembersRequest;
use App\Http\VueRequests\Context\IndexRequest;
use App\Http\VueRequests\Context\IndexSecurityEventsRequest;
use App\Http\VueRequests\Context\IndexSecuritySettingsRequest;
use App\Http\VueRequests\Context\IndexWidgetsRequest;
use App\Http\VueRequests\Context\StoreRequest;
use App\Http\VueRequests\Context\SyncCompaniesRequest;
use App\Http\VueRequests\Context\UpdateRequest;
use App\Repositories\ContextRepository;
use App\Repositories\UserRepository;
use App\Services\ContextCompanyService;
use App\Services\ContextService;
use App\User;
use Auth;
use App\Support\Carbon;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Log;
use Throwable;

class VueContextController extends Controller
{
    public function __construct(
        private readonly ContextRepository $contextRepository,
        private readonly UserRepository $userRepository,
        private readonly ContextCompanyService $contextCompanyService,
        private readonly ContextService $contextService
    ) {
    }

    public function getParentList(Context $context)
    {
        $parents = [];
        $parent = $context;
        do {
            $parents[] = [
                'id' => $parent->id,
                'url' => '/manage/groups/' . $parent->id . '/overview',
                'title' => $parent->name
            ];
        } while ($parent = $parent->parent()->first());

        array_pop($parents); //remove last item, is account and should not be displayed.

        return array_reverse($parents);
    }

    /**
     * Get JSON with all widgets in this group
     * @param Context $context
     * @param IndexWidgetsRequest $request
     * @return array
     */
    public function widgets(Context $context, IndexWidgetsRequest $request)
    {
        $widgets = $context->access_widgets()->withAlotOfChildren()->get()->sortByLabel();

        //add communication widgets to the end of the list.
        $tools = $context->communication_widgets()->get()->sortByLabel();

        return [
            'parents' => $this->getParentList($context),
            'widgets' => ContextWidgetSettingsResource::collection($widgets->merge($tools))
        ];
    }

    public function index(IndexRequest $request)
    {
        $query = Context::onlySub();

        if (!$request->user()->isAdminUser()) {
            $query->whereManagedBy(Auth::user(), true);
        }

        $contexts = $query->where(Context::ACCOUNT_ID, Account::getHostAccount()->id)
            ->orderBy('level')
            ->orderBy('name')
            ->get(['id', 'name', 'path']);

        return IndexResource::collection($contexts);
    }

    public function events(Context $context, IndexSecurityEventsRequest $request)
    {
        $start = $request->input('start', 0);
        $count = $request->input('count', 0);

        $event = new Event();
        $event->setClientDatabaseConnection($request->account()->id);

        $events = $event->where('created_at', '>', Carbon::now()->subMonth()->toDateTimeString());

        $events = $events->whereIn(
            'action',
            [
                'startUserWidget',
                'login',
                'logout',
                'loginAttempt',
                'userSecurityChanged',
                'userActivated',
                'userActivationRequested',
                'userReactivationRequested',
                'contextCreated',
                'contextDeleted',
                'contextWidgetCreated',
                'contextWidgetDeleted',
                'contextWidgetPropertiesChanged',
                'contextSecuritySettingsChanged',
                'membershipCreated',
                'membershipDeleted',
                'membershipChanged',
                'identityAttributeDeleted',
                'authenticationRecoveryRequested',
                'userBlocked',
                'userUnblocked',
                'userSecretChanged',
                'startedStaticLink',
                'staticLinkError'
            ]
        );

        $events = $events->relatedForContext($context);
        $events = $events->orderBy('created_at', 'desc');

        $total_count = $events->count();
        $events = $events->skip($start)->take(!empty($count) ? $count : $total_count);
        $events = $events->get();

        return EventResource::collection($events);
    }

    public function membersWithPagination(Context $context, UserPaginationRequest $request): AnonymousResourceCollection
    {
        if ($context->isManagedBy($request->user())) {
            $users = $this->userRepository->getContextUsers($context, $request->filters());
            return UserIndexResource::collection($users);
        }
        throw new UnauthorizedException('Group is not managed by user');
    }

    public function relationInfo(Context $context, RelationInfoRequest $request): array
    {
        $managers = $context->managers()->orderBy('firstname')->orderBy('lastname')->get();
        return [
            'id' => $context->id,
            'path' => $context->path,
            'name' => $context->name,
            'auth_methods' => $context->auth_methods,
            'parents' => $this->getParentList($context),
            'member_count' => $context->users()->count(),
            'managers' => UserIndexResource::collection($managers)
        ];
    }

    public function members(Context $context, IndexMembersRequest $request): AnonymousResourceCollection
    {
        $users = $context->users()->orderBy('firstname')->orderBy('lastname')->get();
        return UserIndexResource::collection($users);
    }

    /**
     * Export users as CSV
     * @param \App\Context $context
     * @param \App\Http\VueRequests\Context\ExportUsersRequest $request
     * @return \Illuminate\Http\Response
     */
    public function exportUsers(Context $context, ExportUsersRequest $request): Response
    {
        $users = CsvExportFactory::createFromCollection(
            $context->users()
                ->orderBy(User::FIRST_NAME)
                ->orderBy(User::LAST_NAME)
                ->get()
        );

        return CsvExportHelper::respondCsv($users, $request->path());
    }

    public function settings(Context $context, IndexSecuritySettingsRequest $request)
    {
        return SettingsSecurityResource::make($context);
    }

    public function store(StoreRequest $request)
    {
        $attributes = [
            Context::PARENT_ID => $request->input('parent_id'),
            Context::NAME => trim($request->input('name')),
            Context::IS_EXTERNAL => (int)$request->input('isExternal'),
        ];

        $securityInfo = [];

        if (!$request->input('securityInfo.inheritSecuritySettings')) {
            $securityInfo = [
                'maxLifeTime' => $request->input('securityInfo.maxLifeTime'),
                'applyLifeTimeUsers' => $request->input('securityInfo.applyLifeTimeUsers'),
                'authMethods' => $request->input('securityInfo.authMethods'),
                'passwordValidity' => $request->input('securityInfo.passwordValidity'),
                'ip_whitelist_enabled' => $request->input('ip_whitelist_enabled'),
                'singleSessionEnforced' => $request->input('securityInfo.singleSessionEnforced')
            ];
        }

        $context = $this->contextRepository->create($attributes, $securityInfo);

        return $request->success(IndexResource::make($context));
    }

    public function update(Context $context, UpdateRequest $request)
    {
        if ($request->filled('name')) {
            $context->name = trim($request->input('name'));
        }

        if (
            !$context->isMain() && $request->has('changeParentGroup') &&
            $request->input('changeParentGroup')
        ) {
            $context->parent_id = $request->input('parent_id', $context->parent_id);
        }

        if (Account::getHostAccount()->isAdminAccount()) {
            $context->is_external = (int)$request->input('isExternal');
        }

        $context->save();

        if ($request->input('inheritSecuritySettings')) {
            $context->session_lifetime_max = null;
            $context->session_lifetime_default = null;
            $context->auth_secret_expire_days = null;
            $context->auth_methods = null;
            $context->ip_whitelist_mode = null;
            $context->ip_whitelist = null;
            $context->single_session_enforced = null;
        } else {
            $context->session_lifetime_max = $request->input('maxLifeTime') * 60;

            if ($request->input('applyLifeTimeUsers')) {
                $context->session_lifetime_default = $context->session_lifetime_max;
            } else {
                $context->session_lifetime_default = null;
            }

            $auth_methods = [];
            foreach ($request->input('authMethods') as $auth_method) {
                $auth_methods[] = $auth_method;
            }

            $context->auth_methods = implode(",", $auth_methods);
            $context->auth_secret_expire_days = $request->input('passwordValidity');

            if ($request->input('enableIpWhitelist') && $context->account->hasLicense('ip_whitelist')) {
                if ($request->filled('ipWhitelistingMode') && !empty($request->input('ipWhitelistingMode'))) {
                    $context->ip_whitelist_mode = $request->input('ipWhitelistingMode');
                } else {
                    $context->ip_whitelist_mode = 'standard';
                }

                if (!$context->ip_whitelist_standard) {
                    $context->ip_whitelist = $request->input('ipWhitelist');
                } else {
                    $context->ip_whitelist = null;
                }
            } else {
                $context->ip_whitelist_mode = 'disabled';
                $context->ip_whitelist = "";
            }

            $context->single_session_enforced = $request->input('singleSessionEnforced');
        }

        $context->save();

        ContextSecuritySettingsChangedEvent::fire($context);
        Log::info('User #' . auth()->user()->id . 'updated group #' . $context->id);

        return $request->success(null, 'context.update.succeed');
    }

    public function delete(Context $context, DeleteRequest $request)
    {
        $context->delete();
        return $request->success(null, 'context.delete.succeed');
    }

    public function notMembers(Context $context, IndexNotMembersRequest $request)
    {
        $account = $context->account;
        return UserIndexResource::collection(
            User::notHasMembership($context)->withinAccount($account->id)->whereManagedByMe(false)->with(
                'account'
            )->get()
        );
    }

    /**
     * This is a stripped down copy of the old UI method and should be updated for the new UI.
     * TODO: SL-3070
     */
    public function exportCsv(ExportCsvRequest $request)
    {
        $user = Auth::user();
        $contexts = Context::whereManagedBy($user, false)->get();

        $items = [];
        foreach ($contexts as $sub_context) {
            $items[] = $sub_context->toDataArray(false);
        }

        return CsvExportHelper::respondCsv($items, 'groups');
    }

    /**
     * Get companies from context id
     * @param Context $context
     * @param GetCompaniesRequest $request
     * @return \Illuminate\Http\Response
     */
    public function companies(Context $context, GetCompaniesRequest $request): Response
    {
        return $request->ok(
            CompanyResource::collection(
                $this->contextCompanyService->getCompaniesByContextId(
                    $context->id,
                    $request->getLimit()
                )
            )
        );
    }

    /**
     * Get available companies by context
     *
     * @param Context $context
     * @param GetCompaniesRequest $request
     * @return AnonymousResourceCollection
     */
    public function availableCompanies(Context $context, GetCompaniesRequest $request): AnonymousResourceCollection
    {
        $companies = $this->contextCompanyService->getAvailableCompaniesByContextId($request->user(), $context->id);
        return AvailableCompanyResource::collection($companies);
    }

    /**
     * Sync companies by context
     *
     * @param SyncCompaniesRequest $request
     * @return ResponseFactory|Response
     */
    public function syncCompanies(SyncCompaniesRequest $request): Response|ResponseFactory
    {
        try {
            $context = $this->contextService->getForAccount($request->account(), $request->context_id, ['users']);

            // The function returns false if it's on a job, and true if it's done directly.
            if (
                !$this->contextCompanyService->syncCompaniesWithContext(
                    $context,
                    $request->company_ids,
                    canQueueJob: true
                )
            ) {
                return $request->success(
                    data: ['properties' => $context->properties],
                    key: 'context.sync_companies.success_with_job'
                );
            }
            return $request->success(
                data: ['properties' => $context->properties],
                key: 'context.sync_companies.success'
            );
        } catch (CompaniesLockedException $exception) {
            // Show this error if it is thrown.
            throw $exception;
        } catch (Throwable $exception) {
            Log::error(
                'Could not update connected companies. Error message: ' . $exception->getMessage(
                ) . ' on file ' . $exception->getFile() . ' on line ' . $exception->getLine()
            ); // phpcs:ignore
            return $request->error(key: 'context.sync_companies.failed');
        }
    }

    /**
     * Get all contexts that the current context can be set as a child.
     * @param GetAvailableParentsRequest $request
     * @return Response|ResponseFactory
     * @throws ContextNotFoundException
     */
    public function availableParentsForContext(GetAvailableParentsRequest $request): Response|ResponseFactory
    {
        $contexts = $this->contextService->getAvailableParents(
            $request->account(),
            $request->user(),
            $request->context_id
        );
        return $request->ok(AvailableParentResource::collection($contexts));
    }

    /**
     * Get all contexts that can be the parent of a new context in this account.
     * @param GetAvailableParentsRequest $request
     * @return Response|ResponseFactory
     */
    public function availableParentsForNew(GetAvailableParentsRequest $request): Response|ResponseFactory
    {
        $contexts = $this->contextService->getByAccount(
            $request->account(),
        );
        return $request->ok(AvailableParentResource::collection($contexts));
    }

    public function indexMemberships(Context $context, IndexContextMembershipsRequest $request)
    {
        $search_phrase = $request->search_phrase;
        $filter = $request->filter;
        $start = $request->start;
        $count = $request->count;
        $output = $request->input('output', 'json');

        $query = $context->memberships();

        if (!empty($filter)) {
            switch ($filter) {
                case "status-active":
                    $query = $query->whereUserStatus('active');
                    break;
                case "status-new":
                    $query = $query->whereUserStatus('new');
                    break;
                case "status-blocked":
                    $query = $query->whereUserStatus('blocked');
                    break;
                case "role-manager":
                    $query = $query->whereManager();
                    break;
                case "role-member":
                    $query = $query->whereMember();
                    break;
            }
        }

        if (!empty($search_phrase)) {
            $query = $query->where('user_label', "LIKE", "%$search_phrase%");
        }

        $total_count = $query->count();

        if (!empty($count)) {
            $query = $query->take($count)->skip($start);
        }

        $memberships = $query->orderBy('user_label')->get();

        $items = [];
        foreach ($memberships as $membership) {
            $items[] = $membership->toDataArray($output == 'json');
        }

        if ($request->get('output') === 'csv') {
            return CsvExportHelper::respondCsv($items, $request->path());
        }

        return response()->json([
            'start' => (int)$this->input('start', 1),
            'count' => count($items),
            'items' => $items,
            'total' => (int)$total_count,
        ]);
    }

    public function indexAccessWidgets(Context $context, IndexContextAccessWidgetsRequest $request)
    {
        $search_phrase = $request->search_phrase;
        $start = (int)$request->start;
        $count = (int)$request->count;
        $output = $request->input('output', 'json');
        $access_widgets = $context->access_widgets()->withAlotOfChildren();
        if (empty($search_phrase)) {
            $total_count = $access_widgets->count();
            if (empty($count)) {
                $count = $total_count;
            }
            $access_widgets = $access_widgets->skip($start)->take($count)->get();
        } else {
            $access_widgets = $access_widgets->get()->searchModel($search_phrase)->slice($start, $count);
            $total_count = $access_widgets->count();
        }
        $items = [];
        foreach ($access_widgets->sortByLabel() as $access_widget) {
            $items[] = $access_widget->toDataArray($output == 'json');
        }

        if ($request->get('output') === 'csv') {
            return CsvExportHelper::respondCsv($items, $request->path());
        }

        return response()->json([
            'start' => (int)$this->input('start', 1),
            'count' => count($items),
            'items' => $items,
            'total' => (int)$total_count,
        ]);
    }

    public function availableUsers(UserPaginationRequest $request): AnonymousResourceCollection
    {
        $context = $this->contextService->getForUser($request->context_id, $request->user());
        $users = $this->contextService->getAvailableUsersForContext($context, $request->filters());
        return AvailableUsersResource::collection($users);
    }
}
