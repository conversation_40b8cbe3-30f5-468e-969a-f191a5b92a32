<?php

namespace App\Http\Controllers;

use App\Account;
use App\Auth\SessionKey;
use App\Http\Resources\Context\ManagerResource;
use App\Repositories\ConsumerRepository;
use App\User;
use Auth;
use Cookie;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\View\View;
use Session;

class PageController extends Controller
{
    public function base()
    {
        return redirect(route('app.any', 'start'), 307);
    }

    /**
     *
     * @param $request
     * @return Redirector|Application|RedirectResponse
     */
    public function redirectWithoutNew(Request $request): Redirector|Application|RedirectResponse
    {
        // Get current url
        $url = $request->getRequestUri();
        // 'reflash' current request parameters/data (meaning it will pass along everything)
        $request->session()->reflash();
        // Redirect to route without /new (4 characters)
        return redirect(substr($url, 4));
    }

    public function app()
    {
        $user = Auth::user();
        self::postLoginActions($user);
        $redirect = self::checkDestination($user);
        if (!empty($redirect)) {
            return $redirect;
        }
        return view('app', ['user' => Auth::user()]);
    }

    public function vueTemplate()
    {
        return view('vue.template', ['account' => Account::getHostAccount(), 'user' => auth()->user()]);
    }

    public static function postLoginActions(User $user)
    {
        if (Auth::loadRememberMeMasterkey()) {
            \Log::channel('redirect')->info(
                request()->url() . 'PageController loadRememberMeMasterkey. No redirect. ' . request()->getHost()
            );
        }

        if (Session::has(SessionKey::MASTERKEY) && empty($user->masterkey_mac)) {
            $user->setMasterkeyMac();
            $user->save();
        }

        if (Session::has(SessionKey::MASTERKEY)) {
            foreach ($user->userWidgets as $user_widget) {
                try {
                    $user_widget->harmonizeProtectedValues();
                    $user_widget->handleDeprecation();
                } catch (\Throwable $e) {
                    \Log::error(
                        'Unable to harmonize or handle deprecated widget for user widget #' . $user_widget->id . ' with exception: ' . $e::class . ' ' . $e->getMessage() // phpcs:ignore
                    );
                }
            }
        }
    }

    public static function checkDestination(User $user): null|RedirectResponse|View
    {
        $issuer = null;

        if ($user->account->isExpired() && !$user->isAdminUser()) {
            if ($user->isManagerOfHostAccount()) {
                return view(
                    'vue.ambrosio',
                    [
                        'user' => $user,
                        'account' => $user->account,
                        'component' => 'ContractView',
                        'data' => [],
                    ]
                );
            }

            return view(
                'vue.ambrosio',
                [
                    'user' => $user,
                    'account' => $user->account,
                    'component' => 'TrialExpiredView',
                    'data' => [
                        'managers' => ManagerResource::collection($user->account->managers())
                    ],
                ]
            );
        }

        if (!Session::has(SessionKey::MASTERKEY)) {
            \Log::critical(
                "Masterkey not loaded for user " . $user->showLabel(false, false, ['id', 'auth_id'])
            ); //phpcs:ignore
            return redirect(route('getLogin'))->with('failed_msg', trans('common.unknown_error_no_feedback'));
        }

        if (Session::has(SessionKey::IDENTITY_ISSUER)) {
            $consumerRepository = resolve(ConsumerRepository::class);
            $issuer = $consumerRepository->findByAuthIdAndAccount(
                Account::getHostAccount(),
                Session::get(SessionKey::IDENTITY_ISSUER)
            );
        }

        if ($user->shouldSetupAuth() && (!isset($issuer) || $issuer->mfa)) {
            \Log::channel('redirect')->info(
                request()->url() . ' Pagecontroller. Redirect to initSetup ' . request()->getHost()
            );
            return redirect(route('initSetup'), 303);
        }

        if (!empty($auto_start_widget = $user->getAutoStartWidget())) {
            \Log::channel('redirect')->info(
                request()->url() . ' Pagecontroller. Redirect to startUserWidget ' . request()->getHost()
            );
            return redirect(route('startUserWidget', ['user_widget' => $auto_start_widget->id]), 303);
        }

        //used by static link
        if (Cookie::has('post_auth_redirect')) {
            $url = Cookie::get('post_auth_redirect');
            \Log::channel('redirect')->info('Pagecontroller cookie redirect to ' . $url . ' ' . request()->getHost());
            return redirect($url, 303)->withCookie(Cookie::forget('post_auth_redirect'));
        }

        return null;
    }
}
