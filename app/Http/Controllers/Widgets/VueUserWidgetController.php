<?php

namespace App\Http\Controllers\Widgets;

use App\Account;
use App\Events\UserWidgetPropertiesChangedEvent;
use App\Helpers\StaticLinkHelper;
use App\Helpers\TotpHelper;
use App\Helpers\WidgetSortHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\CalcUserWidgetTotpRequest;
use App\Http\Requests\ShowSecureloginSafeRequest;
use App\Http\Resources\Member\GroupResource;
use App\Http\Resources\SecureloginSafeResource;
use App\Http\Resources\UserWidget\UserWidgetResource;
use App\Http\Resources\UserWidget\UserWidgetSetupResource;
use App\Http\VueRequests\UserWidget\DeleteRequest;
use App\Http\VueRequests\UserWidget\DragUserWidgetRequest;
use App\Http\VueRequests\UserWidget\GetSettingsRequest;
use App\Http\VueRequests\UserWidget\StoreMemberRequest;
use App\Http\VueRequests\UserWidget\StoreRequest;
use App\Http\VueRequests\UserWidget\UpdateRequest;
use App\Repositories\UserWidgetRepository;
use App\Services\WidgetService;
use App\Support\Database\Builder;
use App\User;
use App\UserWidget;
use App\Widget;
use Auth;

class VueUserWidgetController extends Controller
{
    private UserWidgetRepository $userWidgetRepository;
    private WidgetService $widgetService;

    public function __construct(UserWidgetRepository $userWidgetRepository, WidgetService $widgetService)
    {
        $this->userWidgetRepository = $userWidgetRepository;
        $this->widgetService = $widgetService;
    }

    public function store(StoreRequest $request)
    {
        //cache disabled for user widget limit
        Builder::$cacheEnabled = false;
        $result = true;
        $parent_ids = $request->input('parent_ids', []);

        $stored_user_widgets = collect();

        foreach ($parent_ids as $parent_id) {
            $user_widget = new UserWidget();

            $user_widget->user_id = \Auth::user()->id;
            $user_widget->parent_id = $parent_id;

            $result &= $user_widget->save();
            $stored_user_widgets->add($user_widget);
        }

        if (!$result) {
            return $request->error();
        }

        Builder::$cacheEnabled = true;
        return $request->success(['widgets' => UserWidgetResource::collection($stored_user_widgets)]);
    }

    public function storeMemberWidget(StoreMemberRequest $request, User $user)
    {
        //cache disabled for user widget limit
        Builder::$cacheEnabled = false;
        $result = true;
        $parent_ids = $request->input('parent_ids', []);

        $stored_user_widgets = collect();

        foreach ($parent_ids as $parent_id) {
            $user_widget = new UserWidget();

            $user_widget->user_id = $user->id;
            $user_widget->parent_id = $parent_id;

            $result &= $user_widget->save();
            $stored_user_widgets->add($user_widget);
        }

        if (!$result) {
            return $request->error();
        }

        Builder::$cacheEnabled = true;
        return $request->success(['widgets' => UserWidgetResource::collection($stored_user_widgets)]);
    }

    public function groups(Widget $widget)
    {
        return GroupResource::collection($widget->contexts);
    }

    public function update(UserWidget $user_widget, UpdateRequest $request)
    {
        if ($request->has('status') && $user_widget->user->isManagedBy(Auth::user())) {
            if ($request->input('status') != 'default') {
                $user_widget->status = $request->input('status');
            } else {
                $user_widget->status = null;
            }
        }

        $user_widget->settingsSetValuesFromRequest($request, "user", true);

        if ($user_widget->isDirty()) {
            $user_widget->save();
        }

        if ($user_widget->isOneOrMoreAttributesChangedSincePrevious(['properties'])) {
            UserWidgetPropertiesChangedEvent::fire($user_widget);
        }

        return $request->success(UserWidgetSetupResource::make($user_widget));
    }

    public function delete(DeleteRequest $request)
    {
        $user_widgets = $request->input('user_widget_ids', []);

        $result = true;
        foreach ($user_widgets as $user_widget) {
            $user_widget_model = UserWidget::findOrFail($user_widget);
            $result &= $user_widget_model->delete();
        }

        if (!$result) {
            return $request->error();
        }

        return $request->success();
    }

    public function drag(DragUserWidgetRequest $request)
    {
        $user_widget = UserWidget::findOrFail($request->user_widget_id);
        $target_order = $request->input('target_order');

        $user_widgets = $user_widget->user->userWidgets;

        $sorted = WidgetSortHelper::sort($user_widgets, $request->user_widget_id, $target_order);
        foreach ($sorted as $sort) {
            $sort->save();
        }

        return $request->ok();
    }

    public function secureloginSafe(UserWidget $userWidget, ShowSecureloginSafeRequest $request)
    {
        return SecureloginSafeResource::make($userWidget);
    }

    public function externalSetup(string $key)
    {
        if (!auth()->check()) {
            $account = Account::getHostAccount();
            $account_widget = StaticLinkHelper::getAccountWidgetWithKey($key, $account);
            $this->storeUrlInCookie($account_widget->getSetupUrl());
        }

        $user_widget = $this->userWidgetRepository->getById($key);

        return view(
            'vue.template',
            [
                'account' => Account::getHostAccount(),
                'component' => 'WidgetSetup',
                'data' => [
                    'widget' => UserWidgetSetupResource::make($user_widget)
                ],
                'window_size' => 'medium',
            ]
        );
    }

    public function settings(GetSettingsRequest $request, UserWidget $userWidget): array
    {
        return $this->widgetService->getWidgetSettings($userWidget, 'user');
    }

    /**
     * Return a list of TOTP codes that are each valid for 30 seconds.
     * @param CalcUserWidgetTotpRequest $request
     * @param UserWidget $user_widget
     */
    public function calcTotp(CalcUserWidgetTotpRequest $request, UserWidget $user_widget)
    {
        $user_widget->initAttributeCache('properties');
        $user_widget->settingsDecryptProtectedValues();
        $timestamp = null;
        $totp_secret = $user_widget->getAttributeRecursive('properties[totp_secret]');

        if (empty($totp_secret)) {
            return [];
        }

        if (ctype_digit($request->input('timestamp'))) {
            $timestamp = (int)$request->input('timestamp');
        }

        $response = response(TotpHelper::generate($totp_secret, true, $timestamp, 10));
        $response->header('X-Server-Time', time());
        return $response;
    }
}
