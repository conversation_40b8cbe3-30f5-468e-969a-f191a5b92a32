<?php

namespace App\Http\Controllers;

use App\Helpers\Http\CsvExportHelper;
use App\Http\Requests\Export\DownloadRequest;
use App\Http\Requests\Export\GetRequest;
use App\Http\Requests\Export\StartRequest;
use App\Http\Resources\Export\IndexResource;
use App\Repositories\FileSystem\EncryptedStorageRepository;
use App\Services\ExportService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;

class ExportController extends Controller
{
    public function __construct(
        private readonly ExportService $exportService,
        private readonly EncryptedStorageRepository $encryptedStorageRepository,
    ) {
    }

    public function index(GetRequest $request): Response|ResponseFactory
    {
        $exports = $this->exportService->get($request->account());

        return $request->ok(IndexResource::collection($exports));
    }

    public function download(DownloadRequest $request): Response
    {
        $export = $this->exportService->getByUuid($request->account(), $request->getUuid());
        $csv = $this->encryptedStorageRepository->getFile($export->file_path);

        return CsvExportHelper::respondCsv($csv, $export->getName());
    }

    public function start(StartRequest $request): Response|ResponseFactory
    {
        $export = $this->exportService->export(
            $request->account(),
            $request->user(),
            $request->type,
            $request->getFilters()
        );
        return $request->success($export, message: trans('exports.start_export.succeed.message'));
    }
}
