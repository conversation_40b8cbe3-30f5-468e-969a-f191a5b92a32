<?php

namespace App\Http\Controllers\Front\OpenQuestions;

use App\Exceptions\Template\TemplateEntryIncompleteException;
use App\Http\Controllers\Controller;
use App\Http\Resources\OpenQuestions\Template\ClientResource;
use App\Http\Resources\OpenQuestions\Template\TemplateAnswerResource;
use App\Http\VueRequests\OpenQuestions\Front\DeleteTemplateRequest;
use App\Http\VueRequests\OpenQuestions\Front\SaveTemplateRequest;
use App\Logging\Channels\ServiceLog;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Services\OpenQuestions\OpenQuestionService;
use App\Services\OpenQuestions\Templates\TemplateAnswerAttachmentService;
use App\Services\OpenQuestions\Templates\TemplateEntryService;
use DB;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;
use Throwable;

class TemplateController extends Controller
{
    private OpenQuestionService $openQuestionService;
    private TemplateEntryService $templateEntryService;
    private TemplateAnswerAttachmentService $templateAnswerAttachmentService;

    public function __construct(
        OpenQuestionService $openQuestionService,
        TemplateEntryService $templateEntryService,
        TemplateAnswerAttachmentService $templateAnswerAttachmentService
    ) {
        $this->openQuestionService = $openQuestionService;
        $this->templateEntryService = $templateEntryService;
        $this->templateAnswerAttachmentService = $templateAnswerAttachmentService;
    }

    /**
     * @throws Throwable
     */
    public function saveTemplateEntry(SaveTemplateRequest $request): Response|Application|ResponseFactory
    {
        DB::beginTransaction();
        $templateEntry = $this->templateEntryService->getById($request->template_entry_id, $request->user());

        try {
            if ($templateEntry->openQuestion->status !== OpenQuestion::STATUS_OPEN) {
                return $request->error(null, 'template.save_template_entry.error_not_open');
            }

            if (!empty($request->questions)) {
                $this->templateEntryService->saveChanges(
                    $request->user(),
                    $request->account(),
                    $templateEntry,
                    $request->questions,
                    $request->files,
                    $request->agentData()
                );
            }

            $templateEntry->refresh();

            DB::commit();
            return $request->success(ClientResource::make($templateEntry), 'template.save_template_entry.succeed');
        } catch (Throwable $e) {
            ServiceLog::error(
                'Could not save template entry #' . $templateEntry->id . ': ' . $e::class . ' - ' . $e->getMessage()
            ); //phpcs:ignore
            DB::rollBack();
            return $request->error(key: 'template.save_template_entry.error');
        }
    }

    /**
     * @throws Throwable
     */
    public function submitTemplateEntry(SaveTemplateRequest $request): Response|Application|ResponseFactory
    {
        DB::beginTransaction();
        $templateEntry = $this->templateEntryService->getById($request->template_entry_id, $request->user());

        try {
            if ($templateEntry->openQuestion->status !== OpenQuestion::STATUS_OPEN) {
                return $request->error(null, 'template.submit_template_entry.error_not_open');
            }

            if (!empty($request->questions)) {
                $this->templateEntryService->saveChanges(
                    $request->user(),
                    $request->account(),
                    $templateEntry,
                    $request->questions,
                    $request->files,
                    $request->agentData()
                );
            }

            $this->templateEntryService->checkCompletion($templateEntry);
            $this->templateEntryService->saveSubmitAuditLog($request->user(), $templateEntry, $request->agentData());
            $this->openQuestionService->setToPending($templateEntry->openQuestion);
            $templateEntry->refresh();

            DB::commit();
            return $request->success(ClientResource::make($templateEntry), 'template.submit_template_entry.succeed');
        } catch (TemplateEntryIncompleteException $e) {
            // We shouldn't catch this exception.
            throw $e;
        } catch (Throwable $e) {
            ServiceLog::error(
                'Could not submit template entry #' . $templateEntry->id . ': ' . $e::class . ' - ' . $e->getMessage()
            ); //phpcs:ignore
            DB::rollBack();
            return $request->error(key: 'template.submit_template_entry.error');
        }
    }

    public function deleteTemplateAttachment(DeleteTemplateRequest $request): Response|ResponseFactory
    {
        $templateAttachment = $this->templateAnswerAttachmentService->getById(
            $request->template_attachment_id,
            $request->user()
        );

        $templateAnswer = $templateAttachment->templateAnswer;

        $this->templateAnswerAttachmentService->delete(
            $request->user(),
            $templateAttachment,
            $request->agentData()
        );

        // Refresh the answer after the file is deleted.
        $templateAnswer->refresh();
        $templateAnswer->templateEntry->updateTemplateProgress();

        return $request->success(
            TemplateAnswerResource::make($templateAnswer),
            'template.delete_template_attachment.succeed'
        );
    }
}
