<?php

namespace App\Http\Controllers\Service;

use App\AccountService;
use App\Http\Controllers\Controller;
use App\Http\Requests\OAuth\OAuthCallbackRequest;
use App\Http\VueRequests\AccountService\ConfigureRequest;
use App\Interfaces\Services\OAuthServiceProvider;
use App\Logging\Channels\ServiceLog;
use App\Repositories\Services\AccountServiceRepository;
use Illuminate\Http\RedirectResponse;
use Throwable;

class OAuthServiceController extends Controller
{
    public function __construct(private readonly AccountServiceRepository $accountServiceRepository)
    {
    }

    public function configure(ConfigureRequest $request, AccountService $accountService)
    {
        /** @var OAuthServiceProvider $provider */
        $provider = $accountService->getProvider();

        return $request->ok($provider->getAuthorizationUrl());
    }

    /**
     * @param OAuthCallbackRequest $request
     * @return RedirectResponse
     * @throws Throwable
     */
    public function callback(OAuthCallbackRequest $request): RedirectResponse
    {
        $accountService = $this->accountServiceRepository->getById($request->state);
        /** @var OAuthServiceProvider $provider */
        $provider = $accountService->getProvider();

        try {
            $provider->callback($request->code);
            //phpcs:ignore
            return redirect('https://' . $accountService->account->hostname . '/manage/services/' . $accountService->id . '/overview');
        } catch (Throwable $e) {
            // phpcs:ignore
            ServiceLog::error('Something went wrong handling callback for ' . $accountService->service->display_name . ': ' . $e::class . ' - ' . $e->getMessage());
            throw $e;
        }
    }
}
