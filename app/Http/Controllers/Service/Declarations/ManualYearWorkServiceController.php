<?php

namespace App\Http\Controllers\Service\Declarations;

use App\Account;
use App\AccountService;
use App\Exceptions\Files\FileConversionException;
use App\Exceptions\InvalidXbrlException;
use App\Exceptions\ServiceTask\AnnualReport\SigningColleagueMissingException;
use App\Exceptions\ServiceTask\TaskCannotBeUpdatedException;
use App\Factories\TaskFileFactory;
use App\Helpers\FileConvertHelper;
use App\Helpers\TaskAuditLogHelper;
use App\Http\Resources\ServiceTask\TaskResource;
use App\Http\VueRequests\Service\ManualYearWork\GetPublicationDocumentInformationRequest;
use App\Http\VueRequests\Service\ManualYearWork\SetSigningUserRequest;
use App\Http\VueRequests\Service\ManualYearWork\UploadForMediumCompaniesRequest;
use App\Http\VueRequests\Service\ManualYearWork\UploadForMicroAndSmallCompaniesRequest;
use App\Http\VueRequests\Service\ManualYearWork\UploadForSbrRequest;
use App\Logging\Channels\ServiceLog;
use App\Models\TaskFile;
use App\Repositories\Services\AccountServiceRepository;
use App\Service;
use App\Services\CompanyService;
use App\Services\Gateway\ManualYearWork\ManualYearWorkService;
use App\Services\ServiceTaskService;
use App\Services\TaskFiles\TaskFilePrepService;
use App\Services\UserService;
use App\ValueObject\Declarations\DeclarationData;
use Auth;
use Carbon\Exceptions\InvalidFormatException;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;

class ManualYearWorkServiceController
{
    public function __construct(
        private readonly AccountServiceRepository $accountServiceRepository,
        private readonly CompanyService $companyService
    ) {
    }

    private function getAccountService(Account $account): AccountService
    {
        return $this->accountServiceRepository->getByReferenceName(
            Service::MANUAL_YEAR_WORK_PROVIDER,
            $account,
            true
        )->first();
    }

    /**
     * Convert UploadedFile to TaskFile
     * @param UploadedFile|null $file File uploaded by the user. Can be null.
     * @param AccountService $accountService
     * @param string $type TaskFile type
     * @return TaskFile|null Returns null if no file was provided.
     * @throws Exception
     */
    private function getTaskFile(?UploadedFile $file, AccountService $accountService, string $type): ?TaskFile
    {
        if (is_null($file)) {
            return null;
        }

        $filename = $file->getClientOriginalName();
        $uploadedExtension = $file->getClientOriginalExtension();
        if (!TaskFile::supportsExtension($uploadedExtension)) {
            try {
                $content = FileConvertHelper::pathToPdf($uploadedExtension, $file->getRealPath());
                $filename = pathinfo($filename, PATHINFO_FILENAME) . '.pdf';
            } catch (\Throwable $e) {
                unlink($file->getRealPath());
                throw new FileConversionException($e->getMessage(), previous: $e);
            }
        } else {
            $content = $file->getContent();
        }

        unlink($file->getRealPath());
        return TaskFileFactory::create($accountService, $content, $filename, $type);
    }

    public function createTaskForMicroAndSmallCompanies(
        UploadForMicroAndSmallCompaniesRequest $request,
        TaskFilePrepService $prepService
    ) {
        try {
            $accountService = $this->getAccountService($request->account());
            $company = $this->companyService->getById($request->company_id, $request->user());
            $provider = new ManualYearWorkService($accountService);
            $year = $request->input('year');

            $files = [
                'pubdoc' => $this->getTaskFile(
                    $request->file('publication_document'),
                    $accountService,
                    TaskFile::TYPE_PUBLICATION_DOCUMENT
                ),
                'annual_report' => $this->getTaskFile(
                    $request->file('annual_report'),
                    $accountService,
                    TaskFile::TYPE_ANNUAL_REPORT
                ),
                'audit_report' => $this->getTaskFile(
                    $request->file('audit_report'),
                    $accountService,
                    TaskFile::TYPE_AUDIT_REPORT
                ), // phpcs:ignore
                'ava_notulen' => $this->getTaskFile(
                    $request->file('ava_notulen'),
                    $accountService,
                    TaskFile::TYPE_NOTULEN_AVA
                ), // phpcs:ignore
                'lor' => $this->getTaskFile($request->file('lor'), $accountService, TaskFile::TYPE_LOR)
            ];
            $task = $provider->createTask(
                $company,
                $files['pubdoc'],
                $files['annual_report'],
                $files['audit_report'],
                null,
                $files['ava_notulen'],
                $files['lor'],
                $year
            );

            if ($task === null) {
                throw new TaskCannotBeUpdatedException(
                    'Yearwork for micro or small company can not be created because it already exists'
                );
            }
            $prepService->prepForTask($task);
            return $request->success(TaskResource::make($task));
        } catch (InvalidFormatException $e) {
            ServiceLog::error(
                'Manual yearwork create task failed for company#' . $company->id . ' with exception: ' . $e->getMessage(
                )
            ); //phpcs:ignore
            return $request->error(null, 'task_file.xbrl_invalid_adoption_date');
        } catch (InvalidXbrlException $e) {
            ServiceLog::error(
                'Manual yearwork create task failed for company#' . $company->id . ' with exception: ' . $e->getMessage(
                )
            ); //phpcs:ignore
            return $request->error(null, 'task_file.xbrl_invalid');
        }
    }

    /**
     * @throws \Exception
     */
    public function createTaskForMediumCompanies(
        UploadForMediumCompaniesRequest $request,
        TaskFilePrepService $prepService
    ) {
        $accountService = $this->getAccountService($request->account());
        $company = $this->companyService->getById($request->company_id, $request->user());

        $provider = $accountService->getProvider();
        $year = $request->input('year');

        $files = [
            'annual_report' => $this->getTaskFile(
                $request->file('annual_report'),
                $accountService,
                TaskFile::TYPE_ANNUAL_REPORT
            ), // phpcs:ignore
            'audit_report' => $this->getTaskFile(
                $request->file('audit_report'),
                $accountService,
                TaskFile::TYPE_AUDIT_REPORT
            ), // phpcs:ignore
            'signature_xml' => $this->getTaskFile(
                $request->file('signature_xml'),
                $accountService,
                TaskFile::TYPE_SIGNATURE_XML
            ), // phpcs:ignore
            'ava_notulen' => $this->getTaskFile(
                $request->file('ava_notulen'),
                $accountService,
                TaskFile::TYPE_NOTULEN_AVA
            ), // phpcs:ignore
            'lor' => $this->getTaskFile($request->file('lor'), $accountService, TaskFile::TYPE_LOR)
        ];

        $publicationDocument = $this->getTaskFile(
            $request->file('publication_document'),
            $accountService,
            TaskFile::TYPE_PUBLICATION_DOCUMENT
        );

        if ($publicationDocument) {
            $parsedResult = $provider->getParsedResult($publicationDocument->getContent());
            if (
                !$provider->isProvisionalPublicationDocument($parsedResult)
                && !$request->has('signature_xml')
                && !$request->has('signing_colleague')
            ) {
                throw new SigningColleagueMissingException('Signing colleague missing');
            }
        }

        $task = $provider->createTaskForMediumCompanies(
            $company,
            $request->signing_colleague,
            $publicationDocument,
            $files['annual_report'],
            $files['audit_report'],
            $files['signature_xml'],
            $files['ava_notulen'],
            $files['lor'],
            $year
        );

        foreach ($files as $file) {
            if ($file) {
                $file->task()->associate($task);
                $prepService->prep($file);
            }
        }
        if ($task === null) {
            throw new TaskCannotBeUpdatedException(
                'Yearwork for medium or large company can not be created because it already exists'
            );
        }
        return $request->success(TaskResource::make($task));
    }

    public function getPublicationDocumentInformation(GetPublicationDocumentInformationRequest $request): Response
    {
        try {
            $account = $request->account();
            $accountService = $this->getAccountService($account);
            $provider = $accountService->getProvider();
            $file = $request->file('xbrl_file');
            $parsedResult = $provider->getParsedResult($file->getContent());

            return $request->ok(
                [
                    DeclarationData::COMPANY_SIZE => $parsedResult[DeclarationData::COMPANY_SIZE],
                    DeclarationData::ADOPTION_DATE_FIELD => $parsedResult[DeclarationData::ADOPTION_DATE_FIELD]
                ]
            );
        } catch (InvalidFormatException $e) {
            ServiceLog::error($e::class . ' ' . $e->getMessage());
            return $request->error(null, 'task_file.xbrl_invalid_adoption_date');
        } catch (InvalidXbrlException $e) {
            ServiceLog::error($e::class . ' ' . $e->getMessage());
            return $request->error(null, 'task_file.xbrl_invalid');
        }
    }

    public function setYearworkTaskSigningUser(SetSigningUserRequest $request)
    {
        $task = resolve(ServiceTaskService::class)
            ->getById($request->service_task_id, Auth::user());
        $user = resolve(UserService::class)
            ->getById($request->signing_user_id, $request->account(), Auth::user());

        resolve(ManualYearWorkService::class)
            ->setSigningUsersToTask($task, $user->id);
        TaskAuditLogHelper::signingColleagueUpdated($task, $user);

        return $request->ok();
    }

    /**
     * Create SBR yearwork task
     * @param UploadForSbrRequest $request
     * @return Response
     * @throws \Exception
     */
    public function createTaskForSbr(UploadForSbrRequest $request): Response
    {
        $accountService = $this->getAccountService($request->account());
        $company = $this->companyService->getById($request->company_id, $request->user());

        /** @var ManualYearWorkService $provider */
        $provider = $accountService->getProvider();

        $task = $provider->createSbrTask(
            $company,
            $request->file('yearwork_document'),
            $request->file('audit_report'),
            $request->file('signature_xml')
        );

        return $request->success($task);
    }
}
