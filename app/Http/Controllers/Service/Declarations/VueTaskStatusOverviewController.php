<?php

namespace App\Http\Controllers\Service\Declarations;

use App\Helpers\Http\CsvExportHelper;
use App\Helpers\MemoryHelper;
use App\Http\Resources\ServiceTask\StatusOverviewDataResource;
use App\Http\Resources\ServiceTask\StatusOverviewFiltersResource;
use App\Http\VueRequests\Task\AccountStatusOverviewFiltersRequest;
use App\Http\VueRequests\Task\AccountStatusOverviewResultsRequest;
use App\Http\VueRequests\Task\ManagerStatusOverviewCsvRequest;
use App\Http\VueRequests\Task\UserStatusOverviewCsvRequest;
use App\Http\VueRequests\Task\UserStatusOverviewFiltersRequest;
use App\Http\VueRequests\Task\UserStatusOverviewResultsRequest;
use App\Services\ServiceTaskService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;

class VueTaskStatusOverviewController
{
    private ServiceTaskService $serviceTaskService;

    /**
     * VueMemberController constructor.
     * @param ServiceTaskService $serviceTaskService
     */
    public function __construct(ServiceTaskService $serviceTaskService)
    {
        $this->serviceTaskService = $serviceTaskService;
    }

    /**
     * Get the Companies Tasks Status Overview in CSV (Download).
     * @param UserStatusOverviewCsvRequest $request - Request to use to respond with CSV.
     * @return Response
     */
    public function userCsv(UserStatusOverviewCsvRequest $request): Response
    {
        MemoryHelper::setMemoryLimit('512M');
        $tasks = StatusOverviewDataResource::collection(
            $this->serviceTaskService->userStatusOverviewPage(
                $request->account(),
                $request->user(),
                $request->filters()
            )
        );

        return CsvExportHelper::respondCsv($tasks->toArray($request), $request->path());
    }

    /**
     * @param UserStatusOverviewFiltersRequest $request
     * @return array
     */
    public function userFilters(UserStatusOverviewFiltersRequest $request): array
    {
        $filters = $this->serviceTaskService->getStatusOverviewFilters($request->account(), $request->user());

        StatusOverviewFiltersResource::withoutWrapping();
        $output = StatusOverviewFiltersResource::collection([$filters])->toArray($request);
        return $output[0];
    }

    /**
     * @param UserStatusOverviewResultsRequest $request
     * @return AnonymousResourceCollection
     */
    public function userResults(UserStatusOverviewResultsRequest $request): AnonymousResourceCollection
    {
        MemoryHelper::setMemoryLimit('1024M');
        StatusOverviewDataResource::withoutWrapping();
        return StatusOverviewDataResource::collection(
            $this->serviceTaskService->userStatusOverviewPage(
                $request->account(),
                $request->user(),
                $request->filters()
            )
        );
    }

    /**
     * Get the Companies Tasks Status Overview in CSV (Download).
     * @param ManagerStatusOverviewCsvRequest $request - Request to use to respond with CSV.
     * @return Response
     */
    public function managerCsv(ManagerStatusOverviewCsvRequest $request): Response
    {
        MemoryHelper::setMemoryLimit('1024M');
        $tasks = StatusOverviewDataResource::collection(
            $this->serviceTaskService->accountStatusOverviewPage(
                $request->account(),
                $request->filters()
            )
        );
        return CsvExportHelper::respondCsv($tasks->toArray($request), $request->path());
    }

    /**
     * @param AccountStatusOverviewFiltersRequest $request
     * @return array
     */
    public function managerFilters(AccountStatusOverviewFiltersRequest $request): array
    {
        MemoryHelper::setMemoryLimit('512M');
        $filters = $this->serviceTaskService->getStatusOverviewFilters($request->account());
        StatusOverviewFiltersResource::withoutWrapping();
        $output = StatusOverviewFiltersResource::collection([$filters])->toArray($request);
        return $output[0];
    }

    public function managerResults(AccountStatusOverviewResultsRequest $request): AnonymousResourceCollection
    {
        MemoryHelper::setMemoryLimit('1536M');
        StatusOverviewDataResource::withoutWrapping();
        return StatusOverviewDataResource::collection(
            $this->serviceTaskService->accountStatusOverviewPage(
                $request->account(),
                $request->filters()
            )
        );
    }
}
