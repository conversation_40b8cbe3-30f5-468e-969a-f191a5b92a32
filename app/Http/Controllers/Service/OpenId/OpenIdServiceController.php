<?php

namespace App\Http\Controllers\Service\OpenId;

use App\Account;
use App\AccountService;
use App\Auth\Helpers\OpenIdHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Consumer\OpenIdResource;
use App\Http\VueRequests\Service\GenerateOpenIdRequest;
use App\Http\VueRequests\Service\GetOpenIdRequest;
use App\Http\VueRequests\Service\RemoveOpenIdRequest;
use App\Repositories\ConsumerRepository;
use Exception;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;

class OpenIdServiceController extends Controller
{
    /**
     * @param GetOpenIdRequest $request
     * @param ConsumerRepository $consumerRepository
     * @return OpenIdResource
     * @throws Exception
     */
    public function getOpenId(GetOpenIdRequest $request, ConsumerRepository $consumerRepository): OpenIdResource
    {
        $account = Account::getHostAccount();
        $account_service = AccountService::findOrFail($request->account_service_id);
        $consumer = $consumerRepository->findOrCreateOpenIdConsumerFromAccount($account, $account_service);
        return OpenIdResource::make(['consumer' => $consumer, 'account_service' => $account_service]);
    }

    /**
     * @param GenerateOpenIdRequest $request
     * @return Response|ResponseFactory
     * @throws \Exception
     */
    public function generateOpenId(GenerateOpenIdRequest $request): Response|ResponseFactory
    {
        $account = Account::getHostAccount();
        $account_service = AccountService::findOrFail($request->account_service_id);
        $account_service->enableOpenIDConnect($request->environment);
        $consumer = $account->consumers()->where('account_service_id', $account_service->id)->first();
        $consumer = OpenIdHelper::consumerWithCertificate(
            $request->relying_party,
            $account_service,
            $account,
            $consumer
        );
        $consumer->save();

        return $request->ok(OpenIdResource::make(['consumer' => $consumer, 'account_service' => $account_service]));
    }

    /**
     * @param RemoveOpenIdRequest $request
     * @param ConsumerRepository $consumerRepository
     * @return ResponseFactory|Response
     * @throws \Exception
     */
    public function removeOpenId(
        RemoveOpenIdRequest $request,
        ConsumerRepository $consumerRepository
    ): Response|ResponseFactory {
        $account = Account::getHostAccount();
        $account_service = AccountService::findOrFail($request->account_service_id);
        $account_service->disableOpenIDConnect();

        return $consumerRepository->deleteOpenIdFromAccount($account, $account_service) ?
            $request->ok() :
            $request->error();
    }
}
