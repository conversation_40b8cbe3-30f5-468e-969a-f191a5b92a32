<?php

namespace App\Http\Controllers\Service\OpenQuestions;

use App\Http\Controllers\Controller;
use App\Http\Resources\OpenQuestions\Fiscal\QuestionResource;
use App\Http\VueRequests\OpenQuestions\Declaration\CreateQuestionRequest;
use App\Http\VueRequests\OpenQuestions\Declaration\IndexQuestionsRequest;
use App\Services\OpenQuestions\DeclarationQuestionService;
use App\Services\OpenQuestions\OpenQuestionService;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;

class DeclarationController extends Controller
{
    public function __construct(
        private readonly OpenQuestionService $openQuestionService,
        private readonly DeclarationQuestionService $declarationQuestionService,
    ) {
    }

    public function createQuestion(CreateQuestionRequest $request): Response|ResponseFactory
    {
        $openQuestion = $this->openQuestionService->createQuestion(
            $request->genericAttributes(),
            $request->service_name,
            $request->user(),
            $request->agentData(),
            $request->getFiles()
        );

        $this->declarationQuestionService->create($request->specificAttributes(), $openQuestion);
        return $request->success();
    }

    public function getQuestions(IndexQuestionsRequest $request): AnonymousResourceCollection
    {
        $questions = $this->declarationQuestionService->getQuestions(
            $request->external_id,
            $request->service_name,
            $request->user()
        );

        return QuestionResource::collection($questions);
    }
}
