<?php

namespace App\Http\Controllers\Service\OpenQuestions;

use App\Account;
use App\Exceptions\RateLimitException;
use App\Exceptions\UnauthorizedException;
use App\Helpers\CooldownHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Company\Api\CompanyResource;
use App\Http\Resources\Company\IndexResource;
use App\Http\Resources\OpenQuestions\UnconnectedQuestionResource;
use App\Http\Resources\User\ProfileResource;
use App\Http\VueRequests\OpenQuestions\GetQuestionRequest;
use App\Http\VueRequests\OpenQuestions\UnverifiedQuestionRequest;
use App\Http\VueRequests\OpenQuestions\VerifyQuestionRequest;
use App\Http\VueRequests\UnverifiedQuestion\ConnectQuestionsToCompaniesRequest;
use App\Http\VueRequests\UnverifiedQuestion\GetAvailableCompaniesRequest;
use App\Http\VueRequests\UnverifiedQuestion\GetUnconnectedQuestionsRequest;
use App\Logging\Channels\ServiceLog;
use App\Services\OpenQuestions\ManualQuestionsService;
use App\Services\OpenQuestions\UnverifiedQuestionsService;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use JsonException;
use Throwable;

class UnverifiedQuestionsController extends Controller
{
    public function __construct(
        private readonly ManualQuestionsService $manualQuestionsService,
        private readonly UnverifiedQuestionsService $unverifiedQuestionsService
    ) {
    }

    public function question(GetQuestionRequest $request)
    {
        $manualQuestionService = $this->manualQuestionsService->getManualQuestionsService(
            $request->account(),
            false
        );
        if (empty($manualQuestionService) || !$manualQuestionService->isActive()) {
            return response()->view('errors.404', [], 404);
        }

        $data = [];
        if (!empty($request->user())) {
            $data['user'] = ProfileResource::make($request->user());
        }

        return view(
            'vue.template',
            [
                'component' => 'UnverifiedQuestions',
                'data' => $data,
                'account' => $request->account()
            ]
        );
    }

    public function createQuestion(UnverifiedQuestionRequest $request): Response
    {
        if (CooldownHelper::hasLockout($request)) {
            throw new RateLimitException(
                'Could not create unverified client question because it has been attempted too many times.'
            ); // phpcs:ignore
        }

        $manualQuestionService = $this->manualQuestionsService->getManualQuestionsService($request->account(), false);

        if ($manualQuestionService === null || !$manualQuestionService->isActive()) {
            return response()->view('errors.404', [], 404);
        }

        try {
            $this->unverifiedQuestionsService->createUnverifiedQuestion($request->getQuestion(), $request->account());
            $request->handleCleanup();
            CooldownHelper::clearAttempts($request);
            return $request->success(null, 'open_question.create_client_question.success');
        } catch (UnauthorizedException $exception) {
            CooldownHelper::incrementAttempts($request);
            return $request->error(null, 'open_question.create_client_question.incorrect_email');
        } catch (Exception $e) {
            CooldownHelper::incrementAttempts($request);
            return $request->error(null, 'open_question.create_client_question.failed');
        }
    }

    /**
     * Verify Step
     * @param VerifyQuestionRequest $request
     * @return Application|Factory|View|Response
     * @throws Throwable
     */
    public function verify(VerifyQuestionRequest $request): Factory|View|Response|Application
    {
        if ($request->validationFailed()) {
            return response()->view('errors.404', [], 404);
        }

        $manualQuestionService = $this->manualQuestionsService->getManualQuestionsService($request->account(), false);
        if (empty($manualQuestionService) || !$manualQuestionService->isActive()) {
            return response()->view('errors.404', [], 404);
        }

        $question = $manualQuestionService->getByUuid($request->getUuid());
        if (is_null($question)) {
            return response()->view('errors.404', [], 404);
        }

        // If the user who logged in, has the same email address as the user who uploaded.
        // We expect this user to be the correct and use this, else use the first with email in this account.
        if (
            $request->user() !== null
            && $request->user()->email === $question->email
            && $question->account_id === $request->user()->account_id
        ) {
            $user = $request->user();
        } else {
            $user = $manualQuestionService->getQuestionUser($question);
        }

        try {
            if (
                $manualQuestionService->verifyQuestion(
                    $question,
                    $user,
                    $request->getCompanyId(),
                    $request->agentData()
                )
            ) {
                return $this->handleVerifyView(
                    $request->account(),
                    trans('open_question.create_client_question.verify.success')
                );
            }
        } catch (Throwable $exception) {
            return $this->handleVerifyView(
                $request->account(),
                $exception->getLocalizedMessage(),
                false
            );
        }

        $companies = $manualQuestionService->getCompaniesToVerifyUpload($user);
        if (empty($companies)) {
            ServiceLog::info('Failed to verify client question. No companies found.', [
                'user_id' => $user?->id,
                'request_body' => json_encode($request->all(), JSON_THROW_ON_ERROR),
            ]);

            return $this->handleVerifyView(
                $request->account(),
                trans('open_question.create_client_question.verify.error'),
                false
            );
        }

        return $this->handleVerifyView(
            $request->account(),
            null,
            true,
            $companies->pluck('name', 'id')->toArray()
        );
    }

    protected function handleVerifyView(Account $account, $message = null, $success = true, $companies = [])
    {
        return view(
            'vue.template',
            [
                'window_size' => 'medium',
                'component' => 'VerifyQuestions',
                'data' => [
                    'message' => [
                        'success' => $success,
                        'body' => $message
                    ],
                    'companies' => $companies
                ],
                'account' => $account
            ]
        );
    }

    /**
     * Verifies the question if possible, if not supply the information to continue.
     * @param VerifyQuestionRequest $request
     * @return ResponseFactory|Response
     * @throws JsonException
     */
    public function verification(VerifyQuestionRequest $request): Response|ResponseFactory
    {
        if ($request->validationFailed()) {
            return $request->error(['route' => 'not_found'], 'open_question.create_client_question.verify.not_found');
        }

        $manualQuestionService = $this->manualQuestionsService->getManualQuestionsService($request->account(), false);
        if (empty($manualQuestionService) || !$manualQuestionService->isActive()) {
            return $request->error(['route' => 'not_found'], 'open_question.create_client_question.verify.not_found');
        }

        $question = $manualQuestionService->getByUuid($request->getUuid());
        if (is_null($question)) {
            return $request->error(['route' => 'not_found'], 'open_question.create_client_question.verify.not_found');
        }

        // If the user who logged in, has the same email address as the user who uploaded.
        // We continue with this user if it matches. If not, we try to find the user with email in this account.
        if (
            $request->user() !== null
            && $request->user()->email === $question->email
            && $question->account_id === $request->user()->account_id
        ) {
            $user = $request->user();
        } else {
            $user = $manualQuestionService->getQuestionUser($question);
        }

        try {
            if (
                $manualQuestionService->verifyQuestion(
                    $question,
                    $user,
                    $request->getCompanyId(),
                    $request->agentData()
                )
            ) {
                return $request->ok(data: ['message' => trans('open_question.create_client_question.verify.success')]);
            }
        } catch (Throwable $exception) {
            return $request->error(message: $exception->getLocalizedMessage());
        }

        $companies = $manualQuestionService->getCompaniesToVerifyUpload($user);
        if (empty($companies)) {
            ServiceLog::info('Failed to verify client question. No companies found.', [
                'user_id' => $user?->id,
                'request_body' => json_encode($request->all(), JSON_THROW_ON_ERROR),
            ]);

            return $request->error(key: 'open_question.create_client_question.verify.error');
        }

        return $request->ok(['companies' => CompanyResource::collection($companies)]);
    }

    public function getAvailableCompanies(GetAvailableCompaniesRequest $request): AnonymousResourceCollection
    {
        $manualQuestionService = $this->manualQuestionsService->getManualQuestionsService($request->account(), false);
        IndexResource::withoutWrapping();
        return IndexResource::collection(
            $manualQuestionService?->getCompanies($request->user())
        );
    }

    public function getUnconnectedQuestions(GetUnconnectedQuestionsRequest $request): AnonymousResourceCollection
    {
        return UnconnectedQuestionResource::collection(
            $this->unverifiedQuestionsService->getUnconnectedQuestions($request->account())
        );
    }

    public function connect(ConnectQuestionsToCompaniesRequest $request): Response|ResponseFactory
    {
        $this->unverifiedQuestionsService->connectQuestionsToCompanies(
            $request->user(),
            $request->connections,
            $request->agentData()
        );
        return $request->success(key: 'open_question.connect_unverified_question.success');
    }
}
