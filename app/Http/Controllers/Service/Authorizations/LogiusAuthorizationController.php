<?php

namespace App\Http\Controllers\Service\Authorizations;

use App\Helpers\Http\CsvExportHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\Authorizations\CompanyIdentifiersResource;
use App\Http\Resources\Authorizations\StatusOverviewResource;
use App\Http\VueRequests\Authorizations\CompaniesRequest;
use App\Http\VueRequests\Authorizations\CsvStatusOverviewRequest;
use App\Http\VueRequests\Authorizations\ManagerCsvStatusOverviewRequest;
use App\Http\VueRequests\Authorizations\ManagerStatusOverviewFiltersRequest;
use App\Http\VueRequests\Authorizations\ManagerStatusOverviewRequest;
use App\Http\VueRequests\Authorizations\RefreshConditionRequest;
use App\Http\VueRequests\Authorizations\RequestAuthorizationRequest;
use App\Http\VueRequests\Authorizations\RetractRequest;
use App\Http\VueRequests\Authorizations\StatusOverviewFiltersRequest;
use App\Http\VueRequests\Authorizations\StatusOverviewRequest;
use App\Http\VueRequests\Authorizations\TypesRequest;
use App\Logging\Channels\ServiceLog;
use App\Services\Authorizations\LogiusAuthorizationService;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use SoapFault;

class LogiusAuthorizationController extends Controller
{
    private LogiusAuthorizationService $logiusAuthorizationService;

    public function __construct(
        LogiusAuthorizationService $service
    ) {
        $this->logiusAuthorizationService = $service;
    }

    /**
     * Get all authorizations types
     *
     * @param TypesRequest $request
     * @return array
     */
    public function types(TypesRequest $request): array
    {
        return $this->logiusAuthorizationService->getAuthorizationTypes($request->account());
    }

    /**
     * Get all companies with authorization types
     *
     * @param CompaniesRequest $request
     * @return AnonymousResourceCollection
     */
    public function companies(CompaniesRequest $request): AnonymousResourceCollection
    {
        return CompanyIdentifiersResource::collection(
            $this->logiusAuthorizationService->getCompanies(
                $request->user(),
                $request->getAuthorizationType(),
                $request->getFilters()
            )
        );
    }

    /**
     * Authorize request to service
     *
     * @throws SoapFault
     */
    public function requestAuthorization(RequestAuthorizationRequest $request)
    {
        $authorizations = $this->logiusAuthorizationService->create(
            $request->account(),
            $request->getType(),
            $request->getCompanyIdentifiers(),
            $request->getDate(),
            $request->user()
        );
        $count = count($authorizations);

        return $request->success(
            ['created' => $count],
            null,
            [],
            trans_choice('logius_authorization.authorize.success', $count, ['count' => $count])
        );
    }

    /**
     * Filters for authorizations user status overview page
     *
     * @param StatusOverviewFiltersRequest $request
     * @return array
     */
    public function filters(StatusOverviewFiltersRequest $request): array
    {
        return $this->logiusAuthorizationService->getStatusOverviewFilters(
            $request->user(),
            $request->user()->activeCompanyIds()
        );
    }

    /**
     * @param StatusOverviewRequest $request
     * @return AnonymousResourceCollection
     */
    public function filteredAuthRequests(StatusOverviewRequest $request): AnonymousResourceCollection
    {
        return StatusOverviewResource::collection(
            $this->logiusAuthorizationService->getFilteredAuthorizations(
                $request->user()->activeCompanyIds(),
                $request->filters()
            )
        );
    }

    /**
     * @param CsvStatusOverviewRequest $request
     * @return Response
     */
    public function filteredAuthorizationsCsvExport(CsvStatusOverviewRequest $request): Response
    {
        $filteredQuestions = $this->filteredAuthRequests($request);
        return CsvExportHelper::respondCsv($filteredQuestions->toArray($request), $request->path());
    }

    /**
     * Filters for authorizations manager status overview page
     *
     * @param ManagerStatusOverviewFiltersRequest $request
     * @return array
     */
    public function managerFilters(ManagerStatusOverviewFiltersRequest $request): array
    {
        return $this->logiusAuthorizationService->getStatusOverviewFilters(
            $request->user(),
            $request->account()->activeCompanyIds()
        );
    }

    /**
     * @param ManagerStatusOverviewRequest $request
     * @return AnonymousResourceCollection
     */
    public function managerFilteredAuthorizations(ManagerStatusOverviewRequest $request): AnonymousResourceCollection
    {
        return StatusOverviewResource::collection(
            $this->logiusAuthorizationService->getFilteredAuthorizations(
                $request->account()->activeCompanyIds(),
                $request->filters()
            )
        );
    }

    /**
     * @param ManagerCsvStatusOverviewRequest $request
     * @return Response
     */
    public function managerFilteredAuthorizationsCsvExport(ManagerCsvStatusOverviewRequest $request): Response
    {
        $filteredQuestions = $this->managerFilteredAuthorizations($request);
        return CsvExportHelper::respondCsv($filteredQuestions->toArray($request), $request->path());
    }

    public function retract(RetractRequest $request)
    {
        $logiusAuthorization = $this->logiusAuthorizationService->getById(
            $request->authorizationRequestId,
            $request->user()
        );
        try {
            $logiusAuthorization = $this->logiusAuthorizationService->retract($logiusAuthorization);
            return $request->success(
                StatusOverviewResource::make($logiusAuthorization),
                'logius_authorization.retract.success'
            );
        } catch (\Throwable $e) {
            ServiceLog::error(
                'Logius Authorization #' . $request->authorizationRequestId . ' in account #' . $request->account(
                )->id . ' error retracting: ' . $e::class . ' - ' . $e->getMessage()
            ); // phpcs:ignore
            return $request->error(null, 'logius_authorization.retract.error');
        }
    }

    public function refreshCondition(RefreshConditionRequest $request)
    {
        $logiusAuthorization = $this->logiusAuthorizationService->getById(
            $request->authorizationRequestId,
            $request->user()
        );

        try {
            $logiusAuthorization = $this->logiusAuthorizationService->checkCondition($logiusAuthorization);
            return $request->success(
                StatusOverviewResource::make($logiusAuthorization),
                'logius_authorization.refresh_condition.success'
            );
        } catch (\Throwable $e) {
            ServiceLog::error(
                'Logius Authorization #' . $request->authorizationRequestId . ' in account #' . $request->account(
                )->id . ' error refreshing condition: ' . $e::class . ' - ' . $e->getMessage()
            ); // phpcs:ignore
            return $request->error(null, 'logius_authorization.refresh_condition.error');
        }
    }
}
