<?php

namespace App\Http\Controllers;

use App\Company;
use App\Http\Requests\DeleteCompanyIdentifierRequest;
use App\Http\Requests\InternalGetRequest;
use App\Http\Requests\ToggleCompanyIdentifierLockedRequest;
use App\Http\Resources\Company\CompanyIdentifiersResource;
use App\Services\CompanyIdentifierService;
use Exception;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Response;
use Throwable;

class CompanyIdentifierController extends Controller
{
    private CompanyIdentifierService $companyIdentifierService;

    public function __construct(
        CompanyIdentifierService $companyIdentifierService
    ) {
        $this->companyIdentifierService = $companyIdentifierService;
    }

    public function identifiers(InternalGetRequest $request, Company $company): Response|ResponseFactory
    {
        return $request->ok(CompanyIdentifiersResource::collection($company->getIdentifiers()));
    }

    /**
     * @throws Exception
     * @throws Throwable
     */
    public function deleteIdentifier(DeleteCompanyIdentifierRequest $request): Response|ResponseFactory
    {
        $this->companyIdentifierService->deleteAndUpdateTasks(
            $request->getCompanyId(),
            $request->get('identifier_id')
        );

        return $request->success(null, 'company.identifier.deleted');
    }

    public function toggleLocked(ToggleCompanyIdentifierLockedRequest $request): Response|ResponseFactory
    {
        $updatedIdentifier = $this->companyIdentifierService->toggleIdentifierLockedState(
            $request->getCompanyId(),
            $request->identifier_id,
            $request->user()
        );

        $response_message_key = 'company.identifier.' . ($updatedIdentifier->locked ? 'locked' : 'unlocked');

        return $request->success(CompanyIdentifiersResource::make($updatedIdentifier), $response_message_key);
    }
}
