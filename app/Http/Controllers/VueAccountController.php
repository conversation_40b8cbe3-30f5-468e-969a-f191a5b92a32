<?php

namespace App\Http\Controllers;

use App\Account;
use App\Context;
use App\Events\AccountChangedEvent;
use App\Events\Company\ManagerAddedEvent;
use App\Events\Company\ManagerRemovedEvent;
use App\Events\ContextSecuritySettingsChangedEvent;
use App\Events\SendSupportMessageEvent;
use App\Events\UserReactivationRequest\RequestApprovedEvent;
use App\Events\UserReactivationRequest\RequestDeclinedEvent;
use App\Exceptions\BadRequestException;
use App\Helpers\BrowserExtensionHelper;
use App\Helpers\CertificateHelper;
use App\Helpers\Ip;
use App\Helpers\SlackMessageHelper;
use App\Http\Requests\Account\CreateApiTokenRequest;
use App\Http\Requests\Account\DeleteApiTokenRequest;
use App\Http\Requests\Account\GetApiTokensRequest;
use App\Http\Requests\Account\GetReactivationRequestsRequest;
use App\Http\Requests\Account\SetAccountToBeForgottenRequest;
use App\Http\Requests\Account\UpdateAccountNameRequest;
use App\Http\Requests\Account\UpdateReactivationRequestRequest;
use App\Http\Resources\Account\AccountManagerMembershipResource;
use App\Http\Resources\Account\AccountResource;
use App\Http\Resources\Account\ApiTokensResource;
use App\Http\Resources\Account\AvailableUsersResource;
use App\Http\Resources\Account\EmailTemplateResource;
use App\Http\Resources\Account\ReactivationRequestResource;
use App\Http\Resources\Account\SettingsBrandingResource;
use App\Http\Resources\Account\SettingsGlobalResource;
use App\Http\Resources\Account\SettingsIntegrationsResource;
use App\Http\Resources\Account\SettingsSecurityResource;
use App\Http\Resources\Account\SmsFieldsResource;
use App\Http\Resources\Company\ManagerResource;
use App\Http\Resources\Context\IndexResource;
use App\Http\Resources\Pdf\PdfBackgroundResource;
use App\Http\VueRequests\Account\AddAccountManagersRequest;
use App\Http\VueRequests\Account\AddCertificateRequest;
use App\Http\VueRequests\Account\CertificateKeysRequest;
use App\Http\VueRequests\Account\CompanyManagerDeleteRequest;
use App\Http\VueRequests\Account\CompanyManagerStoreRequest;
use App\Http\VueRequests\Account\EmailTemplateRequest;
use App\Http\VueRequests\Account\EmailTemplateUpdateRequest;
use App\Http\VueRequests\Account\GetPdfBackgroundsRequest;
use App\Http\VueRequests\Account\ManagedContextsRequest;
use App\Http\VueRequests\Account\SendSsoRequest;
use App\Http\VueRequests\Account\SendSupportRequest;
use App\Http\VueRequests\Account\SmsRequest;
use App\Http\VueRequests\Account\SmsUpdateRequest;
use App\Http\VueRequests\Account\UpdateBrandingRequest;
use App\Http\VueRequests\Account\UpdateEmbedRequest;
use App\Http\VueRequests\Account\UpdateGlobalRequest;
use App\Http\VueRequests\Context\UpdateSecurityRequest;
use App\Jobs\GenerateEmailHeaderJob;
use App\Jobs\GenerateFaviconJob;
use App\Jobs\SendEmailJob;
use App\Mail\Account\SupportDefaultMail;
use App\Mail\Account\TrialAccountDeletedMail;
use App\Models\UserReactivationRequest;
use App\Role;
use App\Services\Account\AccountService;
use App\Services\Account\TranslationService;
use App\Services\ContextService;
use App\Services\PersonalAccessTokenService;
use App\Services\UserReactivationService;
use App\Services\UserService;
use App\User;
use App\Support\Carbon;
use Exception;
use File;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Mail;
use Log;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Throwable;

class VueAccountController extends Controller
{
    public function __construct(
        private readonly ContextService $contextService,
        private readonly TranslationService $translationService,
        private readonly PersonalAccessTokenService $personalAccessTokenService,
        private readonly UserReactivationService $userReactivationService,
        private readonly AccountService $accountService,
        private readonly UserService $userService
    ) {
    }

    public function settings(): SettingsGlobalResource
    {
        $account = Account::getHostAccount();
        return SettingsGlobalResource::make($account);
    }

    public function branding(): SettingsBrandingResource
    {
        $account = Account::getHostAccount();
        return SettingsBrandingResource::make($account);
    }

    // phpcs:ignore
    public function account_managers(): AnonymousResourceCollection
    {
        $account = Account::getHostAccount();
        return AccountManagerMembershipResource::collection(
            $account->mainContext->memberships()->whereManager()->get()
        );
    }

    // phpcs:ignore
    public function available_users(): AnonymousResourceCollection
    {
        $account = Account::getHostAccount();
        return AvailableUsersResource::collection($this->accountService->getAvailableUsersToBeAccountManager($account));
    }

    public function certkeys(CertificateKeysRequest $request)
    {
        return $request->ok(CertificateHelper::getList(Account::getHostAccount()));
    }

    /**
     * Process a certificate file uploaded by the user (account manager only)
     * @param AddCertificateRequest $request
     * @return Response
     */
    public function addCertificate(AddCertificateRequest $request): Response
    {
        try {
            if ($request->hasFile('certfile') && !$request->file('certfile')->isValid()) {
                throw new BadRequestException('No valid uploaded file found in request.');
            }

            $account = Account::getHostAccount();
            CertificateHelper::storeUploadedFile($account, $request->file('certfile'), $request->input('password'));

            return $request->success(CertificateHelper::getList($account));
        } catch (\Exception $e) {
            //Many things can go wrong here so we catch all exceptions types.
            Log::warning('Error occurred while adding certificate to account: ' . $e->getMessage());
            return $request->error();
        }
    }

    public function addAccountManagers(AddAccountManagersRequest $request): Response|Application|ResponseFactory
    {
        $account = Account::getHostAccount();
        foreach ($request->users as $user_id) {
            $user = User::findOrFail($user_id);
            if (!$user->isManagedBy(auth()->user())) {
                return $request->error();
            }

            $user->addMembership($account->mainContext, 'manager');
        }

        return $request->success(
            ['account_managers' => $this->account_managers(), 'users' => $this->available_users()]
        );
    }

    public function security(): SettingsSecurityResource
    {
        $account = Account::getHostAccount();
        $main_context = $account->mainContext;
        return SettingsSecurityResource::make($main_context);
    }

    public function integrations(): SettingsIntegrationsResource
    {
        $account = Account::getHostAccount();
        return SettingsIntegrationsResource::make($account);
    }

    public function update(UpdateGlobalRequest $request): Response|ResponseFactory
    {
        $context = Account::getHostAccount()->mainContext;

        if ($context->isMain()) {
            $account = $context->account()->withTrashed()->first();

            if ($request->filled('support_email')) {
                // Trim spaces of each email.
                $supportEmails = Collect(split_and_trim_string($request->input('support_email'), '/[;]/'));
                // Make sure we don't save double addresses.
                $account->support_email = implode(';', $supportEmails->unique()->toArray());
            }

            if ($request->filled('language')) {
                $account->language = $request->input('language');
            }

            if ($account->isDirty()) {
                $account->save();
                AccountChangedEvent::fire($account);
            }
        }

        $context->save();

        return $request->success();
    }

    /**
     * Security settings are set per group. Here we just set them for the main group (account)
     * @param UpdateSecurityRequest $request
     * @return ResponseFactory|Response
     * @throws Exception
     */
    public function updateSecurity(UpdateSecurityRequest $request): Response|ResponseFactory
    {
        $context = $request->getModelObject();

        if ($request->input('enableIpWhitelist') && $context->account->hasLicense('ip_whitelist')) {
            if ($request->filled('ipWhitelistingMode') && !empty($request->input('ipWhitelistingMode'))) {
                $context->ip_whitelist_mode = $request->input('ipWhitelistingMode');
            } else {
                $context->ip_whitelist_mode = 'standard';
            }

            $context->ip_whitelist = $request->input('ipWhitelist');
        } else {
            $context->ip_whitelist_mode = 'disabled';
            $context->ip_whitelist = "";
        }

        $context->session_lifetime_max = $request->input('session_lifetime_max') * 60;

        if ($request->input('applyLifeTimeUsers')) {
            $context->session_lifetime_default = $context->session_lifetime_max;
        } else {
            $context->session_lifetime_default = null;
        }

        $context->auth_methods = implode(",", $request->input('auth_methods'));
        $context->auth_secret_expire_days = $request->input('passwordValidityVisibility')
            ? $request->input('passwordValidity')
            : 0;
        $context->single_session_enforced = $request->input('singleSessionEnforced');

        if ($context->isMain()) {
            $context->auth_methods_enforcement_at = Carbon::parse($request->input('authMethodsEnforcement'), 'UTC');
            $context->account->login_autocomplete = $request->input('loginAutocomplete');
            $settings = $context->account->settings;
            $settings[Account::SETTING_ALLOW_COPY_PASSWORD] = $request->input('allowCopyPassword');
            // phpcs:ignore
            $settings[Account::SETTING_ALLOW_CHANGE_PREFERRED_COMMUNICATION_CHANNEL] = $request->input(
                'allowChangePreferredCommunicationChannel'
            );
            $defaultFrontAuthMethod = $request->input('defaultFrontAuthMethod');
            if ($defaultFrontAuthMethod !== Account::DEFAULT_FRONT_AUTH_METHOD) {
                $settings[Account::SETTING_DEFAULT_FRONT_AUTH_METHOD] = $defaultFrontAuthMethod;
                $this->userService->setDefaultFrontAuthMethod($context->account, $defaultFrontAuthMethod);
            } else {
                unset($settings[Account::SETTING_DEFAULT_FRONT_AUTH_METHOD]);
            }
            $settings[Account::SETTING_ALLOW_EMAIL_FRONT_AUTH] = $request->allowEmailFrontAuth;
            $context->account->settings = $settings;
            $context->account->save();
        }

        $context->save();
        ContextSecuritySettingsChangedEvent::fire($context);
        Log::info('User #' . auth()->user()->id . 'changed security settings for context #' . $context->id);

        return $request->success();
    }

    public function updateBranding(UpdateBrandingRequest $request): Response|ResponseFactory
    {
        $account = Account::getHostAccount();
        $shouldGenerateFavicon = false;

        if ($account->hasLicense('branding')) {
            $account->primary_color = $request->input('primary_color');

            if ($request->hasFile(Account::LOGO_CIRCLE) && $request->file(Account::LOGO_CIRCLE)->isValid()) {
                $account->logo_circle = $request->file(Account::LOGO_CIRCLE);
                //make sure the email image is renewed too. Should be in the model but could not get it to work there :(
                $account->email_header = null;
                $shouldGenerateFavicon = true;
            } else {
                if ($request->has(Account::LOGO_CIRCLE) && empty($request->input(Account::LOGO_CIRCLE))) {
                    $account->logo_circle = null;
                    $account->email_header = null;
                    File::delete($account->getFaviconPath());
                }
            }

            if ($request->hasFile(Account::BACKGROUND_IMG) && $request->file(Account::BACKGROUND_IMG)->isValid()) {
                $account->background_img = $request->file(Account::BACKGROUND_IMG);
            } else {
                if ($request->has(Account::BACKGROUND_IMG) && empty($request->input(Account::BACKGROUND_IMG))) {
                    $account->background_img = null;
                }
            }
        }

        $account->save();
        AccountChangedEvent::fire($account);
        Log::info('User #' . auth()->user()->id . 'changed branding settings of Account #' . $account->id);

        //need to be done after save so that the uploaded image is stored.
        if (is_null($account->email_header) && !is_null($account->logo_circle)) {
            GenerateEmailHeaderJob::dispatch($account->id);
        }

        if ($shouldGenerateFavicon) {
            GenerateFaviconJob::dispatch($account->id);
        }

        return $request->success();
    }

    public function emailTemplates(EmailTemplateRequest $request): EmailTemplateResource
    {
        return EmailTemplateResource::make($request->user()->account);
    }

    /**
     * @throws Exception
     */
    public function emailTemplatesUpdate(EmailTemplateUpdateRequest $request): Response|ResponseFactory
    {
        $values = $request->input('values');
        foreach ($values as $value) {
            $this->translationService->update(
                $request->user()->account,
                'user',
                'msg_header',
                $value['language'],
                $value['salutation']
            );
            $this->translationService->update(
                $request->user()->account,
                'user',
                'msg_invitation_default.intro',
                $value['language'],
                $value['body']
            );
            $this->translationService->update(
                $request->user()->account,
                'user',
                'msg_regards',
                $value['language'],
                $value['regards']
            );
        }

        AccountChangedEvent::fire($request->user()->account);

        return $request->success(null, 'common.changes_saved');
    }

    public function sms(SmsRequest $request): SmsFieldsResource
    {
        return SmsFieldsResource::make($request->user()->account);
    }

    /**
     * @throws Exception
     */
    public function smsUpdate(SmsUpdateRequest $request): Response|ResponseFactory
    {
        $this->translationService->update(
            $request->account(),
            'user',
            'sms_account_name',
            $request->account()->language,
            $request->account_name
        );

        return $request->success(null, 'common.changes_saved');
    }

    public function updateEmbedSettings(UpdateEmbedRequest $request): Response|ResponseFactory
    {
        $account = Account::getHostAccount();
        $settings = [
            'use_secondary_color' => $request->has('use_secondary_color'),
            'no_new_tab' => $request->has('no_new_tab'),
        ];

        $account->embed_settings = $settings;
        $account->save();

        AccountChangedEvent::fire($account);
        Log::info('User #' . auth()->user()->id . 'changed embed settings of Account #' . $account->id);

        return $request->success();
    }

    public function sendSupportRequest(SendSupportRequest $request): Response|ResponseFactory
    {
        $account = Account::getHostAccount();
        $bcc = [];
        $alt_to = null;

        if (auth()->user()->can('manage', $account)) {
            $account = Account::getAdminAccount();
            $bcc = explode(";", config('app.debug_user_email'));
        }

        if ($request->filled('category') && !empty($account->support_categories)) {
            $category = $account->getSupportCategory($request->input('category'));

            $subject = "[HIX SUPPORT / "
                . strtoupper($category['reference']) . "] - "
                . $request->input('subject');
            $alt_to = !empty($category['alt_support_email']) ? split_and_trim_string(
                $category['alt_support_email'],
                "/(,|;)/"
            ) : null;
        } else {
            $subject = "[HIX SUPPORT] - " . $request->input('subject');
        }

        $message = $request->input('message');
        $mail = new SupportDefaultMail(auth()->user(), $request, $subject, $alt_to, $message);
        Mail::to($alt_to ?? $account->getEmailAddresses())->bcc($bcc)->queue($mail);

        SendSupportMessageEvent::fire($request->input('subject'), $message);

        return $request->success();
    }

    public function getSupportCategories(): array
    {
        $result = [];
        if (!auth()->user()->can('manage', Account::getHostAccount())) {
            foreach (Account::getHostAccount()->support_categories as $category) {
                $result[$category['reference']] = $category['label'];
            }
        }
        return $result;
    }

    private function getCommonEmailVariables(Request $request): array
    {
        return [
            'ip' => Ip::format($request->ip()),
            'agent' => $request->userAgent(),
            'browser_ext_version' => $request->header(BrowserExtensionHelper::HEADER_EXTENSION_VERSION)
                ?? trans('common.not_installed')
        ];
    }

    public function sendSsoRequest(SendSsoRequest $request): Response|ResponseFactory
    {
        $urls = array_filter(split_and_trim_string($request->input('sso_urls'), '/[,;\n]/'));

        $account = Account::getAdminAccount();

        $subject = "[HIX SUPPORT] - New SSO Request";
        $sender = auth()->user();
        $recipient = $account;
        $template = 'account.emails.sso_request';
        $data = array_merge(['urls' => $urls], $this->getCommonEmailVariables($request));

        $bcc = explode(";", config('app.debug_user_email'));

        $reply_to_email = $sender->email;
        $reply_to_name = $sender->name;

        dispatch(
            new SendEmailJob(
                $sender,
                $recipient,
                $subject,
                $template,
                $data,
                null,
                null,
                null,
                $bcc,
                $reply_to_email,
                $reply_to_name
            )
        );

        return $request->success();
    }

    public function getCurrentAccount(): AccountResource
    {
        return AccountResource::make(Account::getHostAccount());
    }

    /**
     * Get a list of groups within this account which are managed by me.
     * Is used when selecting a group when creating a new user.
     */
    public function managedContexts(ManagedContextsRequest $request): AnonymousResourceCollection
    {
        $account = Account::getHostAccount();

        $user = $request->user();

        if ($user->isAdminFromDifferentAccount()) {
            $contexts = $this->contextService->getByAccount($account);
        } elseif ($user->hasRole(Role::COMPANY_MANAGER)) {
            $contexts = $this->contextService->getSubContextForAccount($account);
        } else {
            $contexts = $user->managedContexts()->where('account_id', $account->id)->onlySub()->get();
        }

        return IndexResource::collection($contexts);
    }

    public function mainContext(): Context
    {
        return Account::getHostAccount()->mainContext;
    }

    public function companyManagers(): AnonymousResourceCollection
    {
        return ManagerResource::collection(Account::getHostAccount()->companyManagers());
    }

    public function availableUsersForCompanyManagerRole(): AnonymousResourceCollection
    {
        $account = Account::getHostAccount();
        $current_managers = $account->companyManagers()->pluck('id')->toArray();
        $available_users = User::query()->where(User::ACCOUNT_ID, $account->id)->where(
            User::IS_EXTERNAL,
            '0'
        )->whereNotIn('id', $current_managers)->orderBy(User::FIRST_NAME)->orderBy(User::LAST_NAME)->get();

        return AvailableUsersResource::collection($available_users);
    }

    public function addCompanyManager(CompanyManagerStoreRequest $request): Response|ResponseFactory
    {
        $user_ids = $request->user_ids;

        $role = Role::where('name', Role::COMPANY_MANAGER)->first();
        $role->users()->attach($user_ids);

        ManagerAddedEvent::fire($user_ids);
        Log::info('User #' . auth()->user()->id . 'added company managers ' . join(", ", $user_ids));

        return $request->success($this->companyManagers());
    }

    public function deleteCompanyManager(CompanyManagerDeleteRequest $request): Response|ResponseFactory
    {
        $user_id = $request->user_id;

        $role = Role::where('name', Role::COMPANY_MANAGER)->first();
        $role->users()->detach($user_id);
        ManagerRemovedEvent::fire(User::whereKey($user_id)->firstOrFail());
        Log::info('User #' . auth()->user()->id . 'removed company manager with user ID #' . $user_id);

        return $request->success($this->companyManagers());
    }

    public function getAccountApiTokens(GetApiTokensRequest $request): AnonymousResourceCollection
    {
        $account = Account::getHostAccount($request);
        $tokens = $this->personalAccessTokenService->getAccountTokens($account);
        return ApiTokensResource::collection($tokens);
    }

    public function createApiToken(CreateApiTokenRequest $request): JsonResponse
    {
        $account = Account::getHostAccount($request);
        $token = $this->personalAccessTokenService->createForAccount($account);
        return response()->json(['token' => $token]);
    }

    public function deleteApiToken(DeleteApiTokenRequest $request): Response|ResponseFactory
    {
        $tokenId = $request->token_id;
        $this->personalAccessTokenService->deleteToken($tokenId);
        return $request->ok();
    }

    public function getReactivationRequests(GetReactivationRequestsRequest $request): Response
    {
        $reactivationRequest = $this->userReactivationService->get($request->user());

        if (isset($reactivationRequest)) {
            return $request->ok(
                ReactivationRequestResource::collection(
                    $this->userReactivationService->get($request->user())
                )
            );
        }

        return $request->ok();
    }

    public function updateReactivationRequest(
        UpdateReactivationRequestRequest $request,
    ): Response {
        try {
            $reactivationRequest = $this->userReactivationService->update(
                $request->request_id,
                $request->status,
                $request->user()
            );

            if ($reactivationRequest->status === UserReactivationRequest::STATUS_APPROVED) {
                $this->userReactivationService->sendReactivationRequestApprovedMail($reactivationRequest);
                RequestApprovedEvent::fire($reactivationRequest, $request->user());
            } else {
                $this->userReactivationService->sendReactivationRequestDeclinedMail($reactivationRequest);
                RequestDeclinedEvent::fire($reactivationRequest, $request->user());
            }

            return $request->success(null, 'account.user_reactivation_request.update.succeed_' . $request->status);
        } catch (Throwable $e) {
            Log::warning('Could not set user reactivation request #' . $request->request_id . ' to status: ' . $request->status . '. Error message: ' . $e->getMessage()); //phpcs:ignore
            return $request->error(null, 'account.user_reactivation_request.update.error');
        }
    }

    public function setAccountToBeForgotten(SetAccountToBeForgottenRequest $request)
    {
        $account = $request->account();
        $user = $request->user();
        if ($this->accountService->updateStatus($account, Account::STATUS_TOBEFORGOTTEN)) {
            $mail = new TrialAccountDeletedMail($user);
            Mail::to(config('app.contact_email'))->queue($mail);
            SlackMessageHelper::messageStatisticsChannel(
                'Trial account ' . $account->name . '(' . $account->id . ') removed by user #' . $user->id . ' :disappointed:' //phpcs:ignore
            );
            return $request->ok();
        }
        return $request->error();
    }

    public function updateName(UpdateAccountNameRequest $request)
    {
        if ($this->accountService->updateName($request->account(), $request->name)) {
            return $request->success(AccountResource::make($request->account()));
        }
        return $request->error();
    }

    public function getPdfBackgrounds(GetPdfBackgroundsRequest $request): AnonymousResourceCollection
    {
        return PdfBackgroundResource::collection($request->account()->pdfBackgrounds);
    }

    public function favicon(): StreamedResponse
    {
        $account = Account::getHostAccount();
        $path = $account?->getFaviconPath();

        if (!$path || !\file_exists($path)) {
            $path = public_path('favicon.ico');
        }

        return response()->stream(function () use ($path) {
            $stream = fopen($path, 'rb');
            fpassthru($stream);
            if (is_resource($stream)) {
                fclose($stream);
            }
        }, 200, [
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Content-Type' => 'image/png',
            'Pragma' => 'public',
        ]);
    }
}
