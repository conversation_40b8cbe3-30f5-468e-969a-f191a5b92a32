<?php

namespace App\Http\Controllers;

use App\Helpers\AgentHelper;
use App\Helpers\BrowserExtensionHelper as BrowserExtension;
use App\Http\Requests\BrowserExtensionInstallRequest;

class BrowserExtensionController extends Controller
{
    public function install(BrowserExtensionInstallRequest $request)
    {
        if (BrowserExtension::isBrowserSupported()) {
            return view(sprintf('browser_extension.install_extension_%s', strtolower(AgentHelper::browser())));
        }
        return view('browser_extension.install_extension_not_supported');
    }

    // phpcs:ignore
    public function get_recent_browser_extension()
    {
        return redirect(BrowserExtension::getRecentVersionUrl());
    }
}
