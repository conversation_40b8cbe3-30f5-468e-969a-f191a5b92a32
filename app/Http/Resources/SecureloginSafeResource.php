<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class SecureloginSafeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        $output = [];
        foreach ($this->settings as $path => $setting) {
            if (in_array($setting['type'], ['unsafe_login_url', 'username', 'password_to_clipboard', 'email'])) {
                $output[$path] = base64_encode($this->settingGetValue($setting, $path, true));
            }
        }
        return $output;
    }
}
