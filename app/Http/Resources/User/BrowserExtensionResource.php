<?php

namespace App\Http\Resources\User;

use App\Helpers\AgentHelper as Agent;
use App\Helpers\BrowserExtensionHelper as BE;
use Illuminate\Http\Resources\Json\JsonResource;

class BrowserExtensionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * returns browser extension info when the user is also the same one as currently authenticated.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'browser_type' => Agent::getAgentType(),
            'browser_name' => Agent::getAgentName(),
            'supported' => BE::isBrowserSupported(),
            'installed' => BE::isInstalled(),
            'installed_version' => BE::getInstalledVersionNumber(),
            'install_url' => BE::getExtensionUrl(),
            'chrome_url' => BE::getChromeUrl(),
            'firefox_url' => BE::getRecentVersionUrl(),
            'firefox_hash' => BE::getRecentVersion()->update_hash,
            'firefox_version' => BE::getRecentVersion()->version
        ];
    }
}
