<?php

namespace App\Http\Resources\User;

use App\DashboardCategory;
use Illuminate\Http\Resources\Json\JsonResource;

/** @property DashboardCategory $resource */
class DashboardCategoryResource extends JsonResource
{
    public function toArray($request): array
    {
        $categoryUserWidgets = $this->resource->dashboardCategoryUserWidgets;
        return [
            'id' => $this->resource->id,
            'title' => $this->resource->name,
            'name' => $this->resource->name,
            'favorite' => $this->resource->favorite,
            'edit' => true,
            'count' => $categoryUserWidgets->count(),
            'user_widgets' => DashboardCategoryUserWidgetResource::collection(
                $categoryUserWidgets->sortBy('order_index')
            )
        ];
    }
}
