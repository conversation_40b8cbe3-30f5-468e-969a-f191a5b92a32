<?php

namespace App\Http\Resources\Meilisearch;

use App\Enums\Meilisearch\Filters\Categories;
use App\Enums\Meilisearch\Models\MeilisearchCompany;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource[MeilisearchCompany::ID],
            'title' => $this->resource[MeilisearchCompany::NAME],
            'key' => Categories::COMPANIES
        ];
    }
}
