<?php

namespace App\Http\Resources\Company;

use App\Helpers\Color\Color;
use Illuminate\Http\Resources\Json\JsonResource;

class ManagerResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'user_id' => $this->id,
            'name' => $this->showLabel(),
            'color' => Color::generateColor($this->id, $this->auth_id),
            'can_be_deleted' => $this->hasRole('company-manager'),
            'this_is_you' => $this->id == auth()->user()->id
        ];
    }
}
