<?php

namespace App\Http\Resources\Company;

use App\Company;
use App\Http\Resources\JsonResourceWithoutWrapping;
use App\Models\OpenQuestions\Questions\OpenQuestion;

class OpenQuestionsStatisticsResource extends JsonResourceWithoutWrapping
{
    public function toArray($request)
    {
        $data = [
            'company_id' => null,
            'company_name' => null,
            'company_is_blocked' => null,
            'visible' => true,
            'statistics' => []
        ];

        if ($this->resource) {
            $data['company_is_blocked'] = (bool)$this->resource->blocked;

            /** @var Company $company */
            $company = $this->resource->company;
            if ($company) {
                $data['company_id'] = $company->id;
                $data['company_name'] = $company->name;
                $data['visible'] = $request->user()->hasCompany($company->id);
                $data['statistics'] = [
                    'open_questions' => $company->openQuestionsByStatusCount(OpenQuestion::STATUS_OPEN),
                    'pending_questions' => $company->openQuestionsByStatusCount(OpenQuestion::STATUS_PENDING),
                    'closed_questions' => $company->openQuestionsByStatusCount(OpenQuestion::STATUS_COMPLETED)
                ];
            }
        }

        return $data;
    }
}
