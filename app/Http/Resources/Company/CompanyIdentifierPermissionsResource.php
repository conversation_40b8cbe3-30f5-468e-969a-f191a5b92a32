<?php

namespace App\Http\Resources\Company;

use App\Models\CompanyUserPermission;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property CompanyUserPermission $resource
 */
class CompanyIdentifierPermissionsResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'user_id' => $this->resource->companyUser->user_id,
            'permission' => $this->resource->permission
        ];
    }
}
