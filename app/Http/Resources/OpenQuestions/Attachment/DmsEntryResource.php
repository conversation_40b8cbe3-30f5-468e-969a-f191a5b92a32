<?php

namespace App\Http\Resources\OpenQuestions\Attachment;

use App\Models\OpenQuestions\OpenQuestionAttachmentDmsEntry;
use Illuminate\Http\Resources\Json\JsonResource;

class DmsEntryResource extends JsonResource
{
    /**
     * @property OpenQuestionAttachmentDmsEntry $resource
     */
    public function toArray($request): array
    {
        return [
            'status' => $this->resource->status,
            'message' => $this->resource->message,
            'link_title' => $this->resource->link_title,
            'link_url' => $this->resource->link_url,
        ];
    }
}
