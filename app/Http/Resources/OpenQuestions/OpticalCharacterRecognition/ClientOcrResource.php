<?php

namespace App\Http\Resources\OpenQuestions\OpticalCharacterRecognition;

use App\Helpers\CurrencyHelper;
use App\Http\Resources\OpenQuestions\BaseClientResource;
use App\Models\OpenQuestions\Questions\OpticalCharacterRecognition;

/**
 * Class ClientOcrResource
 *
 * @property OpticalCharacterRecognition $resource
 */
class ClientOcrResource extends BaseClientResource
{
    public function toArray($request): array
    {
        return array_merge(parent::toArray($request), [
            'public_url' => $this->resource->public_url,
            'transaction_date' => $this->resource->transaction_date?->format('d-m-Y'),
            'amount' => $this->resource->amount !== null
                ? CurrencyHelper::formatAmountFromFloat($this->resource->amount)
                : null,
            'invoice_number' => $this->resource->invoice_number,
            'currency' => CurrencyHelper::convertCurrencyToSymbol($this->resource->currency),
            'description' => $this->resource->description,
        ]);
    }
}
