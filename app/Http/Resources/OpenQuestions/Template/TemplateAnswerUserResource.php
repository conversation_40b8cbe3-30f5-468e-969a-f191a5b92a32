<?php

namespace App\Http\Resources\OpenQuestions\Template;

use App\Http\Resources\JsonResourceWithoutWrapping;
use App\Models\OpenQuestions\Questions\Templates\TemplateAnswerUser;

/**
 * Class TemplateAnswerUserResource
 * @package App\Http\Resources\OpenQuestions\Template
 * @property TemplateAnswerUser $resource
 */
class TemplateAnswerUserResource extends JsonResourceWithoutWrapping
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource->companyUser->user_id,
            'fullname' => $this->resource->companyUser->user->name,
        ];
    }
}
