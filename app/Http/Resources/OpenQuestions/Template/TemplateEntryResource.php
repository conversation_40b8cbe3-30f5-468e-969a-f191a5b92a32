<?php

namespace App\Http\Resources\OpenQuestions\Template;

use App\Http\Resources\JsonResourceWithoutWrapping;
use App\Models\OpenQuestions\Questions\Templates\TemplateEntry;

/**
 * Class TemplateEntryResource
 *
 * @property TemplateEntry $resource
 */
class TemplateEntryResource extends JsonResourceWithoutWrapping
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource->id,
            'status' => $this->resource->openQuestion->status,
            'total_answers' => $this->resource->total_answers,
            'given_answers' => $this->resource->given_answers,
            'answers' => TemplateAnswerResource::collection($this->resource->templateAnswers)
        ];
    }
}
