<?php

namespace App\Http\Resources\System\TaskTools;

use App\Models\TaskFile;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property TaskFile $resource
 */
class TaskFileResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource->id,
            'filename' => $this->resource->filename,
            'display_name' => $this->resource->display_name,
            'type' => $this->resource->type,
            'extension' => $this->resource->extension,
            'account_name' => $this->resource->account->name,
            'service' => $this->resource->accountService->service->display_name,
            'service_task_id' => $this->resource->service_task_id
        ];
    }
}
