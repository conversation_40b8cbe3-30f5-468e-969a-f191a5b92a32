<?php

namespace App\Http\Resources\Scim;

use Illuminate\Http\Resources\Json\JsonResource;

abstract class AbstractQueryResource extends JsonResource
{
    /**
     * Default attributes that we will send to AzureAD.
     *
     * @var array
     */
    protected const ATTRIBUTES = [];

    /**
     * Attributes wanted in the resource.
     *
     * @var array|null
     */
    private ?array $includedAttributes = null;

    /**
     * Attributes wanted to be excluded in the resource.
     *
     * @var array|null
     */
    private ?array $excludedAttributes = null;

    public function __construct($resource, array $includedAttributes = null, array $excludedAttributes = null)
    {
        parent::__construct($resource);
        $this->includedAttributes = $includedAttributes;
        $this->excludedAttributes = $excludedAttributes;
    }

    /**
     * Get the attributes that we will only show in the response.
     *
     * @return array
     */
    public function getAttributes(): ?array
    {
        // If there are attributes to be included.
        if ($this->includedAttributes !== null) {
            return $this->includedAttributes;
        }

        // If there are attributes that need to be excluded.
        if ($this->excludedAttributes !== null) {
            // Exclude from all the attributes the ones
            // specified in the excluded attribute.
            $attributes = [];
            foreach (static::ATTRIBUTES as $key) {
                if (!\in_array($key, $this->excludedAttributes)) {
                    $attributes[] = $key;
                }
            }

            return $attributes;
        }

        // If no attributes or excluded attributes parameter was sent,
        // show all attributes.
        return static::ATTRIBUTES;
    }
}
