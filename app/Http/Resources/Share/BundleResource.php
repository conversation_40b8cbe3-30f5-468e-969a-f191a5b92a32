<?php

namespace App\Http\Resources\Share;

use App\Http\Resources\JsonResourceWithoutWrapping;
use App\Models\SharedFiles\SharedFileBundle;
use App\Support\Carbon;

/**
 * @property SharedFileBundle $resource
 */
class BundleResource extends JsonResourceWithoutWrapping
{
    public function toArray($request): array
    {
        return [
            'uuid' => $this->resource->uuid,
            'recipients_count' => $this->resource->recipients()->count(),
            'subject' => $this->resource->title,
            'date' => Carbon::parse($this->resource->created_at)->format('d-m-Y H:i:s'),
            'expire_at' => Carbon::parse($this->resource->expire_at)->format('d-m-Y H:i:s')
        ];
    }
}
