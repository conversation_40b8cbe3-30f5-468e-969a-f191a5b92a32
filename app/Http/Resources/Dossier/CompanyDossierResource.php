<?php

namespace App\Http\Resources\Dossier;

use App\Company;
use App\Services\Dossiers\DossierFolderService;
use Color;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property Company $resource
 */
class CompanyDossierResource extends JsonResource
{
    public function toArray($request): array
    {
        $data = [
            'id' => $this->resource->id,
            'title' => $this->resource->name,
            'subtitle' => '',
            'color' => Color::generateColor($this->resource->id, $this->resource->name),
        ];

        $dossierFolderService = resolve(DossierFolderService::class);
        $dossier = FolderResource::collection($dossierFolderService->getParentsForCompany($this->resource->id));

        if ($dossier->count()) {
            $tree = json_decode(json_encode($dossier->collection), 1);
            $maxDepth = $this->getMaxDepth($tree);

            for ($i = 0; $i < $maxDepth; $i++) {
                $tree = $this->removeEmptyArrays($tree);
            }

            $data['dossier'] = $tree;
        }

        return $data;
    }

    private function getMaxDepth($tree): int
    {
        $maxDepth = 1;
        foreach ($tree as $node) {
            if (!$node["isLeaf"]) {
                $maxDepth = max($maxDepth, 1 + $this->getMaxDepth($node["children"]));
            }
        }
        return $maxDepth;
    }

    private function removeEmptyArrays($arr): array
    {
        $newArr = array();
        foreach ($arr as $item) {
            if (!$item['isLeaf'] && empty($item['children'])) {
                continue;
            }
            if (!empty($item['children'])) {
                $item['children'] = $this->removeEmptyArrays($item['children']);
            }
            $newArr[] = $item;
        }
        return $newArr;
    }
}
