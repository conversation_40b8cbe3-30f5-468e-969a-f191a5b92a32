<?php

namespace App\Http\Resources\ServiceTask;

use App\Http\Resources\JsonResourceWithoutWrapping;
use App\ValueObject\ServiceTask\Filters;

/**
 * Class TaskFilterResource
 * @property Filters $resource
 */
class TaskYearFilterResource extends JsonResourceWithoutWrapping
{
    public function toArray($request): array
    {
        $years = $this->resource->years();
        asort($years); // Sort ascending for functions below.
        $data = [
            'title' => trans('common.year'),
            'name' => 'years'
        ];
        foreach ($years as $year) {
            $data['items'][] = [
                'label' => $year,
                'value' => $year,
                'checked' => $this->resource->tab() === 'completed' // Only pre-select options on completed tab
                    && (
                        $year === end($years) // Year is last item in the list (last year with task)
                        || $this->hasInitialFilter('years', $year) // Has filter supplied from frontend
                        || $this->yearShouldBeChecked($year) // If year should be checked based on date.
                    )
            ];
        }
        return $data;
    }

    private function yearShouldBeChecked(int $year): bool
    {
        // We want to select the current year AND the year before if we are in the first quarter of the year.
        // Unless the current year does not have tasks, then select the last year that has tasks.
        $now = now();

        if ($now->quarter !== 1 && $year !== $now->year) {
            return false;
        }

        if ($year < ($now->year - 1)) {
            return false;
        }

        return true;
    }

    private function hasInitialFilter(string $filter, $value): bool
    {
        if (empty($this->resource->initialFilters()) || !array_key_exists($filter, $this->resource->initialFilters())) {
            return false;
        }

        foreach ($this->resource->initialFilters()[$filter] as $selectedFilter) {
            if ($selectedFilter['value'] === $value) {
                return true;
            }
        }

        return false;
    }
}
