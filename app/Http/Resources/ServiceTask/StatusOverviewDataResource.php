<?php

namespace App\Http\Resources\ServiceTask;

use App\Models\ServiceTask\PkisDossiers\ServiceTaskPkiDossierStatus;
use App\Models\TaskFile\Placeholder;
use App\ServiceTask;
use App\ServiceTaskResponse;
use App\TaskAuditLog;
use App\Support\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class StatusOverviewDataResource
 *
 * @property ServiceTask $resource
 *
 * @package App\Http\Resources\ServiceTask
 */
class StatusOverviewDataResource extends JsonResource
{
    public function toArray($request): array
    {
        $company = $this->resource->company;
        $sentBy = $this->resource->sentBy;
        $period = $this->resource->getPeriod();

        $array = [
            'id' => $this->resource->id,
            'service' => $this->resource->accountService->service->reference_name,
            'type' => $this->resource->type,
            'identifier' => $this->resource->getIdentifier(),
            'date_start' => Carbon::parse($this->resource->date_start)->toDateString(),
            'date_end' => Carbon::parse($this->resource->date_end)->toDateString(),
            'company_id' => $company->id,
            'company_name' => $company->name,
            'title' => $this->resource->title,
            'subtitle' => $this->resource->subtitle,
            'companySize' => $this->resource->companySize(),
            'company_internal_client_id' => $company->internal_client_id,
            'sent_by' => $sentBy?->name,
            'sent_at' => Carbon::parse($this->resource->sent_at)->isoFormat('DD-MM-YYYY'),
            'last_reminded_at' => $this->getLastRemindedAt($this->resource),
            'payment_status' => $this->resource->getPaymentStatus(),
            'connected_task_identifiers' => !empty($this->resource->taskGroup) ?
                $this->resource->taskGroup->getConnectedTaskIdentifiers($this->resource->id) : null,
            'frequency' => $period?->frequency()
        ];

        $currentRound = $this->resource->taskResponsesLastRound();

        // Add the Responses summary and the declined count.
        $array['responses_summary'] = '';
        $array['declined_count'] = '';
        $array['signed_signatures_count'] = '';
        $array['signatures_count'] = '';

        if ($currentRound !== null) {
            $array['responses_summary'] = $currentRound->getNumberOfApprovedTasks(
            ) + $currentRound->getNumberOfTasksToInform() . '/' . $currentRound->getTotalTasks();
            $array['declined_count'] = $currentRound->getNumberOfDeclinedTasks();

            $signaturesCount = 0;
            $signedSignaturesCount = 0;

            foreach ($this->resource->files as $file) {
                foreach ($file->signingPlaceholders as $placeholder) {
                    $signaturesCount++;
                    if ($placeholder->status === Placeholder::STATUS_APPLIED) {
                        $signedSignaturesCount++;
                    }
                }
            }

            if ($signaturesCount > 0) {
                $array['signed_signatures_count'] = $signedSignaturesCount;
                $array['signatures_count'] = $signaturesCount;
            }
        }

        $array['requesting_party_status'] = $this->resource->getRequestingPartyStatusTranslated();

        $array['signing_status'] = null;
        if (!empty($this->resource->getLastPkisDossier())) {
            $signingStatus = $this->resource->getSigningStatus();
            if ($signingStatus instanceof ServiceTaskPkiDossierStatus) {
                $array['signing_status'] = trans('service_task.signing.pkisigning.status.' . $signingStatus->status);
            }
        }
        $array['raw_status'] = $this->resource->status;

        $array['status'] = $this->resource->statusTranslated();
        $array['status_state'] = $this->statusState($this->status);
        $array['managed_by_me'] = in_array($this->resource->company_id, auth()->user()->companyIds());
        $array['dms'] = $this->getLastDmsAuditLog();
        $array['updated_at'] = $this->resource->updated_at->isoFormat('DD-MM-YYYY');

        return $array;
    }

    /**
     * Compute color of the status. Only for forecast status the color will change.
     *
     * @param string $status
     * @return string
     * @throws \Exception
     */
    private function statusState(string $status): string
    {
        $state = 'normal';

        if ($status === ServiceTask::STATUS_FORECAST) {
            // if status is forecast and it is too late to create the task
            $now = Carbon::now();
            if ($now->greaterThan($this->resource->getDeadline())) {
                $state = 'late';
            } elseif ($now->diffInDays($this->resource->getDeadline()) <= 7) {
                // if status is forecast and we are in the last week
                $state = 'soon';
            }
        }
        return $state;
    }

    /**
     * Returns latest sent at date for a task or latest reminded_at date from task responses if present.
     * The date is returned in format:  DD-MM-YYYY
     * @param ServiceTask $task
     * @return string|null
     */
    private function getLastRemindedAt(ServiceTask $task): ?string
    {
        // responses are eager loaded onto this task model.
        $responses = $task->taskResponses->sortByDesc(ServiceTaskResponse::REMINDED_AT);
        $latestReminder = $responses->first()?->reminded_at;

        if (!is_null($latestReminder)) {
            return Carbon::parse($latestReminder)->isoFormat('DD-MM-YYYY');
        }

        return null;
    }

    public function getLastDmsAuditLog(): ?string
    {
        return $this->resource->auditLogs
            ->whereIn(TaskAuditLog::ACTION, [
                TaskAuditLog::ERROR_SENDING_TO_DMS,
                TaskAuditLog::DOCUMENT_SENT_TO_DMS
            ])
            ->sortByDesc(TaskAuditLog::CREATED_AT)
            ->first()
            ?->message;
    }
}
