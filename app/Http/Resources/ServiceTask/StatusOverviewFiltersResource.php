<?php

namespace App\Http\Resources\ServiceTask;

use App\ValueObject\Pagination\StatusOverviewFilters;
use Illuminate\Http\Resources\Json\JsonResource;

class StatusOverviewFiltersResource extends JsonResource
{
    public function toArray($request): array
    {
        $data = [
            'services' => [],
            'types' => [],
            'years' => [],
            'months' => [],
            'quarters' => [],
            'statuses' => [],
            'upload_types' => [],
            'company_tags' => []
        ];

        foreach ($this->resource['services'] as $service) {
            $data['services'][] = $service;
        }

        foreach ($this->resource['types'] as $type) {
            $data['types'][] = [
                'name' => trans('service_task.' . $type . '.title'),
                'value' => $type,
            ];
        }

        foreach ($this->resource['years'] as $year) {
            $data['years'][] = [
                'name' => !empty($year) ? $year : trans('datetime.years.other'),
                'value' => $year,
            ];
        }

        foreach ($this->resource['months'] as $month) {
            $data['months'][] = [
                'name' => trans('datetime.months.' . $month),
                'value' => $month,
            ];
        }

        foreach ($this->resource['quarters'] as $months) {
            $data['quarters'][] = [
                'name' => trans('datetime.quarter', [
                    StatusOverviewFilters::MONTH_START => trans('datetime.months.' . $months[0]),
                    StatusOverviewFilters::MONTH_END => trans('datetime.months.' . $months[1])
                ]),
                'value' => [
                    StatusOverviewFilters::MONTH_START => $months[0],
                    StatusOverviewFilters::MONTH_END => $months[1]
                ],
            ];
        }

        foreach ($this->resource['statuses'] as $status) {
            $data['statuses'][] = [
                'name' => trans(
                    'service_task.status.' . $status,
                    ['name' => trans('service_task.sbr_yearwork.recipient')]
                ),
                'value' => $status,
            ];
        }

        foreach ($this->resource['company_tags'] as $tag) {
            $data['company_tags'][] = [
                'name' => $tag,
                'value' => $tag,
            ];
        }

        foreach ($this->resource['upload_types'] as $type) {
            $data['upload_types'][] = [
                'name' => $type['title'],
                'value' => $type['id']
            ];
        }

        return $data;
    }
}
