<?php

namespace App\Http\Resources\ServiceTask;

use App\Http\Resources\JsonResourceWithoutWrapping;
use App\ValueObject\ServiceTask\Filters;

/**
 * Class TaskFilterResource
 * @property Filters $resource
 */
class TaskFilterResource extends JsonResourceWithoutWrapping
{
    public function toArray($request): array
    {
        return [
            TaskYearFilterResource::make($this->resource),
            TaskTypeFilterResource::make($this->resource),
            TaskUploadTypeFilterResource::make($this->resource)
        ];
    }
}
