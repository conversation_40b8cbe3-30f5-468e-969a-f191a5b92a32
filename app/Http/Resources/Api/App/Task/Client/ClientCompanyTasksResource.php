<?php

namespace App\Http\Resources\Api\App\Task\Client;

use App\Company;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;

/**
 * @property Company $resource
 */
class ClientCompanyTasksResource extends JsonResource
{
    public static bool $isSingle;
    public static string $language;

    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'isExpanded' => self::$isSingle,
            'tasks' => ClientTasksResource::collectionCustom($this->resource->taskResponses, self::$language),
            'count' => $this->resource->taskResponses->count()
        ];
    }

    public static function collectionCustom(
        Collection $resource,
        bool $isSingle,
        string $language
    ): AnonymousResourceCollection {
        self::$isSingle = $isSingle;
        self::$language = $language;
        return self::collection($resource);
    }
}
