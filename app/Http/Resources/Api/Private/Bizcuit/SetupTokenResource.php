<?php

namespace App\Http\Resources\Api\Private\Bizcuit;

use App\Token;
use App\User;
use App\ValueObject\MobileNumber;
use Illuminate\Http\Resources\Json\JsonResource;

/** @property array{token: Token, user: User} $resource */
class SetupTokenResource extends JsonResource
{
    public function toArray($request): array
    {
        $result = [
            'account' => [
                'name' => $this->resource['user']->account->name,
                'url' => $this->resource['user']->account->uri,
            ],
            'user' => [
                'name' => $this->resource['user']->name,
            ],
        ];
        if ($this->resource['token'] instanceof Token) {
            $result['token'] = [
                'token' => $this->resource['token']->token,
                'expires_at_utc' => $this->resource['token']->expiration_date->toDateTimeString(),
                'expires_at' => $this->resource['token']->getLocalExpirationDate()->toDateTimeString()
            ];
        } elseif (empty($this->resource['user']->mobile)) {
            $result['error'] = 'mobile_missing';
        } elseif (!MobileNumber::isValid($this->resource['user']->mobile)) {
            $result['error'] = 'mobile_invalid';
        } else {
            $result['error'] = 'unknown_error';
        }
        return $result;
    }
}
