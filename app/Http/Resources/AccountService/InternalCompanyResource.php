<?php

namespace App\Http\Resources\AccountService;

use App\Company;
use App\Helpers\Color\Color;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property Company $resource
 */
class InternalCompanyResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'color' => Color::generateColor($this->resource->id, $this->resource->name),
        ];
    }
}
