<?php

namespace App\Http\Resources\Account;

use App\Account;
use App\Context;
use App\Helpers\Ip;
use App\Service;
use App\Support\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property Context $resource
 */
class SettingsSecurityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $hasWhatsappBusiness = $this->resource->account->hasService(Service::WHATSAPP_BUSINESS);
        return [
            'auth_methods' => $this->resource->auth_methods,
            'available_auth_methods' => array_combine(
                $this->removeSecretFromAuthMethods(),
                array_map(
                    function ($v) {
                        return trans('context.field_auth_method_' . $v);
                    },
                    $this->removeSecretFromAuthMethods()
                )
            ),
            'auth_methods_enforcement_at' => $this->auth_methods_enforcement_at,
            'login_autocomplete' => $this->account->login_autocomplete,
            'auth_secret_expire_days' => $this->auth_secret_expire_days,
            'session_lifetime_max' => $this->session_lifetime_max,
            'slider_max' => ($this->isMain() ? 600 : $this->parent->session_lifetime_max),
            'session_lifetime_max_propagate' => ($this->hasAttribute('session_lifetime_default', true)
                && $this->session_lifetime_max == $this->session_lifetime_default),
            'single_session_enforced' => (bool)$this->single_session_enforced,
            'inherit_security_settings' => $this->inherit_security_settings,
            'has_license_ip_whitelist' => $this->account->hasLicense('ip_whitelist'),
            'ip_whitelist_enabled' => $this->ip_whitelist_enabled,
            'ip_whitelist' => implode('; ', $this->ip_whitelist),
            'ip_whitelist_mode' => $this->ip_whitelist_mode,
            'available_ip_whitelist_modes' => [
                ['key' => 'standard', 'name' => trans('context.field_ip_whitelist_mode_standard')],
                ['key' => 'trusted', 'name' => trans('context.field_ip_whitelist_mode_trusted')],
                ['key' => 'enforced', 'name' => trans('context.field_ip_whitelist_mode_enforced')],
            ],
            'ip_whitelist_modes_notes' => [
                'trusted' => trans('context.field_ip_whitelist_note_trusted'),
                'enforced' => trans('context.field_ip_whitelist_note_enforced'),
                'standard' => trans('context.field_ip_whitelist_note_standard'),
                'disabled' => trans('context.field_ip_whitelist_note_standard')
            ],
            'current_ip' => Ip::format($request->ip()),
            'allow_copy_password' => $this->account->settings[Account::SETTING_ALLOW_COPY_PASSWORD] ?? false,
            'default_front_auth_method' => $this->account->settings[Account::SETTING_DEFAULT_FRONT_AUTH_METHOD] ?? null,
            Account::SETTING_ALLOW_EMAIL_FRONT_AUTH => $this->account->settings[Account::SETTING_ALLOW_EMAIL_FRONT_AUTH] ?? false, // phpcs:ignore
            //phpcs:ignore
            'has_whatsapp_business' => $hasWhatsappBusiness,
            'allow_change_preferred_communication_channel' => $this->when(
                $hasWhatsappBusiness,
                function () {
                    return $this->resource->account->settings[Account::SETTING_ALLOW_CHANGE_PREFERRED_COMMUNICATION_CHANNEL] ?? true; //phpcs:ignore
                }
            ),
        ];
    }

    private function removeSecretFromAuthMethods()
    {
        $accountAuthMethods = $this->available_auth_methods;

        // We compare the account created date with the date of the release
        // where we only allow SMS and TOTP auth methods
        // This is temporary. In the future all account will only have SMS and TOTP
        if ($this->account->created_at > Carbon::parse('04-06-2020')) {
            if (($key = array_search(Account::AUTH_METHOD_SECRET, $accountAuthMethods)) !== false) {
                unset($accountAuthMethods[$key]);
            }
        }
        return $accountAuthMethods;
    }
}
