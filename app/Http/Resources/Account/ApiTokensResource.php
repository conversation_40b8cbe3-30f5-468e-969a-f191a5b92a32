<?php

namespace App\Http\Resources\Account;

use Illuminate\Http\Resources\Json\JsonResource;
use Laravel\Sanctum\PersonalAccessToken;

/**
 * @property PersonalAccessToken $resource
 */
class ApiTokensResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource->id,
            'masked_token' => $this->resource->id . '|' . str_repeat('*', 40 - strlen($this->resource->id)),
            'created_at' => $this->created_at->toDateString(),
        ];
    }
}
