<?php

namespace App\Http\Resources\Account;

use App\Helpers\Color\Color;
use Illuminate\Http\Resources\Json\JsonResource;

class AccountManagerMembershipResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        $user = auth()->user();

        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'context_id' => $this->context_id,
            'name' => $this->user->showLabel(),
            'color' => Color::generateColor($this->user->id, $this->user->auth_id),
            'membership_count' => $this->user->memberships()->count(),
            'can_be_deleted' => ($this->user_id != $user->id),
            'this_is_you' => $this->user_id == auth()->user()->id
        ];
    }
}
