<?php

namespace App\Http\Resources\Service\Signing;

use App\Helpers\Color\Color;
use App\Http\Resources\JsonResourceWithoutWrapping;

class QualifiedSigningUsersResource extends JsonResourceWithoutWrapping
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource->id,
            'user_id' => $this->resource->user_id,
            'name' => $this->resource->name,
            'email' => $this->resource->email,
            'color' => Color::generateColor($this->resource->id, $this->resource->name),
        ];
    }
}
