<?php

namespace App\Http\Resources\Service\Declarations\Twinfield;

use App\AccountService;
use App\Services\Gateway\Twinfield\TwinfieldTaskProvider;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class StatusesResource
 * @property AccountService $resource
 * @package App\Http\Resources\AccountService
 */
class StatusesResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'twinfield_statuses_options' => TwinfieldTaskProvider::getTwinfieldStatusesFormOptions(),
            'twinfield_statuses' => $this->resource->getTwinfieldStatuses()
        ];
    }
}
