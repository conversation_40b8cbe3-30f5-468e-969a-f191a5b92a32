<?php

namespace App\Http\Resources\Context;

use App\Context;
use Color;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property Context $resource
 */
class AvailableGroupResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'color' => Color::generateColor($this->resource->id, $this->resource->name),
            'members_count' => count($this->resource->memberships) . ' ' . trans('context.amount_of_memberships'),
            'has_only_colleagues' => $this->resource->hasOnlyColleagues()
        ];
    }
}
