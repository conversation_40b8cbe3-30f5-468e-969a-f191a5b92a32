<?php

namespace App\Http\Resources\ServiceTaskSigningRequest;

use App\Http\Resources\ServiceTask\TaskResource;
use App\ServiceTaskSigningRequest;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Class IndexResource
 * @package App\Http\Resources\ServiceTaskSigningRequest
 * @property ServiceTaskSigningRequest $resource
 */
class IndexResource extends JsonResource
{
    public function toArray($request)
    {
        $task = $this->resource->serviceTask->refresh();
        return [
            'id' => $this->resource->id,
            'title' => $task->title,
            'subtitle' => $task->generateSubTitle(),
            'subsubtitle' => $task->company->name,
            'image' => $task->entityImage(),
            'user_id' => $this->resource->user_id,
            'task' => TaskResource::make($task),
            'signed' => $this->resource->completed,
            'signed_at' => $this->resource->completed_at,
        ];
    }
}
