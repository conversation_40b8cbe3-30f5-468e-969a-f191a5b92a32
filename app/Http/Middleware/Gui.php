<?php

namespace App\Http\Middleware;

use App;
use App\Account;
use App\Exceptions\Api\UnauthorizedException;
use App\Support\Carbon;
use Closure;
use Illuminate\Http\Request;
use View;

class Gui
{
    /**
     * Handle an incoming request.
     * @param Request $request
     * @param Closure $next
     * @return mixed
     * @throws UnauthorizedException When the user/account does not have GUI access.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $account = Account::getHostAccount($request);

        if ($account !== null) {
            $this->setRequestVariables($account);

            if (!$account->canUse('gui')) {
                throw new UnauthorizedException('Account not have access to GUI');
            }
        }

        if (auth()->check()) {
            $user = auth()->user();
            if (!$user->canUse('gui')) {
                throw new UnauthorizedException('User does not have access to GUI');
            }
        }

        return $next($request);
    }

    private function setRequestVariables(Account $account): void
    {
        View::share('account', $account);
        App::setLocale($account->language);
        Carbon::setLocale($account->language);
    }
}
