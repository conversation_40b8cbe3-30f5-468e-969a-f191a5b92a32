<?php

namespace App\Http\Middleware;

use App\Account;
use App\Services\System\HeraldService;
use Closure;
use Illuminate\Http\Request;

/**
 * Class HeraldHeaders
 * @package App\Http\Middleware
 *
 * This middleware should be used by API endpoints used by the browser extension.
 * The browser extension can check the modification time in the headers to decide when to update its settings.
 * HTTP header X-SL-settings-last-modified with unix timestamp as value.
 */
class HeraldHeaders
{
    public function handle(Request $request, Closure $next)
    {
        $account = Account::getHostAccount($request);
        $response = $next($request);
        try {
            $heraldService = resolve(HeraldService::class);
            $time = $heraldService->getSettingsModificationDate($account);
            $response->headers->set('X-SL-settings-last-modified', $time);
        } catch (\Throwable $e) {
            // ignore exceptions so that the request is never blocked by one
        }
        return $response;
    }
}
