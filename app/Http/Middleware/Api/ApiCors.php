<?php

namespace App\Http\Middleware\Api;

use App\Account;
use App\Helpers\Http\Origin;
use Closure;
use Illuminate\Http\Request;
use Log;

class ApiCors
{
    public function handle(Request $request, Closure $next)
    {
        $origin = Origin::get($request);
        $allowed = !empty($origin)
            && Origin::isRequestAllowed($request, Account::getHostAccount($request)->allowed_origins);
        $response = $request->getMethod() === 'OPTIONS' ? response('') : $next($request);
        return $this->addCorsHeadersIfAllowed($request, $response, $allowed, $origin);
    }

    public function addCorsHeadersIfAllowed(Request $request, $response, bool $allowed, string $origin)
    {
        if (!empty($origin) && !$allowed) {
            Log::channel('api')->warning('Rejected origin ' . $origin . ' for ' . $request->fullUrl());
        }

        if (!$allowed) {
            return $response;
        }

        Log::channel('api')->info('Accepted origin ' . $origin . ' for ' . $request->fullUrl());

        return $response->header('Access-Control-Allow-Origin', $origin)
            ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
            ->header('Access-Control-Allow-Credentials', 'true')
            ->header(
                'Access-Control-Allow-Headers',
                'X-Requested-With, Content-Type, viewport-x, viewport-y, x-csrf-token, x-securelogin-ext, Authorization'
            );
    }
}
