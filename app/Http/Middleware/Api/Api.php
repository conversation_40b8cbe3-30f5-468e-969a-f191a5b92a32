<?php

namespace App\Http\Middleware\Api;

use App\Account;
use App\Exceptions\Api\GoneException;
use App\Exceptions\Api\NotFoundException;
use App\Exceptions\Api\UnauthorizedException;
use Closure;
use Illuminate\Http\Request;

class Api
{
    /**
     * Handle an incoming request.
     * DO NOT translate these exceptions. They are not meant for end users. The API response is only meant for
     * developers who might depend on the text being the same regardless of language.
     * @param Request $request
     * @param Closure $next
     * @return mixed
     * @throws GoneException
     * @throws NotFoundException
     * @throws UnauthorizedException When the user/account does not have API access.
     */
    public function handle(Request $request, Closure $next): mixed
    {
        $account = Account::getHostAccount($request);

        if (empty($account)) {
            throw new NotFoundException(trans('This environment does not exist.'));
        }

        if ($account->isBlocked()) {
            throw new GoneException(trans('This environment is no longer available.'));
        }

        if (!$account->canUse('api')) {
            throw new UnauthorizedException(trans('This environment does not have permission to use the API.'));
        }
        $user = auth()->user();
        if (!is_null($user) && !$user->canUse('api')) {
            throw new UnauthorizedException(trans('This user does not have permission to use the API.'));
        }

        return $next($request);
    }
}
