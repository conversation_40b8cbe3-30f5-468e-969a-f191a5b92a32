<?php

namespace App\Http;

use App\Http\Requests\Request;
use App\OldModel;

class ApiRequest extends Request
{
    protected $consumer = null;

    public function rules(): array
    {
        return [];
    }

    public function authorize(): bool
    {
        $consumer = $this->getModelObject();

        //Only authorized when a valid model is set as issuer
        return !empty($consumer) && $consumer instanceof OldModel;
    }

    public function getConsumer()
    {
        return $this->consumer;
    }
}
