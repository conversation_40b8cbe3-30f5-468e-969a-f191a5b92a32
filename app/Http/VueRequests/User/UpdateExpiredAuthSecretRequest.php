<?php

namespace App\Http\VueRequests\User;

use App\Http\VueRequests\PostRequest;

/**
 * @property string $auth_secret
 * @property string $auth_secret_new
 * @property string $auth_secret_verification
 */
class UpdateExpiredAuthSecretRequest extends PostRequest
{
    public function authorize(): bool
    {
        return auth()->check();
    }

    public function rules(): array
    {
        return [
            'auth_secret' => 'required_with:auth_secret_new|hash_check:' . $this->user()->auth_secret,
            'auth_secret_new' => 'strong_password|limit_password|different:auth_secret',
            'auth_secret_verification' => 'required_with:auth_secret_new|same:auth_secret_new'
        ];
    }

    public function messages(): array
    {
        return [
            'auth_secret.required_with' => trans('user.edit_security_settings.fields.auth_secret.required_with'),
            'auth_secret.hash_check' => trans('user.edit_security_settings.fields.auth_secret.hash_check'),
            'auth_secret_new.strong_password' => trans(
                'user.edit_security_settings.fields.auth_secret_new.strong_password'
            ), // phpcs:ignore
            'auth_secret_new.different' => trans('user.edit_security_settings.fields.auth_secret_new.different'),
            'auth_secret_verification.required_with' => trans(
                'user.edit_security_settings.fields.auth_secret_verification.required_with'
            ), // phpcs:ignore
            'auth_secret_verification.same' => trans('user.edit_security_settings.fields.auth_secret_verification.same')
        ];
    }
}
