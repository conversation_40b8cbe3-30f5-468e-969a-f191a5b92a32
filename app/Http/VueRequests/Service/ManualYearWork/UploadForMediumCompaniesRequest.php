<?php

namespace App\Http\VueRequests\Service\ManualYearWork;

use App\Http\VueRequests\PostRequest;
use Config;

/**
 * Class UploadForMediumCompaniesRequest
 * @package App\Http\VueRequests\Service\ManualYearWork
 * @property int $company_id
 */
class UploadForMediumCompaniesRequest extends PostRequest
{
    protected $model_name = 'ServiceTask';
    protected $action = 'store';

    public function rules(): array
    {
        return [
            'publication_document' => 'file|max:' . $this->getFileMaximumSize() . '|extensions:xbrl',
            'audit_report' => 'file|max:' . $this->getFileMaximumSize() . '|extensions:xbrl',
            'lor' => 'file|max:' . $this->getFileMaximumSize() . '|extensions:pdf,doc,docx,ppt,pptx',
            'ava_notulen' => 'file|max:' . $this->getFileMaximumSize() . '|extensions:pdf,doc,docx,ppt,pptx',
            'annual_report' => 'file|max:' . $this->getFileMaximumSize() . '|extensions:pdf,doc,docx,ppt,pptx',
            'signature_xml' => 'file|max:' . $this->getFileMaximumSize(),
            'company_id' => 'int|required|min:1',
            'account_service_id' => 'int|required|min:1',
            'signing_colleague' => 'int|min:1',
        ];
    }

    public function messages(): array
    {
        return [
            'publication_document.file' => trans('account_service.addXbrlFiles.field.xbrl_file.file'),
            'publication_document.max' => trans('account_service.addXbrlFiles.max'),
            'publication_document.extensions' => trans('account_service.addXbrlFiles.field.xbrl_file.file_type'),
            'audit_report.file' => trans('account_service.addXbrlFiles.field.av_xbrl_file.file'),
            'audit_report.max' => trans('account_service.addXbrlFiles.max'),
            'audit_report.extensions' => trans('account_service.addXbrlFiles.field.av_xbrl_file.file_type'),
            'lor.file' => trans('account_service.addXbrlFiles.field.pdf_file.file'),
            'lor.max' => trans('account_service.addXbrlFiles.max'),
            'lor.extensions' => trans('account_service.addXbrlFiles.field.pdf_file.file_type'),
            'ava_notulen.file' => trans('account_service.addXbrlFiles.field.pdf_file.file'),
            'ava_notulen.max' => trans('account_service.addXbrlFiles.max'),
            'ava_notulen.extensions' => trans('account_service.addXbrlFiles.field.pdf_file.file_type'),
            'annual_report.file' => trans('account_service.addXbrlFiles.field.pdf_file.file'),
            'annual_report.max' => trans('account_service.addXbrlFiles.max'),
            'annual_report.extensions' => trans('account_service.addXbrlFiles.field.pdf_file.file_type'),
            'company_id.required' => $this->getCommonErrorTranslation(),
            'account_service_id.required' => $this->getCommonErrorTranslation()
        ];
    }

    protected function getFileMaximumSize(): string
    {
        return Config::get('services.manual_tasks.upload_max_size');
    }
}
