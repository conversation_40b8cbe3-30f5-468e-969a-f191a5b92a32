<?php

namespace App\Http\VueRequests\Service\ManualYearWork;

use App\Http\VueRequests\PostRequest;
use App\ServiceTaskSigningUser;

class SetSigningUserRequest extends PostRequest
{
    protected $model_name = ServiceTaskSigningUser::class;
    protected $action = 'store';

    public function authorize(): bool
    {
        return $this->user()->isInternal();
    }

    public function rules(): array
    {
        return [
            'service_task_id' => 'int|required',
            'signing_user_id' => 'int|required'
        ];
    }

    public function messages(): array
    {
        return [
            'service_task_id.required' => $this->getCommonErrorTranslation(),
            'signing_user_id.required' => $this->getCommonErrorTranslation(),
        ];
    }
}
