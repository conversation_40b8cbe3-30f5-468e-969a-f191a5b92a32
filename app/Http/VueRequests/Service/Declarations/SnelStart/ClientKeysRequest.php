<?php

namespace App\Http\VueRequests\Service\Declarations\SnelStart;

use App\Http\VueRequests\Request;

/**
 * @property string $client_key
 */
class ClientKeysRequest extends Request
{
    public function rules(): array
    {
        return [
            'ActionType' => 'required|string|in:Create,Regenerate,Delete,CreateMultiple',
            'ReferenceKey' => 'required|string|in:' . config('services.snelstart.subscription_key'),
            'KoppelSleutel' => 'string|nullable',
            'KoppelInformatie' => 'array|nullable',
            'KoppelInformatie.*.KoppelSleutel' => 'string|nullable',
            'KoppelInformatie.*.AdministratieNaam' => 'string|nullable',
            'KoppelInformatie.*.AdministratieIdentifier' => 'string|nullable',
        ];
    }

    public function authorize(): bool
    {
        return true;
    }

    public function clientKeys(): ?array
    {
        return $this->post('KoppelInformatie');
    }

    public function clientKey(): ?string
    {
        return $this->post('KoppelSleutel');
    }

    public function actionType(): string
    {
        return $this->post('ActionType');
    }
}
