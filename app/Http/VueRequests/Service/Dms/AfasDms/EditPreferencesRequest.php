<?php

namespace App\Http\VueRequests\Service\Dms\AfasDms;

use App\Http\VueRequests\PostRequest;

/**
 * Class EditPreferencesRequest
 * @package App\Http\VueRequests\Service\AfasDms
 * @property array $preferences
 * @property bool $dms_audit_log
 * @property bool $enable_references
 * @property bool $send_template_response
 * @property bool $always_send_client_question_attachments
 * @property bool $require_dms_for_tasks
 */
class EditPreferencesRequest extends PostRequest
{
    public function authorize(): bool
    {
        return $this->user()->isManagerOfHostAccount() || $this->user()->isAdminUser();
    }

    public function rules(): array
    {
        return [
            'preferences' => 'array|required',
            'preferences.*.type' => 'required',
            'preferences.*.dossierItem' => 'required',
            'preferences.*.destination' => 'required',
            'dms_audit_log' => 'boolean|required',
            'send_template_response' => 'boolean|required',
            'always_send_client_question_attachments' => 'boolean|required',
            'enable_references' => 'boolean|required',
            'require_dms_for_tasks' => 'boolean|required'
        ];
    }

    public function attributes(): array
    {
        return [
            'preferences.*.type' => trans('service.afas_dms.fields.type'),
            'preferences.*.dossierItem' => trans('service.afas_dms.fields.dossier_item'),
            'preferences.*.destination' => trans('service.afas_dms.fields.destination'),
        ];
    }

    public function accountServiceId(): int
    {
        return $this->route('account_service_id');
    }
}
