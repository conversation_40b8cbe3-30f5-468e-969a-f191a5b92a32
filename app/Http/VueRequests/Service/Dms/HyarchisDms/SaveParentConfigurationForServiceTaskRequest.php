<?php

namespace App\Http\VueRequests\Service\Dms\HyarchisDms;

use App\Http\VueRequests\Request;

/**
 * Class SaveConfigurationRequest.
 *
 * @property string $service_task_id
 * @property array $hyarchis_path
 */
class SaveParentConfigurationForServiceTaskRequest extends Request
{
    public function authorize(): bool
    {
        return $this->user()->isInternal();
    }

    public function rules(): array
    {
        return [
            'service_task_id' => 'required|int|min:0',
            'hyarchis_path' => 'array',
            'hyarchis_path.*.id' => 'required|int',
            'hyarchis_path.*.name' => 'required|string'
        ];
    }
}
