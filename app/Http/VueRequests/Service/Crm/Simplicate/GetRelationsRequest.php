<?php

namespace App\Http\VueRequests\Service\Crm\Simplicate;

use App\Http\VueRequests\GetRequest;

class GetRelationsRequest extends GetRequest
{
    public function authorize(): bool
    {
        return $this->user()->isManager();
    }

    public function rules(): array
    {
        return [];
    }

    public function accountServiceId(): int
    {
        return $this->route('account_service_id');
    }
}
