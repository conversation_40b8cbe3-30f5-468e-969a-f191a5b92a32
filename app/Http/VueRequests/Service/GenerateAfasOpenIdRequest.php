<?php

namespace App\Http\VueRequests\Service;

use App\Http\VueRequests\PostRequest;

/**
 * @property mixed $account_service_id
 * @property mixed $afas_environment_number
 */
class GenerateAfasOpenIdRequest extends PostRequest
{
    protected $model_name = 'Account';
    protected $action = 'generateAfasOpenId';

    public function rules(): array
    {
        return [
            'afas_environment_number' => 'required|digits:5',
            'account_service_id' => 'required|exists:account_services,id',
            'relying_party' => 'required|max:255'
        ];
    }

    public function messages(): array
    {
        return [
            'afas_environment_number.required' => trans('account.afas.environment_number.required'),
            'afas_environment_number.digits' => trans('account.afas.environment_number.digits'),
            'account_service_id.required' => $this->getCommonErrorTranslation(),
            'account_service_id.exists' => $this->getCommonErrorTranslation(),
        ];
    }
}
