<?php

namespace App\Http\VueRequests\System\TestingTools;

use App\Http\VueRequests\GetRequest;

/**
 * @property int year
 * @property int month
 */
class AccountBillingTotalsRequest extends GetRequest
{
    public function authorize(): bool
    {
        return $this->user()->isAdminUser();
    }

    public function rules(): array
    {
        return [
            'year' => 'required|integer:min:2020',
            'month' => 'required|integer:min:1|max:12',
        ];
    }
}
