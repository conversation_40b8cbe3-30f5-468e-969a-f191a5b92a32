<?php

namespace App\Http\VueRequests\Task;

use App\Http\VueRequests\GetRequest;

/**
 * Class DeleteRequest
 * @package App\Http\VueRequests\Task
 * @property string $country_code
 * @property string $vat_number
 */
class ValidateVatRequest extends GetRequest
{
    protected $model_name = 'ServiceTask';
    protected $action = 'validateVat';

    public function rules(): array
    {
        return [
            'country' => 'required|max:2|min:2|alpha',
            'vat' => 'required|max:64|alpha_num'
        ];
    }

    /**
     * Pass route parameters to data so that validation rules can be applied.
     * @param null $keys
     * @return array
     */
    public function all($keys = null): array
    {
        $data = parent::all($keys);
        $data['country'] = $this->route('country');
        $data['vat'] = $this->route('vat');
        return $data;
    }

    public function messages(): array
    {
        return [
            'country.required' => $this->getCommonErrorTranslation(),
            'country.min' => $this->getCommonErrorTranslation(),
            'country.max' => $this->getCommonErrorTranslation(),
            'vat.required' => $this->getCommonErrorTranslation(),
            'vat.max' => $this->getCommonErrorTranslation(),
        ];
    }
}
