<?php

namespace App\Http\VueRequests\Task;

use App\Http\VueRequests\PostRequest;
use App\ServiceTask;
use App\ServiceTaskResponse;

/**
 * Class Task2FARequest
 * @package App\Http\VueRequests\Task
 * @property string $redirect_url
 * @property string $token
 * @property bool $reset_totp
 */
class Task2FARequest extends PostRequest
{
    protected $model_name = ServiceTask::class;
    protected $action = 'show2FA';

    public function rules(): array
    {
        return [
            'redirect_url' => 'required|url',
            'token' => 'required|exists:service_task_responses,token'
        ];
    }

    public function messages(): array
    {
        return [
            'redirect_url.required' => $this->getCommonErrorTranslation(),
            'redirect_url.url' => $this->getCommonErrorTranslation(),
            'token.required' => $this->getCommonErrorTranslation(),
            'token.exists' => $this->getCommonErrorTranslation(),
        ];
    }

    public function authorize(): bool
    {
        /** @var ServiceTaskResponse $serviceTaskResponse */
        $serviceTaskResponse = ServiceTaskResponse::where('token', $this->token)->firstOrFail();
        return !empty($serviceTaskResponse)
            && !empty($serviceTaskResponse->user)
            && $serviceTaskResponse->user->canHaveTaskSession();
    }
}
