<?php

namespace App\Http\VueRequests\Task;

use App\Http\VueRequests\PostRequest;
use App\ServiceTask;
use Illuminate\Http\UploadedFile;

/**
 * @property int $task_id
 * @property UploadedFile $file
 */
class UploadAdditionalTaskFileRequest extends PostRequest
{
    protected $model_name = ServiceTask::class;

    public function rules(): array
    {
        return [
            'task_id' => 'required|exists:service_tasks,id',
            'file' => 'required|file|extensions:pdf,doc,docx,odp,ods,odt,pot,potm,potx,pps,ppsx,ppsxm,ppt,pptm,pptx,rtf,xls,xlsx|max:10240'// phpcs:ignore
        ];
    }

    public function messages(): array
    {
        return [
            'task_id.required' => $this->getCommonErrorTranslation(),
            'task_id.exists' => $this->getCommonErrorTranslation(),
            'file.required' => trans('task_file.upload_additional_task_file.validation.file.required'),
            'file.file' => $this->getCommonErrorTranslation(),
            'file.max' => trans('task_file.upload_additional_task_file.validation.file.max'),
            'file.extensions' => trans('task_file.upload_additional_task_file.validation.file.extensions')
        ];
    }

    public function authorize(): bool
    {
        return $this->user()->isInternal();
    }
}
