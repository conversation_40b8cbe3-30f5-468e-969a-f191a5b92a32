<?php

namespace App\Http\VueRequests\Task;

use App\Http\VueRequests\PostRequest;

/**
 * @property int $task_id
 * @property int $task_file_id
 * @property int $company_id
 * @property array $summary
 */
class UpdateVatDeclarationRequest extends PostRequest
{
    public function authorize(): bool
    {
        return $this->user()->isInternal();
    }

    public function rules(): array
    {
        return [
            'company_id' => 'required|exists:companies,id',
            'task_file_id' => 'required|exists:task_files,id',
            'summary' => 'required|array'
        ];
    }

    public function messages(): array
    {
        return [
            'company_id.required' => $this->getCommonErrorTranslation(),
            'company_id.exists' => $this->getCommonErrorTranslation(),
            'task_file_id.required' => $this->getCommonErrorTranslation(),
            'task_file_id.exists' => $this->getCommonErrorTranslation(),
            'summary.required' => $this->getCommonErrorTranslation(),
            'summary.array' => $this->getCommonErrorTranslation(),
        ];
    }
}
