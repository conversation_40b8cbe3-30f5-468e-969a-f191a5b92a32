<?php

namespace App\Http\VueRequests\Task;

use App\Http\VueRequests\PostRequest;

/**
 * Class UpdateTitleRequest
 * @package App\Http\VueRequests\Task
 * @property int $task_id
 * @property string $title
 */
class UpdateTitleRequest extends PostRequest
{
    public function authorize(): bool
    {
        return $this->user()->isInternal();
    }

    public function rules(): array
    {
        return [
            'task_id' => 'required|exists:service_tasks,id',
            'title' => 'required|string|min:1|max:80|no_url'
        ];
    }

    public function messages(): array
    {
        return [
            'task_id.required' => $this->getCommonErrorTranslation(),
            'task_id.exists' => $this->getCommonErrorTranslation(),
            'title.required' => $this->getCommonErrorTranslation(),
            'title.min' => $this->getCommonErrorTranslation(),
            'title.max' => $this->getCommonErrorTranslation(),
        ];
    }
}
