<?php

namespace App\Http\VueRequests\Dms\Company;

use App\Company;
use App\Http\VueRequests\PostRequest;
use App\ValueObject\Dms\DmsPreferences;

/**
 * @property int $company_id
 * @property array $preferences
 * @property string $relation_type
 */
class UpdateDmsPreferencesRequest extends PostRequest
{
    protected $model_name = Company::class;

    public function rules(): array
    {
        return [
            'company_id' => 'required|exists:companies,id',
            'preferences' => 'required|array',
            'relation_type' => 'required|string|exists:open_question_relation_types,type'
        ];
    }

    public function messages(): array
    {
        return [
            'company_id.required' => $this->getCommonErrorTranslation(),
            'company_id.exists' => $this->getCommonErrorTranslation(),
            'preferences.required' => $this->getCommonErrorTranslation(),
            'preferences.array' => $this->getCommonErrorTranslation(),
            'relation_type.required' => $this->getCommonErrorTranslation(),
            'relation_type.string' => $this->getCommonErrorTranslation(),
            'relation_type.exists' => $this->getCommonErrorTranslation(),
        ];
    }

    public function authorize(): bool
    {
        return $this->user()->isInternal();
    }

    public function getDmsPreference(): DmsPreferences
    {
        return new DmsPreferences($this->preferences);
    }
}
