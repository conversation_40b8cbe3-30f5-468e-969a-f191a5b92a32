<?php

namespace App\Http\VueRequests\Webhooks;

use App\Http\VueRequests\GetRequest;
use App\Models\Webhooks\Webhook;

/**
 * @property array $webhooks
 */
class SaveWebhookRequest extends GetRequest
{
    public function authorize(): bool
    {
        return $this->user()->isManagerOfHostAccount() || $this->user()->isAdminUser();
    }

    public function rules(): array
    {
        return [
            'webhooks.*.url' => 'required|string|url|max:500',
            'webhooks.*.events' => 'required|array|min:1',
            'webhooks.*.events.*' => 'required|string|in:' . implode(',', Webhook::EVENTS),
        ];
    }

    public function attributes(): array
    {
        return [
            'webhooks.*.url' => trans('webhook.attributes.url'),
            'webhooks.*.events' => trans('webhook.attributes.events'),
        ];
    }
}
