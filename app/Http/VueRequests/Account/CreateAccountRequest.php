<?php

namespace App\Http\VueRequests\Account;

use App\Account;
use App\Http\VueRequests\PostRequest;
use App\User;
use App\ValueObject\MobileNumber;
use App\Support\CarbonImmutable;

/**
 * @property string $firstname
 * @property string $lastname
 * @property string $email
 * @property string $mobile
 * @property bool $email_as_auth_id
 * @property string $auth_id
 * @property string $account_name
 * @property string $hostnames
 * @property string $support_email
 * @property string $account_type
 * @property string $category
 * @property array $licenses
 * @property string $language
 */
class CreateAccountRequest extends PostRequest
{
    public function authorize(): bool
    {
        return $this->user()->isAdminUser() && $this->account()->isAdminAccount();
    }

    public function rules(): array
    {
        return [
            'firstname' => 'required|max:50|no_url',
            'lastname' => 'required|max:50|no_url',
            'email' => 'required|email:filter,rfc|required|max:80',
            'mobile' => 'nullable|valid_mobile|max:20',
            'email_as_auth_id' => 'boolean',
            'auth_id' => 'required_if:email_as_auth_id,false|max:80|valid_auth_id',
            'account_name' => 'required|unique:accounts,name|max:255|no_url',
            'hostnames' => 'required|unique_word:accounts,hostname, |valid_hostname|max:255',
            'support_email' => 'email:filter,rfc|required|max:80',
            'account_type' => 'required|in:' . implode(',', Account::VALID_ACCOUNT_TYPES_LIST),
            'category' => 'required|in:' . implode(',', Account::VALID_ACCOUNT_CATEGORIES_LIST),
            'licenses' => 'required|array|exists:licenses,id',
            'language' => 'required|string|in:en,nl',
        ];
    }

    public function messages(): array
    {
        return [
            'hostnames.unique_word' => trans('account.field_hostname_not_unique'),
        ];
    }

    public function attributes(): array
    {
        return [
            'email' => trans('account.field_owner.email'),
            'mobile' => trans('account.field_owner.mobile'),
            'support_email' => trans('account.field_support_email'),
            'auth_id' => trans('account.field_owner.auth_id'),
        ];
    }

    public function getAccountAttributes(): array
    {
        return [
            'name' => $this->account_name,
            'hostname' => $this->hostnames,
            'support_email' => $this->support_email,
            'type' => $this->account_type,
            'category' => $this->category,
            'language' => $this->language,
        ];
    }

    public function getManagerAttributes(): array
    {
        return [
            'firstname' => $this->firstname,
            'lastname' => $this->lastname,
            'email' => $this->email,
            'mobile' => $this->mobile,
            'auth_id' => $this->email_as_auth_id ? $this->email : $this->auth_id,
            'language' => $this->language,
            'auth_method' => null,
            'status' => User::STATUS_NEW,
            'is_external' => false,
            'billing_type_verified_at' => CarbonImmutable::now('UTC')
        ];
    }

    protected function beforeInputValidation(): void
    {
        !empty($this->mobile) && $this->merge(['mobile' => MobileNumber::sanitize($this->mobile)]);
        parent::beforeInputValidation();
    }
}
