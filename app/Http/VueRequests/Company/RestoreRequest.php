<?php

namespace App\Http\VueRequests\Company;

use App\Company;
use App\Http\VueRequests\PostRequest;

/**
 * @property Company $company
 */
class RestoreRequest extends PostRequest
{
    public function authorize(): bool
    {
        $user = $this->user();
        return $user->canManageCompanies() && $user->account_id === $this->company->account_id;
    }

    public function rules(): array
    {
        return [
            'id' => 'required|exists:companies,id'
        ];
    }

    public function messages(): array
    {
        return [
            'id.required' => $this->getCommonErrorTranslation(),
            'id.exists' => $this->getCommonErrorTranslation(),
        ];
    }
}
