<?php

namespace App\Http\VueRequests\OpenQuestions\Fiscal;

use App\Http\VueRequests\OpenQuestions\CreateDefaultManualQuestionRequest;
use App\Models\OpenQuestions\OpenQuestionCategory;
use App\Models\OpenQuestions\OpenQuestionRelationType;

class CreateManualQuestionRequest extends CreateDefaultManualQuestionRequest
{
    public function getCategory(): string
    {
        return OpenQuestionCategory::FISCAL;
    }

    public function getRelationType(): string
    {
        return OpenQuestionRelationType::FISCAL;
    }
}
