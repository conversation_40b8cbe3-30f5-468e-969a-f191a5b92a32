<?php

namespace App\Http\VueRequests\OpenQuestions\Front;

use App\Http\VueRequests\PostRequest;

/**
 * @property int[] $questionIds
 */
class BulkDeleteRequest extends PostRequest
{
    public function authorize(): bool
    {
        return $this->user()->is_external;
    }

    public function rules(): array
    {
        return [
            'questionIds' => 'required|array',
            'questionIds.*' => 'required|exists:open_questions,id',
        ];
    }

    public function messages(): array
    {
        return [
            'questionIds.*.exists' => $this->getCommonErrorTranslation(),
            'questionIds.*.required' => $this->getCommonErrorTranslation(),
            'questionIds.required' => $this->getCommonErrorTranslation(),
            'questionIds.array' => $this->getCommonErrorTranslation(),
        ];
    }
}
