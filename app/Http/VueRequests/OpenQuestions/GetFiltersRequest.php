<?php

namespace App\Http\VueRequests\OpenQuestions;

use App\Models\OpenQuestions\OpenQuestionCategory;

/**
 * @property int $company_id
 */
class GetFiltersRequest extends OpenQuestionsRequest
{
    private array $categories;

    public function __construct(
        array $query = [],
        array $request = [],
        array $attributes = [],
        array $cookies = [],
        array $files = [],
        array $server = [],
        $content = null
    ) {
        parent::__construct($query, $request, $attributes, $cookies, $files, $server, $content);
        $this->categories = OpenQuestionCategory::query()->pluck(OpenQuestionCategory::NAME)->toArray();
    }

    public function rules(): array
    {
        return [
            'categories' => 'array|in:' . implode(',', $this->categories),
        ];
    }

    public function categories(): array
    {
        return $this->input('categories', $this->categories);
    }
}
