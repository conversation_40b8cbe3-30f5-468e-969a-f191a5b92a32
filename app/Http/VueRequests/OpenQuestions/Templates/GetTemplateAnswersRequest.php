<?php

namespace App\Http\VueRequests\OpenQuestions\Templates;

use App\Http\VueRequests\GetRequest;
use App\Models\OpenQuestions\Questions\Templates\TemplateAnswer;

/**
 * @property int $openQuestionId
 */
class GetTemplateAnswersRequest extends GetRequest
{
    protected $model_name = TemplateAnswer::class;

    public function authorize(): bool
    {
        return $this->user()->isInternal();
    }
}
