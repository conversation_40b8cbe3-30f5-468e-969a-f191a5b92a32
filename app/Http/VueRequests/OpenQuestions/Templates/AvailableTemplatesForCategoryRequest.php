<?php

namespace App\Http\VueRequests\OpenQuestions\Templates;

use App\Http\VueRequests\GetRequest;
use App\Models\OpenQuestions\Questions\Templates\Template;

/**
 * @property string $category
 */
class AvailableTemplatesForCategoryRequest extends GetRequest
{
    protected $model_name = Template::class;

    public function authorize(): bool
    {
        return $this->user()->isInternal();
    }

    public function companyId(): ?int
    {
        return is_numeric($this->query('company_id')) ? $this->query('company_id') : null;
    }
}
