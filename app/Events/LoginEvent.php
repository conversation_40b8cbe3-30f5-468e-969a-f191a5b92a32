<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;
use App\User;

class LoginEvent extends Event
{
    use SerializesModels;

    public function __construct(User $user = null)
    {
        if ($user != null) {
            $this->registerSubject($user);
        }
        $this->registerAction('login');
    }

    public function handle(User $user = null, $remember = false, ?string $loginType = null, array $data = [])
    {
        if ($user != null) {
            $this->registerRelatedUser($user);
            $this->setVisibleForUser();
            $this->registerSubject($user);
        }

        if (isset($loginType)) {
            $this->setMessage($loginType);
        } elseif ($remember) {
            $this->setMessage('remember_cookie_login');
        } else {
            $this->setMessage('unknown_login');
        }

        $this->registerData($data);
        (new \App\Handlers\MarkLogin())->handle($this->getSubject());
        $this->queue();
    }
}
