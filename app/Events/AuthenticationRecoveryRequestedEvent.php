<?php

namespace App\Events;

use App\User;

class AuthenticationRecoveryRequestedEvent extends Event
{
    public function handle(User $user)
    {
        $this->registerRelatedUser($user);
        $this->setVisibleForUser();

        $this->registerObject($user);
        $this->setMessage('Authentication recovery link request sent to user ' . $user->auth_id . '(' . $user->id . ')'); //phpcs:ignore
        $this->registerData(['recipient' => $user->email]);

        $this->register();
    }
}
