<?php

namespace App\Events\Company;

use App\User;
use App\Events\Event;

class ManagerRemovedEvent extends Event
{
    public function handle(User $user)
    {
        $this->setMessage('Removed company manager ' . $user->showLabel());
        $this->registerObject($user);
        $this->registerRelatedUser($user);
        $this->registerRelatedAccount($user->account);
        $this->setVisibleForUser($user);
        $this->register();
    }
}
