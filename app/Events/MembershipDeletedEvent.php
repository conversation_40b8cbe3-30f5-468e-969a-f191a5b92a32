<?php

namespace App\Events;

use App\Context;
use App\Membership;

class MembershipDeletedEvent extends Event
{
    public function handle(Membership $membership)
    {
        $this->registerObject($membership);
        $this->registerRelatedContext($membership->context);
        $this->registerRelatedUser($membership->user);
        $this->registerRelatedAccount($membership->account);
        $this->setVisibleForUser();

        $this->registerData([
            'user_id' => $membership->user_id,
            'group_id' => $membership->context_id,
            'type' => $membership->type,
        ]);

        $group_name = '';
        $group = $membership->context()->withTrashed()->first();
        if ($group instanceof Context) {
            $group_name = ' (' . $group->showLabel(false, false) . ')';
        }

        $this->setMessage('Removed membership ' . $membership->type . ' on group #' . $membership->context_id . $group_name); //phpcs:ignore
        $this->register();
    }
}
