<?php

namespace App\Repositories\Declarations;

use App\DataTransferObject\ServiceTask\VpbApprovalTaskSummary;
use App\Interfaces\Services\Declarations\DeclarationStatsRepositoryInterface;
use App\Models\Declarations\VpbDeclarationData;
use App\ServiceTask;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class VpbDeclarationRepository implements DeclarationStatsRepositoryInterface
{
    /**
     * @param array $data
     * @return VpbDeclarationData
     */
    public function create(array $data): VpbDeclarationData
    {
        return VpbDeclarationData::query()->create($data);
    }

    public function update(
        VpbDeclarationData $declarationData,
        array $updatedData
    ): VpbDeclarationData {
        $declarationData->identifier = $updatedData[VpbDeclarationData::IDENTIFIER];
        $declarationData->tax_according_to_ordinary_rate = $updatedData[VpbDeclarationData::TAX_ACCORDING_TO_ORDINARY_RATE]; //phpcs:ignore
        $declarationData->advance_levies_total = $updatedData[VpbDeclarationData::ADVANCE_LEVIES_TOTAL];
        $declarationData->tax_amount_elsewhere_taxed_balance = $updatedData[VpbDeclarationData::TAX_AMOUNT_ELSEWHERE_TAXED_BALANCE]; //phpcs:ignore
        $declarationData->tax_reductions_total = $updatedData[VpbDeclarationData::TAX_REDUCTIONS_TOTAL];
        $declarationData->turnover_net_fiscal = $updatedData[VpbDeclarationData::TURNOVER_NET_FISCAL];
        $declarationData->taxable_profit = $updatedData[VpbDeclarationData::TAXABLE_PROFIT];
        $declarationData->tax_amount_balance = $updatedData[VpbDeclarationData::TAX_AMOUNT_BALANCE];
        $declarationData->frequency = $updatedData[VpbDeclarationData::FREQUENCY];
        $declarationData->save();
        return $declarationData;
    }

    /**
     * @param ServiceTask $serviceTask
     * @return VpbDeclarationData|null
     */
    public function getByServiceTask(ServiceTask $serviceTask): ?Model
    {
        return VpbDeclarationData::query()
            ->where(VpbDeclarationData::SERVICE_TASK_ID, $serviceTask->id)
            ->first();
    }
}
