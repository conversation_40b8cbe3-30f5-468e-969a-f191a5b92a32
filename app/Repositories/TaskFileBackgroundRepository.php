<?php

namespace App\Repositories;

use App\Exceptions\ModelNotSavedException;
use App\Models\TaskFile\Background;
use Illuminate\Support\Collection;

class TaskFileBackgroundRepository
{
    /**
     * Set background to page of a task file document (PDF)
     * If the same one already exists, do not update.
     * If another background is set, update it.
     * @param Background $bg
     * @return Background|null
     */
    public function store(Background $bg): ?Background
    {
        $existing = $this->getForPage($bg->task_file_id, $bg->page);
        if ($existing instanceof Background) {
            if ($existing->pdf_background_id !== $bg->pdf_background_id) {
                if ($existing->update([Background::PDF_BACKGROUND_ID => $bg->pdf_background_id])) {
                    return $existing; // apply different background and return updated existing model
                }
                throw new ModelNotSavedException('Unable to update background of page ' . $bg->page . ' of task file #' . $bg->task_file_id); // phpcs:ignore
            }
            return $existing;
        }
        $bg->save();
        return $bg;
    }

    public function getForPage(int $taskFileId, int $page): ?Background
    {
        return Background::query()
            ->where(Background::TASK_FILE_ID, $taskFileId)
            ->where(Background::PAGE, $page)
            ->first();
    }

    /**
     * Delete a background by task file ID
     * @param int $taskFileId
     * @return bool
     */
    public function delete(int $taskFileId): bool
    {
        return Background::query()->where(Background::TASK_FILE_ID, $taskFileId)->delete();
    }

    /**
     * Delete a background by task file ID
     * @param int $taskFileId
     * @return Collection
     */
    public function getBackgrounds(int $taskFileId): Collection
    {
        return Background::query()->where(Background::TASK_FILE_ID, $taskFileId)->get();
    }
}
