<?php

namespace App\Repositories;

use App\Helpers\IdGeneratorHelper;
use App\Models\Mongo\TrackingPixel;
use App\Support\Carbon;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class TrackingPixelRepository extends RepositoryBase
{
    /**
     * @param string $emailType
     * @param string $emailSubject
     * @param string $to
     * @param int|null $userId
     * @param int|null $taskId
     * @param int|null $taskResponseId
     * @return TrackingPixel
     */
    public function create(
        string $emailType,
        string $emailSubject,
        string $to,
        int $userId = null,
        int $taskId = null,
        int $taskResponseId = null
    ): TrackingPixel {
        $trackingPixel = new TrackingPixel();
        $trackingPixel->tracking_id = IdGeneratorHelper::generateStringIdFromArgs(
            $emailType,
            $emailSubject,
            $userId,
            Carbon::now()
        );
        $trackingPixel->email_type = $emailType;
        $trackingPixel->email_subject = $emailSubject;
        $trackingPixel->user_id = $userId;
        $trackingPixel->to = $to;
        $trackingPixel->service_task_id = $taskId;
        $trackingPixel->service_task_response_id = $taskResponseId;
        $trackingPixel->save();

        return $trackingPixel;
    }

    /**
     * Return single model by unique tracking id.
     * @throws ModelNotFoundException
     * @param string $tracking_id
     * @return TrackingPixel
     */
    public function getTrackingPixelByTrackingId(string $tracking_id): TrackingPixel
    {
        return TrackingPixel::query()->where(TrackingPixel::TRACKING_ID, $tracking_id)->firstOrFail();
    }

    /**
     * Adds a tracking date to a TrackingPixel for the given tracking id.
     * If this TrackingPixel has already been tracked the tracked date doesn't change but the model updates.
     * This means that tracked_at keeps the time it was first tracked and updated_at the last time.
     *
     * @param TrackingPixel $trackingPixel
     * @param string|null $remote_address
     * @param string|null $http_user_agent
     * @return TrackingPixel
     */
    public function track(
        TrackingPixel $trackingPixel,
        string $remote_address = null,
        string $http_user_agent = null
    ): TrackingPixel {
        if (!$trackingPixel->tracked_at) {
            $trackingPixel->tracked_at = Carbon::now();
        }
        $trackingPixel->remote_address = $remote_address;
        $trackingPixel->http_user_agent = $http_user_agent;
        $trackingPixel->touch();
        $trackingPixel->save();

        return $trackingPixel;
    }

    public function updateSmtpStatus(
        TrackingPixel $trackingPixel,
        string $id,
        string $timestamp,
        string $status,
        ?string $host,
        ?string $message
    ): bool {
        $trackingPixel->smtp_id = $id;
        $trackingPixel->smtp_timestamp = $timestamp;
        $trackingPixel->smtp_status = $status;
        $trackingPixel->smtp_host = $host;
        $trackingPixel->smtp_message = $message;
        return $trackingPixel->save();
    }
}
