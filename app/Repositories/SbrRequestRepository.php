<?php

namespace App\Repositories;

use App\Account;
use App\Models\ServiceTask\SbrTask;
use App\Models\Sbr\SbrRequest;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class SbrRequestRepository
{
    public function getById(int $id): SbrRequest
    {
        return SbrRequest::findOrFail($id);
    }

    public function getForTask(SbrTask $task): SbrRequest
    {
        return SbrRequest::where(SbrRequest::SERVICE_TASK_ID, $task->id)->firstOrFail();
    }

    /**
     * Get requests that were put on the queue more than an hour ago and have not been updated since.
     * This is an indication that something is wrong with the
     * queue as the requests are not processed in a timely manner.
     *
     * @return Collection List of requests.
     */
    public static function getDelayedQueuedRequests(): Collection
    {
        return SbrRequest::whereRaw("UNIX_TIMESTAMP(updated_at) < UNIX_TIMESTAMP() - 3600")
            ->where('status', SbrRequest::STATUS_QUEUED)
            ->get();
    }

    public static function getProcessingRequests(): Collection
    {
        return SbrRequest::whereRaw("check_after_date < NOW()")
            ->where('status', SbrRequest::STATUS_PROCESSING)
            ->get();
    }

    /**
     * Abandoned requests are new requests for which a status check was never added to the queue because something went
     * wrong after creating the request and have not been touched for 1 hour.
     * @param $minutes int Optional number of hours of inactivity
     * for the requests to be considered abandoned. Default is 60.
     *
     * @return Collection
     */
    public static function getAbandonedRequests(int $minutes = 60): Collection
    {
        return SbrRequest::whereRaw("UNIX_TIMESTAMP(updated_at) < UNIX_TIMESTAMP() - " . ($minutes * 60))
            ->whereNull('external_id')
            ->where('status', SbrRequest::STATUS_NEW)
            ->get();
    }

    /**
     * Get requests that are new, queued or processing for more than x days. Default is 2
     * @param int $minutes 2 days = 2880 minutes
     * @return Collection
     */
    public function getAttentionNeededRequests(?Account $account = null, int $minutes = 2880): Collection
    {
        $query = SbrRequest::query()
            ->whereRaw("UNIX_TIMESTAMP(updated_at) < UNIX_TIMESTAMP() - " . ($minutes * 60))
            ->whereIn(
                'status',
                [SbrRequest::STATUS_NEW, SbrRequest::STATUS_QUEUED, SbrRequest::STATUS_PROCESSING]
            );
        if (!is_null($account)) {
            $query->where(SbrRequest::ACCOUNT_ID, $account->id);
        }
        return $query->get();
    }

    public function indexOverviewQuery(Account $account, ?string $search): Builder
    {
        $query = SbrRequest::query()
            ->where(SbrRequest::ACCOUNT_ID, $account->id)
            ->whereNull(SbrRequest::SERVICE_TASK_ID)
            ->orderBy(SbrRequest::ID, 'desc');
        // $search can be null or an empty string
        if (!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->where(SbrRequest::LEGAL_ENTITY_NAME, 'LIKE', '%' . $search . '%')
                    ->orWhere(SbrRequest::TITLE, 'LIKE', '%' . $search . '%');
            });
        }
        return $query;
    }
}
