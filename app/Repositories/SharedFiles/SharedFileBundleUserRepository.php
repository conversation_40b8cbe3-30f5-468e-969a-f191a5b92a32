<?php

namespace App\Repositories\SharedFiles;

use App\Account;
use App\Models\SharedFiles\SharedFileBundle;
use App\Models\SharedFiles\SharedFileBundleUser;
use App\User;
use App\Support\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class SharedFileBundleUserRepository
{
    public function store(SharedFileBundle $sharedFileBundle, User $user): SharedFileBundleUser
    {
        $sharedFileBundleUser = new SharedFileBundleUser(
            [
                SharedFileBundleUser::SHARED_BUNDLE_UUID => $sharedFileBundle->uuid,
                SharedFileBundleUser::USER_ID => $user->id,
                SharedFileBundleUser::STATUS => SharedFileBundleUser::STATUS_UNOPENED
            ]
        );
        $sharedFileBundleUser->save();
        return $sharedFileBundleUser;
    }

    public function getUsersToRemindByAccount(Account $account, array $days): Collection
    {
        return SharedFileBundleUser::query()
            ->join(
                'shared_file_bundles',
                'shared_file_bundles.uuid',
                '=',
                'shared_file_bundle_users.shared_bundle_uuid'
            )
            ->where('shared_file_bundles.expire_at', '>', Carbon::now())
            ->where('shared_file_bundle_users.' . SharedFileBundleUser::STATUS, SharedFileBundleUser::STATUS_UNOPENED)
            ->where('shared_file_bundles.account_id', $account->id)
            ->whereRaw('DATEDIFF(CURRENT_DATE, shared_file_bundles.created_at) in (' . implode(',', $days) . ')')
            ->get();
    }

    public function updateStatus(SharedFileBundleUser $bundleUser, string $status): void
    {
        $bundleUser->status = $status;
        $bundleUser->save();
    }

    /**
     * @param SharedFileBundle $bundle
     * @param User $user
     * @return SharedFileBundleUser
     */
    public function get(SharedFileBundle $bundle, User $user): Model
    {
        return SharedFileBundleUser::query()
            ->where(SharedFileBundleUser::SHARED_BUNDLE_UUID, $bundle->uuid)
            ->where(SharedFileBundleUser::USER_ID, $user->id)
            ->first();
    }
}
