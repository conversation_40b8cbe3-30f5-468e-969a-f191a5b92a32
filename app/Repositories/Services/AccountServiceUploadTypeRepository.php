<?php

namespace App\Repositories\Services;

use App\Account;
use App\Exceptions\ConflictException;
use App\Models\TaskFile;
use DB;
use App\AccountService;
use App\AccountServiceUploadType;
use Illuminate\Database\Eloquent\Collection;

class AccountServiceUploadTypeRepository
{
    public function getById(int $id): ?AccountServiceUploadType
    {
        return AccountServiceUploadType::find($id);
    }

    public function getForAccountById(Account $account, int $id): ?AccountServiceUploadType
    {
        return $account->uploadTypes()->find($id);
    }

    /**
     * Save upload types and delete others that are not saved.
     * @param AccountService $accountService
     * @param AccountServiceUploadType[] List of models to save.
     * @return AccountServiceUploadType[] List of models that were saved.
     */
    public function store(AccountService $accountService, array $uploadTypes): array
    {
        $ids = [];
        $saved = [];
        foreach ($uploadTypes as $uploadType) {
            if (isset($uploadType->id)) {
                $old = AccountServiceUploadType::findOrFail($uploadType->id);
                if ($old->account_id !== $uploadType->account_id) {
                    throw new ConflictException('Account ID not the same.');
                }
                if ($old->account_service_id !== $uploadType->account_service_id) {
                    throw new ConflictException('Account Service ID not the same.');
                }
                $old->update($uploadType->getAttributes());
            } else {
                $uploadType = AccountServiceUploadType::create($uploadType->getAttributes());
            }
            $saved[] = $uploadType;
            $ids[] = $uploadType->id;
        }

        //delete any types that were not in the saved list
        DB::table(AccountServiceUploadType::TABLE_NAME)
            ->where('account_service_id', $accountService->id)
            ->whereNotIn('id', $ids)
            ->delete();

        return $saved;
    }

    /**
     * Get upload types for AccountService
     *
     * @param int $accountServiceId
     * @return Collection
     */
    public function getByAccountService(int $accountServiceId): Collection
    {
        return AccountServiceUploadType::where('account_service_id', $accountServiceId)->get();
    }

    /**
     * Delete AccountServiceUploadType
     *
     * @param AccountServiceUploadType $accountServiceUploadType
     * @return bool
     */
    public function delete(AccountServiceUploadType $accountServiceUploadType): bool
    {
        return $accountServiceUploadType->delete();
    }

    /**
     * Delete upload types by AccountService
     *
     * @param AccountService $accountService
     * @return int
     */
    public function deleteByAccountService(AccountService $accountService): int
    {
        return AccountServiceUploadType::query()
            ->where('account_service_id', $accountService->id)
            ->delete();
    }

    public function getByAccountId(int $accountId): Collection
    {
        return AccountServiceUploadType::query()
            ->where('account_id', $accountId)
            ->whereHas('accountService')
            ->get();
    }

    /**
     * @param string $title
     * @return AccountServiceUploadType
     */
    public function getByTitle(string $title, int $accountServiceId): ?object
    {
        return AccountServiceUploadType::where('title', $title)
            ->where('account_service_id', $accountServiceId)
            ->first();
    }

    /**
     * Available Upload types for Account Services
     * @param array $accountServiceIds
     * @return array
     */
    public function availableUploadTypes(array $accountServiceIds = []): array
    {
        $query = AccountServiceUploadType::query()
            ->join(
                TaskFile::TABLE_NAME,
                TaskFile::TABLE_NAME . '.account_service_upload_type_id',
                '=',
                AccountServiceUploadType::TABLE_NAME . '.id'
            )
            ->whereIn(AccountServiceUploadType::TABLE_NAME . '.account_service_id', $accountServiceIds)
            ->whereNotNull(TaskFile::TABLE_NAME . '.account_service_upload_type_id')
            ->orderBy(AccountServiceUploadType::TABLE_NAME . '.title')
            ->distinct();

        return $query->get([
            AccountServiceUploadType::TABLE_NAME . '.id',
            AccountServiceUploadType::TABLE_NAME . '.title'
        ])->toArray();
    }
}
