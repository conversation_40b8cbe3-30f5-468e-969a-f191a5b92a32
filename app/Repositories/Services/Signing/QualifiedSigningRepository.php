<?php

namespace App\Repositories\Services\Signing;

use App\AccountService;
use App\Model;
use App\Models\Signing\QualifiedSigningUser;
use App\User;
use App\ValueObject\Pagination\SearchablePaginationFilters;
use App\Support\Carbon;
use Illuminate\Support\Collection;

class QualifiedSigningRepository
{
    /**
     * Get all non-qualified signing users
     *
     * @param AccountService $accountService
     * @param SearchablePaginationFilters $filters
     * @return Collection
     */
    public function getNonQualifiedSigningUsers(
        AccountService $accountService,
        SearchablePaginationFilters $filters
    ): Collection {
        $qualifiedSigningUserIds = QualifiedSigningUser::query()
            ->where(QualifiedSigningUser::ACCOUNT_SERVICE_ID, $accountService->id)
            ->pluck('user_id')
            ->toArray();

        $users = User::query()
            ->where(User::ACCOUNT_ID, $accountService->account->id)
            ->where(User::IS_EXTERNAL, false)
            ->whereNotIn(User::ID, $qualifiedSigningUserIds)
            ->orderBy(User::FIRST_NAME)
            ->orderBy(User::LAST_NAME)
            ->offset(($filters->page() - 1) * $filters->limit())
            ->limit($filters->limit());

        if ($filters->search() !== '') {
            $users->where(function ($q) use ($filters) {
                $q->where(User::FIRST_NAME, 'like', '%' . $filters->search() . '%')
                    ->orWhere(User::LAST_NAME, 'like', '%' . $filters->search() . '%')
                    ->orWhere(User::EMAIL, 'like', '%' . $filters->search() . '%');
            });
        }

        return $users->get();
    }

    /**
     * Get qualified signing user
     *
     * @param AccountService $accountService
     * @param int $qualifiedSigningUserId
     * @return QualifiedSigningUser|null
     */
    public function getQualifiedSigningUser(
        AccountService $accountService,
        int $qualifiedSigningUserId
    ): ?QualifiedSigningUser {
        return QualifiedSigningUser::query()
            ->where('qualified_signing_users.' . QualifiedSigningUser::ACCOUNT_SERVICE_ID, $accountService->id)
            ->where('qualified_signing_users.' . QualifiedSigningUser::ID, $qualifiedSigningUserId)
            ->first();
    }

    /**
     * Get all qualified signing users
     *
     * @param AccountService $accountService
     * @return Collection
     */
    public function getQualifiedSigningUsers(AccountService $accountService): Collection
    {
        return User::query()
            ->select([
                'qualified_signing_users.id',
                'qualified_signing_users.user_id',
                'users.email',
                'users.firstname',
                'users.lastname'
            ])
            ->join('qualified_signing_users', 'qualified_signing_users.user_id', '=', 'users.id')
            ->where('qualified_signing_users.' . QualifiedSigningUser::ACCOUNT_SERVICE_ID, $accountService->id)
            ->whereNull('qualified_signing_users.' . QualifiedSigningUser::DELETED_AT)
            ->orderBy('users.' . User::FIRST_NAME)
            ->get();
    }

    /**
     * Add qualified signing user with an array of users_ids
     *
     * @param AccountService $accountService
     * @param User $user
     * @param int $userId
     * @return QualifiedSigningUser
     */
    public function addQualifiedSigningUser(
        AccountService $accountService,
        User $user,
        int $userId
    ): QualifiedSigningUser {
        return QualifiedSigningUser::query()->create([
            QualifiedSigningUser::ACCOUNT_SERVICE_ID => $accountService->id,
            QualifiedSigningUser::ACCOUNT_ID => $user->account->id,
            QualifiedSigningUser::USER_ID => $userId,
            QualifiedSigningUser::CREATED_AT => Carbon::now(),
            Model::CREATED_BY => $user->id
        ]);
    }

    /**
     * Remove qualified signing user
     *
     * @param AccountService $accountService
     * @param int $userId
     * @return bool
     */
    public function removeQualifiedSigningUser(AccountService $accountService, int $userId): bool
    {
        return QualifiedSigningUser::query()
            ->where('qualified_signing_users.' . QualifiedSigningUser::ACCOUNT_SERVICE_ID, $accountService->id)
            ->where('qualified_signing_users.' . QualifiedSigningUser::ID, $userId)
            ->delete();
    }

    /**
     * Remove all qualified signing users
     *
     * @param AccountService $accountService
     * @param User $user
     * @return bool
     */
    public function removeAllQualifiedSigningUser(AccountService $accountService, User $user): bool
    {
        return QualifiedSigningUser::query()
            ->where('qualified_signing_users.' . QualifiedSigningUser::ACCOUNT_SERVICE_ID, $accountService->id)
            ->where('qualified_signing_users.' . QualifiedSigningUser::ACCOUNT_ID, $user->account->id)
            ->delete();
    }

    /**
     * Remove qualified signing users by user IDs
     *
     * @param AccountService $accountService
     * @param array $userIds
     * @return Collection
     */
    public function removeQualifiedSigningUsersByUserIds(AccountService $accountService, array $userIds): Collection
    {
        if (empty($userIds)) {
            return new Collection();
        }

        $recordsToDelete = QualifiedSigningUser::query()
            ->where(QualifiedSigningUser::ACCOUNT_SERVICE_ID, $accountService->id)
            ->whereIn(QualifiedSigningUser::USER_ID, $userIds)
            ->get();

        QualifiedSigningUser::query()
            ->where(QualifiedSigningUser::ACCOUNT_SERVICE_ID, $accountService->id)
            ->whereIn(QualifiedSigningUser::USER_ID, $userIds)
            ->delete();

        return $recordsToDelete;
    }
}
