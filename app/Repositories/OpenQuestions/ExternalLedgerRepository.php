<?php

namespace App\Repositories\OpenQuestions;

use App\Models\OpenQuestions\ExternalLedger;

class ExternalLedgerRepository
{
    public function store(ExternalLedger $externalLedger): bool
    {
        return $externalLedger->save();
    }

    /**
     * @param int $accountServiceId
     * @param string $ledgerNumber
     * @param int $externalCompanyId
     * @param array $questionIds
     * @return int[]
     */
    public function findMissingQuestions(
        int $accountServiceId,
        string $ledgerNumber,
        int $externalCompanyId,
        array $questionIds
    ): array {
        return ExternalLedger::where(ExternalLedger::ACCOUNT_SERVICE_ID, $accountServiceId)
            ->where(ExternalLedger::LEDGER_NUMBER, $ledgerNumber)
            ->where(ExternalLedger::EXTERNAL_COMPANY_ID, $externalCompanyId)
            ->whereNotIn(ExternalLedger::OPEN_QUESTION_ID, $questionIds)
            ->get(ExternalLedger::OPEN_QUESTION_ID)->pluck(ExternalLedger::OPEN_QUESTION_ID)->toArray();
    }
}
