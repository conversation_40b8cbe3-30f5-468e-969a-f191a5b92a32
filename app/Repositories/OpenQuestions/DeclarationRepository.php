<?php

namespace App\Repositories\OpenQuestions;

use App\AccountService;
use App\Company;
use App\Models\OpenQuestions\ExternalCompany;
use App\Models\OpenQuestions\Questions\Declaration;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\User;
use App\ValueObject\OpenQuestions\CompletedFilters;
use App\ValueObject\Pagination\OpenQuestionsListFilters;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;

class DeclarationRepository extends CategoryRepository
{
    public function store(Declaration $declarationQuestion): ?Declaration
    {
        if ($declarationQuestion->save()) {
            return $declarationQuestion;
        }
        return null;
    }

    public function create(array $attributes): Declaration
    {
        return Declaration::query()->create($attributes);
    }

    /**
     * This method tries to find a question based on multiple attributes:
     * [name, amount, transaction_date, account_service_id, company_id].
     * IMPORTANT: This could include soft deleted questions where deleted_at is set.
     *
     * @param array $attributes
     * @param int $companyId
     * @param int $accountServiceId
     * @return OpenQuestion|null
     */
    public function findQuestion(array $attributes, int $companyId, int $accountServiceId): ?Declaration
    {
        return Declaration::query()
            ->join(
                'open_questions',
                'open_question_declarations.open_question_id',
                '=',
                'open_questions.id'
            )
            ->where(OpenQuestion::COMPANY_ID, $companyId)
            ->where(OpenQuestion::ACCOUNT_SERVICE_ID, $accountServiceId)
            ->where($attributes)
            ->orderByDesc('created_at')
            ->first();
    }

    /**
     * Get a list of tasks in the To do tab.
     *
     * @param Company $company
     * @param User $user
     * @param array $orderBy Listed as ['column' => 'order', 'column' => 'order']
     * @return Collection|OpenQuestion[]
     */
    public function questionsToAnswer(
        Company $company,
        User $user,
        string $category,
        array $orderBy = []
    ): Collection {
        $forwarded = $this->forwardedQuestions($company, $user, $category);

        $query = Declaration::query()
            ->join('open_questions', 'open_question_declarations.open_question_id', '=', 'open_questions.id')
            ->join('companies_users', 'companies_users.company_id', '=', 'open_questions.company_id')
            ->join('open_questions_emails_settings_users', function (JoinClause $join) {
                $join->on('open_questions_emails_settings_users.company_user_id', '=', 'companies_users.id');
                $join->on('open_questions_emails_settings_users.category', '=', 'open_questions.category');
            })
            ->whereNull('open_questions.deleted_at')
            ->where([
                'open_questions.company_id' => $company->id,
                'open_questions.status' => OpenQuestion::STATUS_OPEN,
                'companies_users.user_id' => $user->id
            ])
            ->where('open_questions.category', $category)
            ->with($this->openQuestionRelations)
            ->union($forwarded);

        if (!empty($orderBy)) {
            foreach ($orderBy as $column => $order) {
                $query->orderBy($column, $order);
            }
        }
        return $query->get(['open_question_declarations.*', 'open_questions.*']);
    }

    public function getByQuestionId(OpenQuestion $openQuestion): ?Declaration
    {
        return Declaration::query()->where('open_question_id', $openQuestion->id)->first();
    }

    public function getForCompany(string $category, Company $company, OpenQuestionsListFilters $filters): Collection
    {
        $query = Declaration::query()
            ->join('open_questions', 'open_question_declarations.open_question_id', '=', 'open_questions.id')
            ->join('account_services', 'open_questions.account_service_id', '=', 'account_services.id')
            ->join('services', 'account_services.service_id', '=', 'services.id')
            ->where('open_questions.category', $category)
            ->whereNull('open_questions.deleted_at');
        return $this->getForCompanyQueryBuilder($query, $company, $filters)
            ->get('open_question_declarations.*');
    }

    public function forwardedQuestions(
        Company $company,
        User $user,
        string $category
    ): Builder {
        return Declaration::query()
            ->join(
                'open_questions',
                'open_question_declarations.open_question_id',
                '=',
                'open_questions.id'
            )
            ->leftJoin('open_question_users', 'open_question_users.question_id', 'open_questions.id')
            ->whereNull('open_questions.deleted_at')
            ->where('open_questions.status', OpenQuestion::STATUS_OPEN)
            ->where('open_questions.category', $category)
            ->where('open_questions.company_id', $company->id)
            ->where('open_question_users.user_id', $user->id)
            ->select(['open_question_declarations.*', 'open_questions.*']);
    }

    /**
     * @param string $externalId
     * @param AccountService $accountService
     * @return Collection<Declaration>
     */
    public function getQuestions(
        string $externalId,
        AccountService $accountService
    ): Collection {
        $companyIds = ExternalCompany::query()
            ->where(ExternalCompany::EXTERNAL_ID, $externalId)
            ->where(ExternalCompany::ACCOUNT_SERVICE_ID, $accountService->id)
            ->pluck('company_id')
            ->toArray();

        $openQuestionIds = OpenQuestion::query()
            ->whereIn('company_id', $companyIds)
            ->whereNotIn(OpenQuestion::STATUS, [OpenQuestion::STATUS_DELETED, OpenQuestion::STATUS_COMPLETED])
            ->where(OpenQuestion::ACCOUNT_SERVICE_ID, $accountService->id)
            ->pluck('id')
            ->toArray();

        return Declaration::query()
            ->whereIn('open_question_id', $openQuestionIds)
            ->with(['openQuestion' => function ($query) {
                $query->with('accountService.service');
            }])
            ->get();
    }
}
