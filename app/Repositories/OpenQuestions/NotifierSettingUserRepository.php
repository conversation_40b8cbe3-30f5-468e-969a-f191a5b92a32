<?php

namespace App\Repositories\OpenQuestions;

use App\Company;
use App\CompanyUser;
use App\Models\OpenQuestions\Notifier\NotifierSettingUser;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\User;
use Illuminate\Database\Eloquent\Collection as DbCollection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class NotifierSettingUserRepository
{
    /**
     * @param Company $company
     * @param array $companyUserIds
     * @param array<int, NotifierSettingUser> $notifierSettingUser
     * @return void
     * @throws Throwable
     */
    public function sync(
        Company $company,
        array $companyUserIds,
        array $notifierSettingUser = []
    ): void {
        NotifierSettingUser::query()
            ->join('companies_users', 'open_questions_emails_settings_users.company_user_id', '=', 'companies_users.id')
            ->where('companies_users.company_id', $company->id)
            ->whereIn('open_questions_emails_settings_users.company_user_id', $companyUserIds)
            ->delete();

        foreach ($notifierSettingUser as $setting) {
            $setting->save();
        }
    }

    public function hasUsers(Company $company): bool
    {
        return NotifierSettingUser::query()
            ->leftJoin(
                'companies_users',
                'companies_users.id',
                '=',
                'open_questions_emails_settings_users.company_user_id'
            )
            ->where('companies_users.company_id', $company->id)
            ->exists();
    }

    public function getUserNotifierSettings(int $companyId): DbCollection
    {
        return NotifierSettingUser::query()
            ->leftJoin(
                'companies_users',
                'companies_users.id',
                '=',
                'open_questions_emails_settings_users.company_user_id'
            )
            ->select('companies_users.user_id', DB::raw('group_concat(category) as category'))
            ->where('companies_users.company_id', $companyId)
            ->groupBy('companies_users.user_id')
            ->get();
    }

    public function getUserNotifierSettingsByCategory(int $companyId, string $category): Collection
    {
        return NotifierSettingUser::query()
            ->where(NotifierSettingUser::CATEGORY, $category)
            ->whereHas('companyUser', function ($query) use ($companyId) {
                $query->where(CompanyUser::COMPANY_ID, $companyId);
            })
            ->with(['companyUser.user'])
            ->get();
    }

    public function usersToGetNotified(Company $company): DbCollection
    {
        return User::query()
            ->join(
                'companies_users',
                'companies_users.user_id',
                '=',
                'users.id'
            )
            ->join(
                'open_questions',
                'open_questions.company_id',
                '=',
                'companies_users.company_id'
            )
            ->join('open_questions_emails_settings_users', function (JoinClause $join) {
                $join->on(
                    'open_questions_emails_settings_users.company_user_id',
                    '=',
                    'companies_users.id'
                );
                $join->on(
                    'open_questions_emails_settings_users.category',
                    '=',
                    'open_questions.category'
                );
            })
            ->whereNull('open_questions.deleted_at')
            ->where('companies_users.company_id', $company->id)
            ->where('open_questions.status', OpenQuestion::STATUS_OPEN)
            ->groupBy('users.id')
            ->get('users.*');
    }

    public function getDistinctCategoriesForExistingUserSettings(int $companyId): Collection
    {
        return NotifierSettingUser::query()
            ->leftJoin(
                'companies_users',
                'companies_users.id',
                '=',
                'open_questions_emails_settings_users.company_user_id'
            )
            ->select(DB::raw('DISTINCT open_questions_emails_settings_users.category as category'))
            ->where('companies_users.company_id', $companyId)
            ->pluck('category');
    }
}
