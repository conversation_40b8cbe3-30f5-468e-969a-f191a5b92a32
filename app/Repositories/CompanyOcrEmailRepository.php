<?php

namespace App\Repositories;

use App\Company;
use App\Models\CompanyOcrEmail;
use Illuminate\Database\Eloquent\Model;

class CompanyOcrEmailRepository
{
    public function updateOrCreate(Company $company, $ocrEmail, string $type): Model
    {
        return CompanyOcrEmail::updateOrCreate(
            [CompanyOcrEmail::COMPANY_ID => $company->id, CompanyOcrEmail::TYPE => $type],
            [CompanyOcrEmail::EMAIL => $ocrEmail]
        );
    }

    /**
     * Delete email by type
     * @param Company $company
     * @param string $type
     * @return bool
     */
    public function delete(Company $company, string $type): bool
    {
        return CompanyOcrEmail::query()
            ->where(CompanyOcrEmail::COMPANY_ID, $company->id)
            ->where(CompanyOcrEmail::TYPE, $type)
            ->delete();
    }
}
