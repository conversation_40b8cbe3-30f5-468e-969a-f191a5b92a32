<?php

namespace App\Repositories\Http\Services;

use App\Exceptions\Services\Declarations\SnelStart\TokenErrorException;
use App\Factories\ValueObjects\Snelstart\VatDeclarationFactory;
use App\Http\Responses\Response;
use App\Logging\Channels\ServiceLog;
use App\Repositories\Http\AbstractHttpRepository;
use App\ValueObject\OAuth\Token;
use App\ValueObject\Services\Declarations\SnelStart\VatDeclaration;
use GuzzleHttp\Exception\RequestException;

class SnelStartRepository extends AbstractHttpRepository
{
    /**
     * @param  string  $clientKey
     *
     * @return Token
     */
    public function token(
        string $clientKey
    ): Token {
        $response = $this->post(
            'https://auth.snelstart.nl/b2b/token',
            [
                'form_params' => [
                    'grant_type' => 'clientkey',
                    'clientkey'  => $clientKey
                ]
            ]
        );

        return new Token(
            $response['access_token'],
            $response['expires_in'],
            $response['token_type'],
        );
    }

    /**
     * @param \App\Token $token
     * @return array[]
     */
    public function getDeclarations(\App\Token $token): array
    {
        $url = config('services.snelstart.api_url') . 'btwaangiftes';
        try {
            return $this->get(
                $url,
                [
                    'headers' => $this->headers($token)
                ]
            );
        } catch (RequestException $e) {
            $statusCode = $e->getResponse()->getStatusCode();
            if ($statusCode === Response::HTTP_UNAUTHORIZED) {
                ServiceLog::error('SnelStart token #' . $token->id . ' is not valid');
                throw new TokenErrorException('Snelstart token is not valid.', $e->getCode(), $e);
            }
            throw $e;
        }
    }

    /**
     * @param \App\Token $token
     * @param string $id
     * @return VatDeclaration
     */
    public function getDeclaration(\App\Token $token, string $id): VatDeclaration
    {
        $url = config('services.snelstart.api_url') . 'btwaangiftes/' . $id;

        try {
            $response = $this->get(
                $url,
                [
                    'headers' => $this->headers($token)
                ]
            );
        } catch (RequestException $e) {
            $statusCode = $e->getResponse()->getStatusCode();
            if ($statusCode === Response::HTTP_NOT_FOUND) {
                ServiceLog::error('SnelStart declaration #' . $id . ' could not be found');
            } elseif ($statusCode === Response::HTTP_UNAUTHORIZED) {
                ServiceLog::error('SnelStart token #' . $token->id . ' is not valid');
                throw new TokenErrorException('Snelstart token is not valid.', $e->getCode(), $e);
            }
            throw $e;
        }

        return VatDeclarationFactory::create($response);
    }

    /**
     * @param \App\Token $token
     * @param string $id
     * @return bool
     */
    public function indicatesSentToLogius(\App\Token $token, string $id): bool
    {
        $url = config('services.snelstart.api_url') . 'btwaangiftes/' . $id . '/externAangeven';

        try {
            $response = $this->client->put(
                $url,
                [
                    'json' => ['isExternAangegeven' => true],
                    'headers' => $this->headers($token)
                ]
            );
        } catch (RequestException $e) {
            $statusCode = $e->getResponse()->getStatusCode();
            if ($statusCode === Response::HTTP_UNAUTHORIZED) {
                ServiceLog::error('SnelStart token #' . $token->id . ' is not valid');
                throw new TokenErrorException('Snelstart token is not valid.', $e->getCode(), $e);
            }
            throw $e;
        }

        return $response->getStatusCode() === Response::HTTP_OK;
    }

    /**
     * @param \App\Token $token
     * @return array
     */
    private function headers(\App\Token $token): array
    {
        return [
            'Ocp-Apim-Subscription-Key' => config('services.snelstart.subscription_key'),
            'Authorization' => 'Bearer ' . $token->token
        ];
    }
}
