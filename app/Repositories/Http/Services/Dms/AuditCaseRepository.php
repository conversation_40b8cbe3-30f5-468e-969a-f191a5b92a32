<?php

namespace App\Repositories\Http\Services\Dms;

use App;
use App\Exceptions\Signing\PkiSigning\ServiceUnavailableException;
use App\Helpers\TaskAuditLogHelper;
use App\Interfaces\Services\ServableTaskFileInterface;
use App\Logging\Channels\ServiceLog;
use App\Repositories\FileSystem\EncryptedStorageRepository;
use App\Repositories\Http\AbstractHttpRepository;
use App\Service;
use App\Services\ServiceTask\NotificationSender;
use App\ServiceTask;
use App\User;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\RequestOptions;

class AuditCaseRepository extends AbstractHttpRepository
{
    /**
     * Returns the basic request for AuditCase, returning also the authenticated user.
     * @param string $domain
     * @param string $jwt
     * @return array
     * @throws GuzzleException
     */
    public function checkAuthorization(string $domain, string $jwt): array
    {
        list($url, $headers) = self::buildParams($domain, $jwt, '/');
        return $this->fetchJson($url, $headers);
    }

    /**
     * Returns the clients within AuditCase, for us known as Companies.
     * @param string $domain
     * @param string $jwt
     * @return array
     * @throws GuzzleException
     */
    public function getClients(string $domain, string $jwt): array
    {
        list($url, $headers) = self::buildParams($domain, $jwt, '/viewdata/clients');
        return $this->fetchJson($url, $headers);
    }

    /**
     * @param string $domain
     * @param string $jwt
     * @param ServiceTask $serviceTask
     * @param string $client_id
     * @param ServableTaskFileInterface[] $files
     * @param string $documentType
     * @throws Exception
     */
    public function sendServiceTaskDocuments(
        string $domain,
        string $jwt,
        ServiceTask $serviceTask,
        string $client_id,
        array $files,
        string $documentType
    ): void {
        list ($url, $headers) = self::buildParams(
            $domain,
            $jwt,
            '/document/dossier/create/' . $client_id . '/' . $documentType
        );

        $documentDescription = $serviceTask->title . ($serviceTask->subtitle ? ' - ' . $serviceTask->subtitle : '');

        $postData = [
            [
                'name' => 'ShortDescriptionDO',
                'contents' => $documentDescription
            ],
            [
                'name' => 'AssignmentYear',
                'contents' => $serviceTask->year
            ],
        ];

        foreach ($files as $file) {
            $postData[] = [
                'name' => 'file',
                'filename' => $file->getFilename(),
                'contents' => $file->getContent()
            ];
        }

        try {
            $response = static::postRequest($url, $headers, $postData);

            TaskAuditLogHelper::documentSentToDms($serviceTask, Service::AUDIT_CASE_SERVICE, [
                'url' => $response['PermanentLink'],
                'filename' => $documentDescription
            ]);
        } catch (\RuntimeException $e) {
            $responseMessage = $e->getMessage();

            $notificationKey = 'dms.auditcase.failed_to_send_document';
            $notificationParams = [
                'retry_url' => $serviceTask->account->route('new.task.resend_documents_to_dms', [
                    'service_task_id' => $serviceTask->id
                ])
            ];

            $errorResponse = json_decode($e->getMessage(), 1);

            if (!empty($errorResponse['message'])) {
                $errorMessage = $errorResponse['message'];
            } elseif (!empty($errorResponse['ValidationProblems'])) {
                $errorMessage = $errorResponse['ValidationProblems'][0]['txt'];
            }

            $auditLogKeyAddition = '';
            $auditLogParams = [];
            if (!empty($errorMessage)) {
                $responseMessage = $errorMessage;
                $auditLogKeyAddition = '_with_message';
                $auditLogParams['error_message'] = $errorMessage;
                $notificationKey .= '_with_message';
                $notificationParams['error_message'] = $errorMessage;
            }

            ServiceLog::error(
                'Error occurred sending a document to audit case: #' . $serviceTask->id . ' to url: ' . $url . ' ' . $responseMessage //phpcs:ignore
            );

            TaskAuditLogHelper::errorSendingToDms(
                $serviceTask,
                Service::AUDIT_CASE_SERVICE,
                $auditLogKeyAddition,
                $auditLogParams
            );

            if (
                !empty($serviceTask->sent_by)
                && !empty($sent_by = User::find($serviceTask->sent_by))
                && $sent_by->isActivatedUser()
            ) {
                // If sent_by user is set and active send the notification only to that user.
                NotificationSender::sendNotificationToSentByUser(
                    $serviceTask,
                    $notificationKey,
                    $notificationParams,
                    true
                );
            } else {
                // If the sent_by user is NOT set or inactive, send the notification to all internal users.
                NotificationSender::sendNotificationToInternalUsers(
                    $serviceTask,
                    $notificationKey,
                    $notificationParams,
                    true
                );
            }
        }
    }

    public function sendDocuments(
        string $domain,
        string $jwt,
        string $client_id,
        array $files,
        string $documentType,
        string $documentDescription,
        ?string $year
    ): array {
        list ($url, $headers) = self::buildParams(
            $domain,
            $jwt,
            '/document/dossier/create/' . $client_id . '/' . $documentType
        );

        $postData = [
            [
                'name' => 'ShortDescriptionDO',
                'contents' => $documentDescription
            ],
            [
                'name' => 'AssignmentYear',
                'contents' => $year ?? ''
            ],
        ];

        $encryptedStorageRepository = App::make(EncryptedStorageRepository::class);

        foreach ($files as $filePath) {
            $postData[] = [
                'name' => 'file',
                'filename' => basename($filePath),
                'contents' => $encryptedStorageRepository->getFile($filePath)
            ];
        }

        return static::postRequest($url, $headers, $postData);
    }

    /**
     * Takes the variables known and turn it into an array used by the curl request.
     * @param string $domain FQDN for Example: demo.changetocomm.nl
     * @param string $jwt
     * @param string $endpoint Path that goes after domain/ac/api
     * @return array
     */
    private static function buildParams(string $domain, string $jwt, string $endpoint): array
    {
        return [
            'https://' . $domain . '/ac/api' . $endpoint,
            [
                'X-API-KEY' => $jwt
            ],
        ];
    }

    /**
     * @param string $url Absolute URL
     * @param array $headers
     * @return array JSON Content decoded to associative array
     * @throws GuzzleException
     */
    private function fetchJson(string $url, array $headers): array
    {
        return json_decode($this->fetchContent($url, $headers), 1);
    }

    /**
     * Fetch raw content from AuditCase Servers.
     * @param string $url Absolute URL
     * @param array $headers
     * @return string Usually JSON but can also be the content of a binary file (XLSX for user export)
     * @throws GuzzleException
     */
    private function fetchContent(string $url, array $headers): string
    {
        try {
            $response = $this->client->get(
                $url,
                [
                    'headers' => $headers
                ]
            );
        } catch (ServerException $exception) {
            throw new ServiceUnavailableException('Auditcase server responses with 5xx status ' . $url);
        } catch (ConnectException $exception) {
            throw new \App\Exceptions\ConnectException('Failed to connect to AuditCase ' . $url);
        }
        return $response->getBody()->getContents();
    }

    private static function postRequest(string $url, array $headers, array $data): array
    {
        $client = new Client();
        $options = [
            RequestOptions::MULTIPART => $data,
            RequestOptions::HEADERS => $headers
        ];

        try {
            $response = $client->request('POST', $url, $options);
        } catch (GuzzleException $e) {
            $message = 'AuditCase error occurred. Error message: ' . $e->getMessage();
            ServiceLog::error($message);
            throw new \RuntimeException($e->getResponse()->getBody(true), $e->getCode(), $e);
        }

        return json_decode($response->getBody()->getContents(), 1);
    }

    public function getUserSpreadsheet(string $domain, string $jwt): string
    {
        list($url, $headers) = self::buildParams($domain, $jwt, '/export/full/excel');
        return $this->fetchContent($url, $headers);
    }
}
