<?php

namespace App\Repositories\Http;

use GuzzleHttp\Client;
use Psr\Http\Message\ResponseInterface;

class AbstractHttpRepository
{
    protected Client $client;
    public string $response;
    public ResponseInterface $httpResponse;

    public function __construct(Client $client)
    {
        $this->client = $client;
    }

    public function get(string $url, array $options = []): array
    {
        $this->httpResponse = $this->client->get($url, $options);
        $this->response = $this->httpResponse->getBody()->getContents();
        return json_decode($this->response, true, flags: JSON_THROW_ON_ERROR);
    }

    public function put(string $url, array $options = []): array
    {
        $this->httpResponse = $this->client->put($url, $options);
        $this->response = $this->httpResponse->getBody()->getContents();
        return json_decode($this->response, true, flags: JSON_THROW_ON_ERROR);
    }

    public function post(string $url, array $options = []): array
    {
        $this->httpResponse = $this->client->post($url, $options);
        $this->response = $this->httpResponse->getBody()->getContents();
        return json_decode($this->response, true, flags: JSON_THROW_ON_ERROR);
    }

    public function getResponse(string $url, array $options = []): ResponseInterface
    {
        return $this->client->get($url, $options);
    }

    public function postResponse(string $url, array $options = []): ResponseInterface
    {
        return $this->client->post($url, $options);
    }
}
