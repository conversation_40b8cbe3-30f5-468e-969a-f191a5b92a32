<?php

namespace App\Repositories\Http;

use App\Exceptions\Api\Exception;
use App\Support\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Log;

class SmtpLogsRepository extends AbstractHttpRepository
{
    /**
     * @var array
     */
    protected array $credentials;

    /**
     *
     */
    protected const LOGIN_URL = 'https://srv01.exofilter.com/index.php';
    /**
     *
     */
    protected const LOGS_URL = 'https://srv01.exofilter.com/master/log/submission///';
    /**
     *
     */
    protected const LOGOUT_URL = 'https://srv01.exofilter.com/index.php?action=logout';

    /**
     * @var string|null
     */
    protected ?string $auth_token;

    /**
     * @var string
     */
    protected string $latest_logs;

    /**
     * SmtpLogsRepository constructor.
     * @param array $credentials
     */
    public function __construct(array $credentials)
    {
        $this->credentials = $credentials;
        $client = new Client([
                'cookies' => true,
                'allow_redirects' => true,
                'headers' => [
                    'Accept' => '*/*',
                    // phpcs:ignore
                    'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36',
                    'Sec-Fetch-Site' => 'same-origin',
                    'Sec-Fetch-Mode' => 'cors',
                    'Accept-Encoding' => 'gzip, deflate, br',
                    'Accept-Language' => 'en-GB,en-US;q=0.9,en;q=0.8'
                ]
            ]);
        parent::__construct($client);
    }

    /** SMTP logs are fetched as follows:
     * 1. Login to ExoFilter with Post Request, save apiToken present in raw text using a pattern.
     * 2. Use api token to make a Get Request for getting smtp logs in Json.
     * 3. Make a Get Request for Logout.
     * @return array
     *
     */
    public function getLogs(): array
    {
        try {
            if ($this->makeLoginRequest()) {
                if (isset($this->auth_token)) {
                    $logs = json_decode($this->fetchLatestLogs(), true);

                    $this->makeLogoutRequest();
                    return $logs['objects'];
                } else {
                    Log::error('smtp-logs-sync error: auth token not found after login request.');
                    return [];
                }
            }
        } catch (Exception $e) {
            Log::error('smtp-logs-sync error: ' . $e->getMessage());
        }
    }

    /**
     * @return bool
     */
    protected function makeLogoutRequest(): bool
    {

        $response = $this->getResponse($this::LOGOUT_URL);
        if ($response->getStatusCode() === 200) {
            return true;
        }
        return false;
    }

    /**
     * @return bool
     */
    protected function makeLoginRequest(): bool
    {
        $options = [
            'form_params' => [
                'username' => $this->credentials['user'],
                'password' => $this->credentials['pass'],
            ]];

        try {
            $response = $this->postResponse($this::LOGIN_URL, $options);
            if ($response->getStatusCode() === 200) {
                $this->saveApiToken($response->getBody()->getContents());
                return true;
            }
        } catch (GuzzleException $e) {
            Log::error('smtp-logs-sync error: failed to login to the logs website. ' . $e->getMessage());
            return false;
        }
    }

    /**
     * @param string $response
     */
    protected function saveApiToken(string $response): void
    {
        preg_match('/"apiToken":"([a-z|A-Z||0-9|-]+)"/', $response, $matches);
        $this->auth_token = $matches[1] ?? null;
    }

    /**
     * @return string
     */
    protected function fetchLatestLogs()
    {

        $options = [
            'headers' => [
                'HTTP-X-AUTH-TOKEN' => $this->auth_token,
            ],
            'query' => [
                'client' => $this->credentials['user'],
                'client_ip' => $this->getServerIp(),
                'page' => '1',
                'page_size' => '1000',
                // phpcs:ignore
                'q' => '{"filters":[{"and":[{"name":"datetime","op":">=","val":"45 minutes ago"},{"name":"datetime","op":"<","val":"0 days ago"}]}],"fields":[{"field":"message_id"},{"field":"datetime"},{"field":"sender"},{"field":"recipient"},{"field":"from_header"},{"field":"to_header"},{"field":"message_id_header"},{"field":"status"},{"field":"delivery_data"},{"field":"domain"},{"field":"filtering_host"},{"field":"filtering_host"},{"field":"sender"}],"order_by":[{"field":"datetime","direction":"desc"}],"page_count":false}',
            ]
        ];
        try {
            $response = $this->getResponse($this::LOGS_URL, $options);

            if ($response->getStatusCode() === 200) {
                Log::info('smtp-logs-sync: latest logs were successfully fetched at ' . Carbon::now()->toDateTimeString()); // phpcs:ignore
                return $response->getBody()->getContents();
            }
        } catch (GuzzleException $e) {
            Log::error('smtp-logs-sync error: failed to fetch logs from the server. error-message: ' . $e->getMessage()); // phpcs:ignore
        }
    }

    /**
     * @return string
     */
    protected function getServerIp(): string
    {
        return file_get_contents('https://ipecho.net/plain');
    }
}
