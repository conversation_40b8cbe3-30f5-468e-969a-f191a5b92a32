<?php

namespace App\Repositories;

use App\AccountService;
use App\Company;
use App\Models\Dms\ServiceTaskCompanyDmsPreference;
use App\ServiceTask;

class ServiceTaskCompanyDmsPreferenceRepository
{
    public function getByCompanyAndTaskType(
        Company $company,
        string $taskType,
        ?int $uploadTypeId,
        int $accountServiceId
    ): ?ServiceTaskCompanyDmsPreference {
        return $company->serviceTaskDmsPreferences()
            ->where(ServiceTaskCompanyDmsPreference::TASK_TYPE, $taskType)
            ->where(ServiceTaskCompanyDmsPreference::UPLOAD_TYPE_ID, $uploadTypeId)
            ->where(ServiceTaskCompanyDmsPreference::ACCOUNT_SERVICE_ID, $accountServiceId)
            ->first();
    }

    public function saveForCompanyAndTaskType(
        Company $company,
        int $accountServiceId,
        string $taskType,
        ?int $uploadTypeId,
        array $preferences
    ): ServiceTaskCompanyDmsPreference {
        return $company->serviceTaskDmsPreferences()->updateOrCreate([
            ServiceTaskCompanyDmsPreference::COMPANY_ID => $company->id,
            ServiceTaskCompanyDmsPreference::ACCOUNT_SERVICE_ID => $accountServiceId,
            ServiceTaskCompanyDmsPreference::TASK_TYPE => $taskType,
            ServiceTaskCompanyDmsPreference::UPLOAD_TYPE_ID => $uploadTypeId
        ], [
            ServiceTaskCompanyDmsPreference::PREFERENCES => $preferences
        ]);
    }
}
