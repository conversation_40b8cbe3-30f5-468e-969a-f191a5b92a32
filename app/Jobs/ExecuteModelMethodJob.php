<?php

namespace App\Jobs;

use App\OldModel;

class ExecuteModelMethodJob extends Job
{
    private OldModel $model;
    private string $method;

    public function __construct(OldModel $model, string $method)
    {
        if (!method_exists($model, $method)) {
            trigger_error("The requested method $method does not exist in this model" . get_class($model));
        }
        $this->model = $model;
        $this->method = $method;
    }

    public function tags(): array
    {
        return ['executeModelMethod', 'method:' . $this->method, 'model:' . get_class($this->model)];
    }

    public function handle()
    {
        $this->model->{$this->method}();
    }
}
