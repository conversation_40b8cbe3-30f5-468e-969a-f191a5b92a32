<?php

namespace App\Jobs\RemoteWorker;

use App\Enums\QueueName;
use App\Jobs\Job;
use App\Repositories\Services\AccountServiceRepository;
use App\Services\RemoteWorker\RemoteWorkerService;
use Log;

class RemoteWorkerResultJob extends Job
{
    public $queue = QueueName::IMPORT;

    public function __construct(
        private string $path
    ) {
    }

    public function handle(): void
    {
        Log::channel('remote-worker')->debug('Started result processing job for ' . $this->path);
        $remoteWorkerService = resolve(RemoteWorkerService::class);
        $accountServiceRepo = resolve(AccountServiceRepository::class);
        $pathinfo = $remoteWorkerService->parseResultPath($this->path);
        $accountService = $accountServiceRepo->getById($pathinfo['account_service_id']);
        $provider = $accountService->getProvider();
        /* @var $provider \App\Services\Service\Crm\AlureOnPremiseProvider */
        $token = $provider->getInstructorToken();
        $password = $remoteWorkerService->getResultZipPassword($token);
        $tempDir = $remoteWorkerService->unzipResults($this->path, $password);
        Log::channel('remote-worker')->debug('Extracted results from ZIP into temp dir ' . $tempDir);
        $importService = $provider->getImportService();
        Log::channel('remote-worker')->debug('Loaded import service ' . $importService::class);
        $context = $provider->getImportContext();
        $importService->import($tempDir, $context, $accountService);

        $remoteWorkerService->cleanupTempResults($tempDir);
        Log::channel('remote-worker')->debug('Deleted temp dir ' . $tempDir);
        Log::channel('remote-worker')->debug('Completed result processing job for ' . $this->path);
    }
}
