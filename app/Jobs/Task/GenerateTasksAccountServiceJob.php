<?php

namespace App\Jobs\Task;

use App\AccountService;
use App\Factories\Services\AccountServiceProviderFactory;
use App\Jobs\Job;
use App\Logging\Channels\ServiceLog;
use App\Services\ServiceTask\TaskGenerator;

/**
 * Generate service tasks for a specific account service.
 * This job is dispatched once a company is connected to a service.
 * This is done through a job because a single company may call several (slow) external APIs.
 *
 * OPTIONS (second parameter in constructor)
 * changedAfterDate: is used by Fiscaal Gemak and contains a date time string YYYY-MM-DD HH:mm:ss
 * scheduled: boolean, false by default. Should be set to TRUE for automatic syncs that need to check custom rules.
 *
 * Class GenerateTasksAccountServiceJob
 */
class GenerateTasksAccountServiceJob extends Job
{
    private int $accountServiceId;
    private array $jobOptions;
    private bool $scheduled = false;

    /**
     * GenerateTasksAccountServiceJob constructor.
     * Beware the parameters should not include relations that are not used as the entire objects will be serialized
     * and stored in Redis memory until the job is done. We don't use IDs as parameters because then the job would need
     * to do queries to the MySQL database.
     * @param int $accountServiceId
     * @param array $jobOptions Optional array with additional settings. See class comment (above) for more info.
     */
    public function __construct(int $accountServiceId, array $jobOptions = [])
    {
        $this->accountServiceId = $accountServiceId;
        $this->jobOptions = $jobOptions;
        if (isset($jobOptions['scheduled'])) {
            $this->scheduled = $jobOptions['scheduled'];
        }
    }

    public function tags(): array
    {
        $tags = [
            'generateTasks',
            'accountService',
            'accountService:' . $this->accountServiceId
        ];

        if ($this->scheduled) {
            $tags[] = 'scheduled';
        }

        return $tags;
    }

    public function handle(): void
    {
        $accountService = AccountService::findOrFail($this->accountServiceId);
        $accountService->jobOptions = $this->jobOptions;
        $bufferFile = 'Account/' . $accountService->account_id . '/log/as-' . $accountService->id . '.log';
        ServiceLog::startBuffer();
        try {
            ServiceLog::clearBufferFile($bufferFile);
            $this->generateTasksFromAccountService($accountService);
        } catch (\Throwable $e) {
            ServiceLog::error(
                'Job could not generate tasks : ' . $e->getMessage(),
                ['line' => $e->getLine(),
                    'file' => $e->getFile(),
                    'message' => $e->getMessage(),
                    'exception' => get_class($e),
                    'account_id' => $accountService->account_id,
                    'trace' => $e->getTraceAsString()
                ]
            );
            if (isset($bufferFile)) {
                ServiceLog::writeBuffer($bufferFile);
            }
            throw $e;
        }
        if (isset($bufferFile)) {
            ServiceLog::writeBuffer($bufferFile);
        }
    }

    private function generateTasksFromAccountService(AccountService $accountService)
    {
        $taskGenerator = resolve(TaskGenerator::class);
        // Check if the account service should run in a job.
        if (!AccountServiceProviderFactory::hasTaskProvider($accountService)) {
            ServiceLog::warning('AccountService #' . $accountService->id . ' (' . $accountService->service->reference_name . ') cannot run as a job.'); //phpcs:ignore
            return;
        }

        $tasks = $taskGenerator->generateForAccountService($accountService, $this->scheduled);
        ServiceLog::info('Generated ' . count($tasks) . ' tasks for account service #' . $accountService->id . ' in job.'); // phpcs:ignore
    }
}
