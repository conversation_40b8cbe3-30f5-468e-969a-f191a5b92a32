<?php

namespace App\Jobs\Dms;

use App;
use App\Jobs\Job;
use App\Services\Dms\DmsService;
use App\ServiceTask;

class DmsSendServiceTaskDocumentsJob extends Job
{
    protected int $serviceTaskId;

    public function __construct(int $serviceTaskId)
    {
        $this->serviceTaskId = $serviceTaskId;
    }

    public function tags(): array
    {
        return ['dmsSendServiceTaskDocuments', 'task:' . $this->serviceTaskId];
    }

    public function handle()
    {
        $serviceTask = ServiceTask::findOrFail($this->serviceTaskId);
        App::make(DmsService::class)->sendServiceTaskDocuments($serviceTask);
    }
}
