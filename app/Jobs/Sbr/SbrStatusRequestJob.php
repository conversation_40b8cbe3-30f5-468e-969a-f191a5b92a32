<?php

namespace App\Jobs\Sbr;

use App\Enums\QueueName;
use App\Jobs\Job;
use App\Logging\Channels\ServiceLog;
use App\Models\Sbr\SbrRequest;
use App\Services\Gateway\Sbr\SbrStatusChecker;
use App\Services\SbrRequestService;

class SbrStatusRequestJob extends Job
{
    public $queue = QueueName::SBR;

    private SbrRequest $request;

    public function __construct(int $requestId)
    {
        $this->request = SbrRequest::findOrFail($requestId);
    }

    public function tags(): array
    {
        return ['sbrStatus', 'sbr', 'sbr:' . $this->request->id, 'task:' . $this->request->service_task_id];
    }

    public function handle()
    {
        $checker = new SbrStatusChecker($this->request->external_id);
        $checker->doRequest();
        $status = $checker->getActualStatus();

        $sbrRequestService = resolve(SbrRequestService::class);
        $sbrRequestService->updateFromStatus($this->request, $status);
        ServiceLog::info('Status check for task #' . $this->request->service_task_id . ' (SbrRequest #' . $this->request->id . ') returned ' . json_encode($status->getArray())); //phpcs:ignore

        if ($status->isError()) {
            ServiceLog::error('Error for task #' . $this->request->service_task_id . ' (SbrRequest #' . $this->request->id . ') returned ' . $checker->response); //phpcs:ignore
        }

        if ($this->request->isTakingLong()) {
            ServiceLog::warning('SbrRequest #' . $this->request->id . ' is taking very long too be processed.');
        }
    }
}
