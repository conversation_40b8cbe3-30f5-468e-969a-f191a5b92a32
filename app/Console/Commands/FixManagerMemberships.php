<?php

namespace App\Console\Commands;

use App\Membership;
use Illuminate\Console\Command;
use App\User;
use DB;
use App\Helpers\MemoryHelper;

class FixManagerMemberships extends Command
{
    protected $signature = 'memberships:fix_manager_memberships {--force}';
    protected $description = 'This command make memberships into member where the user is already manager from parent group (to keep widget settings intact)'; //phpcs:ignore

    public function handle(): void
    {
        MemoryHelper::setMemoryLimit('512M');
        try {
            DB::beginTransaction();
            User::whereNotNull(User::MANAGER_LEVEL)->chunk(100, function ($users) {
                foreach ($users as $user) {
                    $min = $user->memberships()->where('type', 'manager')->min('level');
                    if ($min != $user->manager_level) {
                        $this->comment('user #' . $user->id . ' manager_membership_level:' . ($min ?? 'none') . ' manager_level:' . $user->manager_level); //phpcs:ignore

                        if (is_null($min)) {
                            DB::table('users')
                                ->where('id', $user->id)
                                ->update([User::MANAGER_LEVEL => null]);
                            $user->manager_level = null;
                            $this->comment('user #' . $user->id . ' manager level set to null');
                        } elseif (!is_null($min) && $min > $user->manager_level) {
                            DB::table('users')
                                ->where('id', $user->id)
                                ->update([User::MANAGER_LEVEL => $min]);
                            $user->manager_level = $min;
                            $this->comment('user #' . $user->id . ' manager level set to ' . $min);
                        } else {
                            $this->error('Could not repair user #' . $user->id);
                        }
                    }
                    if (!is_null($user->manager_level)) {
                        $memberships = $user->memberships()
                            ->where(Membership::TYPE, 'manager')
                            ->where(Membership::LEVEL, '>', $user->manager_level)
                            ->get();

                        foreach ($memberships as $membership) {
                            if ($membership->level > 0) {
                                $membershipContextParent = $membership->context->parent;
                                if (!empty($membershipContextParent) && $membershipContextParent->isManagedBy($user)) {
                                    DB::table('memberships')
                                        ->where('id', $membership->id)
                                        ->update(['type' => 'member']);
                                    $this->comment('Changing membership #' . $membership->id . ' from manager to member'); //phpcs:ignore
                                }
                            }
                        }
                    }
                }
                $this->comment('FixManagerMemberships processed ' . $users->count() . ' managers', 'quiet'); //phpcs:ignore
            });

            if ($this->option('force') || $this->confirm('Commit these changes?', false)) {
                DB::commit();
                $this->comment('Changes were committed.');
            } else {
                DB::rollback();
                $this->info('Changes reverted with rollback.');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
        }
    }
}
