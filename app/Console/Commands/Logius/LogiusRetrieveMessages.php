<?php

namespace App\Console\Commands\Logius;

use App\Account;
use App\Service;
use App\Console\Command;
use App\Services\Declarations\DeclarationsService;
use App\Services\Gateway\Logius\LogiusSbaProvider;

class LogiusRetrieveMessages extends Command
{
    protected $signature = 'logius:retrieve-messages {account}';
    protected $description = 'Retrieve SBA message from Logius and create tasks';

    public function handle(): int
    {
        $accountId = $this->argument('account');

        if (isset($accountId)) {
            $account = Account::findOrFail($accountId);
            $this->info('Found account #' . $account->id . ' ' . $account->name);
        }

        $accountService = $this->accountServiceRepository
            ->getByReferenceName(Service::LOGIUS_SBA_PROVIDER, $account)
            ->first();

        /** @var $provider LogiusSbaProvider */
        $provider = $accountService->getProvider();

        $datas = $provider->getTasksFromAccountService();
        $this->info('Found ' . count($datas) . ' task data items');
        $declarationService = resolve(DeclarationsService::class);
        foreach ($datas as $data) {
            $declarationService->generateTask($accountService, $data);
        }

        return self::SUCCESS;
    }
}
