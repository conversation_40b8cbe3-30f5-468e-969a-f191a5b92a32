<?php

namespace App\Console\Commands\Cleanup;

use App\Console\Command;
use App\Logging\Channels\ServiceLog;
use App\InternetKassa;
use App\Repositories\Services\InternetKassaRepository;
use DB;

class CleanupInternetKassa extends Command
{
    protected $signature = 'cleanup:internetkassa {--force}';
    protected $description = 'Clean up expired internetkassa payments';

    public function handle(InternetKassaRepository $repo): int
    {
        try {
            $payments = $repo->getExpired();
            DB::beginTransaction();

            $this->info('Found ' . count($payments) . ' expired payments');

            $deleted = 0;
            /**
             * @var $payment InternetKassa
             */
            foreach ($payments as $payment) {
                $payment->delete();
                $deleted++;
            }

            if ($deleted > 0) {
                $message = 'Deleting ' . $deleted . ' expired  Internetkassa payments';
                $this->slack($message);
                ServiceLog::info($message);

                if (
                    $this->option('force') || $this->confirm(
                        'Commit these changes? The Internetkassa payments will be hard deleted!',
                        false
                    )
                ) {
                    $this->slack('Deleted ' . $deleted . ' Internetkassa payments.');
                } else {
                    DB::rollback();
                    $this->info('Changes reverted with rollback.');
                }
            }
            DB::commit();
            return static::SUCCESS;
        } catch (\Throwable $e) {
            DB::rollBack();
            ServiceLog::error('Error deleting Internetkassa payments: ' . $e::class . ' - ' . $e->getMessage());
            $this->error($e->getMessage());
            throw $e;
        }
    }
}
