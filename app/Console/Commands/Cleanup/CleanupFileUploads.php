<?php

namespace App\Console\Commands\Cleanup;

use App\Models\FileUploads\Upload;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File as FileSystem;
use Log;

class CleanupFileUploads extends Command
{
    protected $signature = 'cleanup:file-uploads';

    protected $description = 'Deletes outdated uploads from file system and database';

    public function handle(): int
    {
        $this->softDeleteOutdated();
        $this->hardDeleteExpired();
        $this->hardDeleteUnverified();
        return 0;
    }

    /**
     *  Soft Deletes verified uploads that are older than 30 days.
     * @return void
     */
    private function softDeleteOutdated(): void
    {
        $uploads = Upload::query()
            ->where('expire_at', '<', now())
            ->whereNotNull('verified_at')
            ->cursor();

        foreach ($uploads as $upload) {
            /* @var Upload $upload */
            // deletes dir and all files inside
            FileSystem::deleteDirectory(dirname($upload->uploadedFiles()->withTrashed()->first()->path));
            $upload->uploadedFiles()->delete();
            $upload->delete();
            Log::info('Upload #' . $upload->id . ' has been SoftDeleted.');
        }
    }

    /**
     *  Hard deletes uploads that are soft deleted and older than 60 days.
     * @return void
     */
    private function hardDeleteExpired(): void
    {
        $uploads = Upload::query()
            ->withTrashed()
            ->with('uploadedFiles')
            ->where('deleted_at', '<', now()->subDays(30))
            ->whereNotNull('verified_at')
            ->cursor();

        foreach ($uploads as $upload) {
            $upload->uploadedFiles()->forceDelete();
            $upload->forceDelete();
            Log::info('Upload #' . $upload->id . ' has been HardDeleted.');
        }
    }

    /**
     * Hard deletes unverified uploads that are older than 1 day.
     * @return void
     */
    private function hardDeleteUnverified(): void
    {
        $uploads = Upload::query()
            ->withTrashed()
            ->with('uploadedFiles')
            ->where('created_at', '<', now()->subDay())
            ->whereNull('verified_at')
            ->cursor();

        foreach ($uploads as $upload) {
            if ($upload->uploadedFiles()->withTrashed()->exists()) {
                FileSystem::deleteDirectory(dirname($upload->uploadedFiles()->withTrashed()->first()->path));
                $upload->uploadedFiles()->withTrashed()->forceDelete();
            }
            $upload->forceDelete();
            Log::info('Unverified Upload #' . $upload->id . ' has been HardDeleted.');
        }
    }
}
