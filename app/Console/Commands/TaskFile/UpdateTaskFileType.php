<?php

namespace App\Console\Commands\TaskFile;

use App\Models\TaskFile;
use Database\Helpers\MigrationHelper;
use Illuminate\Console\Command;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;

class UpdateTaskFileType extends Command
{
    protected $signature = 'update:task-file-type {--revert}';
    protected $description = 'Update task_files type column to enum';

    public function handle()
    {
        if ($this->option('revert')) {
            $this->revert();
            return;
        }

        if ($this->confirm('Are you sure you want to do this?')) {
            $this->migrate();
            return;
        }

        $this->info('Nothing was done.');
    }

    public function migrate(): void
    {
        MigrationHelper::alterEnum('task_files', 'type', [
            TaskFile::TYPE_PUBLICATION_DOCUMENT,
            TaskFile::TYPE_PUBLICATION_DOCUMENT_PDF,
            TaskFile::TYPE_AUDIT_REPORT,
            TaskFile::TYPE_AUDIT_REPORT_PDF,
            TaskFile::TYPE_ANNUAL_REPORT,
            TaskFile::TYPE_SIGNATURE_XML,
            TaskFile::TYPE_DISPLAY_SUMMARY,
            TaskFile::TYPE_AUDIT_LOG,
            TaskFile::TYPE_ATTACHMENT,
            TaskFile::TYPE_LOR,
            TaskFile::TYPE_NOTULEN_AVA,
            TaskFile::TYPE_ICT_DECLARATION,
            TaskFile::TYPE_VAT_DECLARATION,
            TaskFile::TYPE_VAT_SUPPLEMENTATION,
            TaskFile::TYPE_INCOME_TAX_DECLARATION,
            TaskFile::TYPE_CORPORATE_TAX_DECLARATION,
            TaskFile::TYPE_WAGE_TAX_DECLARATION,
            TaskFile::TYPE_SEPA_SALARY,
            TaskFile::TYPE_SEPA_TAX,
            TaskFile::TYPE_SBA_MESSAGE,
            TaskFile::TYPE_SBR_YEARWORK,
            TaskFile::TYPE_DIVIDEND_TAX_DECLARATION,
            TaskFile::TYPE_GENERIC_XML,
            TaskFile::TYPE_DOCUMENT_APPROVAL
        ]);
        $this->info('task_files migrated successfully.');
    }

    public function revert(): void
    {
        DB::statement('ALTER TABLE task_files MODIFY COLUMN type VARCHAR(255)');

        $this->info('task_files type column reverted to varchar.');
    }
}
