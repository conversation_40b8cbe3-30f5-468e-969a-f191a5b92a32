<?php

namespace App\Console\Commands\Service;

use App\Console\CommandTrait;
use App\Repositories\Services\ServiceTaskRepository;
use App\ServiceTask;
use Illuminate\Console\Command;
use App\Support\Database\DBHelper;

class ServiceTaskDelete extends Command
{
    use CommandTrait;

    protected $signature = 'service:task-delete {ids}';
    protected $description = 'Hard delete a service task and all associated things.';

    public function handle()
    {
        DBHelper::runOnTransaction(
            function () {
                $ids = explode(',', $this->argument('ids'));
                $tasks = ServiceTask::withTrashed()->whereIn('id', $ids)->get();

                if (count($tasks) === 0) {
                    $this->error('No tasks found');
                    exit();
                }

                $prevAccountId = null;
                $c = 1;
                foreach ($tasks as $task) {
                    $this->printTaskData($task, $c);
                    if (!empty($task->relatedTasks()->count() > 1)) {
                        $this->warn('This task belongs to a task group. These tasks will also be deleted:');

                        //show the same stuff for all the tasks which are not the selected task.
                        foreach (
                            $task->relatedTasks()->where(ServiceTask::ID, '!=', $task->id)->get() as $serviceTask
                        ) {
                            $this->printTaskData($serviceTask, $c);
                        }
                    }
                    if (isset($prevAccountId) && $prevAccountId != $task->account->id) {
                        $this->warn('Task #' . $task->id . ' belongs to account #' . $task->account_id . ' and not #' . $prevAccountId); //phpcs:ignore
                    }
                    $prevAccountId = $task->account_id;
                    $c++;
                }
                $reason = $this->ask('Reason for deleting ' . count($tasks) . ' task(s) (for events log)');
                if (strlen($reason) < 4) {
                    $this->error('No good reason given.');
                    exit();
                }
                foreach ($tasks as $task) {
                    resolve(ServiceTaskRepository::class)->hardDeleteAll($task, $reason);
                }
                return $task;
            },
            function (\Exception $e) {
                $this->error($e->getMessage());
            },
            function () {
                if ($this->confirm('Commit these changes?', false)) {
                    $this->info('Deleted task(s).');
                    return true;
                }
                return false;
            }
        );
    }

    public function printTaskData(ServiceTask $task, int $c): void
    {
        $indent = str_repeat(' ', strlen($c) + 2);
        $this->info($c . '. Task #' . $task->id . ' ' . $task->title . ' for account #' . $task->account->id . ' ' . $task->account->name); //phpcs:ignore
        $this->info($indent . 'Type: ' . $task->type . '  Round: ' . $task->round . '  Status: ' . $task->status);
        $this->info($indent . count($task->taskResponses) . ' responses');
    }
}
