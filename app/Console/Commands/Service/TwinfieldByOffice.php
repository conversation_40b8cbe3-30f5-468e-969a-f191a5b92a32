<?php

namespace App\Console\Commands\Service;

use App\AccountService;
use App\Console\CommandTrait;
use App\Services\Declarations\DeclarationsService;
use App\Services\Gateway\Twinfield\TwinfieldTaskProvider;
use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputArgument;

class TwinfieldByOffice extends Command
{
    use CommandTrait;

    protected $signature = 'twinfield:by-office {account_service_id} {office_id}';
    protected $description = 'Check twinfield service for task by office id';

    protected function getArguments(): array
    {
        return [
            ['account_service_id', InputArgument::REQUIRED, 'Account Service ID'],
            ['office_id', InputArgument::REQUIRED, 'Twinfield Office ID'],
        ];
    }

    public function handle()
    {
        // For extra debugging turn this on.
        $throwError = false;

        $accountServiceId = $this->argument('account_service_id');
        $officeId = $this->argument('office_id');

        $this->info('syncing Twinfield service #' . $accountServiceId . ' with officeId #' . $officeId);

        $accountService = AccountService::findOrFail($accountServiceId);
        /** @var TwinfieldTaskProvider $twinfieldTaskProvider */
        $twinfieldTaskProvider = $accountService->getProvider();
        $declarationDatas = $twinfieldTaskProvider->getTasksByOfficeId($officeId);
        $this->info('Creating Twinfield declarations for officeId: ' . $officeId);
        $declarationService = resolve(DeclarationsService::class);
        $declarationService->generateTasks($accountService, $declarationDatas, throwError: $throwError);
    }
}
