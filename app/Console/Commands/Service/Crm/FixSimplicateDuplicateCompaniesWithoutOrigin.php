<?php

namespace App\Console\Commands\Service\Crm;

use App\AccountServiceCompany;
use App\Company;
use App\Console\Command;
use App\Repositories\Services\AccountServiceCompanyRepository;
use App\Repositories\Services\AccountServiceRepository;
use App\Service;
use DB;
use Illuminate\Support\Str;
use Throwable;

class FixSimplicateDuplicateCompaniesWithoutOrigin extends Command
{
    protected $signature = 'service:simplicate-fix-duplicates {account_service_id} {fix_not_found?}';
    protected $description = 'Fix duplicates created by companies without origin simplicate.';

    public function handle(): int
    {
        $migrated = 0;
        $accountServiceId = intval($this->argument('account_service_id'));
        $fixNotFound = $this->argument('fix_not_found') === '1';
        $accountServiceRepository = resolve(AccountServiceRepository::class);
        $accountService = $accountServiceRepository->getById($accountServiceId);

        if (empty($accountService)) {
            $this->warn('Could not find AccountService #' . $accountServiceId);
            return self::FAILURE;
        }

        if ($accountService->service->reference_name !== Service::SIMPLICATE_CRM_SERVICE) {
            $this->warn('AccountService #' . $accountServiceId . ' is not a Simplicate service');
            return self::FAILURE;
        }

        $accountServiceCompanyRepository = resolve(AccountServiceCompanyRepository::class);
        $accountServiceCompanies = $accountServiceCompanyRepository->getByAccountService($accountService);
        DB::beginTransaction();
        try {
            foreach ($accountServiceCompanies as $accountServiceCompany) {
                // Check if a company was created with (1) at the end.
                $wrongCompany = $accountServiceCompany->company;
                if (Str::endsWith($wrongCompany->name, ' (1)')) {
                    $this->info('Found company #' . $wrongCompany->id . ' ' . $wrongCompany->name);

                    // Get the original company name.
                    $companyNameWithoutNumber = mb_substr($wrongCompany->name, 0, (strlen($wrongCompany->name) - 4));
                    /** @var Company $originalCompany */
                    $originalCompany = Company::query()
                        ->where(Company::ACCOUNT_ID, $accountService->account_id)
                        ->where(Company::NAME, $companyNameWithoutNumber)
                        ->first();

                    // Check if the original company still exists, if not we skip this company.
                    if (empty($originalCompany)) {
                        if ($fixNotFound) {
                            $this->info('Company without (1) not found, Fixing #' . $wrongCompany->id);
                            $wrongCompany->name = Str::replace(' (1)', '', $wrongCompany->name);
                            $wrongCompany->save();
                            $migrated++;
                        } else {
                            $this->warn('Company #' . $wrongCompany->id . '. Original company without (1) not found.'); //phpcs:ignore
                        }

                        continue;
                    } else {
                        $this->info('Found matching company #' . $originalCompany->id . ' ' . $originalCompany->name);
                    }

                    if (
                        $originalCompany->account_services()
                            ->where(AccountServiceCompany::ACCOUNT_SERVICE_ID, $accountService->id)
                            ->exists()
                    ) {
                        $this->warn('Skipping migrating company #' . $wrongCompany->id . ' to company #' . $originalCompany->id . '. Both have a service connection with the same service.'); // phpcs:ignore
                        continue;
                    }

                    if ($wrongCompany->openQuestions()->exists()) {
                        $this->warn('Skipping migrating company #' . $wrongCompany->id . ' to company #' . $originalCompany->id . '. This company has questions.'); //phpcs:ignore
                        continue;
                    }

                    if ($wrongCompany->tasks()->exists()) {
                        $this->warn('Skipping migrating company #' . $wrongCompany->id . ' to company #' . $originalCompany->id . '. This company has tasks.'); //phpcs:ignore
                        continue;
                    }

                    $this->info('Setting company #' . $originalCompany->id . ' with the external_id and settings from company #' . $wrongCompany->id); //phpcs:ignore
                    $originalCompany->external_id = $wrongCompany->external_id;
                    $originalCompany->settings = $wrongCompany->settings;
                    $originalCompany->save();

                    $this->info('Setting AccountServiceCompany->company_id to #' . $originalCompany->id);
                    $accountServiceCompany->company_id = $originalCompany->id;
                    $accountServiceCompany->save();

                    $this->info('Deleting wrong company #' . $wrongCompany->id);
                    $deleted = $wrongCompany->delete();

                    if (!$deleted) {
                        $this->warn('Could not delete company #' . $wrongCompany->id); //phpcs:ignore
                        continue;
                    }
                    $this->info('Successfully migrated company #' . $wrongCompany->id . ' to company #' . $originalCompany->id); //phpcs:ignore

                    $migrated++;
                }
            }

            if ($migrated === 0) {
                $this->info('Nothing was changed.');
                // Just in case
                DB::rollBack();
                return self::SUCCESS;
            }

            if ($migrated > 0 && $this->confirm('Are you sure you wish to commit these changes?')) {
                $this->info('Changes were saved.');
                DB::commit();
            } else {
                $this->info('Changes were rolled back.');
                DB::rollBack();
            }
        } catch (Throwable $e) {
            $this->info('Changes were rolled back due to an exception.');
            DB::rollBack();
            $this->error($e::class . ' - ' . $e->getMessage() . ' - ' . $e->getFile() . ' ' . $e->getLine());
        }
        return self::SUCCESS;
    }
}
