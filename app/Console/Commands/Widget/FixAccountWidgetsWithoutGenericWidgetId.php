<?php

namespace App\Console\Commands\Widget;

use App\Console\Command;
use App\ContextWidget;
use App\Model;
use DB;
use Throwable;

class FixAccountWidgetsWithoutGenericWidgetId extends Command
{
    protected $signature = 'widget:fix-generic-widget-id';
    protected $description = 'Set the generic_widget_id for AccountWidgets that do not have them.';

    public function handle()
    {
        DB::beginTransaction();
        try {
            $accountWidgets = ContextWidget::query()
                ->whereNull(ContextWidget::GENERIC_WIDGET_ID)
                ->whereNotNull(ContextWidget::GENERIC_PARENT_ID)
                ->cursor();

            /** @var ContextWidget $accountWidget */
            foreach ($accountWidgets as $accountWidget) {
                $this->info('Changing generic_widget_id to ' . $accountWidget->generic_parent_id . ' for AccountWidget #' . $accountWidget->id); //phpcs:ignore
                ContextWidget::query()
                    ->where(Model::ID, $accountWidget->id)
                    ->update([ContextWidget::GENERIC_WIDGET_ID => $accountWidget->generic_parent_id]);
            }

            if ($this->confirm('Are you sure you want to commit these changes?')) {
                DB::commit();
                $this->info('Saved.');
            } else {
                DB::rollBack();
                $this->info('Rolled back.');
            }
        } catch (Throwable) {
            DB::rollBack();
            $this->error('Failed. Rolled back.');
        }
    }
}
