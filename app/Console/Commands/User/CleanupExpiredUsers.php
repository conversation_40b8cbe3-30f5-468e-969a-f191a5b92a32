<?php

namespace App\Console\Commands\User;

use App\Repositories\UserExpirationRepository;
use DB;
use App\Console\Command;
use Log;
use Throwable;

/**
 * Command options:
 * --force automatically commit without asking confirmation
 */
class CleanupExpiredUsers extends Command
{
    protected $signature = 'cleanup:expired-users {--force}';
    protected $description = 'Delete expired users, use --force to skip the commit prompt.';

    public function handle(): int
    {
        try {
            DB::beginTransaction();
            $expiredUsers = resolve(UserExpirationRepository::class)->getExpiredUsers();
            $totalCount = $expiredUsers->count();
            $this->info($totalCount . ' users are expired');

            if ($expiredUsers->count() !== 0) {
                $currentDeleteCount = 0;
                foreach ($expiredUsers as $user) {
                    $user->delete();
                    $currentDeleteCount++;
                    $this->info('User #' . $user->user_id . ' soft-deleted (' . $currentDeleteCount . '/' . $totalCount . ')'); //phpcs:ignore
                }

                if ($this->option('force') || $this->confirm('Commit changes?')) {
                    DB::commit();
                    $this->info('Successfully Soft-deleted ' . $currentDeleteCount . ' users');
                } else {
                    DB::rollback();
                    $this->info('Changes reverted with rollback.');
                }
            }

            return self::SUCCESS;
        } catch (Throwable $e) {
            DB::rollBack();
            $this->error('Something went wrong trying to soft-deleted expired users. Thrown error: ' . $e->getMessage()); //phpcs:ignore
            Log::error(get_class($e) . ' ' . $e->getMessage());
            throw $e;
        }
    }
}
