<?php

namespace App\Console\Commands\UserWidget;

use App\Console\Command;
use App\User;

class RecryptUserWidgets extends Command
{
    protected $signature = 'widgets:recrypt {user_id} {old_masterkey} {new_masterkey}';
    protected $description = 'List all widget corrections per account';

    public function handle()
    {
        $user = User::findOrFail($this->argument('user_id'));

        $this->info('Starting recrypting user widgets for user #' . $user->id);
        foreach ($user->userWidgets as $user_widget) {
            $user_widget->recryptProtectedValues($this->argument('old_masterkey'), $this->argument('new_masterkey'));
        }
        $this->info($user->userWidgets()->count() . ' user widgets recrypted for user #' . $user->id);
    }
}
