<?php

namespace App\Console\Commands;

use App\Account;
use App\Database\ModelCollection;
use App\Helpers\Mail\HeaderImageHelper;
use App\Console\Command;

/**
 * This command will generate the account email headers for every accounts.
 * These email headers are now generated with the account logo when it is modified in the back-office.
 * This command is useful only for re-processing the accounts with logos that were changed beforehand,
 *
 * Class GenerateAccountEmailHeaders
 * @package App\Console\Commands
 */
class GenerateAccountEmailHeaders extends Command
{
    protected $signature = 'generate:account_email_headers';
    protected $description = 'Generate account email headers';

    public function handle()
    {
        $iteration = 0;
        $accountsPerIteration = 20;
        $failedEmailHeaderGenerations = 0;

        $this->info('Start generating account email headers ...');
      /** @var ModelCollection $accounts */
        while (($accounts = Account::all()->forPage(++$iteration, 20)) && $accounts->isEmpty() === false) {
          /** @var Account[] $accounts */
            foreach ($accounts as $account) {
                try {
                    HeaderImageHelper::generateEmailHeader($account);
                } catch (\Exception $exception) {
                    // phpcs:ignore
                    $this->error('An error occurred for account : ' . $account->name . ' [' . $account->id . ']. (' . $exception->getMessage() . ')');
                    ++$failedEmailHeaderGenerations;
                }
            }

            if ($iteration % 5 === 0) {
                $this->info('Generated ' . ($iteration * $accountsPerIteration) . ' email headers so far, ongoing ...');
            }
        }

        $this->info('Finished generating account email headers');

        if ($failedEmailHeaderGenerations > 0) {
            // phpcs:ignore
            $this->warn($failedEmailHeaderGenerations . ' failed email header generations during the process, check logs for details.');
        }
    }
}
