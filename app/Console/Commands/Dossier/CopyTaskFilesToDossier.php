<?php

namespace App\Console\Commands\Dossier;

use App;
use App\Company;
use App\Services\Dossiers\DossierFileService;
use App\ServiceTask;
use DB;
use Illuminate\Console\Command;

class CopyTaskFilesToDossier extends Command
{
    protected $signature = 'dossier:copy-files {task_id} {company_id} {--force}';
    protected $description = 'Copy task files to dossier, force will skip the checks if it was already done.';

    public function handle()
    {
        $taskId = intval($this->argument('task_id'));
        $companyId = intval($this->argument('company_id'));

        $task = ServiceTask::findOrFail($taskId);
        $company = Company::findOrFail($companyId);

        if ($task->company_id !== $company->id) {
            $this->error("Company id and Task Company id don't match");
        }

        $copiedToDossier = $task->getProperty(ServiceTask::COPIED_TO_DOSSIER);
        if (
            isset($copiedToDossier)
            && $copiedToDossier
            && !$this->option('force')
        ) {
            $this->info('Skipped task #' . $task->id . ' because it already has been copied.');
            return;
        }

        $dossierFileService = resolve(DossierFileService::class);
        try {
            DB::beginTransaction();
            $dossierFileService->copyTaskFiles($task);
            $this->info('Copied files from task #' . $task->id . ' to dossier');
            DB::commit();
        } catch (\Exception $exception) {
            $this->error('Files from task #' . $task->id . ' failed copy: ' . $exception->getMessage());
            DB::rollBack();
        }
    }
}
