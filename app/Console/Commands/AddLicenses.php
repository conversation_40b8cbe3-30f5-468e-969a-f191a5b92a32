<?php

namespace App\Console\Commands;

use App\Account;
use App\Services\AccountLicenseService;
use DB;
use Illuminate\Console\Command;
use Throwable;

/**
 * Adds licenses to multiple Accounts
 * If the license does not exist, it will skip this license.
 * If the license is already present on the Account, it will skip this license.
 */
class AddLicenses extends Command
{
    protected $signature = 'account:add-licenses {account_ids} {licenses}';
    protected $description = 'Add licence to a range of accounts.';

    /**
     * @throws Throwable
     */
    public function handle(): int
    {
        DB::beginTransaction();
        try {
            $accountIds = explode(',', $this->argument('account_ids'));
            $licenses = explode(',', $this->argument('licenses'));

            $accounts = Account::query()->whereIn(Account::ID, $accountIds)->get();

            foreach ($accounts as $account) {
                $accountLicenseService = resolve(AccountLicenseService::class);
                $addedLicenses = $accountLicenseService->installLicenses($account, $licenses);
                if (count($addedLicenses)) {
                    $licenseNames =  array_map(
                        function (\App\AccountLicense $license) {
                            return $license->license->reference_name . ' - ' . $license->license->name;
                        },
                        $addedLicenses
                    );
                    $this->info('Added licenses ' . implode(', ', $licenseNames) . ' to account #' . $account->id);
                    if (count($addedLicenses) !== count($licenses)) {
                        $this->warn('Some licenses were not added, check your licenses.');
                    }
                } else {
                    $this->warn('No new licenses were added, check your licenses.');
                }
            }
            DB::commit();
            return self::SUCCESS;
        } catch (Throwable $e) {
            $this->error($e->getMessage());
            DB::rollBack();
            return self::FAILURE;
        }
    }
}
