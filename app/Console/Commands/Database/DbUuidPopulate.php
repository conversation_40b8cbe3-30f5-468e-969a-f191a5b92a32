<?php

namespace App\Console\Commands\Database;

use App\Console\Command;
use App\Helpers\HixUuid;

class DbUuidPopulate extends Command
{
    // phpcs:ignore
    protected $signature = 'db:uuid-populate {--table=} {--accountColumn=account_id} {--timestampColumn=created_at} {--uuidColumn=uuid} {--limit=}';
    protected $description = 'Generate a Hix UUID';

    public function handle()
    {
        $table = $this->option('table');
        $accountColumn = $this->option('accountColumn');
        $timestampColumn = $this->option('timestampColumn');
        $uuidColumn = $this->option('uuidColumn');
        $limit = $this->option('limit');

        $updated = HixUuid::populate($table, $accountColumn, $timestampColumn, $uuidColumn, $limit);
        $this->info('Updated ' . $updated . ' rows in ' . $table);
    }
}
