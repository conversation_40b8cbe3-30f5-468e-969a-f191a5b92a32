<?php

namespace App\Objects;

use App\Model;
use App\ValueObject\Dms\DmsPreferences;

/**
 * Interface DmsPreferenceAwareInterface
 * use DmsPreferenceAwareTrait to implement this interface.
 * @package App\Objects
 * @property array $preferences
 */
interface DmsPreferenceAwareInterface
{
    public function getPreferencesAttribute(): array;
    public function setPreferencesAttribute(array $preferences): void;
    public function getDmsPreferences(): DmsPreferences;
    public function setDmsPreferences(DmsPreferences $dmsPreferences): void;
    public function getRelatedModel(): Model;
}
