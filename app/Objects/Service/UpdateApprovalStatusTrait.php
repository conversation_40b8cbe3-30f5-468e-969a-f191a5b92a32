<?php

namespace App\Objects\Service;

use App\Repositories\Services\ServiceTaskResponseRepository;
use App\Services\ServiceTask\ResponseNotifier;
use App\Services\ServiceTask\TaskSendService;
use App\ServiceTaskResponse;
use App\Support\Database\Builder;
use Exception;
use Illuminate\Support\Collection;

trait UpdateApprovalStatusTrait
{
    /**
     * @param bool|null $saveAfterUpdate
     * @param bool|null $shouldNotify
     * @throws Exception
     */
    protected function updateApprovalStatus(?bool $saveAfterUpdate = false, ?bool $shouldNotify = false)
    {
        $taskResponsesApprove = $this->task->getApproversForRound();

        if ($this->isTaskWaitingForApproval($taskResponsesApprove)) {
            return;
        }

        $usersToNotify = new Collection();
        /** @var ServiceTaskResponse $taskResponse */
        foreach ($taskResponsesApprove as $taskResponse) {
            if ($taskResponse->isRevoked() === false) {
                $usersToNotify->push($taskResponse->user);
            }
        }

        $service = resolve(TaskSendService::class);
        $result = $service->updateTaskStateToRequestingParty($this->task);
        if ($result && $shouldNotify) {
            ResponseNotifier::notifyTask($this->task, $usersToNotify);
        }
    }

    /**
     * @param Collection<ServiceTaskResponse> $taskResponses
     * @return bool
     */
    protected function isTaskWaitingForApproval($taskResponses): bool
    {
        Builder::$cacheEnabled = false; //prevent cached result of approval status
        $responseRepository = resolve(ServiceTaskResponseRepository::class);
        // If the task has only 'inform' permission it cannot have any approvals, so we return false.
        if (!$responseRepository->hasApprovalPermissions($this->task)) {
            return false;
        }

        if ($this->task->needsOneApproval()) {
            foreach ($taskResponses as $taskResponse) {
                if ($taskResponse->isApproved()) {
                    return false;
                }
            }
            return true;
        } else {
            foreach ($taskResponses as $taskResponse) {
                if ($taskResponse->isWaitingApproval()) {
                    return true;
                }
            }
            return false;
        }
    }
}
