<?php

namespace App\Objects\Service;

use App\Exceptions\Services\IncorrectApprovalPreferenceException;
use App\Logging\Channels\ServiceLog;
use App\Objects\PropertiesAwareInterface;
use RuntimeException;

trait ApprovalPreferencesAwareTrait
{
    /**
     * @return array
     * @codeCoverageIgnore
     */
    public static function getApprovalPreferencesFormOptions(): array
    {
        $approvalPreferencesFormOptions = array_combine(
            ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES_OPTIONS,
            array_map(
                function ($option) {
                    return trans('generic_service.approval_preferences.' . $option);
                },
                ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES_OPTIONS
            )
        );

        return is_array($approvalPreferencesFormOptions) ?
            $approvalPreferencesFormOptions :
            [];
    }

    public function setDefaultApprovalPreferences(): void
    {
        $this->assertObjectHasPropertiesField();

        $properties = $this->properties;
        $properties[ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES] = ApprovalPreferencesAwareInterface::DEFAULT_APPROVAL_PREFERENCE; // phpcs:ignore
        $this->properties = $properties;
    }

    /**
     * @param  string  $approvalPreferences
     */
    public function setApprovalPreferences(string $approvalPreferences): void
    {
        $this->assertObjectHasPropertiesField();

        $properties = $this->properties;
        if (!in_array($approvalPreferences, ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES_OPTIONS)) {
            ServiceLog::error('Incorrect approval preference received : ' . $approvalPreferences);
            throw new IncorrectApprovalPreferenceException();
        }
        $properties[ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES] = $approvalPreferences;
        $this->properties = $properties;
    }

    /**
     * @return string
     */
    public function getApprovalPreference(): string
    {
        $this->assertObjectHasPropertiesField();
        if (
            !isset($this->properties[ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES])
            || !in_array(
                $this->properties[ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES],
                ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES_OPTIONS
            )
        ) {
            return ApprovalPreferencesAwareInterface::DEFAULT_APPROVAL_PREFERENCE;
        }
        return $this->properties[ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES];
    }
    /**
     * @return bool
     */
    public function needsAllApproval(): bool
    {
        return $this->getApprovalPreference() === ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES_ALL;
    }

    /**
     * @return bool
     */
    public function needsOneApproval(): bool
    {
        return $this->getApprovalPreference() === ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES_ONE;
    }

    /**
     * @codeCoverageIgnore
     */
    private function assertObjectHasPropertiesField(): void
    {
        if (($this instanceof PropertiesAwareInterface) === false) {
            throw new RuntimeException(
                'ApprovalPreferencesAwareTrait should be called from a class that has a properties field'
            );
        }
    }
}
