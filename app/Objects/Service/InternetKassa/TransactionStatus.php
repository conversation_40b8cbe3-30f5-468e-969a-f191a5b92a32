<?php

namespace App\Objects\Service\InternetKassa;

use App\Objects\SoapRequestData;

class TransactionStatus implements SoapRequestData
{
    private TransactionData $transactionData;

    public function __construct(TransactionData $transactionData)
    {
        $this->transactionData = $transactionData;
    }

    public function toRequestData(): array
    {
        return [
            'FIToFIPmtStsReq' => [
                'GrpHdr' => [
                    'MsgId' => $this->transactionData->getMsgId(),
                    'CreDtTm' => now()->toIso8601String(),
                    'InstgAgt' => [
                        'FinInstnId' => [
                            'Othr' => [
                                'Id' => config('services.internetkassa.inititatingparty.id'),
                            ],
                        ],
                    ],
                    'InstdAgt' => [
                        'FinInstnId' => [
                            'Othr' => [
                                'Id' => 'KASXX',
                            ],
                        ],
                    ],
                ],
                'OrgnlGrpInf' => [
                    'OrgnlMsgId' => $this->transactionData->getMsgId(),
                    'OrgnlMsgNmId' => 'ksbv.001.001.07',
                    'OrgnlCreDtTm' => $this->transactionData->getCreatedAt()->toIso8601String(),
                ],
                'TxInf' => [
                    'OrgnlInstrId' => $this->transactionData->getPaymentReference(),
                ],
                'SplmtryData' => [
                    'Envlp' => [
                        'Kassa' => [
                            'Language' => 'NL',
                            'PortalID' => config('services.internetkassa.connection_number'),
                            'Subscription' => config('services.internetkassa.connection_subnumber'),
                        ],
                    ],
                ],
            ],
        ];
    }
}
