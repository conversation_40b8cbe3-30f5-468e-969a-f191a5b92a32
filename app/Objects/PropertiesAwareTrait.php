<?php

namespace App\Objects;

trait PropertiesAwareTrait
{
    /**
     * Add properties or overwrite values of existing ones.
     * Existing ones that are not added remain and are not modified.
     * @param array $properties Associative array.
     */
    public function appendProperties(array $properties): self
    {
        $mergedProperties = array_merge($this->properties, $properties);
        $this->attributes[PropertiesAwareInterface::PROPERTIES] = json_encode($mergedProperties);
        return $this;
    }

    /**
     * WARNING: this will overwrite any existing properties!
     * @param array $value
     */
    public function setPropertiesAttribute(array $value): void
    {
        $this->attributes[PropertiesAwareInterface::PROPERTIES] = json_encode($value);
    }

    /**
     * @return array|mixed
     */
    public function getPropertiesAttribute()
    {
        if (
            isset($this->attributes[PropertiesAwareInterface::PROPERTIES]) && is_string(
                $this->attributes[PropertiesAwareInterface::PROPERTIES]
            )
        ) {
            $decodedValue = json_decode($this->attributes[PropertiesAwareInterface::PROPERTIES], true);

            return $decodedValue !== null ?
                $decodedValue :
                $this->attributes[PropertiesAwareInterface::PROPERTIES];
        }
        return $this->attributes[PropertiesAwareInterface::PROPERTIES] ?? [];
    }

    /**
     * @param string $key Name of property
     * @param mixed $value Can be string, bool, int or even array
     */
    public function setProperty(string $key, mixed $value): self
    {
        $this->assertPropertyKeyAllowed($key);
        $properties = $this->properties;

        if (
            defined('static::ENCRYPTED_PROPERTY_KEYS') &&
            in_array($key, static::ENCRYPTED_PROPERTY_KEYS)
        ) {
            $value = encrypt($value);
        }

        $properties[$key] = $value;
        $this->properties = $properties;
        return $this;
    }

    /**
     * @param string $key Name of property
     * @return mixed Value of property.
     */
    public function getProperty(string $key): mixed
    {
        $this->assertPropertyKeyAllowed($key);
        if ($this->hasProperty($key)) {
            if (
                defined('static::ENCRYPTED_PROPERTY_KEYS') &&
                in_array($key, static::ENCRYPTED_PROPERTY_KEYS)
            ) {
                return decrypt($this->properties[$key]);
            }
            return $this->properties[$key];
        }
        return null;
    }

    /**
     * @param string $key Name of property to remove.
     */
    public function unsetProperty(string $key): void
    {
        if ($this->hasProperty($key)) {
            $properties = $this->properties;
            unset($properties[$key]);
            $this->properties = $properties;
        }
    }

    public function hasProperty(string $key): bool
    {
        if (empty($this->properties)) {
            return false;
        }
        return key_exists($key, $this->properties);
    }

    /**
     * This method can be overwritten for more specific context dependent property key rules.
     * @return string[] List of allowed keys
     */
    public function getAllowedPropertyKeys(): array
    {
        return static::PROPERTY_KEYS;
    }

    public function assertPropertyKeyAllowed(string $key): void
    {
        if (!in_array($key, $this->getAllowedPropertyKeys(), true)) {
            throw new \InvalidArgumentException('Unknown property key ' . $key . ' for ' . $this::class . ' #' . $this->id); //phpcs:ignore
        }
    }
}
