<?php

namespace App\Models\RibbonNotification;

use App\Model;
use App\Role;
use Database\Factories\RibbonNotification\RibbonNotificationRecipientFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property int $id
 * @property int $ribbon_notification_id
 * @property string $recipient
 * @property Role $role
 */
class RibbonNotificationRecipient extends Model
{
    use HasFactory;

    public const RECIPIENT = 'recipient';
    public const RIBBON_NOTIFICATION_ID = 'ribbon_notification_id';

    public const RECIPIENT_TYPES = [
        Role::ENVIRONMENT_MANAGER,
        Role::COMPANY_MANAGER,
        Role::GROUP_MANAGER,
        Role::COLLEAGUE,
        Role::CLIENT_USER
    ];

    public $timestamps = false;

    protected $fillable = [
        self::RECIPIENT,
        self::RIBBON_NOTIFICATION_ID,
    ];

    protected static function newFactory(): RibbonNotificationRecipientFactory
    {
        return RibbonNotificationRecipientFactory::new();
    }
}
