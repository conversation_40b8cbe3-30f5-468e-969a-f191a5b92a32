<?php

namespace App\Models\SharedFiles;

use App\Account;
use App\Helpers\FilenameHelper;
use App\Model;
use App\Repositories\FileSystem\EncryptedStorageRepository;
use Database\Factories\SharedFiles\SharedFileFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $account_id
 * @property string $shared_bundle_uuid
 * @property string $name
 * @property string $path
 * @property string $checksum
 * @property int $size
 * @property string $extension
 * @property Account $account
 */
class SharedFile extends Model
{
    public $timestamps = false;

    public const ACCOUNT_ID = 'account_id';
    public const SHARED_BUNDLE_UUID = 'shared_bundle_uuid';
    public const NAME = 'name';
    public const PATH = 'path';
    public const CHECKSUM = 'checksum';
    public const SIZE = 'size';
    public const EXTENSION = 'extension';

    protected $fillable = [
        self::ACCOUNT_ID,
        self::SHARED_BUNDLE_UUID,
        self::NAME,
        self::PATH,
        self::CHECKSUM,
        self::SIZE,
        self::EXTENSION
    ];

    public function bundle(): BelongsTo
    {
        return $this->belongsTo(SharedFileBundle::class, self::SHARED_BUNDLE_UUID);
    }

    /**
     * @return string Decrypted content of file
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     */
    public function getContent(): string
    {
        $storageRepo = resolve(EncryptedStorageRepository::class);
        return $storageRepo->getFile($this->path);
    }

    /**
     * Public path that will download file. Relative by default.
     * @param bool $absolute Set to TRUE to get absolute URL
     * @return string URL where file can be downloaded.
     */
    public function getDownloadUrl(bool $absolute = false): string
    {
        $filename = FilenameHelper::sanitize($this->name);
        return route('front.share.file', ['id' => $this->id, 'filename' => $filename], $absolute);
    }

    protected static function newFactory(): SharedFileFactory
    {
        return SharedFileFactory::new();
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }
}
