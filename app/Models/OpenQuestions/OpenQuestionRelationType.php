<?php

namespace App\Models\OpenQuestions;

use App\Model;
use Database\Factories\OpenQuestions\OpenQuestionRelationTypeFactory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @property int open_question_id
 * @property string type
 */
class OpenQuestionRelationType extends Model
{
    public $timestamps = false;
    protected $primaryKey = self::TYPE;
    protected static $unguarded = true;
    public $incrementing = false;

    public const TYPE = 'type';

    public const BOOKKEEPING = 'bookkeeping';
    public const WAGE = 'wage';
    public const OCR = 'ocr';
    public const FISCAL = 'fiscal';
    public const OTHER = 'other';
    public const CLIENT = 'client';
    public const YEARWORK = 'yearwork';
    public const DECLARATION = 'declaration';
    public const TEMPLATE = 'template';

    public const SECTION = 'section';

    public const ALL = [
        self::BOOKKEEPING,
        self::WAGE,
        self::OCR,
        self::FISCAL,
        self::OTHER,
        self::CLIENT,
        self::YEARWORK,
        self::DECLARATION,
        self::TEMPLATE,
        self::SECTION
    ];

    protected $fillable = [self::TYPE];

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return OpenQuestionRelationTypeFactory::new();
    }
}
