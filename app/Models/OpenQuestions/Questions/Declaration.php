<?php

namespace App\Models\OpenQuestions\Questions;

use App\Interfaces\CustomRuleOpenQuestion;
use App\Models\OpenQuestions\OpenQuestionCategory;
use App\Services\CustomRule\CustomRuleService;
use App\Services\OpenQuestions\DeclarationQuestionService;
use Database\Factories\OpenQuestions\Questions\DeclarationFactory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * Class Declaration
 *
 * @property int $id
 * @property int $open_question_id
 * @property null|string $description
 * @property null|string $external_id
 */
class Declaration extends OpenQuestionRelation implements CustomRuleOpenQuestion
{
    protected $table = 'open_question_declarations';
    public $timestamps = false;

    public const QUESTION_TYPES = [
        OpenQuestion::TYPE_DECLARATION_VAT,
        OpenQuestion::TYPE_DECLARATION_SUPPLEMENTATION,
        OpenQuestion::TYPE_DECLARATION_ICT,
        OpenQuestion::TYPE_DECLARATION_IB,
        OpenQuestion::TYPE_DECLARATION_VA_IB,
        OpenQuestion::TYPE_DECLARATION_VA_VPB,
        OpenQuestion::TYPE_DECLARATION_VPB,
        OpenQuestion::TYPE_DECLARATION_YEARWORK,
        OpenQuestion::TYPE_DECLARATION_DIVIDEND,
        OpenQuestion::TYPE_OTHER
    ];

    public const DESCRIPTION = 'description';
    public const EXTERNAL_ID = 'external_id';

    protected static $unguarded = true;

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return DeclarationFactory::new();
    }



    public function getCustomRuleCreatedEvent(): string
    {
        $category = $this->getQuestionCategory();
        return match ($category) {
            OpenQuestionCategory::BOOKKEEPING => CustomRuleService::QUESTION_BOOKKEEPING_CREATED,
            OpenQuestionCategory::FISCAL => CustomRuleService::QUESTION_FISCAL_CREATED,
            OpenQuestionCategory::YEARWORK => CustomRuleService::QUESTION_YEARWORK_CREATED,
            OpenQuestionCategory::OTHER => CustomRuleService::QUESTION_OTHER_CREATED,
        };
    }

    public function getCustomRuleToBeReviewedEvent(): string
    {
        $category = $this->getQuestionCategory();
        return match ($category) {
            OpenQuestionCategory::BOOKKEEPING => CustomRuleService::QUESTION_BOOKKEEPING_TO_BE_REVIEWED,
            OpenQuestionCategory::FISCAL => CustomRuleService::QUESTION_FISCAL_TO_BE_REVIEWED,
            OpenQuestionCategory::YEARWORK => CustomRuleService::QUESTION_YEARWORK_TO_BE_REVIEWED,
            OpenQuestionCategory::OTHER => CustomRuleService::QUESTION_OTHER_TO_BE_REVIEWED,
        };
    }

    public function getCustomRuleCompletedEvent(): string
    {
        $category = $this->getQuestionCategory();
        return match ($category) {
            OpenQuestionCategory::BOOKKEEPING => CustomRuleService::QUESTION_BOOKKEEPING_COMPLETED,
            OpenQuestionCategory::FISCAL => CustomRuleService::QUESTION_FISCAL_COMPLETED,
            OpenQuestionCategory::YEARWORK => CustomRuleService::QUESTION_YEARWORK_COMPLETED,
            OpenQuestionCategory::OTHER => CustomRuleService::QUESTION_OTHER_COMPLETED,
        };
    }

    public function getQuestionCategory(): string
    {
        return resolve(DeclarationQuestionService::class)->getCategoryByType($this->openQuestion->questionType->key);
    }
}
