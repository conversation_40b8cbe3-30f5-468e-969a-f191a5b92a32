<?php

namespace App\Models\OpenQuestions\Questions\Templates;

use App\Account;
use App\Enums\Meilisearch\Models\MeilisearchTemplate;
use App\Model;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use Carbon\Carbon;
use Database\Factories\OpenQuestions\Templates\TemplateFactory;
use DateTime;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;
use Laravel\Scout\Searchable;

/**
 * Class Template
 * @property int $account_id
 * @property string $name
 * @property string|null $intro_text
 * @property string $category
 * @property Account $account
 * @property array $settings
 * @property TemplateField[]|Collection $fields
 * @property DefaultTemplate[]|Collection $defaultTemplate
 * @property DateTime $archived_at
 * @property int $archived_by
 * @property Collection<TemplateEntry> $entries
 * @property Collection<OpenQuestion> $openQuestions
 */
class Template extends Model
{
    use Searchable;

    protected static $unguarded = true;
    protected $table = self::TABLE;

    public const TABLE = 'open_question_templates';
    public const ACCOUNT_ID = 'account_id';
    public const NAME = 'name';
    public const INTRO_TEXT = 'intro_text';
    public const CATEGORY = 'category';
    public const SETTINGS = 'settings';
    public const ARCHIVED_AT = 'archived_at';

    public const SETTING_USE_DEFAULT_TITLE = 'use_default_title';

    public const ALLOWED_SETTINGS = [
        self::SETTING_USE_DEFAULT_TITLE
    ];

    protected $casts = [
        self::SETTINGS => 'array',
    ];

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return TemplateFactory::new();
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class, self::ACCOUNT_ID);
    }

    public function entries(): HasMany
    {
        return $this->hasMany(TemplateEntry::class, TemplateField::TEMPLATE_ID, self::ID);
    }

    public function fields(): HasMany
    {
        return $this->hasMany(TemplateField::class, TemplateField::TEMPLATE_ID, self::ID)->orderBy('order');
    }

    public function fieldsToAnswer(): HasMany
    {
        return $this->hasMany(TemplateField::class, TemplateField::TEMPLATE_ID, self::ID)
            ->whereNotIn(TemplateElement::TYPE, TemplateFieldType::FIELDS_WITHOUT_ANSWERS);
    }

    public function defaultTemplate(): HasOne
    {
        return $this->hasOne(DefaultTemplate::class);
    }

    public function scopeNameSearch(Builder $query, string $search): Builder
    {
        return $query->where(self::TABLE . '.' . self::NAME, 'like', '%' . $search . '%');
    }

    public function latestEntry(?int $companyId): ?TemplateEntry
    {
        if (is_null($companyId)) {
            return null;
        }
        return $this->entries->filter(function (TemplateEntry $entry) use ($companyId) {
            return $entry->openQuestion->company_id === $companyId;
        })
            ->sortByDesc('created_at')
            ->first();
    }

    public function toSearchableArray(): array
    {
        return [
            MeilisearchTemplate::ID => $this->id,
            MeilisearchTemplate::ACCOUNT_ID => $this->account_id,
            MeilisearchTemplate::NAME => $this->name,
            MeilisearchTemplate::UPDATED_AT => Carbon::parse($this->updated_at)->getTimestamp(),
        ];
    }
}
