<?php

namespace App\Models\ServiceTask;

use App\AccountService;
use App\Events\CustomRule\CustomRuleEvent;
use App\Interfaces\CustomRuleTask;
use App\Mail\AbstractMail;
use App\Models\TaskFile;
use App\Services\CustomRule\CustomRuleService;
use App\ServiceTask;
use App\Traits\ServiceTasks\CustomRuleTaskTrait;

class SbaMessageTask extends ServiceTask implements CustomRuleTask
{
    use CustomRuleTaskTrait;

    public const ALLOWED_FILE_TYPES = [
        TaskFile::TYPE_SBA_MESSAGE
    ];

    public function generatePdfTitle(TaskFile $taskFile): string
    {
        return $this->title;
    }

    /**
     * Overwrite label() method from parent class so that it does not add all kinds of stuff to a perfectly good title
     * when sending an email
     * @return string Description of task used in email sent to inform/approve users.
     */
    public function label(string $lang = null): string
    {
        return $this->title;
    }

    /**
     * We cannot send documents (PDFs) to Logius so always return "none".
     * @return string "none"
     */
    public function getLogiusConnection(): string
    {
        return AccountService::NONE;
    }

    public function getRequestingPartySetting(): string
    {
        return AccountService::NONE;
    }

    public function fireMailSentEvent(AbstractMail $mail): void
    {
        if ($this->hasUploadType()) {
            CustomRuleEvent::fire(CustomRuleService::TASK_UPLOAD_TYPE_EMAIL_SENT, $this->account, $mail, $this);
        } else {
            CustomRuleEvent::fire(CustomRuleService::TASK_SBA_MESSAGE_EMAIL_SENT, $this->account, $mail);
        }
    }

    public function getLogiusStatusTranslated(): string
    {
        return '';
    }

    /**
     * @return bool TRUE if the responders should receive the "approved" email
     */
    public function readyToSendApprovedMail(): bool
    {
        return $this->isCompleted();
    }

    public function getSignerEventName(): ?string
    {
        if ($this->hasUploadType()) {
            return CustomRuleService::TASK_UPLOAD_TYPE_SIGNER;
        } else {
            return CustomRuleService::TASK_SIGNER_OPTIONS;
        }
    }
}
