<?php

namespace App\Models\Dossiers;

use App\Company;
use App\Model;
use App\User;
use Database\Factories\Dossiers\DossierFolderUserFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $dossier_folder_id
 * @property int $user_id
 * Relations:
 * @property User $user
 * @property DossierFolder $dossierFolder
 */
class DossierFolderUser extends Model
{
    use HasFactory;

    public const ID = 'id';
    public const DOSSIER_FOLDER_ID = 'dossier_folder_id';
    public const USER_ID = 'user_id';

    protected $fillable = [
        self::DOSSIER_FOLDER_ID,
        self::USER_ID
    ];

    protected static function newFactory(): DossierFolderUserFactory
    {
        return DossierFolderUserFactory::new();
    }

    public function dossierFolder(): BelongsTo
    {
        return $this->belongsTo(DossierFolder::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }
}
