<?php

namespace App\Models\Webhooks;

use App\Model;

/**
 * @property int $id
 * @property int $account_id
 * @property int $webhook_id
 * @property string $event
 */
class WebhookCondition extends Model
{
    public $timestamps = false;

    public const ACCOUNT_ID = 'account_id';
    public const WEBHOOK_ID = 'webhook_id';
    public const EVENT = 'event';

    protected $fillable = [
        self::ACCOUNT_ID,
        self::WEBHOOK_ID,
        self::EVENT
    ];
}
