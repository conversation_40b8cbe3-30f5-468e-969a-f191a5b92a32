<?php

namespace App\Models\Dms;

use App\AccountService;
use App\Company;
use App\Model;
use App\Objects\DmsPreferenceAwareInterface;
use App\Objects\DmsPreferenceAwareTrait;
use App\ValueObject\Services\Dms\MicrosoftSharepoint\DirectoryConfiguration;
use Database\Factories\ServiceTask\ServiceTaskCompanyDmsPreferenceFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_id
 * @property Company $company
 * @property int $account_service_id
 * @property AccountService $accountService
 * @property string $task_type
 * @property null|string $preferences
 */
class ServiceTaskCompanyDmsPreference extends Model implements DmsPreferenceAwareInterface
{
    use DmsPreferenceAwareTrait;

    public const ID = 'id';
    public const COMPANY_ID = 'company_id';
    public const ACCOUNT_SERVICE_ID = 'account_service_id';
    public const TASK_TYPE = 'task_type';
    public const UPLOAD_TYPE_ID = 'upload_type_id';
    public const PREFERENCES = 'preferences';

    protected $fillable = [
        self::COMPANY_ID,
        self::ACCOUNT_SERVICE_ID,
        self::TASK_TYPE,
        self::PREFERENCES,
        self::UPLOAD_TYPE_ID
    ];

    protected $casts = [
        self::PREFERENCES => 'array'
    ];

    protected static function newFactory(): Factory
    {
        return ServiceTaskCompanyDmsPreferenceFactory::new();
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function accountService(): BelongsTo
    {
        return $this->belongsTo(AccountService::class);
    }

    public function getRelatedModel(): Model
    {
        return $this->company;
    }

    public function getSharepointDirectoryConfiguration(): ?DirectoryConfiguration
    {
        $sharepointConfig = $this->preferences;
        if (!isset($sharepointConfig['site_id'], $sharepointConfig['drive_id'])) {
            return null;
        }

        return new DirectoryConfiguration(
            $sharepointConfig['site_id'],
            $sharepointConfig['drive_id'],
            $sharepointConfig['item_id'] ?? null
        );
    }
}
