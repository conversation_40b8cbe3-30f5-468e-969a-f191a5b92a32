<?php

namespace App\Models\Dms;

use App\AccountService;
use App\Company;
use App\Model;
use App\Models\FileUploads\Upload;
use App\Objects\DmsPreferenceAwareInterface;
use App\Objects\DmsPreferenceAwareTrait;
use Database\Factories\FileUploads\UploadedFileCompanyDmsPreferenceFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $company_id
 * @property int $account_service_id
 * @property Company $company
 * @property AccountService $accountService
 * @property Collection|Upload[] $uploads
 */
class UploadedFileCompanyDmsPreference extends Model implements DmsPreferenceAwareInterface
{
    use DmsPreferenceAwareTrait;

    public const ID = 'id';
    public const COMPANY_ID = 'company_id';
    public const ACCOUNT_SERVICE_ID = 'account_service_id';
    public const PREFERENCES = 'preferences';

    protected $fillable = [
        self::COMPANY_ID,
        self::ACCOUNT_SERVICE_ID,
        self::PREFERENCES
    ];

    protected static function newFactory(): Factory
    {
        return UploadedFileCompanyDmsPreferenceFactory::new();
    }

    /**
     * Get all questions that use this DMS Preference
     * @return HasManyThrough
     */
    public function uploads(): HasManyThrough
    {
        return $this->hasManyThrough(
            Upload::class,
            Company::class,
            Company::ID,
            Upload::COMPANY_ID,
            self::COMPANY_ID,
            Company::ID
        );
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * The related accountService
     * @return BelongsTo
     */
    public function accountService(): BelongsTo
    {
        return $this->belongsTo(AccountService::class);
    }

    public function getRelatedModel(): Model
    {
        return $this->company;
    }
}
