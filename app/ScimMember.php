<?php

namespace App;

use Database\Factories\Scim\ScimMemberFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class AzureMembers
 * @package App
 * @property string $name
 * @property User $user
 * @property Context $context
 * @property string|null $hostname
 */
class ScimMember extends Model
{
    public const ID = 'id';
    public const USER_ID = 'user_id';
    public const CONTEXT_ID = 'context_id';

    public $timestamps = false;

    public $fillable = [
        'user_id',
        'context_id',
    ];

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return ScimMemberFactory::new();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function context(): BelongsTo
    {
        return $this->belongsTo(Context::class);
    }

    public function getUserId(): ?int
    {
        return $this->getAttribute(self::USER_ID);
    }

    public function getContextId(): ?int
    {
        return $this->getAttribute(self::CONTEXT_ID);
    }

    public function isUser(): bool
    {
        return $this->getUserId() !== null;
    }

    public function isContext(): bool
    {
        return $this->getContextId() !== null;
    }
}
