<?php

namespace App;

use App;
use App\Database\SoftDeletes;
use App\Enums\Meilisearch\Models\MeilisearchUserWidget;
use App\Events\StartUserWidgetEvent;
use App\Events\UserWidgetChangedEvent;
use App\Events\UserWidgetCreatedEvent;
use App\Events\UserWidgetDeletedEvent;
use App\Events\UserWidgetFailedOnstartChecksEvent;
use App\Exceptions\ConnectException;
use App\Exceptions\UnauthorizedException;
use App\Exceptions\UserWidgetLimitException;
use App\Helpers\BrowserExtensionHelper as BrowserExtension;
use Auth;
use App\Support\Carbon;
use Database\Factories\Widget\UserWidgetFactory;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Collection;
use Laravel\Scout\Searchable;
use Log;
use Operation;
use Request;
use SoapFault;

/**
 * Class UserWidget
 * @package App
 * @property string $status
 * @property string $reference_name
 * @property int $user_id
 * @property int $generic_widget_id
 * @property int $parent_id
 * @property int $order
 * @property User $user
 * @property array $properties
 * @property array $own_properties
 * @property Collection<int, DashboardCategoryUserWidget> $dashboardCategoriesUserWidgets
 * @property GenericWidget $generic_widget
 * @method static UserWidget findOrFail($id)
 */
class UserWidget extends Widget implements App\Contracts\ISortable
{
    use SoftDeletes;
    use Searchable;

    public const USER_ID = 'user_id';
    public const GENERIC_WIDGET_ID = 'generic_widget_id';
    public const USE_DEPRECATION = 'use_deprecation';
    public const USER_WIDGET_LIMIT = 500;
    public const ORDER = 'order';

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return UserWidgetFactory::new();
    }

    public function parent()
    {
        return $this->inheritFrom(ContextWidget::class)
            ->withTrashed()
            ->with('generic_parent', 'context_parent')
            ->ordered();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function generic_widget(): BelongsTo //phpcs:ignore
    {
        return $this->belongsTo(GenericWidget::class);
    }

    public function dashboardCategoriesUserWidgets(): HasMany
    {
        return $this->hasMany(DashboardCategoryUserWidget::class);
    }

    public function isManagedBy(User $user, $direct = false): bool
    {
        if ($direct) {
            return $user->directManagedUsers()->find($this->user_id) != null;
        }

        return $user->managedUsers()->find($this->user_id) != null;
    }

    public function beforeCreate(array $options = []): bool
    {
        $userWidgets = $this->user->userWidgets();
        $this->setSortIndex($userWidgets->max('order') + 1);

        if ($this->user->account_id != $this->parent->account_id) {
            trigger_error("UserWidget $this->parent_id of account " . $this->parent->account_id . " cannot be available for user $this->user_id of account " . $this->user->account_id); //phpcs:ignore
        }

        if ($userWidgets->count() + 1 >= self::USER_WIDGET_LIMIT) {
            Log::warning("User #" . $this->user->id . " has reached the maximum number of user widgets");
            throw new UserWidgetLimitException('User widget limit exceeded');
        }

        return parent::beforeCreate($options);
    }

    public function beforeSave(array $options = []): bool
    {
        $this->generic_widget_id = $this->getGenericWidget()->id;

        return parent::beforeSave($options);
    }

    public function save(array $options = []): bool
    {
        if (isset($this->attributes['auto_populate'])) {
            Log::critical("Auto populate value is set in User Widget for route " . Request::route()->getName());
        }

        $this->rollback(['auto_populate']);

        return parent::save($options);
    }

    public function afterCreate(array $options = []): bool
    {
        if (!array_key_exists('event_created_fired', $options) || !$options['event_created_fired']) {
            $options['event_created_fired'] = true;
            UserWidgetCreatedEvent::fire($this);
        }

        $this->parent()->increment('user_widgets_count');

        return parent::afterCreate($options);
    }

    public function afterUpdate(array $options = [], array $originalValues = []): bool
    {
        if (!array_key_exists('event_created_fired', $options) || !$options['event_created_fired']) {
            $options['event_created_fired'] = true;
            UserWidgetChangedEvent::fire($this);
        }

        return parent::afterUpdate($options, $originalValues);
    }

    public function beforeDelete(array $options = []): bool
    {
        foreach ($this->dashboardCategoriesUserWidgets as $dashboardCategoriesUserWidget) {
            if (!$dashboardCategoriesUserWidget->delete()) {
                return false;
            }
        }
        return parent::beforeDelete($options);
    }

    public function delete(array $options = []): bool
    {
        //When there are setting configured let the user react
        if (
            isset($options[self::USE_DEPRECATION])
            && $options[self::USE_DEPRECATION]
            && !empty($this->attributes[self::PROPERTIES])
        ) {
            //These two lines are needed untill all Widget properties are harmonised
            $this->parent;
            $this->rollback();

            $this->status = 'deprecated';

            return $this->save();
        }

        return parent::delete($options);
    }

    public function afterDelete(array $options = []): bool
    {
        if (!array_key_exists('event_deleted_fired', $options) || !$options['event_deleted_fired']) {
            $options['event_deleted_fired'] = true;
            if (!empty($this->user)) {
                UserWidgetDeletedEvent::fire($this);
            }
        }

        $this->parent()->where('user_widgets_count', '>', 0)->decrement('user_widgets_count');

        return parent::afterDelete($options);
    }

    public function isReadOnly(): bool
    {
        return in_array($this->status, ['readonly', 'blocked', 'deprecated']);
    }

    public function isBlocked(): bool
    {
        return in_array($this->status, ['blocked', 'deprecated']);
    }

    /**
     * @deprecated old ui, also remove getStatusReasonAttribute() when deleting this.
     * @param $private_fields
     * @return array
     */
    public function toDataArray($private_fields = true): array
    {
        $data = [
            'label' => $this->showLabel(),
            'description' => $this->showDescription(),
            self::USER_ID => $this->showUserId(),
            'context_fqn' => !empty($this->parent) && !empty($this->parent->context) ?
                $this->parent->context->FQN : "---",
        ];

        if ($private_fields) {
            $data = array_merge($data, [
                'id' => $this->id,
                'image' => $this->showImage(),
                'template' => $this->content_template,
                'content' => $this->showContent(),
                'status' => $this->status,
                'settings_complete' => $this->settingsComplete(),
                'status_msg_summary' => $this->isBlocked() ?
                    trans('user_widget.' . $this->status . '_summary', ['widget_name' => $this->showLabel()]) : '',
                'status_reason' => $this->status_reason,
                'can_be_edited' => Auth::user()->can('edit', $this),
                'start_url' => route('startUserWidget', ['user_widget' => $this]),
                'actions_url' => route('showUserWidgetActions', ['user_widget' => $this]),
                'drag_url' => route('dragUserWidget', [$this->id]),
                'edit_url' => route('editUserWidget', [$this->id]),
                'totp_url' => $this->hasFilledSetting('totp_secret') && !$this->isDeprecated() ?
                    route('new.user_widget.totp.calc', [$this->id]) : '',
                'alerts' => [],
                'securelogin_safe' => $this->shouldShowSecureLoginSafe(),
                'securelogin_safe_url' => route('showSecureLoginSafe', [$this->id]),
            ]);
        }

        return $data;
    }

    /**
     * Register starting a widget.
     * Includes onstart checks that can be different per widget.
     * If a check fails the resulting array will contain a value with the value FALSE.
     * @return array List of check results. All values will be TRUE if all checks succeed.
     */
    public function onStart(): array
    {
        try {
            $this->execDefaultOperations();
            $this->execOnStartOperations();
        } catch (ConnectException | SoapFault $e) {
            Log::error('Error when opening ' . $this->reference_name . ' : ' . $e->getMessage());
            return [
                'connect_exception' => false,
            ];
        }

        list($check_results, $messages) = $this->execOnStartChecks();

        if (in_array(false, $check_results)) {
            UserWidgetFailedOnstartChecksEvent::fire($this, [$check_results, $messages]);
        } else {
            StartUserWidgetEvent::fire($this);
        }

        return $check_results;
    }

    protected function execDefaultOperations(): void
    {
        if (!empty($this->operations)) {
            $this->execOperations($this->operations);
        }
    }

    protected function execOnStartOperations(): void
    {
        if (!empty($this->onstart_operations)) {
            $this->execOperations($this->onstart_operations);
        }
    }

    protected function execOnStartChecks(): array
    {
        if (empty($this->onstart_checks)) {
            return [[], []];
        }

        return $this->execChecks($this->onstart_checks);
    }

    //TODO: move to Widget class
    protected function execChecks(array $checks): array
    {
        $check_results = [];
        $messages = [];
        foreach ($checks as $key => $checks_step) {
            $check_result = true;
            $value = $this->getAttributeRecursive($key);
            foreach ($checks_step as $function => $args) {
                list($execute, $exec_result, $printable) = Operation::exec($this, $function, $args, $value);

                if (!$exec_result) {
                    $check_result = false;
                    break;
                }
            }
            $check_results[$key] = $check_result;
            $messages[$key] = $printable;
        }
        return [$check_results, $messages];
    }

    public function redirectToNativeRoute(): Redirector|Application|RedirectResponse
    {
        return Account::getHostAccount()->redirectToNativeRoute('startUserWidget', ['user_widget' => $this->id]);
    }

    protected function settingEncrypt($value, $key = ""): string
    {
        return $this->user->encrypt($value, $key);
    }

    protected function isOwnedByCurrentUser(): bool
    {
        return Auth::user()->id === $this->user_id;
    }

    protected function settingDecrypt($value, $key = ""): string
    {
        if ($key == "" && Auth::check()) {
            if (Auth::user()->id != $this->user_id) {
                throw new UnauthorizedException('Method settingDecrypt could not be executed for a user widget (' . $this->id . ') which is not owned by the authanticated user (' . Auth::user()->id . ')'); //phpcs:ignore
            }
        }

        return $this->user->decrypt($value, $key);
    }

    protected function valueCouldBeDecrypted($value, $key = ""): bool
    {
        if ($key == "" && Auth::check()) {
            if (Auth::user()->id != $this->user_id) {
                throw new UnauthorizedException('Method valueCouldBeDecrypted could not be executed for a user widget (' . $this->id . ') which is not owned by the authenticated user (' . Auth::user()->id . ')'); //phpcs:ignore
            }
        }

        return $this->user->couldBeDecrypted($value, $key);
    }

    protected function valueShouldBeRecrypted($value): bool
    {
        if (!Auth::check()) {
            trigger_error("Method valueShouldBeRecrypted could only be executed by its owner if authenticated");
            return false;
        }

        if (Auth::user()->id != $this->user_id) {
            trigger_error("Method valueShouldBeRecrypted could not be executed for a user widget ($this->id) which is not owned by the authanticated user (" . Auth::user()->id . ")"); //phpcs:ignore
        }

        $user = Auth::user();

        return $user->shouldBeRecrypted($value);
    }

    public function fixIfNeeded($delete_user_widgets = false): array
    {
        $fixes = [];

        //A - If user is deleted, then delete
        if ($this->user()->withTrashed()->first()->trashed()) {
            $this->delete();

            $fixes[] = "A.$this->user_id";
            Log::notice("User Widget $this->id fixed by deleting it because user is deleted");

            return $fixes;
        }

        //B - If parent is deleted, then try find alternative, otherwise delete
        if ($this->parent()->withTrashed()->first()->trashed()) {
            return $this->handleParentWidgetDeletion($delete_user_widgets);
        }

        //C - If membership does not exist between user and context , then try find alternative, otherwise delete
        if (
            !Membership::where('context_id', $this->parent->context_id)
                ->where(self::USER_ID, $this->user_id)
                ->exists()
        ) {
            return $this->handleNonexistantMembershipBetweenUserAndContext($delete_user_widgets);
        }
        return $fixes;
    }

    /**
     * Tries to find an alternative parent for this widget.
     * If none is found and $delete_user_widgets is True, this widget is deleted.
     * @param bool $delete_user_widgets True if the widget is to be deleted when failing a soft fix, otherwise, False.
     * @return array $fixes. Contains the performed fixes.
     * @throws Exception
     */
    public function handleParentWidgetDeletion(bool $delete_user_widgets = false): array
    {
        $fixes = [];
        $alt_parent = $this->findAlternativeParent();

        if (!empty($alt_parent)) {
            $old_parent_id = $this->parent_id;

            $this->parent;
            $this->rollback();

            $this->parent_id = $alt_parent->id;
            $this->save();

            $fixes[] = "Ba.$old_parent_id.$this->parent_id";
            Log::notice(
                "User Widget $this->id fixed by changing parent ($old_parent_id -> $this->parent_id) because old parent is deleted" //phpcs:ignore
            );
        } else {
            if ($delete_user_widgets) {
                $this->delete([self::USE_DEPRECATION => true]);
            }
            $fixes[] = (!$delete_user_widgets ? "*" : "") . "Bb.$this->parent_id";
            Log::notice("User Widget $this->id fixed by deleting it because parent is deleted");
        }
        return $fixes;
    }

    /**
     * Tries to find an alternative parent for this widget.
     * If none is found and $delete_user_widgets is True, then this widget is deleted.
     * @param bool $delete_user_widgets True if the widget is to be deleted when failing a soft fix, otherwise, False.
     * @return array $fixes. Contains the performed fixes.
     * @throws Exception
     */
    public function handleNonexistantMembershipBetweenUserAndContext(bool $delete_user_widgets = false): array
    {
        $fixes = [];
        $alt_parent = $this->findAlternativeParent();

        if (!empty($alt_parent)) {
            $old_parent_id = $this->parent_id;

            $this->parent;
            $this->rollback();

            $this->parent_id = $alt_parent->id;
            $this->save();

            $fixes[] = "Ca.$old_parent_id.$this->parent_id";
            Log::notice(
                "User Widget $this->id fixed by changing parent ($old_parent_id -> $this->parent_id) because user is not member of the regarding context of old parent" //phpcs:ignore
            );
        } else {
            if ($delete_user_widgets) {
                $this->delete([self::USE_DEPRECATION => true]);
            }
            $fixes[] = (!$delete_user_widgets ? "*" : "") . "Cb.$this->user_id." . $this->parent->context_id;
            Log::notice(
                "User Widget $this->id fixed by deleting it because user is not member of the regarding context"
            );
        }
        return $fixes;
    }

    public function findAlternativeParent(): ?ContextWidget
    {
        $current_parent = $this->parent()->withTrashed()->firstOrFail();

        foreach ($this->user->getAvailableWidgets() as $alt_parent) {
            if ($alt_parent->equalTo($current_parent)) {
                return $alt_parent;
            }
        }
        return null;
    }

    public function handleDeprecation(): void
    {
        if (!$this->isDeprecated()) {
            return;
        }

        $alt_parent = $this->findAlternativeParent();

        if (!empty($alt_parent)) {
            $this->parent;
            $this->rollback();

            $this->parent_id = $alt_parent->id;
            $this->status = null;
            $this->delete_after = null;
            $this->save();
            return;
        }

        //These two lines are needed untill all Widget properties are harmonised
        $this->parent;
        $this->rollback();

        //Plan delete action and send notification
        $this->delete_after = Carbon::now()->addDays(10);
        $this->save();

        //Disabled for now because notification should be disabled when harmonized
        /*$this->user->sendNotification(
            trans('user_widget.deprecated_notification_message',['widget'=>$this->showLabel()]),
            trans('user_widget.deprecated_notification_title',['widget'=>$this->showLabel()])
        );*/

        return;
    }

    /**
     * @deprecated should be removed when we remove the toDataArray() function
     * @return string
     */
    public function getStatusReasonAttribute(): string
    {
        if ($this->isDeprecated()) {
            if ($this->parent()->withTrashed()->first()->context()->withTrashed()->first()->trashed()) {
                return trans('user_widget.deprecated_reason_deleted_context', [
                    'group' => $this->parent()->withTrashed()->first()->context()->withTrashed()->first()->showLabel()
                ]);
            } elseif ($this->parent()->withTrashed()->first()->trashed()) {
                return trans('user_widget.deprecated_reason_deleted_widget', [
                    'group' => $this->parent()->withTrashed()->first()->context->showLabel()
                ]);
            }
            return trans('user_widget.deprecated_reason_unknown');
        } elseif ($this->isBlocked()) {
            return trans('user_widget.unavailable_reason_blocked');
        }
        return '';
    }

    public function isUserWidget(): bool
    {
        return true;
    }

    public function shouldShowSecureLoginSafe(): bool
    {
        return isset($this->properties['securelogin_safe']) && $this->properties['securelogin_safe'];
    }

    public function getAccountWidget(): mixed
    {
        $parent = $this->parent;
        if (!is_null($parent)) {
            do {
                if ($parent->isAccountWidget()) {
                    //parent is always a context widget but considered an account widget because it on the top level.
                    return $parent;
                }
            } while ($parent = $parent->parent);
        }
        return null;
    }

    public function getSortIndex(): int
    {
        return $this->order;
    }

    public function setSortIndex(int $newIndex): void
    {
        $this->order = $newIndex;
    }

    public function updateProperties(array $properties): bool
    {
        $this->properties = $properties;
        return $this->save();
    }

    public function toSearchableArray(): array
    {
        return [
            MeilisearchUserWidget::ID => $this->id,
            MeilisearchUserWidget::ACCOUNT_ID => $this->getAccountWidget()->account->id,
            MeilisearchUserWidget::USER_ID => $this->user_id,
            MeilisearchUserWidget::TITLE => $this->showLabel(),
            MeilisearchUserWidget::UPDATED_AT => Carbon::parse($this->updated_at)->getTimestamp(),
        ];
    }
}
