<?php

namespace App;

use App\Helpers\TaskAuditLogHelper;
use App\Models\TaskFile\Placeholder;

/**
 * Class User
 * @package App
 * @method static OneTimeSigner findOrFail($id)
 * @method static null|OneTimeSigner find($id)
 */
class OneTimeSigner extends ExpiringUser
{
    public const AUTH_ID_PREFIX = 'one_time_signer_';
    public const EXPIRE_DAYS_AFTER_SIGNING = 7;
    public const EXPIRE_DAYS = 90;

    public function beforeDelete(array $options = []): bool
    {
        /** @var Placeholder $placeholder */
        $placeholder = $this->placeholders()->with(['taskFile', 'taskFile.task'])->first();
        // If the placeholders still exist it means that the user was expired
        // and placeholders have been applied 7 days ago, thus deleting the user.
        // If the task no longer exists we do not need to put anything on the audit log.
        if (!empty($placeholder) && !empty($placeholder->taskFile->task)) {
            TaskAuditLogHelper::oneTimeSignerDeleted($placeholder->taskFile->task, $this);
        }

        return parent::beforeDelete($options);
    }

    /**
     * This is needed when querying for relations
     */
    public function getForeignKey(): string
    {
        return 'user_id';
    }
}
