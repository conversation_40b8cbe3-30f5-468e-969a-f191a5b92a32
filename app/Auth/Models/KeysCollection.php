<?php

namespace App\Auth\Models;

use Illuminate\Support\Collection;

class KeysCollection extends Collection
{
    /**
     * KeysCollection constructor.
     * Supports array of json elements (value) or array of Key or JSON string
     * @param array|string|null $keys
     */
    public function __construct($keys)
    {
        if (is_string($keys) && !empty($keys)) {
            $decoded = json_decode($keys, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $keys = $decoded;
            } else {
                $keys = null;
            }
        }
        parent::__construct($this->loadKeys($keys));
    }

    private function loadKeys(?array $keys): Collection
    {
        if (empty($keys)) {
            return collect();
        }

        return collect(array_values($keys))
            ->map(
                function ($data) {
                    if ($data instanceof Key) {
                        return $data;
                    }
                    return new Key($data);
                }
            )->filter(
                function (Key $key) {
                    return $key->hasValue();
                }
            );
    }

    public static function fromKeyValue(?string $key): KeysCollection
    {
        return new KeysCollection([Key::fromValue($key)]);
    }

    public static function fromCertificate(Certificate $certificate): KeysCollection
    {
        return new KeysCollection([new Key($certificate)]);
    }

    public static function mergeValidKeys(KeysCollection $set1, KeysCollection $set2): KeysCollection
    {
        $valid = $set1->valid()->merge($set2->valid());

        return $valid->unique(
            function (Key $key) {
                return $key->getValue();
            }
        );
    }

    public function firstByValue(?string $value): ?Key
    {
        return $this->first(
            function (Key $key) use ($value) {
                return !empty($key) && $key->getValue() === $value;
            }
        );
    }

    public function getDefaultVerificationValue(): ?string
    {
        $key = $this->getDefaultVerificationKey();
        if (empty($key)) {
            return null;
        }
        return $key->getValue();
    }

    public function getDefaultVerificationKey(): ?Key
    {
        return $this->firstByDefaultAndValid($this);
    }

    public function getDefaultSigningKey(): ?Key
    {
        $signingKeys = $this->filter(
            function (Key $key) {
                return $key->canBeUsedForSigning();
            }
        );
        return $this->firstByDefaultAndValid($signingKeys);
    }

    private function firstByDefaultAndValid(Collection $collection)
    {
        return $collection->first(
            function (Key $key) {
                return $key->isDefault() && $key->isValid() && !$key->almostExpired();
            }
        ) ?? $collection->first(
            function (Key $key) {
                return $key->isValid() && !$key->almostExpired();
            }
        ) ?? $collection->first(
            function (Key $key) {
                return $key->isValid();
            }
        ) ?? $collection->first(
            function (Key $key) {
                return !$key->almostExpired();
            }
        ) ?? $collection->first(
            function (Key $key) {
                return $key->isDefault();
            }
        ) ?? $collection->first();
    }

    public function valid(): KeysCollection
    {
        return $this->filter(
            function (Key $key) {
                return $key->isValid();
            }
        );
    }

    public function toArray(): array
    {
        return array_map(
            function (Key $value) {
                return $value->toArray();
            },
            $this->all()
        );
    }
}
