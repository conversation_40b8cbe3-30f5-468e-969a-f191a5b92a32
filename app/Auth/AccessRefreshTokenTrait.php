<?php

namespace App\Auth;

use App\Repositories\TokenRepository;
use App\Token;
use Illuminate\Database\Eloquent\ModelNotFoundException;

/**
 * This trait is used for storing tokens in the database for authentication later.
 * Is used by Twinfield and Exact API connections.
 *
 * Classes that use this trait need to have the following constants:
 * - ACCESS_TOKEN_TYPE
 * - REFRESH_TOKEN_TYPE
 */
trait AccessRefreshTokenTrait
{
    /**
     * The access token should be valid for 1 hour.
     * @param string $token
     * @param int $timestamp Expiration timestamp in seconds.
     * @return Token
     */
    public function updateAccessToken(string $token, int $timestamp): Token
    {
        $this->deleteAccessTokens();
        return $this->updateToken($token, $timestamp, self::ACCESS_TOKEN_TYPE);
    }

    public function deleteAccessTokens(): void
    {
        $this->deleteTokens(self::ACCESS_TOKEN_TYPE);
    }

    public function deleteTokens(string $type): void
    {
        $tokenRepo = resolve(TokenRepository::class);
        $tokenRepo->cleanTokensForAccountService($type, $this->accountService);
    }

    protected function getToken(string $type): ?Token
    {
        return resolve(TokenRepository::class)->getTokenFromAccountService($type, $this->accountService);
    }

    public function updateToken(string $token, int $expirationEpoch, string $type): Token
    {
        return resolve(TokenRepository::class)
            ->updateTokenForAccountService($token, $type, $this->accountService, $expirationEpoch);
    }

    /**
     * @return Token|null object or NULL.
     */
    public function getAccessToken(): ?Token
    {
        return $this->getToken(self::ACCESS_TOKEN_TYPE);
    }

    /**
     * The refresh token should be valid for 550 days.
     * @param string $token
     * @return Token
     */
    public function updateRefreshToken(string $token): Token
    {
        $this->deleteRefreshTokens();
        return $this->updateToken($token, time() + 550 * 24 * 3600, self::REFRESH_TOKEN_TYPE);
    }

    public function deleteRefreshTokens(): void
    {
        $this->deleteTokens(self::REFRESH_TOKEN_TYPE);
    }

    /**
     * @param bool $exceptionOnFail set to TRUE to throw exception when token is not found.
     * @return Token|null object or NULL.
     */
    public function getRefreshToken($exceptionOnFail = false): ?Token
    {
        $token = $this->getToken(self::REFRESH_TOKEN_TYPE);
        if ($exceptionOnFail && !$token instanceof Token) {
            throw new ModelNotFoundException('Refresh token not found in database.');
        }
        return $token;
    }
}
