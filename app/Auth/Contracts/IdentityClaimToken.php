<?php

namespace App\Auth\Contracts;

use App\Account;
use App\Auth\IdentityIssuer;
use App\Consumer;
use App\Repositories\ConsumerRepository;
use Illuminate\Http\Request;
use Log;

abstract class IdentityClaimToken
{
    protected Account $account;
    private ?IdentityIssuer $issuer;

    public function __construct(Account $account)
    {
        $this->account = $account;
    }

    public static function consumerRepository(): ConsumerRepository
    {
        return new ConsumerRepository();
    }

    public function getIssuer(): ?IdentityIssuer
    {
        return ($this->issuer = ($this->issuer ?? $this->loadIssuer()));
    }

    public function getIssClaim()
    {
        return null;
    }

    abstract public function validate(Request $request, IdentityIssuer $issuer): bool;

    abstract public function getType(): string;

    abstract public function getIssuerAuthId(): ?string;

    /**
     * @param string $key
     * @param string|null $default
     * @return mixed
     */
    abstract public function getAttribute(string $key, ?string $default = null);

    abstract public function hasSubject(): bool;

    abstract public function getSubject(): ?string;

    public function getName(): ?string
    {
        $name = $this->getAttribute('name');
        if (empty($name)) {
            $name = trim($this->getAttribute('firstname') . ' ' . $this->getAttribute('lastname'));
        }
        return $name;
    }

    public function getEmail(): ?string
    {
        $attr = $this->getAttribute('email');

        if (is_array($attr)) {
            if (empty($attr)) {
                return null;
            }
            return $attr[0];
        }

        return $attr;
    }

    public function getSessionId(?string $default = null): ?string
    {
        return $default;
    }

    protected function loadIssuer(): ?IdentityIssuer
    {
        return self::loadIssuerByAuthId($this->account, $this->getIssuerAuthId());
    }

    public static function loadIssuerByAuthId(Account $account, ?string $authId): ?IdentityIssuer
    {
        if ($authId == $account->uri) {
            Log::debug("Host Account is issuer. Supported on custom to native domain redirects only.");
            return $account;
        }

        $issuer = self::consumerRepository()->findByConsumerAuthIdAndAccount($account, $authId);

        if (empty($issuer)) {
            $issuer = $account->identityIssuers()->where(Consumer::AUTH_ID, $authId)->first();
        }

        if (empty($issuer)) {
            Log::error('Unknown issuer ' . $authId . ' for account ' . $account->uri);
        }

        return $issuer;
    }

    /**
     * Get the AMR token if any, the default will be 'pop'.
     * This method should be abstract but avoid breaking things
     * since there are no tests, won't be.
     *
     * @see https://connect2id.com/blog/standard-authentication-methods
     *
     * @return string[]
     */
    public function getAMR(): array
    {
        return ['pop'];
    }
}
