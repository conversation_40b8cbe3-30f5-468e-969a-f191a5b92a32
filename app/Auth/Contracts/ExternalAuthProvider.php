<?php

namespace App\Auth\Contracts;

use App\Account;
use App\Auth\IdentityIssuer;
use App\Auth\Models\Endpoint;
use App\Auth\Models\EndpointsCollection;
use App\Exceptions\BadRequestException;
use App\Support\CarbonImmutable;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Http\Request;
use Log;
use Symfony\Component\HttpFoundation\Response;

abstract class ExternalAuthProvider
{
    protected Account $account;
    protected IdentityIssuer $issuer;
    protected EndpointsCollection $loginEndpoints;
    protected EndpointsCollection $logoutEndpoints;
    protected CarbonImmutable $now;

    public function __construct(Account $account, IdentityIssuer $issuer)
    {
        $this->issuer = $issuer;
        $this->loginEndpoints = new EndpointsCollection($this->issuer->login_endpoints);
        $this->logoutEndpoints = new EndpointsCollection($this->issuer->logout_endpoints);
        $this->account = $account;
        $this->now = CarbonImmutable::now('UTC');
    }

    abstract protected function logoutSupported(): bool;

    public function logoutEnabled()
    {
        return $this->logoutSupported() && (bool)$this->issuer->getConfiguration('logout_enabled');
    }

    public function redirectToLogin(Request $request): Response
    {
        $endpoint = $this->getDefaultLoginEndpoint();

        Log::channel('redirect')->info('Redirecting to IDP Login: ' . $endpoint->getLocation());

        return redirect($endpoint->getLocation());
    }

    public function redirectToLogout(?Request $request = null, ?Authenticatable $user = null): Response
    {
        $endpoint = $this->getDefaultLogoutEndpoint();

        Log::channel('redirect')->info('Redirecting to IDP Logout: ' . $endpoint->getLocation());

        return redirect($endpoint->getLocation());
    }

    protected function getDefaultLoginEndpoint(): Endpoint
    {
        return $this->getDefaultEndpointOrThrow(
            $this->loginEndpoints,
            "Unable to initiate external login for consumer {$this->issuer->id} ({$this->issuer->auth_id})"
        );
    }

    protected function getDefaultLogoutEndpoint(): Endpoint
    {
        return $this->getDefaultEndpointOrThrow(
            $this->logoutEndpoints,
            "Unable to initiate external logout for consumer {$this->issuer->id} ({$this->issuer->auth_id})"
        );
    }

    protected function getDefaultEndpointOrThrow(EndpointsCollection $endpoints, string $message): Endpoint
    {
        $endpoint = $endpoints->getDefault();

        if (empty($endpoint) || empty($endpoint->getLocation())) {
            throw new BadRequestException($message);
        }
        return $endpoint;
    }
}
