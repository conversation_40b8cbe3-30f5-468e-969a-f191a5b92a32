<?php

namespace App\Services\Declarations;

use App\AccountService;
use App\Company;
use App\Exceptions\UnauthorizedException;
use App\Factories\Services\Declarations\IctXbrlFactory;
use App\Factories\TaskFileFactory;
use App\Repositories\CompanyUserRepository;
use App\Repositories\Services\ServiceTaskRepository;
use App\Services\CompanyIdentifierSyncService;
use App\Services\TaskFileService;
use App\ServiceTask;
use App\Support\Database\Builder;
use App\User;
use App\ValueObject\Declarations\DeclarationData;
use App\ValueObject\Declarations\Ict\ContactDetails;
use App\ValueObject\Declarations\Ict\Ict;
use App\ValueObject\Identifier\ObNumber;
use App\Support\Carbon;
use Illuminate\Database\Eloquent\Collection;

class IctService
{
    private CompanyUserRepository $companyUserRepository;
    private CompanyIdentifierSyncService $companyFiscalIdentifierNumbersSyncService;
    private ServiceTaskRepository $serviceTaskRepository;
    private TaskFileService $taskFileService;
    private DeclarationsService $declarationsService;

    public function __construct(
        CompanyUserRepository $companyUserRepository,
        CompanyIdentifierSyncService $companyFiscalIdentifierNumbersSyncService,
        ServiceTaskRepository $serviceTaskRepository,
        TaskFileService $taskFileService,
        DeclarationsService $declarationsService
    ) {
        $this->companyUserRepository = $companyUserRepository;
        $this->companyFiscalIdentifierNumbersSyncService = $companyFiscalIdentifierNumbersSyncService;
        $this->serviceTaskRepository = $serviceTaskRepository;
        $this->taskFileService = $taskFileService;
        $this->declarationsService = $declarationsService;
    }

    public function createIctDeclaration(
        AccountService $accountService,
        Company $company,
        DeclarationData $declarationData
    ): ServiceTask {
        // Adds VAT number to company if it doesn't exist
        $this->companyFiscalIdentifierNumbersSyncService->createOrSyncVatNumbers(
            $company->id,
            $declarationData->fiscalIdentifier()
        );

        $task = $this->declarationsService->generateTask($accountService, $declarationData, $company);

        $this->taskFileService->storeFiles($declarationData->files(), $task);

        return $task;
    }

    public function createDeclarationData(
        AccountService $accountService,
        Company $company,
        User $user,
        Ict $ict,
        string $mobile
    ): DeclarationData {
        Builder::$cacheEnabled = false;

        // Check if there are similar ICT's by general information.
        $similarIcts = $this->indexWithoutData(
            $accountService,
            $ict->getObNumber(),
            $ict->getStartDate(),
            $ict->getEndDate(),
            $company
        );

        // If there are previous ICT's, we will create them with the same contact details from them.
        if ($similarIcts->count() > 0) {
            $contactDetails = $similarIcts->first()->contactDetails();
        } else {
            if (!$this->companyUserRepository->exists($user, $company)) {
                throw new UnauthorizedException('Company user does not exist');
            }

            $contactDetails = ContactDetails::createFromUser($user, $mobile);
        }

        // Creates XBRL in XML format
        $xbrl = IctXbrlFactory::generateXbrlFromIct($ict, $contactDetails);
        $file = TaskFileFactory::create(
            $accountService,
            IctXbrlFactory::getIctXbrlAsXmlString($xbrl),
            $ict->filename()
        );

        $refreshId = null;
        if (isset($file->parsed_data['source_id'])) {
            $refreshId = $file->parsed_data['source_id'];
        }

        $data = $file->parsed_data;
        if ($contactDetails !== null) {
            $data = array_merge(
                $data,
                $contactDetails->toArray()
            );
        }

        return new DeclarationData(
            $file,
            $data,
            [],
            $refreshId
        );
    }

    /**
     * Search for ICT's without caring about its data, just general information.
     *
     * @param  AccountService  $accountService
     * @param  ObNumber  $obNumber
     * @param  Carbon  $dateStart
     * @param  Carbon  $dateEnd
     * @param  Company  $company
     *
     * @return Collection|ServiceTask[]
     */
    public function indexWithoutData(
        AccountService $accountService,
        ObNumber $obNumber,
        Carbon $dateStart,
        Carbon $dateEnd,
        Company $company
    ): Collection {
        return $this->serviceTaskRepository->indexTasks(
            $dateStart,
            $dateEnd,
            ServiceTask::TYPE_ICT_APPROVAL,
            $company,
            $accountService,
            $obNumber->__toString(),
            DeclarationData::OBNUMBER_FIELD
        );
    }

    public function replaceIctDeclaration(
        AccountService $accountService,
        Company $company,
        User $user,
        Ict $ict,
        string $mobile
    ): ?ServiceTask {
        // Check if there are similar ICT's by general information.
        $similarServiceTasks = $this->indexWithoutData(
            $accountService,
            $ict->getObNumber(),
            $ict->getStartDate(),
            $ict->getEndDate(),
            $company
        );

        /** @var ServiceTask $task */
        foreach ($similarServiceTasks as $serviceTask) {
            $serviceTask->delete();
        }

        $declarationData = $this->createDeclarationData(
            $accountService,
            $company,
            $user,
            $ict,
            $mobile
        );

        return $this->createIctDeclaration($accountService, $company, $declarationData);
    }
}
