<?php

namespace App\Services\Webhook;

use App\Account;
use App\Helpers\WebhookAuditLogHelper;
use App\Model;
use App\Models\Webhooks\Webhook;
use App\Models\Webhooks\WebhookCondition;
use App\Repositories\TokenRepository;
use App\Repositories\Webhook\WebhookRepository;
use App\Token;
use App\User;
use App\Webhooks\LogiusStatusChangeWebhook;
use App\Webhooks\SbrStatusChangeWebhook;
use App\Webhooks\ServiceTaskResponseStatusChangeWebhook;
use App\Webhooks\ServiceTaskStatusChangeWebhook;
use App\Webhooks\TaskFilePlaceholderAppliedWebhook;
use App\Webhooks\WebhookDispatcher;
use Illuminate\Database\Eloquent\Collection;

class WebhookService
{
    private const WEBHOOK_CLASSES = [
        Webhook::SERVICE_TASK_STATUS_CHANGE => ServiceTaskStatusChangeWebhook::class,
        Webhook::SERVICE_TASK_RESPONSE_STATUS_CHANGE => ServiceTaskResponseStatusChangeWebhook::class,
        Webhook::TASK_FILE_PLACEHOLDER_APPLIED => TaskFilePlaceholderAppliedWebhook::class,
        Webhook::LOGIUS_STATUS_CHANGE => LogiusStatusChangeWebhook::class,
        Webhook::SBR_STATUS_CHANGE => SbrStatusChangeWebhook::class
    ];

    public function __construct(private readonly WebhookRepository $webhookRepository)
    {
    }

    public function get(Account $account): Collection
    {
        return $this->webhookRepository->get($account->id);
    }

    public function getByCondition(Account $account, string $event): Collection
    {
        return $this->webhookRepository->getByCondition($account->id, $event);
    }

    public function triggerWebhook(Account $account, string $event, Model $model, array $data = []): void
    {
        $webhooks = $this->getByCondition($account, $event);
        if ($webhooks->isEmpty()) {
            return;
        }
        $secret = $this->getToken($account)->token;
        foreach ($webhooks as $accountWebhook) {
            $dispatcherClass = static::WEBHOOK_CLASSES[$event];
            $webhookDispatcher = new $dispatcherClass($accountWebhook, $model);
            if (!$webhookDispatcher instanceof WebhookDispatcher) {
                // event to class mapping can be set above in const WEBHOOK_CLASSES
                die('No class found for dispatching webhook for event ' . $event);
            }
            $webhookDispatcher->setAdditionalData($data)
                ->setSecret($secret)
                ->dispatch();
            WebhookAuditLogHelper::webhookDispatched($accountWebhook, $event, $webhookDispatcher->getRequestBody());
        }
    }

    public function save(Account $account, array $data, ?User $user = null): \Illuminate\Support\Collection
    {
        $created = collect();

        foreach ($data as $webhookData) {
            $webhook = $this->updateOrCreateWebhook($account, $webhookData, $user);
            $created->push($webhook);
            $conditions = collect();
            foreach ($webhookData['events'] as $event) {
                $conditions->push($this->setConditions($event, $webhook));
            }
            $this->webhookRepository->deleteRemainingWebhookConditions($webhook, $conditions->pluck('id')->toArray());
        }

        $this->webhookRepository->deleteRemainingForAccount($account->id, $created->pluck('id')->toArray());
        return $created;
    }

    protected function updateOrCreateWebhook(Account $account, array $webhookData, ?User $user = null): Webhook
    {
        return Webhook::updateOrCreate(
            [
                Webhook::ID => $webhookData['id'] ?? null,
                Webhook::ACCOUNT_ID => $account->id,
            ],
            [
                Webhook::URL => $webhookData['url'],
                Webhook::CREATED_BY => $user?->id
            ]
        );
    }

    protected function setConditions(string $event, Webhook $webhook): WebhookCondition
    {
        return WebhookCondition::firstOrCreate(
            [
                WebhookCondition::WEBHOOK_ID => $webhook->id,
                WebhookCondition::ACCOUNT_ID => $webhook->account_id,
                WebhookCondition::EVENT => $event,
            ]
        );
    }

    /**
     * Create and save token without expiration date in tokens table.
     * @param Account $account
     * @return Token
     */
    public function createToken(Account $account): Token
    {
        $tokenRepository = resolve(TokenRepository::class);

        $attributes = [
            Token::TOKEN => random_string(40),
            Token::ACCOUNT_ID => $account->id,
            Token::TYPE => Token::TYPE_WEBHOOK_DIGEST,
            Token::EXPIRATION_DATE => null
        ];
        return $tokenRepository->create($attributes);
    }

    /**
     * Get the token to use as secret in creating digest webhook request header
     * @param Account $account
     * @return Token
     */
    public function getToken(Account $account): Token
    {
        $tokenRepository = resolve(TokenRepository::class);
        $token = $tokenRepository->getForAccount($account->id, Token::TYPE_WEBHOOK_DIGEST);

        if (!is_null($token)) {
            return $token;
        }

        return $this->createToken($account);
    }
}
