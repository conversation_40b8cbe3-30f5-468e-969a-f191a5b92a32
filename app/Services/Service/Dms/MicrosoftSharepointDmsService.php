<?php

namespace App\Services\Service\Dms;

use App;
use App\Account;
use App\AccountService;
use App\CompanyIdentifier;
use App\Exceptions\Api\ForbiddenException;
use App\Exceptions\Api\NotFoundException;
use App\Exceptions\PreconditionFailedException;
use App\Exceptions\Services\Dms\MicrosoftSharepoint\AuthorizingNonMicrosoftSharepointAccountServiceException;
use App\Exceptions\Services\Dms\MicrosoftSharepoint\CachedStateNotFoundException;
use App\Exceptions\Services\Dms\MicrosoftSharepoint\ClientSecretExpiredException;
use App\Exceptions\Services\Dms\MicrosoftSharepoint\DriveDoesNotExistsException;
use App\Exceptions\Services\Dms\MicrosoftSharepoint\FullControlScopeNotGrantedException;
use App\Exceptions\Services\Dms\MicrosoftSharepoint\ItemIsNotFolderException;
use App\Exceptions\Services\Dms\MicrosoftSharepoint\MissingSharepointTokenException;
use App\Exceptions\Services\Dms\MicrosoftSharepoint\RefreshTokenNotGrantedException;
use App\Exceptions\Services\Dms\MicrosoftSharepoint\SiteDoesNotExistsException;
use App\GenericService;
use App\Helpers\NotificationHelper;
use App\Helpers\QuarantineHelper;
use App\Helpers\TaskAuditLogHelper;
use App\Interfaces\Services\Dms\CanSendToDmsInterface;
use App\Logging\Channels\DmsLog;
use App\Logging\Channels\ServiceLog;
use App\Models\OpenQuestions\OpenQuestionAttachment;
use App\Models\OpenQuestions\OpenQuestionRelationType;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Models\ServiceTask\IctApprovalTask;
use App\Repositories\FileSystem\EncryptedStorageRepository;
use App\Repositories\Http\OAuthRepository;
use App\Repositories\Http\Services\Dms\MicrosoftSharepointDmsRepository;
use App\Repositories\Services\AccountServiceRepository;
use App\Repositories\TokenRepository;
use App\Service;
use App\Services\AccountServiceService;
use App\Services\CompanyIdentifierSyncService;
use App\Services\CompanyIdentifierService;
use App\Services\Dms\DmsService;
use App\Services\OAuth\OAuthService;
use App\Services\ServiceTask\ServiceTaskCompanyDmsPreferenceService;
use App\ServiceTask;
use App\Token;
use App\User;
use App\ValueObject\Dms\OpenQuestionAttachmentDmsBundle;
use App\ValueObject\OAuth\Token as OAuthToken;
use App\ValueObject\Services\Dms\MicrosoftSharepoint\Configuration;
use App\ValueObject\Services\Dms\MicrosoftSharepoint\DirectoryConfiguration;
use App\ValueObject\Services\Dms\MicrosoftSharepoint\Drive;
use App\ValueObject\Services\Dms\MicrosoftSharepoint\Item;
use App\ValueObject\Services\Dms\MicrosoftSharepoint\Sites;
use Cache;
use App\Support\Carbon;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class MicrosoftSharepointDmsService extends AbstractDmsService
{
    private readonly AccountServiceRepository $accountServiceRepository;
    private readonly OAuthService $oauthService;
    private readonly OAuthRepository $oauthRepository;
    private readonly TokenRepository $tokenRepository;
    private readonly MicrosoftSharepointDmsRepository $dmsRepository;
    private readonly AccountServiceService $accountServiceService;
    private readonly CompanyIdentifierSyncService $companyFiscalIdentifierNumbersSyncService;
    private readonly CompanyIdentifierService $companyIdentifierService;
    private readonly ServiceTaskCompanyDmsPreferenceService $serviceTaskCompanyDmsPreferenceService;

    public const PROPERTY_DMS_AUDIT_LOG = 'dms_audit_log';
    public const PROPERTY_CLIENT_ID = 'client_id';
    public const PROPERTY_CLIENT_SECRET = 'client_secret';
    public const PROPERTY_TENANT_ID = 'tenant_id';
    public const PROPERTY_DEFAULT_PATHS = 'default_paths';
    public const PROPERTY_SHAREPOINT_PATH = 'sharepoint_path';
    public const PROPERTY_SHAREPOINT_PATH_ID = 'id';
    public const PROPERTY_TYPES = 'types';
    public const PROPERTY_DOMINANT = 'dominant';

    public const ALLOWED_PROPERTY_KEYS = [
        self::PROPERTY_DMS_AUDIT_LOG,
        self::PROPERTY_CLIENT_ID,
        self::PROPERTY_CLIENT_SECRET,
        self::PROPERTY_TENANT_ID,
        self::PROPERTY_DEFAULT_PATHS,
        self::PROPERTY_TYPES
    ];

    private const CONFIG_KEY_AUTH_URL = 'services.microsoft_sharepoint_dms.authorization_url';
    private const CONFIG_KEY_TOKEN_URL = 'services.microsoft_sharepoint_dms.token_url';
    private const CONFIG_KEY_CALLBACK_URL = 'services.microsoft_sharepoint_dms.callback_url';
    private const SHAREPOINT_CACHE_KEY = 'oauth_authorize_microsoft_sharepoint';

    private const CLIENT_SECRET_EXPIRED_CODE = 'AADSTS7000222';

    public const UNPROTECTED_PROPERTY_KEYS = [
        self::PROPERTY_DEFAULT_PATHS,
        AccountService::PROPERTY_SEND_TEMPLATE_RESPONSE
    ];

    public function __construct(AccountService $accountService)
    {
        parent::__construct($accountService);
        $this->accountServiceRepository = resolve(AccountServiceRepository::class);
        $this->oauthService = resolve(OAuthService::class);
        $this->oauthRepository = resolve(OAuthRepository::class);
        $this->tokenRepository = resolve(TokenRepository::class);
        $this->dmsRepository = resolve(MicrosoftSharepointDmsRepository::class);
        $this->accountServiceService = resolve(AccountServiceService::class);
        $this->companyFiscalIdentifierNumbersSyncService = resolve(CompanyIdentifierSyncService::class);
        $this->companyIdentifierService = resolve(CompanyIdentifierService::class);
        $this->serviceTaskCompanyDmsPreferenceService = resolve(ServiceTaskCompanyDmsPreferenceService::class);
    }

    public function authorize(
        int $accountServiceId,
        User $user,
        Configuration $configuration
    ): string {
        $accountService = $this->accountServiceService->getById($accountServiceId, $user);

        if ($accountService->service->reference_name !== Service::MICROSOFT_SHAREPOINT_DMS_SERVICE) {
            throw new AuthorizingNonMicrosoftSharepointAccountServiceException('Service is not sharepoint');
        }

        // Save the state a random string, so we can find back the account that made the request.
        $state = Str::random();
        Cache::set(
            self::SHAREPOINT_CACHE_KEY . ':' . $state,
            json_encode([
                'account_service_id' => $accountService->id,
                self::PROPERTY_CLIENT_ID => $configuration->clientId,
                self::PROPERTY_CLIENT_SECRET => $configuration->clientSecret,
                self::PROPERTY_TENANT_ID => $configuration->tenantId,
            ]),
            5 * 60 // Save in cache for 5 minutes
        );

        return $this->oauthService->getAuthorizationUrl(
            str_replace(
                ':' . self::PROPERTY_TENANT_ID . ':',
                $configuration->tenantId,
                config(self::CONFIG_KEY_AUTH_URL)
            ),
            config(self::CONFIG_KEY_CALLBACK_URL),
            $configuration->clientId,
            $configuration->clientSecret,
            $state,
            ['Sites.FullControl.All', 'offline_access']
        );
    }

    public function authenticated(string $authorizationCode, string $state): AccountService
    {
        // Try to find the key if its on Cache.
        $configuration = Cache::get(self::SHAREPOINT_CACHE_KEY . ':' . $state);

        /*
         * This can happen if:
         * - The user took more than 15min to consent
         * - The same state was used already.
         * - The user tries a different state other than what we provided before going to the consent screen.
         */
        if ($configuration === null) {
            throw new CachedStateNotFoundException('Cache not found');
        }

        // Decode the configuration saved and delete it from cache so it is not used again.
        $configuration = json_decode($configuration, true);
        Cache::delete(self::SHAREPOINT_CACHE_KEY . ':' . $state);

        $token = $this->useAuthorizationCode(
            $configuration[self::PROPERTY_CLIENT_ID],
            $configuration[self::PROPERTY_CLIENT_SECRET],
            $configuration[self::PROPERTY_TENANT_ID],
            $authorizationCode
        );

        // Get the AccountService saved in cache.
        $accountService = $this->accountServiceRepository->getById($configuration['account_service_id']);

        // Delete all previous tokens if exists.
        $this->tokenRepository->deleteAllOfTypeAndAccountService(Token::TYPE_OAUTH2_ACCESS_TOKEN, $accountService);
        $this->tokenRepository->deleteAllOfTypeAndAccountService(Token::TYPE_OAUTH2_REFRESH_TOKEN, $accountService);

        // Save the access and refresh token.
        $this->tokenRepository->create([
            'token' => $token->accessToken(),
            'type' => Token::TYPE_OAUTH2_ACCESS_TOKEN,
            'expiration_date' => Carbon::now('UTC')->addSeconds($token->expiresIn())->toDateTimeString(),
            'account_service_id' => $configuration['account_service_id'],
            'data' => ['scopes' => $token->scopes()]
        ]);
        $this->tokenRepository->create([
            'token' => $token->refreshToken(),
            'type' => Token::TYPE_OAUTH2_REFRESH_TOKEN,
            'expiration_date' => Carbon::now('UTC')->addMonths(6)->toDateTimeString(),
            'account_service_id' => $configuration['account_service_id'],
            'data' => ['scopes' => $token->scopes()]
        ]);

        // Save the configurations.
        $this->accountServiceRepository->updateProperties(
            $accountService,
            [
                self::PROPERTY_CLIENT_ID => $configuration[self::PROPERTY_CLIENT_ID],
                self::PROPERTY_CLIENT_SECRET => $configuration[self::PROPERTY_CLIENT_SECRET],
                self::PROPERTY_TENANT_ID => $configuration[self::PROPERTY_TENANT_ID],
            ]
        );

        // Return the AccountService for redirect in controller.
        return $accountService;
    }

    public function getByAccount(Account $account, User $user = null)
    {
        $accountService = $this->accountServiceRepository->firstEnabled(
            $account,
            GenericService::TYPE_DOCUMENT_MANAGEMENT_SYSTEM
        );

        if (
            empty($accountService)
            || $accountService->service->reference_name !== Service::MICROSOFT_SHAREPOINT_DMS_SERVICE
        ) {
            throw new NotFoundException();
        }

        if ($user !== null && $account->id !== $user->account_id) {
            throw new ForbiddenException();
        }

        return $accountService;
    }

    /**
     * @param Account $account
     * @param User $user
     * @param string|null $search
     * @return \App\ValueObject\Services\Dms\MicrosoftSharepoint\Sites
     * @throws \App\Exceptions\Api\ForbiddenException
     * @throws \App\Exceptions\Api\NotFoundException
     * @throws \Exception
     */
    public function listSites(Account $account, User $user, ?string $search = ''): Sites
    {
        $accessToken = $this->getAccessToken($account, $user);

        return $this->dmsRepository->listAllSites($accessToken, !empty($search) ? $search : '*');
    }

    /**
     * @param Account $account
     * @param User $user
     * @param string $siteId
     *
     * @return Drive[]
     */
    public function listDrives(Account $account, User $user, string $siteId): array
    {
        $accessToken = $this->getAccessToken($account, $user);

        return $this->dmsRepository->listDrives($accessToken, $siteId);
    }

    /**
     * @param Account $account
     * @param User $user
     * @param string $driveId
     *
     * @return Item[]
     */
    public function listFoldersInDriveChildren(Account $account, User $user, string $driveId): array
    {
        $accessToken = $this->getAccessToken($account, $user);

        $driveChildren = $this->dmsRepository->listDriveChildren($accessToken, $driveId);

        return $this->getFolders($driveChildren);
    }

    /**
     * @param array $items
     *
     * @return Item[]
     */
    private function getFolders(array $items): array
    {
        $folders = [];
        foreach ($items as $item) {
            if ($item->isFolder()) {
                $folders[] = $item;
            }
        }

        return $folders;
    }

    public function listFoldersInItemChildren(
        Account $account,
        User $user,
        string $driveId,
        string $itemId
    ): array {
        $accessToken = $this->getAccessToken($account, $user);

        $itemChildren = $this->dmsRepository->listItemChildren($accessToken, $driveId, $itemId);

        return $this->getFolders($itemChildren);
    }

    public function useAuthorizationCode(
        string $clientId,
        string $clientSecret,
        string $tenantId,
        string $authorizationCode
    ): OAuthToken {
        // Use the authorization code to get an access and refresh token.
        $token = $this->oauthRepository->useAuthorizationCode(
            $clientId,
            $clientSecret,
            str_replace(
                ':' . self::PROPERTY_TENANT_ID . ':',
                $tenantId,
                config(self::CONFIG_KEY_TOKEN_URL)
            ),
            config(self::CONFIG_KEY_CALLBACK_URL),
            $authorizationCode
        );

        // Check if we have the right scope.
        if (!\in_array('sites.fullcontrol.all', $token->scopes())) {
            throw new FullControlScopeNotGrantedException('Full control scope not applied inside sharepoint');
        }

        // Check if we have a refresh token to use.
        if ($token->refreshToken() === null) {
            throw new RefreshTokenNotGrantedException('Refresh is no longer valid');
        }

        return $token;
    }

    public function useRefreshToken(
        string $clientId,
        string $clientSecret,
        string $tenantId,
        string $refreshToken
    ): OAuthToken {
        // Use the authorization code to get an access and refresh token.
        $token = $this->oauthRepository->useRefreshToken(
            $clientId,
            $clientSecret,
            str_replace(
                ':' . self::PROPERTY_TENANT_ID . ':',
                $tenantId,
                config(self::CONFIG_KEY_TOKEN_URL)
            ),
            $refreshToken
        );

        // Check if we have the right scope.
        if (!\in_array('sites.fullcontrol.all', $token->scopes())) {
            throw new FullControlScopeNotGrantedException('Full control scope not applied inside sharepoint');
        }

        // Check if we have a refresh token to use.
        if ($token->refreshToken() === null) {
            throw new RefreshTokenNotGrantedException('Refresh token not granted');
        }

        return $token;
    }

    /**
     * @throws NotFoundException
     * @throws ForbiddenException
     * @throws MissingSharepointTokenException
     */
    public function getAccessToken(Account $account, User $user = null): Token
    {
        $accountService = $this->getByAccount($account, $user);

        $accessToken = $this->tokenRepository->getLatestTokenFromAccountService(
            Token::TYPE_OAUTH2_ACCESS_TOKEN,
            $accountService
        );

        if ($accessToken === null || $accessToken->isExpired()) {
            $refreshToken = $this->tokenRepository->getLatestTokenFromAccountService(
                Token::TYPE_OAUTH2_REFRESH_TOKEN,
                $accountService
            );

            if ($refreshToken === null) {
                throw new MissingSharepointTokenException('Sharepoint not correctly configured');
            }

            /** @var Configuration $configuration */
            $configuration = $accountService->getProvider()->getConfiguration();

            try {
                $token = $this->oauthRepository->useRefreshToken(
                    $configuration->clientId,
                    $configuration->clientSecret,
                    str_replace(
                        ':' . self::PROPERTY_TENANT_ID . ':',
                        $configuration->tenantId,
                        config(self::CONFIG_KEY_TOKEN_URL)
                    ),
                    $refreshToken->token
                );
            } catch (ClientException $e) {
                if ($e->getCode() === 401 && Str::contains($e->getMessage(), self::CLIENT_SECRET_EXPIRED_CODE)) {
                    throw new ClientSecretExpiredException(
                        'Client secret expired for account service #' . $accountService->id,
                        previous: $e
                    );
                } else {
                    throw $e;
                }
            }

            $refreshToken->forceDelete();
            // Only delete Access Token when it exists.
            if ($accessToken !== null) {
                $accessToken->forceDelete();
            }

            // Save the access and refresh token.
            $accessToken = $this->tokenRepository->create([
                'token' => $token->accessToken(),
                'type' => Token::TYPE_OAUTH2_ACCESS_TOKEN,
                'expiration_date' => Carbon::now('UTC')->addSeconds($token->expiresIn())->toDateTimeString(),
                'account_service_id' => $accountService->id,
                'data' => $token->scopes()
            ]);
            $this->tokenRepository->create([
                'token' => $token->refreshToken(),
                'type' => Token::TYPE_OAUTH2_REFRESH_TOKEN,
                'expiration_date' => Carbon::now('UTC')->addMonths(6)->toDateTimeString(),
                'account_service_id' => $accountService->id,
                'data' => $token->scopes()
            ]);
        }

        return $accessToken;
    }

    public function getDmsConfigurationForTask(ServiceTask $task): ?DirectoryConfiguration
    {
        $companyIdentifier = $this->companyIdentifierService->getByIdentifierAndType(
            $task,
            [IctApprovalTask::USE_OBNUMBER => true]
        );

        $taskTypeConfiguration = null;
        if ($companyIdentifier instanceof CompanyIdentifier) {
            $sharepointConfiguration = $companyIdentifier->getSharepointDirectoryConfiguration();
            if ($sharepointConfiguration !== null) {
                $taskTypeConfiguration = $sharepointConfiguration;
            }
        } elseif (is_null($task->getIdentifier())) {
            $taskDmsPreference = $this->serviceTaskCompanyDmsPreferenceService->getByCompanyAndTaskType($task);
            if ($taskDmsPreference !== null) {
                $taskTypeConfiguration = $taskDmsPreference->getSharepointDirectoryConfiguration();
            }
        }

        /**
         * @deprecated DmsConfigurations saved in tasks should be slowly phased out.
         * Was here for when there was no place to save the configuration for some tasks.
         * This has now been addressed and thus this code should not be needed anymore.
         * However, this code is still filled for existing tasks.
         * When we think this code is no longer needed we can phase this out.
         */
        $taskDmsConfiguration = $task->dmsConfiguration();
        // for completed tasks we always check the task for the path used for sharepoint
        if ($task->status === ServiceTask::STATUS_COMPLETED && $taskDmsConfiguration !== null) {
            return $taskDmsConfiguration;
        }

        $activeDmsAccountService = $this->accountServiceRepository->firstEnabledByServiceName(
            $task->account,
            Service::MICROSOFT_SHAREPOINT_DMS_SERVICE
        );

        $serviceDmsConfiguration = null;
        if ($activeDmsAccountService) {
            if ($task->hasUploadType()) {
                $taskType = $task->service()->reference_name . '-' . $task->uploadTypeId();
            } else {
                $taskType = $task->type === ServiceTask::TYPE_YEARWORK_DOCUMENTS ? ServiceTask::TYPE_YEARWORK_APPROVAL : $task->type; //phpcs:ignore
            }
            $serviceDmsConfiguration = $this->getSharepointPathFromAccount($activeDmsAccountService, $taskType);
        }

        // When there is a service configuration which is dominant and there were no specific settings set for this task
        if ($serviceDmsConfiguration !== null && $serviceDmsConfiguration->isDominant()) {
            return $serviceDmsConfiguration;
        }

        // If there is a configuration saved for the identifier we take that
        if (!empty($taskTypeConfiguration)) {
            return $taskTypeConfiguration;
        }

        // If the identifier is either empty or has no configuration we check the task
        if (!empty($taskDmsConfiguration)) {
            return $taskDmsConfiguration;
        }

        // If the identifier and task configurations are both empty we check the service configuration.
        if (!empty($serviceDmsConfiguration)) {
            return $serviceDmsConfiguration;
        }

        return null;
    }

    /**
     * @param string $type
     * @return DirectoryConfiguration|null
     */
    public function getSharepointPathFromAccount(AccountService $accountService, string $type): ?DirectoryConfiguration
    {
        $defaultPaths = $accountService->getProperty(self::PROPERTY_DEFAULT_PATHS);
        if (is_null($defaultPaths)) {
            return null;
        }
        foreach ($defaultPaths as $defaultPath) {
            if (
                isset($defaultPath[self::PROPERTY_SHAREPOINT_PATH])
                && !empty($defaultPath[self::PROPERTY_TYPES])
                && in_array($type, $defaultPath[self::PROPERTY_TYPES], true)
            ) {
                try {
                    $itemId = null;
                    if (count($defaultPath[self::PROPERTY_SHAREPOINT_PATH]) > 2) {
                        $itemId = array_last($defaultPath[self::PROPERTY_SHAREPOINT_PATH])[self::PROPERTY_SHAREPOINT_PATH_ID]; // phpcs:ignore
                    }
                    $siteId = array_first($defaultPath[self::PROPERTY_SHAREPOINT_PATH])[self::PROPERTY_SHAREPOINT_PATH_ID]; // phpcs:ignore
                    $driveId = null;
                    if (count($defaultPath[self::PROPERTY_SHAREPOINT_PATH]) > 1) {
                        $driveId = $defaultPath[self::PROPERTY_SHAREPOINT_PATH][1][self::PROPERTY_SHAREPOINT_PATH_ID];
                    }
                    return new DirectoryConfiguration(
                        $siteId,
                        $driveId,
                        $itemId,
                        $defaultPath[self::PROPERTY_DOMINANT] ?? false
                    );
                } catch (Throwable $e) {
                    ServiceLog::error('Wrong path saved for Type: ' . $type . ' with MicrosoftSharepoint: ' . $e->getMessage()); // phpcs:ignore
                    return null;
                }
            }
        }
        return null;
    }

    /**
     * @inheritDoc
     */
    public function sendServiceTaskDocuments(
        ServiceTask $serviceTask,
        array $files,
    ): void {
        $accessToken = $this->getAccessToken($serviceTask->account);
        $dmsConfiguration = $this->getDmsConfigurationForTask($serviceTask);

        $errorMessages = [];
        // Loop through all files of the task.
        foreach ($files as $file) {
            try {
                // Upload the file and get the URL to access it.
                $webUrl = $this->dmsRepository->uploadFile(
                    $accessToken,
                    $dmsConfiguration,
                    $file->getFilename(),
                    $file->getContent()
                );

                TaskAuditLogHelper::documentSentToDms($serviceTask, Service::MICROSOFT_SHAREPOINT_DMS_SERVICE, [
                    'url' => $webUrl,
                    'filename' => $file->getFilename()
                ]);
            } catch (Throwable $exception) {
                ServiceLog::error('File Upload failed to MicrosoftSharepoint: ' . $exception->getMessage());

                $keyAddition = '';

                TaskAuditLogHelper::errorSendingToDms(
                    $serviceTask,
                    Service::MICROSOFT_SHAREPOINT_DMS_SERVICE,
                    $keyAddition
                );

                if ($exception->getCode() === Response::HTTP_REQUEST_ENTITY_TOO_LARGE) {
                    $errorMessages[] = $this->accountService->account->trans(
                        'service_task.notifications.dms.sharepoint.file_too_big',
                        ['file' => $file->getFilename()]
                    );
                } else {
                    $errorMessages[] = $exception->getMessage();
                }
            }
        }
        if (!empty($errorMessages)) {
            $notificationKey = 'dms.sharepoint.failed_to_send_document';
            $errorMessage = implode('<br>', $errorMessages);
            $this->sendServiceTaskErrorNotification($notificationKey, $serviceTask, $errorMessage);
        }
    }

    /**
     * @throws SiteDoesNotExistsException
     * @throws Throwable
     * @throws ItemIsNotFolderException
     * @throws DriveDoesNotExistsException
     */
    public function saveConfiguration(
        ServiceTask $serviceTask,
        ?User $user,
        DirectoryConfiguration $configuration = null
    ): void {
        if (!$serviceTask->isOpen()) {
            throw new PreconditionFailedException('Task should be in status open');
        }

        $path = '';
        if ($configuration !== null) {
            try {
                $accessToken = $this->getAccessToken($serviceTask->account, $user);

                $site = $this->dmsRepository->getSite(
                    $accessToken,
                    $configuration->siteId()
                );

                if ($site === null) {
                    throw new SiteDoesNotExistsException();
                }

                $path .= $site->name() . '/';

                $drive = $this->dmsRepository->getDrive(
                    $accessToken,
                    $configuration->driveId()
                );

                if ($drive === null) {
                    throw new DriveDoesNotExistsException();
                }

                $path .= $drive->name();

                if (!$configuration->isDrive()) {
                    $item = $this->dmsRepository->getItem(
                        $accessToken,
                        $configuration->driveId(),
                        $configuration->itemId()
                    );

                    if (!$item->isFolder()) {
                        throw new ItemIsNotFolderException();
                    }

                    if ($item->hasParentPath()) {
                        $path .= $item->parentPath() . '/';
                    }

                    $path .= $item->name();
                }

                // always save the task path to the task
                $serviceTask->appendProperties([
                    $serviceTask::PROPERTY_DMS_CONFIGURATIONS => $configuration->toArray(),
                ]);
                $serviceTask->save();
            } catch (Throwable $e) {
                QuarantineHelper::quarantineFile(Service::MICROSOFT_SHAREPOINT_DMS_SERVICE . '-' . date('Y-m-d_His') . '.txt', $e->getMessage()); // phpcs:ignore
                throw $e;
            }
        }
        if (in_array($serviceTask->type, ServiceTask::TYPES_WITHOUT_IDENTIFIER)) {
            $this->serviceTaskCompanyDmsPreferenceService->saveDmsConfiguration($serviceTask, $configuration);
        } else {
            $this->companyFiscalIdentifierNumbersSyncService->saveDmsConfiguration(
                $serviceTask,
                $configuration
            );
        }

        TaskAuditLogHelper::preferencesUpdated($serviceTask, trans('service_task.preferences.microsoft_sharepoint_path', [], $serviceTask->account->language) . ': ' . $path); // phpcs:ignore
    }

    public function getConfigurationsPathForServiceTask(ServiceTask $serviceTask): array
    {
        $configuration = $this->getDmsConfigurationForTask($serviceTask);
        if ($configuration === null) {
            return [];
        }

        $accessToken = $this->getAccessToken($serviceTask->account);

        $path = [];
        try {
            if ($configuration->itemId() !== null) {
                $folder = $this->dmsRepository->getItem(
                    $accessToken,
                    $configuration->driveId(),
                    $configuration->itemId()
                );

                $path = [$folder];

                while (!$folder->isRoot()) {
                    $folder = $this->dmsRepository->getItem(
                        $accessToken,
                        $configuration->driveId(),
                        $folder->parentId()
                    );

                    array_unshift($path, $folder);
                }

                // Last folder is always root that is the same as the drive
                // with a name 'root' that we don't want.
                array_shift($path);
            }

            if ($configuration->driveId() !== null) {
                $drive = $this->dmsRepository->getDrive($accessToken, $configuration->driveId());
                array_unshift($path, $drive);
            }

            $site = $this->dmsRepository->getSite($accessToken, $configuration->siteId());

            array_unshift($path, $site);
        } catch (Throwable $e) {
            ServiceLog::error(
                'Exception: ' . get_class($e) . ' trying to get item from sharepoint: ' . $e->getMessage()
            );
        }
        return $path;
    }

    public function canSendServiceTask(ServiceTask $task): bool
    {
        return $this->getDmsConfigurationForTask($task) !== null;
    }

    private function getConfigurationPath(ServiceTask $serviceTask)
    {
        // Get only the names for the path array and glue them with a /
        $path = array_map(function ($item) {
            return $item->name();
        }, $this->getConfigurationsPathForServiceTask($serviceTask));
        return implode('/', $path);
    }

    public function saveDmsConfigurationToTask(ServiceTask $task): ServiceTask
    {
        $configuration = $this->getDmsConfigurationForTask($task);
        $path = $this->getConfigurationPath($task);

        $task->appendProperties([
            ServiceTask::PROPERTY_DMS_CONFIGURATIONS => $configuration?->toArray(),
            'microsoft_sharepoint_sent_path' => $path
        ]);

        $task->save();
        return $task;
    }

    public function shouldBeSent(CanSendToDmsInterface $model): bool
    {
        return !is_null($this->getSharepointPath($model));
    }

    /**
     * @param CanSendToDmsInterface $model
     * @return array|null
     */
    private function getSharepointPath(CanSendToDmsInterface $model): ?DirectoryConfiguration
    {
        $preferences = $model->getDmsPreferences($this->accountService);
        if (
            !is_null($preferences)
        ) {
            $sharepointPath = $preferences->getDmsPreferences()->getSharepointPath();
            if (empty($sharepointPath)) {
                $relationType = $model->getDmsType();
                return $this->getSharepointPathFromAccount($this->accountService, $relationType);
            }

            return new DirectoryConfiguration(
                $sharepointPath[0]['id'],
                $sharepointPath[1]['id'],
                count($sharepointPath) > 2 ? end($sharepointPath)['id'] : null,
            );
        }
        return null;
    }

    public function sendDocuments(
        CanSendToDmsInterface $model,
        array $files,
        ?User $user
    ): void {
        $accessToken = $this->getAccessToken($this->accountService->account);
        $dmsConfiguration = $this->getSharepointPath($model);
        if (is_null($dmsConfiguration)) {
            DmsLog::error('File upload to DMS failed for AccountService#' . $this->accountService->id . ' Sharepoint path not set'); //phpcs:ignore
            throw new PreconditionFailedException('Sharepoint path not set');
        }

        $clientId = $model->getClientId();
        $encryptedStorageRepository = App::make(EncryptedStorageRepository::class);

        foreach ($files as $filePath) {
            try {
                $fileName = basename($filePath);

                // If client id is set for company we show it at the start of the name of the file.
                if (!empty($clientId)) {
                    $fileName = $clientId . ' - ' . $fileName;
                }

                $fileContent = $encryptedStorageRepository->getFile($filePath);
                // Upload the file and get the URL to access it.
                $webUrl = $this->dmsRepository->uploadFile(
                    $accessToken,
                    $dmsConfiguration,
                    $fileName,
                    $fileContent
                );
                DmsLog::info(
                    $fileName . ' uploaded successfully to dms for AccountService#' . $this->accountService->id,
                    [
                        'url' => $webUrl,
                    ]
                );
                $model->sendDmsSuccessMessage(
                    $model->getAccount()->trans(
                        'dms.sharepoint.send_documents.success',
                    ),
                    [
                        'link_title' => $fileName,
                        'link_url' => $webUrl
                    ],
                    $user,
                    $filePath
                );
            } catch (Throwable $exception) {
                DmsLog::info(
                    'file upload to dms failed for AccountService#' . $this->accountService->id . 'Exception: ' . get_class($exception) . ' ' . $exception->getMessage() //phpcs:ignore
                );

                $modelKey = snake_case(class_basename($model));
                $replaces = [
                    'source' => $model->getAccount()->trans('dms.source.' . $modelKey),
                    'title' => $model->getDmsDisplayName(),
                    'error_message' => $exception->getMessage(),
                ];

                if ($exception->getCode() === Response::HTTP_REQUEST_ENTITY_TOO_LARGE) {
                    // If the request was too big, meaning sharepoint cannot handle our filesize.
                    // We will send a notification with a link to the file to manually handle it themselves.
                    $downloadUrl = $model->getAccount()->route('new.dms.download', [
                        'model' => $modelKey,
                        'id' => $model->id
                    ]);
                    // For notification
                    $replaces['download_url'] = $downloadUrl;
                    // For Auditlogs
                    $replaces['link_title'] = $model->getAccount()->trans('common.click_here');
                    $replaces['link_url'] = $downloadUrl;

                    $auditLogKey = 'dms.sharepoint.send_documents.error_too_large';
                    $notificationKey = 'dms.sharepoint.send_documents.too_large_error_notification';
                } else {
                    $replaces['retry_url'] = $model->getAccount()->route('new.dms.resend_documents', [
                        'model' => $modelKey,
                        'id' => $model->id
                    ]);
                    $auditLogKey = 'dms.sharepoint.send_documents.error';
                    $notificationKey = 'dms.sharepoint.send_documents.common_error_notification';
                }

                $model->sendDmsErrorMessage($model->getAccount()->trans(
                    $auditLogKey,
                    $replaces
                ), $replaces, $user, $filePath);

                $notificationMessageKey = $notificationKey . '.message';
                if ($model->allowResend()) {
                    $notificationMessageKey .= '_resend';
                }

                NotificationHelper::translateAndSendToUsers(
                    $model->getDmsRecipients(),
                    $notificationKey . '.title',
                    $replaces,
                    $notificationMessageKey,
                    $replaces,
                    true,
                    $model->getAccount()->language
                );
            }
        }
    }

    public function isConfigured(): bool
    {
        return $this->accountService->hasProperty('client_id') &&
            $this->accountService->hasProperty('client_secret') &&
            $this->accountService->hasProperty('tenant_id');
    }

    public function getConfiguration(): ?Configuration
    {
        if ($this->isConfigured()) {
            return new Configuration(
                $this->accountService->getProperty('client_id'),
                $this->accountService->getProperty('client_secret'),
                $this->accountService->getProperty('tenant_id'),
                $this->accountServiceService->getDmsSourceTypes($this->accountService->account)
            );
        }
        return null;
    }

    public function settingsMatchService(array $settings): bool
    {
        return isset($settings['site_id'], $settings['drive_id']);
    }

    public function saveSettingsToTask(ServiceTask $serviceTask, array $settings): void
    {
        $configuration = new DirectoryConfiguration(
            $settings['site_id'],
            $settings['drive_id'],
            $settings['item_id'] ?? null
        );
        $this->saveConfiguration($serviceTask, auth()->user(), $configuration);
    }
}
