<?php

namespace App\Services\Service\Signing;

use App\Account;
use App\AccountService;
use App\BroadcastEvents\RealTimePdfSigningEvent;
use App\BroadcastEvents\RealTimeXbrlSigningEvent;
use App\Exceptions\NotFoundException;
use App\Exceptions\PreconditionFailedException;
use App\Exceptions\Signing\QualifiedSigning\NoCertificateFoundException;
use App\Exceptions\Users\UserNotFoundException;
use App\Factories\TaskFileFactory;
use App\Helpers\TaskAuditLogHelper;
use App\Logging\Channels\ServiceLog;
use App\Models\CustomRules\CustomRule;
use App\Models\TaskFile;
use App\Models\TaskFile\Placeholder;
use App\Repositories\Http\UbiquApiRepository;
use App\Events\QualifiedSigningUserAddEvent;
use App\Events\QualifiedSigningUserDeleteEvent;
use App\Events\Service\AccountService\PreferencesUpdatedEvent;
use App\Repositories\Services\AccountServiceRepository;
use App\Repositories\Services\Signing\QualifiedSigningRepository;
use App\Repositories\ServiceTaskSigningRequestRepository;
use App\Repositories\TaskFilePlaceholderRepository;
use App\Repositories\TaskFileRepository;
use App\Repositories\TokenRepository;
use App\Repositories\UserRepository;
use App\Service;
use App\Services\CustomRule\CustomRuleService;
use App\Services\Generator\Xml\SignatureXmlGenerator;
use App\Services\Pdf\PdfFooterTraceService;
use App\Services\Pdf\PdfSigningService;
use App\Services\Service\Signing\Kpn\KpnMobileCertService;
use App\Services\TaskFileService;
use App\Services\ServiceTask\TaskSendService;
use App\ServiceTaskSigningRequest;
use App\Token;
use App\User;
use App\ValueObject\Pagination\SearchablePaginationFilters;
use App\ValueObject\Services\Signing\Kpn\CallbackResponse;
use App\ValueObject\Services\Signing\Kpn\Certificate;
use Illuminate\Support\Collection;
use SetaPDF_Core_SecHandler_Exception;
use SetaPDF_Signer_Exception;
use SetaPDF_Signer_Exception_ContentLength;
use Throwable;

class QualifiedSigningService
{
    private const UBIQU_TOKEN = 'ubiqu_token';

    private const FILE_TYPE_PDF = 'pdf';
    private const FILE_TYPE_XBRL = 'xbrl';

    public function __construct(
        private readonly QualifiedSigningRepository $qualifiedSigningRepository,
        private readonly KpnMobileCertService $kpnMobileCertService,
        private readonly TaskFileRepository $taskFileRepository,
        private readonly UbiquApiRepository $ubiquApiRepository,
        private readonly TokenRepository $tokenRepository,
        private readonly UserRepository $userRepository,
        private readonly CustomRuleService $customRuleService
    ) {
    }

    /**
     * Get all non-qualified signing users
     *
     * @param AccountService $accountService
     * @param SearchablePaginationFilters $filters
     * @return Collection
     */
    public function getNonQualifiedSigningUsers(
        AccountService $accountService,
        SearchablePaginationFilters $filters
    ): Collection {
        return $this->qualifiedSigningRepository->getNonQualifiedSigningUsers($accountService, $filters);
    }

    /**
     * Get all qualified signing users
     *
     * @param AccountService $accountService
     * @return Collection
     */
    public function getQualifiedSigningUsers(AccountService $accountService): Collection
    {
        return $this->qualifiedSigningRepository->getQualifiedSigningUsers($accountService);
    }

    /**
     * Add qualified signing users with an array of users_ids
     *
     * @param AccountService $accountService
     * @param User $currentUser
     * @param array $usersIds
     * @return bool
     * @throws UserNotFoundException
     */
    public function addQualifiedSigningUsers(
        AccountService $accountService,
        User $currentUser,
        array $usersIds
    ): bool {
        $existingQualifiedUserIds = $this->qualifiedSigningRepository
            ->getQualifiedSigningUsers($accountService)
            ->pluck('user_id')
            ->toArray();

        $newUserIds = array_diff($usersIds, $existingQualifiedUserIds);

        $userIdsToRemove = array_diff($existingQualifiedUserIds, $usersIds);

        $removedUsers = new Collection();
        if (!empty($userIdsToRemove)) {
            $removedUsers = $this->qualifiedSigningRepository->removeQualifiedSigningUsersByUserIds(
                $accountService,
                $userIdsToRemove
            );
        }

        $addedUsers = new Collection();
        foreach ($newUserIds as $userId) {
            $user = $this->userRepository->getById($userId);
            if ($user->isExternal()) {
                throw new PreconditionFailedException('Supplied User #' . $userId . ' is a external user');
            }
            $addedUsers->push($this->qualifiedSigningRepository->addQualifiedSigningUser(
                $accountService,
                $currentUser,
                $userId
            ));
        }

        if (!empty($addedUsers) || !empty($removedUsers)) {
            PreferencesUpdatedEvent::fire($accountService);

            foreach ($addedUsers as $addedUser) {
                QualifiedSigningUserAddEvent::fire(User::find($addedUser->user_id));
            }

            foreach ($removedUsers as $removedUser) {
                QualifiedSigningUserDeleteEvent::fire(User::find($removedUser->user_id));
            }
        }

        return true;
    }

    /**
     * Remove qualified signing user
     *
     * @param AccountService $accountService
     * @param int $qualifiedSigningUserId
     * @return bool
     */
    public function removeQualifiedSigningUser(
        AccountService $accountService,
        int $qualifiedSigningUserId
    ): bool {
        $qualifiedSigningUser = $this->qualifiedSigningRepository->getQualifiedSigningUser(
            $accountService,
            $qualifiedSigningUserId
        );
        $deleteUser = $this->qualifiedSigningRepository->removeQualifiedSigningUser(
            $accountService,
            $qualifiedSigningUserId
        );
        if ($deleteUser) {
            PreferencesUpdatedEvent::fire($accountService);
            QualifiedSigningUserDeleteEvent::fire(User::find($qualifiedSigningUser->user_id));
        }

        return $deleteUser;
    }

    /**
     * Generate temporary signature file in memory containing checksums of data to sign so that a digest van be
     * generated and sent to Ubiqu. A token is saved in the database while waiting for the callback.
     * @param User $user
     * @param int $serviceTaskSigningRequestId
     * @return Token
     */
    public function signXbrl(User $user, int $serviceTaskSigningRequestId): Token
    {
        $cert = $this->kpnMobileCertService->getSigningCertificate($user);
        if (is_null($cert)) {
            throw new NoCertificateFoundException('User #' . $user->id . 'does not have a valid certificate in KPN');
        }

        $serviceTaskSigningRequest = ServiceTaskSigningRequest::findOrFail($serviceTaskSigningRequestId);

        $task = $serviceTaskSigningRequest->serviceTask;
        $publicationDocument = $this->taskFileRepository->getFirstForTask($task, TaskFile::TYPE_PUBLICATION_DOCUMENT);
        $auditReport = $this->taskFileRepository->getFirstForTask($task, TaskFile::TYPE_AUDIT_REPORT);

        $signingTime = SignatureXmlGenerator::signingTime();
        $generator = new SignatureXmlGenerator('', $cert, $publicationDocument, $auditReport, $signingTime);
        $generator->generateFile();
        $signedInfoDigest = $generator->getSignedInfoDigestHex();
        $response = $this->ubiquApiRepository->signDigest($cert->token, $signedInfoDigest, $user->language);

        $tokenAttributes = [
            Token::TOKEN => $response->getToken(),
            Token::TYPE => self::UBIQU_TOKEN,
            Token::ACCOUNT_ID => $user->account_id,
            Token::USER_ID => $user->id,
            Token::EXPIRATION_DATE => $response->getExpiration(),
            Token::DATA => [
                'signing_file_type' => self::FILE_TYPE_XBRL,
                'service_task_signing_request_id' => $serviceTaskSigningRequest->id,
                'publication_document_id' => $publicationDocument->id,
                'audit_report_id' => $auditReport->id,
                'signing_time' => $signingTime
            ]
        ];

        return $this->tokenRepository->create($tokenAttributes);
    }

    /**
     * Prepares PDF for signing and send a sign request to Ubiqu.
     * A token is saved in the database with a serialized temporary document which also contains a reference to a file
     * on the file system, to prevent this file from being deleted automatically we copy it to another file with the
     * addition of the extension ".pdf".
     * @param User $user
     * @param Placeholder $placeholder
     * @param string $ip IPv4 or Ipv6 address of user doing the signing, formatted as string.
     * @return Token
     */
    public function signPdf(User $user, Placeholder $placeholder, string $ip): Token
    {
        $cert = $this->kpnMobileCertService->getSigningCertificate($user);
        if (is_null($cert)) {
            throw new NoCertificateFoundException('User #' . $user->id . 'does not have a valid certificate in KPN');
        }

        $taskFile = $placeholder->taskFile;

        $service = new PdfSigningService();
        $service->setContent($taskFile->getContent());
        $service->setPlaceholder($placeholder, false);

        $tempDoc = $service->prepareKpnSignature($cert);

        $footerService = new PdfFooterTraceService();
        $footerService->signingService = $service;
        $footerCleared = $footerService->clearExisting();

        $checksum = hash_file('sha256', $tempDoc->getHashFile()->getPath());
        $tempPdfPath = $tempDoc->getWriter()->getPath();
        copy($tempPdfPath, $tempPdfPath . '.pdf');

        $response = $this->ubiquApiRepository->signPdf($cert, $checksum, $user->language);
        $tokenAttributes = [
            Token::TOKEN => $response->getToken(),
            Token::TYPE => self::UBIQU_TOKEN,
            Token::ACCOUNT_ID => $user->account_id,
            Token::USER_ID => $user->id,
            Token::EXPIRATION_DATE => $response->getExpiration(),
            Token::DATA => [
                'signing_file_type' => self::FILE_TYPE_PDF,
                'pdf_document_id' => $taskFile->id,
                'temp_doc' => base64_encode(serialize($tempDoc)),
                'field_name' => $service->getFieldName(),
                'placeholder_id' => $placeholder->id,
                'prefix' => pathinfo($tempPdfPath, PATHINFO_BASENAME),
                'request_ip' => $ip,
                'footer_cleared' => $footerCleared
            ]
        ];
        return $this->tokenRepository->create($tokenAttributes);
    }

    /**
     * Processes the callback from Ubiqu to apply signature value for XBL or PDF.
     * Looks up a token in the database and selects the right signing flow.
     * Sends a websocket message to the signing user or throws an exception
     * @param CallbackResponse $response
     * @return void
     * @throws SetaPDF_Core_SecHandler_Exception
     * @throws SetaPDF_Signer_Exception
     * @throws SetaPDF_Signer_Exception_ContentLength
     * @throws Throwable
     * @throws UserNotFoundException
     */
    public function processCallback(CallbackResponse $response): void
    {
        // token from the callback response
        $transactionToken = $response->getTransactionToken();
        // token saved in database matching token from callback response
        $token = $this->tokenRepository->getByToken($transactionToken);

        if (empty($token)) {
            throw new NotFoundException('Token not found for KPN transaction token ' . $transactionToken);
        }

        $user = resolve(UserRepository::class)->getById($token->user->id);
        $cert = $this->kpnMobileCertService->getSigningCertificate($user);
        $tokenData = $token->data;
        $fileType = $tokenData['signing_file_type'];

        if ($fileType === self::FILE_TYPE_PDF) {
            try {
                $placeholder = $this->applyPdfSignature($response, $token);
                event(new RealTimePdfSigningEvent($placeholder)); // Notifies front-end of this signed placeholder.
            } catch (\Throwable $e) {
                $placeholder = Placeholder::findOrFail($tokenData['placeholder_id']);
                event(new RealTimePdfSigningEvent($placeholder, false));
                throw $e;
            }
        } elseif ($fileType === self::FILE_TYPE_XBRL) {
            try {
                $serviceTaskSigningRequest = $this->applyYearworkSignature($response, $token, $cert, $user);
                event(new RealTimeXbrlSigningEvent($serviceTaskSigningRequest));
            } catch (\Throwable $e) {
                $serviceTaskSigningRequest = resolve(ServiceTaskSigningRequestRepository::class)
                    ->getById($tokenData['service_task_signing_request_id']);
                event(new RealTimeXbrlSigningEvent($serviceTaskSigningRequest, false));
                throw $e;
            }
        }
    }

    /**
     * Apply a signature to PDF.
     * This method does the dirty work of unserializing the data and copying and deleting temporary files.
     * Calls the PdfSigningService to handle the actual signing.
     * @param CallbackResponse $response Response from Ubiqu containing signature
     * @param Token $token Token of type "ubiqu_token" with references to placeholder and temporary PDF
     * @return Placeholder
     * @throws \SetaPDF_Core_SecHandler_Exception
     * @throws \SetaPDF_Signer_Exception
     * @throws \SetaPDF_Signer_Exception_ContentLength
     */
    private function applyPdfSignature(CallbackResponse $response, Token $token): Placeholder
    {
        $tokenData = $token->data;
        $placeholder = Placeholder::findOrFail($tokenData['placeholder_id']);
        $pdfFile = $placeholder->taskFile;
        $taskFile = $placeholder->taskFile;

        $signingService = resolve(PdfSigningService::class);
        $signingService->setContent($pdfFile->getContent());
        $signingService->setPlaceholder($placeholder);
        $signature = $response->getDecodedSignature();
        $dir = storage_path('setapdf/');
        $tempPdf = $dir . $tokenData['prefix'] . '.pdf';
        copy($tempPdf, $dir . $tokenData['prefix']); // copy PDF to file without extension for use by SetaSign
        $tempDoc = unserialize(base64_decode($tokenData['temp_doc']));
        $signingService->applyKpnSignature($tokenData['field_name'], $signature, $tempDoc);
/*
        $footerCleared = $tokenData['footer_cleared'];
        $shouldAddSigningFooter = $this->customRuleService->shouldAddFooterTrace($token->account);
        if (is_bool($footerCleared) && $shouldAddSigningFooter) {
            $footerService = new PdfFooterTraceService();
            $footerService->signingService = $signingService;
            $footerService->text = PdfFooterTraceService::generateText($placeholder);
            $footerService->addFooterTrace(!$footerCleared);
        }
*/
        $pdfFile->setContent($signingService->getContent());
        $pdfFile->save();

        // Set the current placeholder to applied before checking if everything is done.
        $placeholder->saveStatusApplied();
        TaskAuditLogHelper::fileSignaturesApplied($pdfFile, 1);

        $placeholderRepo = resolve(TaskFilePlaceholderRepository::class);
        $openPlaceholderCount = $placeholderRepo->getOpenPlaceholdersCountForUserPerTask(
            $token->user->id,
            $pdfFile->service_task_id
        );

        if ($openPlaceholderCount === 0) {
            resolve(TaskSendService::class)->handleNextTaskState($taskFile->task);
        }

        $token->delete();

        try {
            $deleted = unlink($tempPdf); // delete temporary PDF file
            if (!$deleted) {
                throw new \RuntimeException('unlink() returned false');
            }
        } catch (\Throwable $e) {
            // Signing can continue, but we log a warning when the temporary file is not deleted
            ServiceLog::warning('Failed to delete file ' . $tempPdf . 'after signing. ' . get_class($e) . ': ' . $e->getMessage()); //phpcs:ignore
        }
        return $placeholder;
    }

    /**
     * Generate Signature XML file according to XADES specification. Signature XML is stored as file in database.
     * @param CallbackResponse $response Response from Ubiqu containing signature
     * @param Token $token Token containing references to file IDs and signing time,
     * @param Certificate $cert User's professional signing certificate from KPN
     * @param User $user User doing the signing, should have access to the files being signed
     * @return ServiceTaskSigningRequest
     * @throws \Exception
     */
    private function applyYearworkSignature(
        CallbackResponse $response,
        Token $token,
        Certificate $cert,
        User $user
    ): ServiceTaskSigningRequest {
        $tokenData = $token->data;
        $taskFileService = resolve(TaskFileService::class);
        $publicationDocument = $taskFileService->getById($tokenData['publication_document_id'], $user);
        $auditReport = $taskFileService->getById($tokenData['audit_report_id'], $user);
        $task = $publicationDocument->task;

        $generator = new SignatureXmlGenerator(
            $response->getSignature(),
            $cert,
            $publicationDocument,
            $auditReport,
            $tokenData['signing_time']
        );

        $signatureFile = $generator->generateFile();

        // Create and save task file signature
        $signatureFile = TaskFileFactory::create(
            $task->accountService,
            $signatureFile,
            'sl-signature-task-' . $task->id . '.xml',
            TaskFile::TYPE_SIGNATURE_XML
        );
        $taskFileService->storeFiles([$signatureFile], $task);

        $serviceTaskSigningRequest = resolve(ServiceTaskSigningRequestRepository::class)
            ->getById(
                $tokenData['service_task_signing_request_id']
            );

        // Update request with status signed
        $accountService = resolve(AccountServiceRepository::class)->firstEnabledByServiceName(
            $task->account,
            Service::SECURELOGIN_QUALIFIED_SIGNING_SERVICE
        );

        resolve(ServiceTaskSigningRequestRepository::class)
            ->setSigned($serviceTaskSigningRequest, $accountService);
        $task->refresh();
        // After successfully sign a document we continue the task to the next step.
        resolve(TaskSendService::class)->handleNextTaskState($task);
        $task->refresh();
        $serviceTaskSigningRequest->refresh();

        TaskAuditLogHelper::signed($serviceTaskSigningRequest);
        $token->delete();
        return $serviceTaskSigningRequest;
    }
}
