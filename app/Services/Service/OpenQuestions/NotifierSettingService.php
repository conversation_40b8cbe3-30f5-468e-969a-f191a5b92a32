<?php

namespace App\Services\Service\OpenQuestions;

use App;
use App\Helpers\DatetimeHelper;
use App\Models\OpenQuestions\Notifier\NotifierSettingCompany;
use App\Support\Carbon;

class NotifierSettingService
{
    public function getUpcomingSendReminderAt(string $frequency, string $day, int $hour, ?Carbon $last_sent): Carbon
    {
        $startDate = $last_sent;
        // If the last sent date is set we update the time from which we have to check the upcoming day/hour.
        if (!empty($startDate)) {
            switch ($frequency) {
                case NotifierSettingCompany::FREQUENCY_WEEKLY:
                    $startDate->endOfWeek();
                    break;
                case NotifierSettingCompany::FREQUENCY_BIWEEKLY:
                    $startDate->addWeek()->endOfWeek();
                    break;
                case NotifierSettingCompany::FREQUENCY_MONTHLY:
                    $startDate->endOfMonth();
                    break;
            }
        }

        if (
            empty($startDate)
            && $frequency == NotifierSettingCompany::FREQUENCY_MONTHLY
        ) {
            $firstDayThisMonth = Carbon::now()->subMonth()->endOfMonth()->next($day)->setHour($hour)->startOfHour();
            if (DatetimeHelper::getUpcomingDayHour($day, $hour) > $firstDayThisMonth) {
                $startDate = Carbon::now()->startOfHour()->endOfMonth();
            }
        }

        $newDate = DatetimeHelper::getUpcomingDayHour($day, $hour, $startDate);

        // If the new date is in the past, we create a new date which is in the future.
        if ($newDate < Carbon::now()->startOfHour()) {
            $newDate = DatetimeHelper::getUpcomingDayHour($day, $hour);
        }
        return $newDate;
    }
}
