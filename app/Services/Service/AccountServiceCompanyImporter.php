<?php

namespace App\Services\Service;

use App\AccountService;
use App\AccountServiceCompany;
use App\Company;
use App\DataTransferObject\ExternalCompany;
use App\Exceptions\Services\FailedToImportAccountServiceCompaniesException;
use App\Repositories\Services\AccountServiceCompanyRepository;
use App\Services\CompanyIdentifierSyncService;
use App\Services\CompanyService;

class AccountServiceCompanyImporter
{
    private const ID = 'id';
    private const NAME = 'name';

    public function __construct(
        private readonly CompanyIdentifierSyncService $companyFiscalIdentifierNumbersSyncService,
        private readonly AccountServiceCompanyRepository $accountServiceCompanyRepository,
        private readonly CompanyService $companyService
    ) {
    }

    /**
     * Tries to import companies and dispatches a job to add VAT and Fiscal Numbers to them.
     * @param  AccountService  $accountService
     * @param  array  $companies
     * @return  array
     * @throws  \Exception
     */
    public function import(AccountService $accountService, array $companies)
    {
        try {
            $failedImports = [];
            $accountServiceCompanies = [];

            foreach ($companies as $externalCompany) {
                $externalId = $externalCompany['client_id'] ?? null;
                $internalCompany = new Company(
                    [
                        self::NAME => $externalCompany[self::NAME],
                        Company::INTERNAL_CLIENT_ID => $externalId
                    ]
                );
                $accountService->account->companies()->save($internalCompany);

                $accountServiceCompany = $this->accountServiceCompanyRepository->create(
                    $accountService->account_id,
                    $accountService->id,
                    $internalCompany->id,
                    $externalCompany[self::ID],
                    $externalCompany[self::NAME],
                    $externalId
                );

                if ($accountServiceCompany instanceof AccountServiceCompany) {
                    $accountServiceCompanies[] = $accountServiceCompany;
                    $this->importExternalCompanyIdentifiers($internalCompany['id'], $externalCompany);
                } else {
                    $failedImports[] = $externalCompany;
                }
            }
            if ($failedImports !== []) {
                throw (new FailedToImportAccountServiceCompaniesException())->setFailedImports($failedImports);
            }

            return $accountServiceCompanies;
        } catch (\Exception $exception) {
            throw $exception;
        }
    }

    public function checkForDuplicates(AccountService $accountService, array $companies): array
    {
        $failedImports = [];

        foreach ($companies as $externalCompany) {
            $duplicatesCount = $this->companyService->checkDuplicates(
                $accountService->account,
                $externalCompany[self::NAME]
            );
            if ($duplicatesCount > 0) {
                $externalCompany['duplicates_count'] = $duplicatesCount;
                $failedImports[] = $externalCompany;
            }
        }
        return $failedImports;
    }

    public function connect(
        AccountService $accountService,
        array $internalCompany,
        array $externalCompany
    ): ?AccountServiceCompany {
        $accountServiceCompany = null;
        $duplicateConnection = $this->accountServiceCompanyRepository->findDuplicateConnection(
            $accountService,
            $internalCompany['id'],
            $externalCompany['id']
        );
        if (!$duplicateConnection) {
            $accountServiceCompany = $this->accountServiceCompanyRepository->create(
                $accountService->account_id,
                $accountService->id,
                $internalCompany['id'],
                $externalCompany['id'],
                $externalCompany['name'],
                $externalCompany['client_id'] ?? null
            );
        }

        if ($accountServiceCompany instanceof AccountServiceCompany) {
            $this->importExternalCompanyIdentifiers($internalCompany['id'], $externalCompany);
            return $accountServiceCompany;
        }
        return null;
    }

    public function importExternalCompanyIdentifiers(int $internalCompanyId, array $externalCompany)
    {
        if (isset($externalCompany[ExternalCompany::VAT])) {
            $this->companyFiscalIdentifierNumbersSyncService->createOrSyncVatNumbers(
                $internalCompanyId,
                $externalCompany[ExternalCompany::VAT]
            );
        }
        if (isset($externalCompany[ExternalCompany::RSIN])) {
            $this->companyFiscalIdentifierNumbersSyncService->createOrSyncFiscalNumbers(
                $internalCompanyId,
                $externalCompany[ExternalCompany::RSIN]
            );
        }
        if (isset($externalCompany[ExternalCompany::BSN])) {
            $this->companyFiscalIdentifierNumbersSyncService->createOrSyncBsnNumbers(
                $internalCompanyId,
                $externalCompany[ExternalCompany::BSN],
                ignoreInvalid: true
            );
        }
    }
}
