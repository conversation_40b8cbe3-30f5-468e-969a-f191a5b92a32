<?php

namespace App\Services\Service\Sync;

use App;
use App\AccountService;
use App\Service;
use App\Services\Service\Crm\AfasCrmServiceSynchronizer;
use App\Services\Service\Crm\AfasNoabCrmServiceSynchronizer;
use App\Services\Service\Crm\MicrosoftDynamicsCrmServiceSynchronizer;
use App\Services\Service\Crm\SimplicateCrmServiceSynchronizer;

class ServiceSynchronizerFactory
{
    public function makeServiceSynchronizer(AccountService $accountService): ServiceSynchronizerInterface
    {
        switch ((string)$accountService->service->provider) {
            case Service::TWINFIELD_PROVIDER:
                return new TwinfieldTaskSynchronizer();
            case Service::EXACT_PROVIDER:
                return new ExactServiceSynchronizer();
            case Service::FISCAAL_GEMAK_PROVIDER:
                return new FiscaalGemakServiceSynchronizer();
            case Service::NMBRS_PROVIDER:
                return new NmbrsServiceSynchronizer();
            case Service::SNELSTART_SERVICE:
                return new SnelStartServiceSynchronizer();
            case Service::AFAS_CRM_SERVICE:
                return new AfasCrmServiceSynchronizer();
            case Service::AFAS_NOAB_CRM_SERVICE:
                return new AfasNoabCrmServiceSynchronizer();
            case Service::YUKI_PROVIDER:
                return new YukiServiceSynchronizer();
            case Service::SIMPLICATE_CRM_SERVICE:
                return new SimplicateCrmServiceSynchronizer();
            case Service::MICROSOFT_DYNAMICS_CRM_SERVICE:
                return new MicrosoftDynamicsCrmServiceSynchronizer();
            case Service::LOKET_TASKS_API:
                return new LoketTasksApiServiceSynchronizer();
            default:
                throw new \InvalidArgumentException(
                    'Service synchronizer not instantiable for provider : ' . (string)$accountService->service->provider
                );
        }
    }
}
