<?php

namespace App\Services\Pdf\Replacer;

class PdfJsonTitleReplacer extends AbstractPdfJsonReplacer
{
    private const MATCH_START = '/"\/Title":\s{0,}"/';
    private const MATCH_END = '/",\s/';

    protected function findStart(string $content, int $offset): ?int
    {
        preg_match(self::MATCH_START, $content, $matches, PREG_OFFSET_CAPTURE, $offset);
        if (count($matches)) {
            return (int)$matches[0][1] + strlen($matches[0][0]) - 1;
        }
        return null;
    }

    protected function findEnd(string $content, int $offset): ?int
    {
        preg_match(self::MATCH_END, $content, $matches, PREG_OFFSET_CAPTURE, $offset);
        if (count($matches)) {
            return (int)$matches[0][1] + 1;
        }
        return null;
    }

    public function removeStream(string $content): ?string
    {
        $start = $this->findStart($content, $this->lastOffset);
        if (is_null($start)) {
            return false;
        }
        $this->lastOffset = $start;
        $end = $this->findEnd($content, $start);

        if (is_null($end)) {
            throw new \UnexpectedValueException('No end match');
        }

        $stream = substr($content, $start, $end - $start);
        $key = md5($stream);

        $replacement = $this->getReplacement($key);
        $this->list[$key] = $stream;
        $this->lastOffset = $start + strlen($replacement);
        $content = substr($content, 0, $start) . $replacement . substr($content, $end);
        $this->content = $content;
        return true;
    }
}
