<?php

namespace App\Services\Generator\Xbrl\Nt14;

use App\Services\Generator\Xbrl\AbstractVatXbrlGenerator;
use App\Services\Parser\Xbrl\Nt14\Nt14VatXbrlParser;

class Nt14VatXbrlGenerator extends AbstractVatXbrlGenerator
{
    public const DECLARATION_YEAR = 2020;
    public const TAXONOMY = Nt14VatXbrlParser::TAXONOMY_NT14;
    public const SUPPLEMENTATION_TAXONOMY = Nt14VatXbrlParser::TAXONOMY_NT14_SUPPLETION;
    public const BD_DATA = Nt14VatXbrlParser::NAMESPACE_BDI;
}
