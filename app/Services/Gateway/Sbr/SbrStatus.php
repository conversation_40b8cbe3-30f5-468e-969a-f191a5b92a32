<?php

namespace App\Services\Gateway\Sbr;

use App\Support\Carbon;
use App\Exceptions\Sbr\UnknownStatusException;

class SbrStatus
{
    //unauthorized response happens when using the wrong certificate
    public const STATUS_CODE_UNAUTHORIZED = '000';

    public const STATUS_CODE_SENT = 105;
    public const STATUS_CODE_RECEIVED = 100;
    public const STATUS_CODE_ACCEPTED = 110;
    public const STATUS_CODE_TECHNICAL_ERROR = 120;
    public const STATUS_CODE_AUTHENTICATED = 200;
    public const STATUS_CODE_AUTHENTICATION_FAILED = 210;
    public const STATUS_CODE_AUTHENTICATION_ERROR = 230;
    public const STATUS_CODE_VALIDATED = 301;
    public const STATUS_CODE_XBRL_VALIDATION_FAILED = 310;
    public const STATUS_CODE_VALIDATION_FAILED = 311;
    public const STATUS_CODE_VALIDATION_ATTACHMENT_FAILED = 312;
    public const STATUS_CODE_VALIDATION_ERROR = 320;
    public const STATUS_CODE_VALIDATION_ERROR_FROM_REQUESTING_PARTY = 321;
    public const STATUS_CODE_VALIDATION_ATTACHMENT_ERROR = 322;
    public const STATUS_CODE_DELIVERED = 400;
    public const STATUS_CODE_DELIVERY_IN_PROGRESS = 405;
    public const STATUS_CODE_DELIVERY_FAILED = 410;
    public const STATUS_CODE_DELIVERY_ERROR = 420;

    public const STATUS_CODE_VIRUS_CHECK_SUCCESS = 600;
    public const STATUS_CODE_VIRUS_CHECK_FAILED = 610;
    public const STATUS_CODE_VIRUS_CHECK_ERROR = 620;

    public const STATUS_CODE_SIGNATURE_CHECK_PASSED = 2100;
    public const STATUS_CODE_SIGNATURE_CHECK_FAILED = 2110;
    public const STATUS_CODE_SIGNATURE_CHECK_ERROR = 2120;
    public const STATUS_CODE_FILING_RULE_VALIDATED = 2400;
    public const STATUS_CODE_FILING_RULE_VALIDATION_FAILED = 2401;

    public const STATUS_CODE_MDMB_CHECK_PASSED = 4000;
    public const STATUS_CODE_MDMB_CHECK_NOT_ALLOWED = 4010;
    public const STATUS_CODE_MDMB_CHECK_ERROR = 4020;
    public const STATUS_CODE_MDMB_CHECK_DISABLED = 4030;

    public const STATUS_CODE_MDMB_SUBMIT_PASSED = 4100;
    public const STATUS_CODE_MDMB_SUBMIT_FAILED = 4110;
    public const STATUS_CODE_MDMB_SUBMIT_ERROR = 4120;

    public const STATUS_IS_DONE = [
        self::STATUS_CODE_AUTHENTICATION_FAILED,
        self::STATUS_CODE_AUTHENTICATION_ERROR,
        self::STATUS_CODE_VALIDATION_FAILED,
        self::STATUS_CODE_DELIVERY_FAILED,
        self::STATUS_CODE_SIGNATURE_CHECK_FAILED,
        self::STATUS_CODE_SIGNATURE_CHECK_ERROR,
        self::STATUS_CODE_XBRL_VALIDATION_FAILED,
        self::STATUS_CODE_VALIDATION_FAILED,
        self::STATUS_CODE_VIRUS_CHECK_FAILED,
        self::STATUS_CODE_VIRUS_CHECK_ERROR,
        self::STATUS_CODE_FILING_RULE_VALIDATION_FAILED,
        self::STATUS_CODE_VALIDATION_ATTACHMENT_FAILED,
        self::STATUS_CODE_VALIDATION_ERROR,
        self::STATUS_CODE_VALIDATION_ATTACHMENT_ERROR,
        self::STATUS_CODE_DELIVERED,
        self::STATUS_CODE_DELIVERY_ERROR,
        self::STATUS_CODE_TECHNICAL_ERROR
    ];

    public const STATUS_IS_ERROR = [
        self::STATUS_CODE_UNAUTHORIZED,
        self::STATUS_CODE_AUTHENTICATION_ERROR,
        self::STATUS_CODE_VALIDATION_FAILED,
        self::STATUS_CODE_DELIVERY_ERROR,
        self::STATUS_CODE_SIGNATURE_CHECK_FAILED,
        self::STATUS_CODE_SIGNATURE_CHECK_ERROR,
        self::STATUS_CODE_VALIDATION_FAILED,
        self::STATUS_CODE_VALIDATION_ERROR,
        self::STATUS_CODE_VALIDATION_ERROR_FROM_REQUESTING_PARTY,
        self::STATUS_CODE_VIRUS_CHECK_ERROR,
        self::STATUS_CODE_VALIDATION_ATTACHMENT_ERROR,
        self::STATUS_CODE_TECHNICAL_ERROR
    ];

    public const STATUS_IN_PROGRESS = [
        self::STATUS_CODE_UNAUTHORIZED,
        self::STATUS_CODE_SENT,
        self::STATUS_CODE_RECEIVED,
        self::STATUS_CODE_ACCEPTED,
        self::STATUS_CODE_AUTHENTICATED,
        self::STATUS_CODE_VALIDATED,
        self::STATUS_CODE_DELIVERY_IN_PROGRESS,
        self::STATUS_CODE_VIRUS_CHECK_SUCCESS,
        self::STATUS_CODE_SIGNATURE_CHECK_PASSED,
        self::STATUS_CODE_FILING_RULE_VALIDATED,
        self::STATUS_CODE_MDMB_CHECK_PASSED,
        self::STATUS_CODE_MDMB_CHECK_NOT_ALLOWED,
        self::STATUS_CODE_MDMB_CHECK_ERROR,
        self::STATUS_CODE_MDMB_CHECK_DISABLED,
        self::STATUS_CODE_MDMB_SUBMIT_PASSED,
        self::STATUS_CODE_MDMB_SUBMIT_FAILED,
        self::STATUS_CODE_MDMB_SUBMIT_ERROR
    ];

    public const STATUS_IS_SUCCESS = [
        self::STATUS_CODE_DELIVERED
    ];

    protected ?array $error = null;

    /**
     * LogiusStatus constructor. Accepts multiple forms of input.
     * @param string|int $code Integer or string
     * @param string|null $timestamp Optional timestamp ISO8601 formatted.
     * @param string|null $description Optional description, SBR provides an NL (Dutch) text.
     * @param string|null $error_description Optional error_description, SBR provides an NL (Dutch) text.
     */
    public function __construct(
        public readonly string|int $code,
        public readonly ?string $timestamp = null,
        public readonly ?string $description = null,
        public readonly ?string $error_description = null
    ) {
        if (!$this->codeExists()) {
            throw new UnknownStatusException('Unknown status code: ' . $this->code);
        }
    }

    /**
     * Check if the status code is a valid, known code.
     * We check this by seeing if it is either processing or done. If the code is not in either list it is not valid.
     * @return bool TRUE if the status code is valid.
     */
    public function codeExists(): bool
    {
        return ($this->isProcessing() || $this->isDone());
    }

    public function isError(): bool
    {
        return in_array($this->code, self::STATUS_IS_ERROR);
    }

    /**
     * Done can be because a success or failure.
     * @return bool
     */
    public function isDone(): bool
    {
        return in_array($this->code, self::STATUS_IS_DONE);
    }

    public function isProcessing(): bool
    {
        return in_array($this->code, self::STATUS_IN_PROGRESS);
    }

    public function isSuccess(): bool
    {
        return in_array($this->code, self::STATUS_IS_SUCCESS);
    }

    public function getArray(): array
    {
        return [
            'code' => $this->code,
            'timestamp' => $this->timestamp,
            'description' => $this->description,
            'error' => $this->error
        ];
    }

    public function getErrorDescription(): ?string
    {
        if ($this->error !== null && isset($this->error['description'])) {
            return $this->error['description'];
        }

        return null;
    }

    public function getTimestamp(): ?Carbon
    {
        if (is_string($this->timestamp)) {
            return Carbon::parse($this->timestamp, 'UTC')->setTimezone('Europe/Amsterdam');
        }
        return null;
    }
}
