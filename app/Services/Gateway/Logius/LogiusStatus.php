<?php

namespace App\Services\Gateway\Logius;

use App\Support\Carbon;
use App\Exceptions\UnknownLogiusStatusException;

class LogiusStatus
{
    //unauthorized response happens when using the wrong certificate
    public const STATUS_CODE_UNAUTHORIZED = '000';

    public const STATUS_CODE_SENT = 105;
    public const STATUS_CODE_RECEIVED = 100;
    public const STATUS_CODE_ACCEPTED = 110;
    public const STATUS_CODE_AUTHENTICATED = 200;
    public const STATUS_CODE_AUTHENTICATION_FAILED = 210;
    public const STATUS_CODE_AUTHENTICATION_ERROR = 230;
    public const STATUS_CODE_VALIDATED = 301;
    public const STATUS_CODE_VALIDATION_FAILED = 311;
    public const STATUS_CODE_VALIDATION_ERROR = 321;
    public const STATUS_CODE_DELIVERY_IN_PROGRESS = 405;
    public const STATUS_CODE_DELIVERED = 400;
    public const STATUS_CODE_DELIVERY_FAILED = 410;
    public const STATUS_CODE_DELIVERY_ERROR = 420;
    public const STATUS_CODE_MAX_ATTEMPTS_EXCEEDED = 425;
    public const STATUS_CODE_DELIVERED_AND_VALIDATED = 500;
    public const STATUS_CODE_DELIVERED_AND_VALIDATION_FAILED = 510;
    public const STATUS_CODE_BLACKLIST_FAILED = 910;
    public const STATUS_CODE_BLACKLIST_SUCCEEDED = 900;
    public const STATUS_CODE_AUTHORIZATION_SUCCESS = 1200;
    public const STATUS_CODE_AUTHORIZATION_FAILED = 1210;
    public const STATUS_CODE_MUTATION_SUCCESS = 1300;
    public const STATUS_CODE_MUTATION_FAILED = 1310;
    public const STATUS_CODE_ADDING_AUTHORIZATION_SUCCESS = 1400;
    public const STATUS_CODE_ADDING_AUTHORIZATION_FAILED = 1410;
    public const STATUS_CODE_REVOKE_AUTHORIZATION_SUCCESS = 1450;
    public const STATUS_CODE_REVOKE_AUTHORIZATION_FAILED = 1460;
    public const STATUS_CODE_ADDING_SUBSCRIPTION_SUCCESS = 1500;
    public const STATUS_CODE_ADDING_SUBSCRIPTION_FAILED = 1510;
    public const STATUS_CODE_REMOVING_SUBSCRIPTION_SUCCESS = 1550;
    public const STATUS_CODE_REMOVING_SUBSCRIPTION_FAILED = 1560;
    public const STATUS_CODE_ACTIVATION_SUCCESS = 1600;
    public const STATUS_CODE_ACTIVATION_FAILED = 1610;
    public const STATUS_CODE_VALIDATION_RULES_SUCCEEDED = 2400;
    public const STATUS_CODE_ATTACHMENTS_CONSISTENT = 2000;
    public const STATUS_CODE_ATTACHMENTS_NOT_CONSISTENT = 2010;
    public const STATUS_CODE_ATTACHMENTS_ERROR = 2020;
    public const STATUS_CODE_SIGNATURE_CHECK_PASSED = 2100;
    public const STATUS_CODE_SIGNATURE_CHECK_FAILED = 2110;
    public const STATUS_CODE_SIGNATURE_CHECK_ERROR = 2120;
    public const STATUS_CODE_CERTIFICATE_CHECK_PASSED = 2200;
    public const STATUS_CODE_CERTIFICATE_CHECK_FAILED = 2210;
    public const STATUS_CODE_CERTIFICATE_CHECK_ERROR = 2220;
    public const STATUS_CODE_ARCHITECTURE_VALIDATED = 2300;
    public const STATUS_CODE_ARCHITECTURE_VALIDATION_FAILED = 2310;
    public const STATUS_CODE_ARCHITECTURE_VALIDATION_ERROR = 2320;
    public const STATUS_CODE_FILING_RULE_VALIDATED = 2400;
    public const STATUS_CODE_FILING_RULE_VALIDATION_FAILED = 2410;
    public const STATUS_CODE_FILING_RULE_VALIDATION_ERROR = 2420;
    public const STATUS_CODE_TAXONOMY_VALIDATED = 2500;
    public const STATUS_CODE_TAXONOMY_VALIDATION_FAILED = 2510;
    public const STATUS_CODE_TAXONOMY_VALIDATION_ERROR = 2520;

    public const STATUS_IS_DONE = [
        self::STATUS_CODE_AUTHENTICATION_FAILED,
        self::STATUS_CODE_AUTHENTICATION_ERROR,
        self::STATUS_CODE_VALIDATION_FAILED,
        self::STATUS_CODE_VALIDATION_ERROR,
        self::STATUS_CODE_DELIVERY_FAILED,
        self::STATUS_CODE_DELIVERY_ERROR,
        self::STATUS_CODE_MAX_ATTEMPTS_EXCEEDED,
        self::STATUS_CODE_DELIVERED_AND_VALIDATED,
        self::STATUS_CODE_DELIVERED_AND_VALIDATION_FAILED,
        self::STATUS_CODE_BLACKLIST_FAILED,
        self::STATUS_CODE_ATTACHMENTS_NOT_CONSISTENT,
        self::STATUS_CODE_ATTACHMENTS_ERROR,
        self::STATUS_CODE_SIGNATURE_CHECK_FAILED,
        self::STATUS_CODE_SIGNATURE_CHECK_ERROR,
        self::STATUS_CODE_CERTIFICATE_CHECK_FAILED,
        self::STATUS_CODE_CERTIFICATE_CHECK_ERROR,
        self::STATUS_CODE_ARCHITECTURE_VALIDATION_FAILED,
        self::STATUS_CODE_ARCHITECTURE_VALIDATION_ERROR,
        self::STATUS_CODE_FILING_RULE_VALIDATION_FAILED,
        self::STATUS_CODE_FILING_RULE_VALIDATION_ERROR,
        self::STATUS_CODE_TAXONOMY_VALIDATION_FAILED,
        self::STATUS_CODE_TAXONOMY_VALIDATION_ERROR,
        self::STATUS_CODE_AUTHORIZATION_FAILED,
        self::STATUS_CODE_VALIDATION_FAILED,
        self::STATUS_CODE_VALIDATION_ERROR,
        self::STATUS_CODE_MUTATION_FAILED,
        self::STATUS_CODE_ADDING_AUTHORIZATION_FAILED,
        self::STATUS_CODE_ACTIVATION_SUCCESS,
        self::STATUS_CODE_ACTIVATION_FAILED,
        self::STATUS_CODE_REVOKE_AUTHORIZATION_FAILED,
        self::STATUS_CODE_ADDING_SUBSCRIPTION_SUCCESS,
        self::STATUS_CODE_ADDING_SUBSCRIPTION_FAILED,
        self::STATUS_CODE_REMOVING_SUBSCRIPTION_SUCCESS,
        self::STATUS_CODE_REMOVING_SUBSCRIPTION_FAILED,
    ];

    public const STATUS_IS_ERROR = [
        self::STATUS_CODE_UNAUTHORIZED,
        self::STATUS_CODE_AUTHENTICATION_ERROR,
        self::STATUS_CODE_VALIDATION_FAILED,
        self::STATUS_CODE_VALIDATION_ERROR,
        self::STATUS_CODE_DELIVERY_FAILED,
        self::STATUS_CODE_DELIVERY_ERROR,
        self::STATUS_CODE_MAX_ATTEMPTS_EXCEEDED,
        self::STATUS_CODE_DELIVERED_AND_VALIDATION_FAILED,
        self::STATUS_CODE_BLACKLIST_FAILED,
        self::STATUS_CODE_ATTACHMENTS_NOT_CONSISTENT,
        self::STATUS_CODE_ATTACHMENTS_ERROR,
        self::STATUS_CODE_SIGNATURE_CHECK_FAILED,
        self::STATUS_CODE_SIGNATURE_CHECK_ERROR,
        self::STATUS_CODE_CERTIFICATE_CHECK_FAILED,
        self::STATUS_CODE_CERTIFICATE_CHECK_ERROR,
        self::STATUS_CODE_ARCHITECTURE_VALIDATION_FAILED,
        self::STATUS_CODE_ARCHITECTURE_VALIDATION_ERROR,
        self::STATUS_CODE_FILING_RULE_VALIDATION_FAILED,
        self::STATUS_CODE_FILING_RULE_VALIDATION_ERROR,
        self::STATUS_CODE_TAXONOMY_VALIDATION_FAILED,
        self::STATUS_CODE_TAXONOMY_VALIDATION_ERROR,
        self::STATUS_CODE_AUTHORIZATION_FAILED,
        self::STATUS_CODE_VALIDATION_FAILED,
        self::STATUS_CODE_VALIDATION_ERROR,
        self::STATUS_CODE_MUTATION_FAILED,
        self::STATUS_CODE_ADDING_AUTHORIZATION_FAILED,
        self::STATUS_CODE_ACTIVATION_FAILED,
        self::STATUS_CODE_REVOKE_AUTHORIZATION_FAILED,
        self::STATUS_CODE_ADDING_SUBSCRIPTION_FAILED,
        self::STATUS_CODE_REMOVING_SUBSCRIPTION_FAILED,
    ];

    public const STATUS_IN_PROGRESS = [
        self::STATUS_CODE_UNAUTHORIZED,
        self::STATUS_CODE_SENT,
        self::STATUS_CODE_RECEIVED,
        self::STATUS_CODE_ACCEPTED,
        self::STATUS_CODE_AUTHENTICATED,
        self::STATUS_CODE_VALIDATED,
        self::STATUS_CODE_DELIVERY_IN_PROGRESS,
        self::STATUS_CODE_DELIVERED,
        self::STATUS_CODE_BLACKLIST_SUCCEEDED,
        self::STATUS_CODE_ATTACHMENTS_CONSISTENT,
        self::STATUS_CODE_SIGNATURE_CHECK_PASSED,
        self::STATUS_CODE_CERTIFICATE_CHECK_PASSED,
        self::STATUS_CODE_ARCHITECTURE_VALIDATED,
        self::STATUS_CODE_VALIDATION_RULES_SUCCEEDED,
        self::STATUS_CODE_TAXONOMY_VALIDATED,
        self::STATUS_CODE_AUTHORIZATION_SUCCESS,
        self::STATUS_CODE_MUTATION_SUCCESS,
        self::STATUS_CODE_ADDING_AUTHORIZATION_SUCCESS,
        self::STATUS_CODE_REVOKE_AUTHORIZATION_SUCCESS,
    ];

    public const STATUS_IS_SUCCESS = [
        self::STATUS_CODE_DELIVERED_AND_VALIDATED,
        self::STATUS_CODE_ADDING_SUBSCRIPTION_SUCCESS,
        self::STATUS_CODE_REMOVING_SUBSCRIPTION_SUCCESS,
        self::STATUS_CODE_ACTIVATION_SUCCESS
    ];

    protected string|int $code; // can be string for "000" (unauthorized)
    protected ?string $timestamp = null;
    protected ?string $description = null;
    protected ?array $error = null;

    /**
     * LogiusStatus constructor. Accepts multiple forms of input.
     * @param string|array|int $status Integer, string or array with key 'code' are allowed.
     * @param string|null $timestamp Optional timestamp ISO8601 formatted.
     * @param string|null $description Optional description, Logius provides an NL (Dutch) text.
     */
    public function __construct(string|array|int $status, ?string $timestamp = null, ?string $description = null)
    {
        if (is_int($status)) {
            $this->code = $status;
        } elseif (is_string($status)) {
            $this->code = intval($status);
        } elseif (is_array($status) && isset($status['code'])) {
            $this->code = intval($status['code']);
            if (isset($status['timestamp'])) {
                $this->timestamp = $status['timestamp'];
                $this->description = $status['description'];
            }
            if (isset($status['error'])) {
                $this->error = $status['error'];
            }
        }

        if (isset($timestamp)) {
            $this->timestamp = $timestamp;
        }

        if (isset($description)) {
            $this->description = $description;
        }

        if (!$this->codeExists()) {
            throw new UnknownLogiusStatusException('Unknown status code: ' . $this->code);
        }
    }

    /**
     * Check if the status code is a valid, known code.
     * We check this by seeing if it is either processing or done. If the code is not in either list it is not valid.
     * @return bool TRUE if the status code is valid.
     */
    public function codeExists(): bool
    {
        return ($this->isProcessing() || $this->isDone());
    }

    public function isError(): bool
    {
        return in_array($this->code, self::STATUS_IS_ERROR);
    }

    /**
     * Done can be because a success or failure.
     * @return bool
     */
    public function isDone(): bool
    {
        return in_array($this->code, self::STATUS_IS_DONE);
    }

    public function isProcessing(): bool
    {
        return in_array($this->code, self::STATUS_IN_PROGRESS);
    }

    public function isSuccess(): bool
    {
        return in_array($this->code, self::STATUS_IS_SUCCESS);
    }

    public function getArray(): array
    {
        return [
            'code' => $this->code,
            'timestamp' => $this->timestamp,
            'description' => $this->description,
            'error' => $this->error
        ];
    }

    public function getCode(): int
    {
        return intval($this->code);
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * @return string|null Maximum of 2000 characters, text will be cut off if it was longer than that.
     */
    public function getErrorDescription(): ?string
    {
        if ($this->error !== null && isset($this->error['description'])) {
            return mb_substr($this->error['description'], 0, 2000);
        }

        return null;
    }

    public function getTimestamp(): ?Carbon
    {
        if (is_string($this->timestamp)) {
            return Carbon::parse($this->timestamp, 'UTC')->setTimezone('Europe/Amsterdam');
        }
        return null;
    }
}
