<?php

namespace App\Services\Gateway\Visionplanner;

use App\Account;
use App\Exceptions\PreconditionFailedException;
use App\Factories\TaskFileFactory;
use App\Models\TaskFile;
use App\Repositories\Services\AccountServiceRepository;
use App\Service;
use App\Services\Declarations\DeclarationsService;
use App\Services\TaskFileService;
use App\ServiceTask;
use App\ValueObject\Declarations\DeclarationData;
use Exception;

class VisionplannerService
{
    private DeclarationsService $declarationsService;
    private AccountServiceRepository $accountServiceRepository;
    private TaskFileService $taskFileService;

    public function __construct(
        DeclarationsService $declarationsService,
        AccountServiceRepository $accountServiceRepository,
        TaskFileService $taskFileService
    ) {
        $this->declarationsService = $declarationsService;
        $this->accountServiceRepository = $accountServiceRepository;
        $this->taskFileService = $taskFileService;
    }

    /**
     * If an XBRL (Publication Document) and PDF are supplied they are linked together.
     * If a company is found we save as a new task, otherwise they go to unmatched files.
     *
     * @param Account $account
     * @param string|null $name
     * @param string|null $xbrl
     * @param string|null $pdf base64 encoded pdf
     * @return ServiceTask|null
     * @throws Exception
     */
    public function generateTask(Account $account, ?string $name, ?string $xbrl, ?string $pdf): ?ServiceTask
    {
        $accountService = $this->accountServiceRepository->getByReferenceName(
            Service::VISIONPLANNER_SERVICE,
            $account,
            true
        )->first();

        if (!empty($pdf)) {
            if (is_null($name)) {
                $name = 'jaarwerk';
            }

            $annualReport = TaskFileFactory::create(
                $accountService,
                base64_decode($pdf),
                $name . '.pdf',
                ($xbrl ? TaskFile::TYPE_ANNUAL_REPORT : null)
            );
        }

        if (!empty($xbrl)) {
            $pubDoc = TaskFileFactory::create(
                $accountService,
                $xbrl,
                'jaarwerk.xbrl',
                TaskFile::TYPE_PUBLICATION_DOCUMENT
            );
            $attachments = [];
            if (!empty($annualReport)) {
                $attachments = [$annualReport];
            }
            $declarationData = new DeclarationData($pubDoc, $pubDoc->parsed_data, $attachments);

            return $this->declarationsService->generateTask($accountService, $declarationData);
        }

        if (empty($annualReport)) {
            throw new PreconditionFailedException('At this point PDF should always be present');
        }

        $this->taskFileService->store($annualReport);
        return null;
    }
}
