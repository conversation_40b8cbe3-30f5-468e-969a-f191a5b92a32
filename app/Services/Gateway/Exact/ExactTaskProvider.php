<?php

namespace App\Services\Gateway\Exact;

use App\AccountService;
use App\AccountServiceCompany;
use App\DataTransferObject\ServiceTask\WageTaxApprovalTaskSummary;
use App\Factories\TaskFileFactory;
use App\Helpers\QuarantineHelper;
use App\Interfaces\Services\CompanyProviderInterface;
use App\Models\CustomRules\CustomRule\CustomRuleServiceSync;
use App\Models\ServiceTask\WageTaxApprovalTask;
use App\Models\TaskFile\Xml;
use App\Services\CustomRule\CustomRuleService;
use App\Logging\Channels\ServiceLog;
use App\Services\Gateway\Exact\Model\ActiveDivision;
use App\Services\Gateway\Exact\Model\DocumentAttachment;
use App\Services\Gateway\Exact\Model\DeclarationReturn;
use App\Services\Service\AbstractExactServiceProvider;
use App\Services\ServiceTask\TaskProviderInterface;
use App\ServiceTask;
use App\Helpers\DatetimeHelper;
use App\ValueObject\Declarations\DeclarationData;
use Carbon\CarbonPeriod;
use Picqer\Financials\Exact\ApiException;
use Symfony\Component\HttpFoundation\Response;

class ExactTaskProvider extends AbstractExactServiceProvider implements
    TaskProviderInterface,
    CompanyProviderInterface
{
    public const TYPE_VAT_RETURN = 31;
    public const TYPE_ICT_RETURN = 32;
    public const TYPE_WAGE_TAX_RETURN = 146;

    public const ALLOWED_PROPERTY_KEYS = [
        AccountService::PROPERTY_LOGIUS_CONNECTION,
        AccountService::PROPERTY_REFRESH_PERIOD,
        AccountService::PROPERTY_REFRESH_PERIOD_SUPPLEMENTATION,
        AccountService::PROPERTY_REFRESH_PERIOD_WAGE_TAXES,
        AccountService::PROPERTY_INCLUDE_CURRENT_PERIOD,
        AccountService::PROPERTY_APPROVAL_PREFERENCES,
        AccountService::PROPERTY_AUTOMATED_SENDING,
        AccountService::PROPERTY_IGNORE_UNMATCHED_FILES,
        AbstractExactServiceProvider::PROPERTY_USER_ID
    ];

    public array $relevantCodes = [
        self::TYPE_VAT_RETURN,
        self::TYPE_ICT_RETURN,
        self::TYPE_WAGE_TAX_RETURN
    ];

    /**
     * ExactOnlineDataProvider constructor.
     * @param AccountService $accountService
     */
    public function __construct(AccountService $accountService)
    {
        parent::__construct($accountService);
        $this->client = resolve(ExactOnlineClient::class);
    }

    /**
     * Sometimes for debugging purposes it is useful to have access to the Client object.
     * @return ExactOnlineClient
     */
    public function getClient(): ExactOnlineClient
    {
        return $this->client;
    }

    /**
     * Check custom rules that prevent syncing specific types of tasks at this specific moment.
     * @return string[] List of Exact declaration codes that should be included when fetching declarations.
     */
    public function getRelevantDeclarationCodes(): array
    {
        $codes = [];
        if (CustomRuleServiceSync::allowSyncForEvent($this->accountService, CustomRuleService::SERVICE_SYNC_VAT)) {
            $codes[] = self::TYPE_VAT_RETURN;
        }
        if (CustomRuleServiceSync::allowSyncForEvent($this->accountService, CustomRuleService::SERVICE_SYNC_ICT)) {
            $codes[] = self::TYPE_ICT_RETURN;
        }
        if (CustomRuleServiceSync::allowSyncForEvent($this->accountService, CustomRuleService::SERVICE_SYNC_WAGE_TAX)) {
            $codes[] = self::TYPE_WAGE_TAX_RETURN;
        }
        return $codes;
    }

    /**
     * @return array
     */
    public function getCompanies(): array
    {
        $companies = [];
        try {
            /** @var ActiveDivision[] $divisions */
            $divisions = (new ActiveDivision(
                $this->client->getAuthenticatedConnectionFromAccountService($this->accountService)
            ))->get();
            foreach ($divisions as $division) {
                $company = [
                    'id' => $division->Code,
                    'name' => $division->Description,
                    'client_id' => $division->Code,
                    'vat' => $division->VATNumber
                ];

                $companies[] = $company;
            }
        } catch (\Throwable $e) {
            $this->handleErrorMessage($e, $this->accountService, 'Exception while getting companies from Exact'); // phpcs:ignore
        }

        return $companies;
    }

    /**
     * @param AccountService $accountService
     * @param string $externalId Exact Division ID
     * @return DeclarationData[]
     */
    public function getTasksFromExternalCompany(AccountService $accountService, string $externalId): array
    {
        $files = [];
        try {
            /** @var DeclarationReturn[] $declarationReturns */
            $connection = $this->client->getAuthenticatedConnectionFromAccountService($accountService);
            $this->checkRateLimit($connection);
            $connection->setDivision($externalId);
            $declarationReturns = (new DeclarationReturn($connection))->get();

            $i = 0;
            foreach ($declarationReturns as $declarationReturn) {
                try {
                    $i++;
                    if ($this->isDeclarationReturnCandidateForImport($declarationReturn, $accountService)) {
                        $file = $this->getTaskFile($accountService, $declarationReturn->DocumentID);
                        ServiceLog::debug($i . '. Fetched from Exact #' . $declarationReturn->DocumentID);
                        if ($file instanceof Xml) {
                            $file = $this->addExtraFields($file, $declarationReturn);
                            $files[] = new DeclarationData(
                                $file,
                                $file->parsed_data,
                                [],
                                $declarationReturn->DocumentID
                            );
                            ServiceLog::debug($i . '. Added to list. ' . $declarationReturn->DocumentID);
                        }
                    }
                } catch (ApiException $e) {
                    ServiceLog::error(
                        $i . '. Exact API error. ' . $e->getMessage(),
                        ['class' => $e::class, 'Account service ID' => $accountService->id]
                    );
                    break;
                } catch (\Throwable $e) {
                    ServiceLog::error(
                        $i . '. An error occurred while getting document ' . $declarationReturn->DocumentID . ' for division #' . $externalId . ' from Exact: ' . $e->getMessage(), // phpcs:ignore
                        ['class' => $e->getFile(), 'line' => $e->getLine(), 'Account service ID' => $accountService->id]
                    );
                }
            }
            ServiceLog::debug('Finished fetching ' . $i . '  out of ' . count($declarationReturns) . ' for division #' . $externalId); // phpcs:ignore
        } catch (\Throwable $e) {
            $this->handleErrorMessage($e, $accountService, 'Exception while fetching declarations for division #' . $externalId . ' from Exact'); // phpcs:ignore
        }
        return $files;
    }

    /**
     * @param DeclarationReturn $declaration
     * @param AccountService $accountService
     * @return bool
     */
    public function isDeclarationReturnCandidateForImport(DeclarationReturn $declaration, AccountService $accountService): bool //phpcs:ignore
    {
        if (!in_array($declaration->Type, $this->relevantCodes)) {
            ServiceLog::debug('Exact declaration #' . $declaration->DocumentID . ' is ignored. Type ' . $declaration->Type . ' is not relevant.'); // phpcs:ignore
            return false;
        }

        switch ($declaration->Type) {
            case self::TYPE_VAT_RETURN:
                $file = $this->getTaskFile($accountService, $declaration->DocumentID);
                if ($file instanceof Xml && !empty($file->parsed_data['suppletion'])) {
                    $period = $accountService->getRefreshPeriodSupplementationInMonths();
                    break;
                }
                $period = $accountService->getRefreshPeriodInMonths();
                break;
            case self::TYPE_ICT_RETURN:
                $period = $accountService->getRefreshPeriodInMonths();
                break;
            case self::TYPE_WAGE_TAX_RETURN:
                $period = $accountService->getWageTaxRefreshPeriodInMonths();
                break;
            default:
                ServiceLog::debug('Exact declaration #' . $declaration->DocumentID . ' is ignored. Declaration is not of type VAT/ICT/Wage Tax'); //phpcs:ignore
                return false;
        }

        if (is_null($period)) {
            ServiceLog::debug('Exact declaration #' . $declaration->DocumentID . ' is ignored. Refresh period is set to none.'); //phpcs:ignore
            return false;
        }

        $startDate = $declaration->getDateStart();
        $endDate = $declaration->getDateEnd();

        if (
            $declaration->Type === self::TYPE_WAGE_TAX_RETURN &&
            (
                DatetimeHelper::dateInRefreshPeriod($startDate, $period) ||
                (
                    DatetimeHelper::dateInCurrentPeriod($startDate) &&
                    $accountService->getIncludeCurrentPeriodEnabled()
                )
            )
        ) {
            return true;
        }

        if (DatetimeHelper::dateInRefreshPeriod($endDate, $period)) {
            return true;
        }

        ServiceLog::debug('Exact declaration #' . $declaration->DocumentID . ' is ignored. End date ' . $endDate->isoFormat('YYYY-MM-DD') . ' < ' . DatetimeHelper::$lastCutOffDate); //phpcs:ignore
        return false;
    }

    /**
     * @param AccountService $accountService
     * @param string $guid
     * @return Xml|null
     */
    public function getTaskFile(AccountService $accountService, string $guid): ?Xml
    {
        try {
            $connection = $this->client->getAuthenticatedConnectionFromAccountService($accountService);
            $this->checkRateLimit($connection);

            $attachments = new DocumentAttachment($connection);
            ServiceLog::debug('Attachment URL: ' . $attachments->url());
            $attachment = static::getSingleAttachment($attachments->find($guid));
            try {
                ServiceLog::debug('Fetching ' . $attachment->Url);
                $xml = $this->fetchXml($attachment->Url, $connection->getAccessToken());
                if (isset($xml)) {
                    return TaskFileFactory::create($accountService, $xml, $attachment->FileName);
                }
            } catch (\Throwable $e) {
                ServiceLog::error(
                    'Could not parse Exact VAT response: ' . $e->getMessage(),
                    ['DocumentID' => $guid]
                );
            }
        } catch (\Throwable $e) {
            $this->handleErrorMessage($e, $this->accountService, 'Exception while getting task file from Exact'); // phpcs:ignore
        }

        return null;
    }

    /**
     * Exact can return a single attachment object or an array. If it is a list we find the XML/XBRL file.
     * @param object|array $attachments
     * @return object
     */
    public static function getSingleAttachment(object|array $attachments): object
    {
        if (is_array($attachments)) {
            foreach ($attachments as $item) {
                $extension = strtolower(pathinfo($item->FileName, PATHINFO_EXTENSION));
                if (in_array($extension, ['xml', 'xbrl'])) {
                    ServiceLog::debug('Found ' . $item->FileName . ' in list.');
                    return $item;
                }
            }
        }
        return $attachments;
    }

    public function addExtraFields(Xml $file, DeclarationReturn $declaration): Xml
    {
        $data = $file->parsed_data;
        $data['source'] = 'exact';
        $data['source_id'] = $declaration->DocumentID; //used by DeclarationData
        $data['external_id'] = $declaration->DocumentID; // Legacy
        $data['division'] = $declaration->connection()->getDivision();
        $file->parsed_data = $data;
        return $file;
    }

    /**
     * @param bool $scheduled
     * @return array
     * @throws \Throwable
     */
    public function getTasksFromAccountService(bool $scheduled = false): array
    {
        if ($scheduled) {
            $this->relevantCodes = $this->getRelevantDeclarationCodes();
        }

        $companies = $this->getCompanies();

        $tasks = [];
        foreach ($companies as $company) {
            $declarations = $this->getTasksFromExternalCompany($this->accountService, $company['id']);
            $tasks = array_merge($tasks, $declarations);
        }

        return $tasks;
    }

    /**
     * @param ServiceTask $task
     * @return DeclarationData|null Date for a single existing task, or none if it cannot be found.
     * @throws \Throwable
     */
    public function getTaskData(ServiceTask $task): ?DeclarationData
    {
        try {
            /** @var DeclarationReturn[] $vatReturns */
            $connection = $this->client->getAuthenticatedConnectionFromAccountService($task->accountService);
            $this->checkRateLimit($connection);
            $connection->setDivision($task->data['division']);
            $vatReturns = (new DeclarationReturn($connection))->get();

            foreach ($vatReturns as $vatReturn) {
                if ((string)$vatReturn->DocumentID !== $task->refresh_id) {
                    continue;
                }

                $file = $this->getTaskFile($task->accountService, $vatReturn->DocumentID);
                if (empty($file)) {
                    continue;
                }

                $file = $this->addExtraFields($file, $vatReturn);

                if ($task instanceof WageTaxApprovalTask) {
                    // We need to update the task file with this task data to avoid hash mismatch
                    $fileParsedData = $this->mergeDataFromTaskToFile($file->parsed_data, $task->data);
                    $file->setParsedDataAttribute($fileParsedData);

                    $taskFile = $task->getFiles($task->getDeclarationFileExtension())->first();
                    if (!empty($taskFile)) {
                        $taskFile->setParsedDataAttribute($fileParsedData);
                        $taskFile->save();
                    }
                }

                return new DeclarationData($file, $file->parsed_data, [], $vatReturn->DocumentID);
            }
        } catch (\Throwable $e) {
            $this->handleErrorMessage($e, $this->accountService, 'Exception while getting task data from Exact'); // phpcs:ignore
        }

        return null;
    }

    public function mergeDataFromTaskToFile(array $fileParsedData, array $taskData): array
    {
        //phpcs:disable
        $fileParsedData['summary'][WageTaxApprovalTaskSummary::PERIOD] = $taskData['summary'][WageTaxApprovalTaskSummary::PERIOD];
        $fileParsedData['summary'][WageTaxApprovalTaskSummary::FINAL_DECLARATION_DATE] = $taskData['summary'][WageTaxApprovalTaskSummary::FINAL_DECLARATION_DATE];
        $fileParsedData['summary'][WageTaxApprovalTaskSummary::FINAL_PAYMENT_DATE] = $taskData['summary'][WageTaxApprovalTaskSummary::FINAL_PAYMENT_DATE];
        //phpcs:enabled
        return $fileParsedData;
    }

    /**
     * @param string $url
     * @param string $token
     * @return string|null XML/XBRL Document as string extracted from response.
     */
    public function fetchXml(string $url, string $token): ?string
    {
        $curl = curl_init();
        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/x-www-form-urlencoded',
                    'Authorization: Bearer ' . $token
                ],
            )
        );

        $response = curl_exec($curl);
        curl_close($curl);

        if (!str_contains($response, ':xbrl xml') && !str_contains($response, '?xml')) {
            $filename = 'exact-response-' . time() . '.html';
            QuarantineHelper::quarantineFile($filename, $response);
            ServiceLog::warning('Quarantined Exact response from ' . $url . ' that does not contain XBRL at ' . $filename); // phpcs:ignore
            return null;
        }

        $body = simplexml_load_string($response)->body;
        return (string)$body->pre;
    }

    /**
     * @param AccountServiceCompany $accountServiceCompany
     * @param CarbonPeriod $period
     * @param string $declarationType
     * @return array|DeclarationData[]
     */
    public function getVatDeclarationsWithAccountServiceCompanyAndPeriodAndType(
        AccountServiceCompany $accountServiceCompany,
        CarbonPeriod $period,
        string $declarationType
    ): array {
        $filtered = [];
        try {
            /** @var DeclarationReturn[] $vatReturns */
            $connection = $this->client->getAuthenticatedConnectionFromAccountService(
                $accountServiceCompany->accountService
            );
            $this->checkRateLimit($connection);
            $companies = $this->getCompanies();

            foreach ($companies as $company) {
                $connection->setDivision($company['id']);
                $vatReturns = (new DeclarationReturn($connection))->get();

                foreach ($vatReturns as $vatReturn) {
                    try {
                        if (
                            $period->getStartDate()->isSameMonth($vatReturn->getDateStart()) &&
                            $period->getEndDate()->isSameMonth($vatReturn->getDateEnd())
                        ) {
                            $file = $this->getTaskFile($accountServiceCompany->accountService, $vatReturn->DocumentID);
                            if (!empty($file) && $file['type'] === $declarationType) {
                                $file = $this->addExtraFields($file, $vatReturn);
                                $filtered[] = new DeclarationData(
                                    $file,
                                    $file->parsed_data,
                                    [],
                                    $vatReturn->DocumentID
                                );
                            }
                        }
                    } catch (\Throwable $e) {
                        $this->handleErrorMessage($e, $this->accountService, 'Unable to get VAT declaration ' . $vatReturn->DocumentID . ' from Exact for division ' . $company['id']); // phpcs:ignore
                    }
                }
            }
        } catch (\Throwable $e) {
            $this->handleErrorMessage($e, $this->accountService, 'Exception while getting vat declarations from Exact'); // phpcs:ignore
        }

        return $filtered;
    }

    public function refreshOnReopen(): bool
    {
        return true;
    }

    public function getTaskTypes(): array
    {
        return [
            ServiceTask::TYPE_VAT_APPROVAL,
            ServiceTask::TYPE_ICT_APPROVAL,
            ServiceTask::TYPE_WAGE_TAX_APPROVAL
        ];
    }

    /**
     * @param \Throwable $e
     * @param AccountService $accountService
     * @param string $errorMessage
     * @return void
     */
    protected function handleErrorMessage(
        \Throwable $e,
        AccountService $accountService,
        string $errorMessage = ''
    ): void {
        $message = $e->getMessage();
        if ($e->getCode() === Response::HTTP_SERVICE_UNAVAILABLE) {
            $message = 'Service unavailable';
        }
        ServiceLog::error(
            $errorMessage . ': ' . $message,
            ['class' => $e::class, 'Account service ID' => $accountService->id]
        );
    }
}
