<?php

namespace App\Services\OpenQuestions;

use App\AccountService;
use App\Company;
use App\Events\CustomRule\CustomRuleEvent;
use App\Exceptions\AccountService\NotEnabledException;
use App\Interfaces\Services\OpenQuestions\OpenQuestionCustomRuleService;
use App\Interfaces\Services\OpenQuestions\OpenQuestionsServiceInterface;
use App\Models\OpenQuestions\OpenQuestionType;
use App\Models\OpenQuestions\Questions\Bookkeeping;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Repositories\OpenQuestions\OpenQuestionRepository;
use App\Repositories\OpenQuestions\BookkeepingRepository;
use App\Repositories\Services\AccountServiceRepository;
use App\Services\CompanyService;
use App\User;
use App\ValueObject\Pagination\OpenQuestionsListFilters;
use Illuminate\Database\Eloquent\Collection;

readonly class BookkeepingQuestionService implements OpenQuestionsServiceInterface, OpenQuestionCustomRuleService
{
    public function __construct(
        private BookkeepingRepository $bookkeepingRepository,
        private AccountServiceRepository $accountServiceRepository,
        private CompanyService $companyService,
        private OpenQuestionTypeService $openQuestionTypeService
    ) {
    }

    public function store(Bookkeeping $bookkeeping): ?Bookkeeping
    {
        if ($bookkeeping->openQuestion instanceof OpenQuestion && is_null($bookkeeping->openQuestion->id)) {
            $openQuestionRepo = resolve(OpenQuestionRepository::class);
            $openQuestion = $openQuestionRepo->store($bookkeeping->openQuestion);
            $bookkeeping->openQuestion()->associate($openQuestion);
        }
        return $this->bookkeepingRepository->store($bookkeeping);
    }

    /**
     * Find an existing Bookkeeping question in the database that matches some properties of the one supplied.
     * @param Bookkeeping $bookkeepingQuestion
     * @return Bookkeeping|null Extsing question if match is found, NULL if no match is found.
     */
    public function findSame(Bookkeeping $bookkeepingQuestion): ?Bookkeeping
    {
        $attributes = [
            Bookkeeping::NAME => $bookkeepingQuestion->name,
            Bookkeeping::CURRENCY => $bookkeepingQuestion->currency,
            Bookkeeping::AMOUNT => $bookkeepingQuestion->amount,
            Bookkeeping::TRANSACTION_DATE => $bookkeepingQuestion->transaction_date
        ];
        return $this->bookkeepingRepository->findQuestion(
            $attributes,
            $bookkeepingQuestion->openQuestion->company_id,
            $bookkeepingQuestion->openQuestion->accountService->id
        );
    }

    /**
     * Store bookkeeping question in database, but only if no other one with the same attributes exists.
     * @param Bookkeeping $bookkeepingQuestion
     * @return Bookkeeping|null
     */
    public function storeUnique(Bookkeeping $bookkeepingQuestion): ?Bookkeeping
    {
        $sameBookkeepingQuestion = $this->findSame($bookkeepingQuestion);
        if ($sameBookkeepingQuestion instanceof Bookkeeping) {
            return $sameBookkeepingQuestion;
        }
        return $this->store($bookkeepingQuestion);
    }

    public function create(array $attributes, OpenQuestion $openQuestion): Bookkeeping
    {
        $attributes['open_question_id'] = $openQuestion->id;
        $question = $this->bookkeepingRepository->create($attributes);

        CustomRuleEvent::fire($question->getCustomRuleCreatedEvent(), $openQuestion->getAccount(), $openQuestion);
        return $question;
    }

    /**
     * @param $companyId
     * @param string $serviceName
     * @param User $user
     * @return Collection
     */
    public function getQuestionsForCompanyAndService($companyId, string $serviceName, User $user): Collection
    {
        $company = $this->companyService->getById($companyId, $user);
        $accountService = $this->accountServiceRepository->getByReferenceName(
            $serviceName,
            $company->account,
            true
        );

        if ($accountService->isEmpty()) {
            throw new NotEnabledException(params: ['reference_name' => $serviceName]);
        }

        return $this->bookkeepingRepository->getQuestionsForCompanyAndAccountService($company, $accountService[0]);
    }

    public function getQuestionFromBrowserExtension(
        array $attributes,
        string $serviceName,
        User $user
    ): ?Bookkeeping {
        $company = $this->companyService->getById($attributes['company_id'], $user);

        $accountService = $this->accountServiceRepository->getByReferenceName(
            $serviceName,
            $company->account,
            true
        );
        if ($accountService->isEmpty()) {
            throw new NotEnabledException(params: ['reference_name' => $serviceName]);
        }

        return $this->bookkeepingRepository->findQuestion($attributes, $company->id, $accountService->first()->id);
    }

    public function questionsToAnswer(
        Company $company,
        User $user,
        array $orderBy = ['open_questions.created_at' => 'desc'],
        ?string $alternateCategory = null
    ): Collection {
        return $this->bookkeepingRepository->questionsToAnswer($company, $user, $orderBy);
    }

    /**
     * Get types for a question
     *
     * @param AccountService $accountService
     * @param array|null $data
     * @return Collection
     */
    public function questionTypes(AccountService $accountService, ?array $data): Collection
    {
        return $this->openQuestionTypeService->getBySubject(
            $accountService->id,
            OpenQuestionType::SUBJECT_COMPANY
        );
    }

    public function fireCustomRulePending(OpenQuestion $openQuestion): void
    {
        $bookkeeping = $this->bookkeepingRepository->getByQuestionId($openQuestion);
        if (!empty($bookkeeping)) {
            CustomRuleEvent::fire(
                $bookkeeping->getCustomRuleToBeReviewedEvent(),
                $openQuestion->getAccount(),
                $openQuestion
            );
        }
    }

    public function fireCustomRuleCompleted(OpenQuestion $openQuestion): void
    {
        $bookkeeping = $this->bookkeepingRepository->getByQuestionId($openQuestion);
        if (!empty($bookkeeping)) {
            CustomRuleEvent::fire(
                $bookkeeping->getCustomRuleCompletedEvent(),
                $openQuestion->getAccount(),
                $openQuestion
            );
        }
    }

    public function getForCompany(
        Company $company,
        OpenQuestionsListFilters $filters,
        ?string $category = null
    ): Collection {
        return $this->bookkeepingRepository->getForCompany($company, $filters);
    }

    public function findQuestion(array $attributes, int $companyId, int $accountServiceId): ?Bookkeeping //phpcs:ignore
    {
        return $this->bookkeepingRepository->findQuestion(
            $attributes,
            $companyId,
            $accountServiceId
        );
    }
}
