<?php

namespace App\Services\OpenQuestions;

use App\Company;
use App\Exceptions\DmsNotConfiguredException;
use App\Exceptions\NotFoundException;
use App\Exceptions\OpenQuestions\Attachment\AttachmentAlreadyExistsException;
use App\Exceptions\PreconditionFailedException;
use App\Exceptions\UnauthorizedException;
use App\Helpers\FileHelper;
use App\Helpers\FilenameHelper;
use App\Helpers\ImageHelper;
use App\Models\OpenQuestions\OpenQuestionAttachment;
use App\Models\OpenQuestions\OpenQuestionRelationType;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Repositories\FileSystem\EncryptedStorageRepository;
use App\Repositories\OpenQuestions\OpenQuestionAttachmentRepository;
use App\Services\Dms\DmsService;
use App\Services\Dms\OpenQuestionPreferencesService;
use App\Services\Dossiers\DossierFileService;
use App\User;
use App\ValueObject\AgentData;
use App\ValueObject\Dms\OpenQuestionAttachmentDmsBundle;
use App\Support\Carbon;
use Config;
use Exception;
use File;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Support\Collection;
use RuntimeException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Throwable;
use ZipArchive;

class OpenQuestionAttachmentService
{
    public const MAX_FILENAME_LENGTH = 120;

    protected const ALLOWED_FILE_MIME_TYPES = [
        'image/png',
        'image/jpeg',
        'application/pdf',
        'application/msword',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'text/csv',
        'text/xml',
        'application/xml',
        'application/octet-stream',
        'application/x-sqlite3'
    ];

    public function __construct(
        private readonly OpenQuestionAttachmentRepository $openQuestionAttachmentsRepository,
        private readonly EncryptedStorageRepository $encryptedStorageRepository
    ) {
    }

    public function saveAttachment(
        OpenQuestion $openQuestion,
        UploadedFile $file,
        ?User $user = null,
        int $fileSize = null
    ): OpenQuestionAttachment {
        if (!in_array($file->getMimeType(), self::ALLOWED_FILE_MIME_TYPES)) {
            throw new PreconditionFailedException('Wrong mimetype provided');
        }

        if (is_null($fileSize)) {
            $fileSize = Config::get('services.client_question.upload_max_size');
        }

        FileHelper::validateFileSize($file, $fileSize);

        if ($file->getMimeType() === 'image/jpeg' && ImageHelper::shouldCompressImage($file)) {
            ImageHelper::compressImageSize($file, 80);
            clearstatcache(); //clear the cached filesize, because it changed. This is needed for validation.
        }
        $filename = FilenameHelper::sanitize($file->getClientOriginalName(), self::MAX_FILENAME_LENGTH);
        $folderPath = $this->getFolderPath($openQuestion);

        if ($this->openQuestionAttachmentsRepository->fileExists($openQuestion, $folderPath . $filename)) {
            throw new AttachmentAlreadyExistsException($filename . ' for question #' . $openQuestion->id . ' already exists'); //phpcs:ignore
        }

        $filePath = $this->encryptedStorageRepository->storeFile($file, $folderPath, $filename);

        // Only expire attachments of client (received) questions.
        $shouldExpire = $openQuestion->relation_type === OpenQuestionRelationType::CLIENT;

        return $this->openQuestionAttachmentsRepository->create(
            $openQuestion,
            $filePath,
            md5_file($filePath),
            $shouldExpire ? Carbon::now()->addDays(30) : null,
            $user
        );
    }

    /**
     * Download question attachments
     * @param Collection<OpenQuestionAttachment> $attachments
     * @return string Zip content
     * @throws FileNotFoundException
     */
    public function downloadAttachments(Collection $attachments): string
    {
        $zipPath = storage_path('tmp/open_questions/attachments/');

        if (!is_dir($zipPath)) {
            mkdir($zipPath, 0755, true);
        }

        $zipFilePath = $zipPath . random_string(32) . '.zip';

        $zip = new ZipArchive();
        $zip->open($zipFilePath, ZipArchive::CREATE);

        foreach ($attachments as $attachment) {
            $zip->addFromString($attachment->name(), $attachment->getContent());
        }

        $zip->close();

        return File::get($zipFilePath);
    }

    /**
     * Delete question attachment
     * @param Collection $attachments
     * @param User $user
     * @param AgentData $agentData
     * @return void
     */
    public function deleteAttachments(Collection $attachments, User $user, AgentData $agentData): void
    {
        $auditLogService = resolve(OpenQuestionAuditLogService::class);

        /** @var OpenQuestionAttachment $attachment */
        foreach ($attachments as $attachment) {
            if (!empty($attachment)) {
                if (!$this->openQuestionAttachmentsRepository->delete($attachment)) {
                    throw new RuntimeException('Could not delete OpenQuestionAttachment #' . $attachment->id);
                }

                $auditLogService->auditLogDeletedAttachment(
                    $attachment->openQuestion,
                    $attachment,
                    $user,
                    $agentData
                );

                $this->encryptedStorageRepository->deleteFile($attachment->path);
            }
        }
    }

    public function getByOpenQuestion(OpenQuestion $openQuestion, string $filename): string
    {
        $possibleFilePaths = $openQuestion->attachments()->pluck(OpenQuestionAttachment::PATH)->toArray();

        $path = null;
        foreach ($possibleFilePaths as $possibleFilePath) {
            if (basename($possibleFilePath) === $filename) {
                $path = $possibleFilePath;
            }
        }

        if ($path === null) {
            throw new NotFoundException('Could not find attachment with filename: ' . $filename . ' for question #' . $openQuestion->id); //phpcs:ignore
        }

        return $this->encryptedStorageRepository->getFile($path);
    }

    public function getById(int $attachmentId, User $user): OpenQuestionAttachment
    {
        $attachment = $this->openQuestionAttachmentsRepository->getById($attachmentId);

        $companyId = $attachment->openQuestion->company_id;
        if (!$user->hasCompany($companyId) && !$user->canManageCompany($companyId)) {
            throw new UnauthorizedException('User #' . $user->id . ' does not have permission to view OpenQuestionAttachment #' . $attachmentId); //phpcs:ignore
        }

        return $attachment;
    }

    private function getFolderPath(OpenQuestion $openQuestion): string
    {
        return storage_path(
            'app/Account/' . $openQuestion->company->account->id . '/OpenQuestion/' . $openQuestion->id . '/'
        );
    }

    /**
     * @param Collection<OpenQuestionAttachment> $attachments
     * @return void
     * @throws Throwable
     */
    public function sendAttachmentsToDossier(Collection $attachments): void
    {
        $dossierFileService = resolve(DossierFileService::class);

        foreach ($attachments as $attachment) {
            $dossierFileService->copyOpenQuestionAttachment($attachment);
        }
    }

    /**
     * @throws Exception
     */
    public function sendToDms(Company $company, int $attachmentId, ?User $user = null): void
    {
        $attachment = $this->openQuestionAttachmentsRepository->getByCompanyAndId($company, $attachmentId);
        $account = $attachment->getAccount();
        $dmsService = resolve(DmsService::class);
        $activeDmsService = $dmsService->getActiveDms($account);

        if (
            !empty($activeDmsService)
            && $dmsService->shouldBeSentToDms($attachment, $account)
        ) {
            resolve(OpenQuestionPreferencesService::class)->createForOpenQuestion(
                $attachment->openQuestion,
                $activeDmsService
            );

            $dmsService->dispatchDmsJob($attachment, $user?->id);
        }
    }

    /**
     * @throws Exception
     */
    public function sendMultipleToDms(Company $company, array $attachmentIds, ?User $user = null): void
    {
        $attachments = $this->openQuestionAttachmentsRepository->getByCompanyAndIds($company, $attachmentIds);
        /** @var OpenQuestionAttachment $firstAttachment */
        $firstAttachment = $attachments->first();
        $title = $firstAttachment->openQuestion->title
            . ' - ' . $firstAttachment->openQuestion->subtitle
            . ' - ' . FilenameHelper::sanitizeFolder($company->name)
            . ' - ' . Carbon::now()->format('d-m-Y H:i');
        $bundle = new OpenQuestionAttachmentDmsBundle($attachments->pluck('id')->toArray(), $company, $title);
        $account = $company->account;
        $dmsService = resolve(DmsService::class);
        $activeDmsService = $dmsService->getActiveDms($account);

        if (
            !empty($activeDmsService)
            && $dmsService->shouldBeSentToDms($bundle, $account)
        ) {
            resolve(OpenQuestionPreferencesService::class)->createForOpenQuestion(
                $firstAttachment->openQuestion,
                $activeDmsService
            );

            $dmsService->dispatchDmsJob($bundle, $user?->id);
        } else {
            throw new DmsNotConfiguredException('Could not send attachments (' . implode(',', $attachmentIds) . ') to dms because no dms is configured.'); //phpcs:ignore
        }
    }
}
