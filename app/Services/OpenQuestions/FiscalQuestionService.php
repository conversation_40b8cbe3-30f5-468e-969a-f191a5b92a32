<?php

namespace App\Services\OpenQuestions;

use App\AccountService;
use App\Company;
use App\Events\CustomRule\CustomRuleEvent;
use App\Exceptions\AccountService\NotEnabledException;
use App\Interfaces\Services\OpenQuestions\OpenQuestionCustomRuleService;
use App\Interfaces\Services\OpenQuestions\OpenQuestionsServiceInterface;
use App\Models\OpenQuestions\OpenQuestionType;
use App\Models\OpenQuestions\Questions\Fiscal;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Repositories\OpenQuestions\FiscalRepository;
use App\Repositories\Services\AccountServiceRepository;
use App\User;
use App\ValueObject\Pagination\OpenQuestionsListFilters;
use Illuminate\Database\Eloquent\Collection;

readonly class FiscalQuestionService implements OpenQuestionsServiceInterface, OpenQuestionCustomRuleService
{
    public function __construct(
        private FiscalRepository $fiscalRepository,
        private AccountServiceRepository $accountServiceRepository,
        private OpenQuestionTypeService $openQuestionTypeService,
    ) {
    }

    public function create(OpenQuestion $openQuestion): Fiscal
    {
        $attributes['open_question_id'] = $openQuestion->id;
        $question = $this->fiscalRepository->create($attributes);
        CustomRuleEvent::fire($question->getCustomRuleCreatedEvent(), $openQuestion->getAccount(), $openQuestion);
        return $question;
    }

    public function questionsToAnswer(
        Company $company,
        User $user,
        array $orderBy = ['open_questions.created_at' => 'desc'],
        ?string $alternateCategory = null
    ): Collection {
        return $this->fiscalRepository->questionsToAnswer($company, $user, $orderBy);
    }

    /**
     * Get types for a question
     *
     * @param AccountService $accountService
     * @param array|null $data
     * @return Collection
     */
    public function questionTypes(AccountService $accountService, ?array $data): Collection
    {
        return $this->openQuestionTypeService->getBySubject(
            $accountService->id,
            OpenQuestionType::SUBJECT_COMPANY
        );
    }

    /**
     * @param string $externalId
     * @param string $serviceName
     * @param User $user
     * @return Collection|Fiscal[]
     */
    public function getQuestions(
        string $externalId,
        string $serviceName,
        User $user
    ): Collection {
        $accountService = $this->accountServiceRepository->getByReferenceName(
            $serviceName,
            $user->account,
            true
        );
        if ($accountService->isEmpty()) {
            throw new NotEnabledException(params: ['reference_name' => $serviceName]);
        }
        return $this->fiscalRepository->getQuestions(
            $externalId,
            $accountService->first()
        );
    }

    public function fireCustomRulePending(OpenQuestion $openQuestion): void
    {
        $fiscal = $this->fiscalRepository->getByQuestionId($openQuestion);
        if (!empty($fiscal)) {
            CustomRuleEvent::fire(
                $fiscal->getCustomRuleToBeReviewedEvent(),
                $openQuestion->getAccount(),
                $openQuestion
            );
        }
    }

    public function fireCustomRuleCompleted(OpenQuestion $openQuestion): void
    {
        $fiscal = $this->fiscalRepository->getByQuestionId($openQuestion);
        if (!empty($fiscal)) {
            CustomRuleEvent::fire($fiscal->getCustomRuleCompletedEvent(), $openQuestion->getAccount(), $openQuestion);
        }
    }

    public function getForCompany(
        Company $company,
        OpenQuestionsListFilters $filters,
        ?string $category = null
    ): Collection {
        return $this->fiscalRepository->getForCompany($company, $filters);
    }

    public function createManualQuestion(
        OpenQuestion $openQuestion
    ): Fiscal {
        $question = $this->fiscalRepository->create(['open_question_id' => $openQuestion->id]);
        CustomRuleEvent::fire($question->getCustomRuleCreatedEvent(), $openQuestion->getAccount(), $openQuestion);
        return $question;
    }
}
