<?php

namespace App\Services;

use App\Account;
use App\Exceptions\BadRequestException;
use App\Interfaces\CustomRuleCustomActionInterface;
use App\Models\OcrTag;
use App\Repositories\OcrTagRepository;
use Illuminate\Support\Collection;
use Throwable;

class OcrTagService implements CustomRuleCustomActionInterface
{
    public function __construct(
        private readonly OcrTagRepository $ocrTagRepository
    ) {
    }

    /**
     * @throws Throwable
     */
    public function getByAccount(Account $account): Collection
    {
        $ocrTags = $this->ocrTagRepository->getByAccount($account);
        if ($ocrTags->isEmpty()) {
            return $this->createDefaultTags($account);
        }
        return $ocrTags;
    }

    /**
     * @throws Throwable
     */
    public function createDefaultTags(Account $account): Collection
    {
        $ocrTags = new Collection();
        $ocrTags->push($this->ocrTagRepository->create($account, OcrTag::TYPE_PURCHASE, OcrTag::DEFAULT_TAG_PURCHASE));
        $ocrTags->push($this->ocrTagRepository->create($account, OcrTag::TYPE_SALE, OcrTag::DEFAULT_TAG_SALE));
        return $ocrTags;
    }

    /**
     * @throws Throwable
     */
    public function saveOcrTags(
        array $tags,
        Account $account
    ): Collection {
        $ocrTags = new Collection();
        foreach ($tags as $tag) {
            $ocrTag = $this->ocrTagRepository->getById($account, $tag['id']);
            if (empty($ocrTag)) {
                throw new BadRequestException('OcrTag with #' . $tag['id'] . ' does not exist.');
            }
            $ocrTags->push($this->ocrTagRepository->update($ocrTag, $tag['tag']));
        }
        return $ocrTags;
    }

    public function isCustomized(Account $account): bool
    {
        return $this->ocrTagRepository->hasForAccount($account);
    }

    /**
     * @throws Throwable
     */
    public function getByType(Account $account, string $type): ?OcrTag
    {
        // Make sure there are OCR Tags for this account.
        if (!$account->ocrTags()->exists()) {
            $this->createDefaultTags($account);
        }

        return $this->ocrTagRepository->getByType($account, $type);
    }
}
