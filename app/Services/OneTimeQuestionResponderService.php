<?php

namespace App\Services;

use App\Account;
use App\Company;
use App\ExpiringUser;
use App\Factories\Models\User\ExpiringUserFactory;
use App\OneTimeQuestionResponder;
use App\Repositories\CompanyUserRepository;
use App\Repositories\UserExpirationRepository;
use App\Role;
use App\Support\Carbon;

class OneTimeQuestionResponderService
{
    public function __construct(
        private readonly CompanyUserRepository $companyUserRepository,
        private readonly UserExpirationRepository $userExpirationRepository
    ) {
    }

    public function createOneTimeQuestionResponder(
        array $data,
        Account $account,
        Company $company,
        int $days
    ): ExpiringUser {
        $oneTimeUser = ExpiringUserFactory::create(
            $account,
            $data['firstname'],
            $data['lastname'],
            $data['email'],
            OneTimeQuestionResponder::AUTH_ID_PREFIX,
            $data['mobile'],
        );

        $oneTimeUser->save();

        // Add role to user
        $role = Role::where(Role::NAME, Role::ONE_TIME_QUESTION_RESPONDER)->first();
        $oneTimeUser->roles()->attach($role);

        $this->companyUserRepository->addUserToCompany($oneTimeUser, $company);
        $this->userExpirationRepository->updateOrCreate($oneTimeUser, Carbon::now()->addDays($days));

        return $oneTimeUser;
    }
}
