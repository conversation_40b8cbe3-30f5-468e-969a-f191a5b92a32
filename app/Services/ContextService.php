<?php

namespace App\Services;

use App\Account;
use App\Context;
use App\Exceptions\Context\ContextNotFoundException;
use App\Exceptions\UnauthorizedException;
use App\Repositories\ContextRepository;
use App\Repositories\UserRepository;
use App\User;
use App\ValueObject\Pagination\UsersIndexFilters;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

class ContextService
{
    public function __construct(private readonly ContextRepository $contextRepository)
    {
    }

    public function getByAccount(Account $account): Collection
    {
        return $this->contextRepository->getByAccount($account);
    }

    public function getForUser(int $id, User $user): Context
    {
        // Get the context.
        $context = $this->contextRepository->getById($id);
        if ($user->isAdminUser()) {
            return $context;
        }

        if ($user->account_id !== $context->account_id) {
            throw new UnauthorizedException('User is not from the same account');
        }

        return $context;
    }

    public function getForAccount(Account $account, int $contextId, array $with = []): Context
    {
        // Get the context.
        $context = $this->contextRepository->getById($contextId, $with);

        if ($account->id !== $context->account_id) {
            throw new UnauthorizedException('Context is not from the same account');
        }

        return $context;
    }

    /**
     * Get all companies from the given account where the given context can become a child.
     * @param Account $account
     * @param User $user
     * @param int $currentContextId
     * @return Collection
     * @throws ContextNotFoundException
     */
    public function getAvailableParents(Account $account, User $user, int $currentContextId): Collection
    {
        $currentContext = $this->contextRepository->getById($currentContextId);
        if ($currentContext->account_id !== $account->id) {
            throw new UnauthorizedException('Context #' . $currentContext->id . ' is not part of Account #' . $account->id); //phpcs:ignore
        }
        $withRoot = $user->isAdminUser() || $user->isManagerOfAccount($account);
        $allContexts = $this->getByAccount($account);

        $results = new Collection();
        /** @var Context $context */
        foreach ($allContexts as $context) {
            // Skip itself.
            if ($currentContext->id === $context->id) {
                continue;
            }

            // Skip root if not allowed to.
            if ($context->isMain() && !$withRoot) {
                continue;
            }

            // If context path starts with current context path + name
            // (eg: /Hix/ + Intern = /Hix/Intern) and current path is /Hix/Intern/
            if (Str::startsWith($context->path, $currentContext->path . $currentContext->name)) {
                continue;
            }

            $results->push($context);
        }

        return $results;
    }

    public function getSubContextForAccount(Account $account): Collection
    {
        return $this->contextRepository->getSubContextForAccount($account);
    }

    public function getAvailableUsersForContext(Context $context, UsersIndexFilters $filters = null)
    {
        $user_ids = $context->users->map(
            function (User $user) {
                return $user->id;
            }
        )->toArray();

        $query = $context->account->users()->whereNotIn(User::ID, $user_ids);

        if ($filters !== null) {
            $userRepository = \App::make(UserRepository::class);
            $query = $userRepository->indexFilters($query, $filters);
        }

        return $query->get();
    }
}
