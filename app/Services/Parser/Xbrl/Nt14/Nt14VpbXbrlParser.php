<?php

namespace App\Services\Parser\Xbrl\Nt14;

use App\Services\Parser\Xbrl\AbstractVpbXbrlParser;

class Nt14VpbXbrlParser extends AbstractVpbXbrlParser
{
    public const TAXONOMY_NT14 = 'http://www.nltaxonomie.nl/nt14/bd/20191211/entrypoints/bd-rpt-vpb-aangifte-2019.xsd';
    public const TAXONOMY_NT14_VA = 'http://www.nltaxonomie.nl/nt14/bd/20191211/entrypoints/bd-rpt-vpb-verzoekwijzigingva-2020.xsd'; // phpcs:ignore

    public const SUPPORTED_TAXONOMIES = [
        self::TAXONOMY_NT14,
        self::TAXONOMY_NT14_VA,
    ];

    public const NAMESPACE_BDI = 'http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-data';

    public function getMessageType(): string
    {
        $taxonomy_url = self::extractTaxonomyUrl($this->dom);
        if ($taxonomy_url === self::TAXONOMY_NT14_VA) {
            return static::MESSAGE_TYPE_VPB_VA;
        }
        return static::MESSAGE_TYPE_VPB;
    }
}
