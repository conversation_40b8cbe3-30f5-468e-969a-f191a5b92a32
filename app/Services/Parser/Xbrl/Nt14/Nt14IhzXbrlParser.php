<?php

namespace App\Services\Parser\Xbrl\Nt14;

use App\Services\Parser\Xbrl\AbstractIhzXbrlParser;

class Nt14IhzXbrlParser extends AbstractIhzXbrlParser
{
    public const TAXONOMY_NT14 = 'http://www.nltaxonomie.nl/nt14/bd/20191211/entrypoints/bd-rpt-ihz-aangifte-2019.xsd';
    public const TAXONOMY_NT14_VA = 'http://www.nltaxonomie.nl/nt14/bd/20200916/entrypoints/bd-rpt-ihz-verzoekwijzigingva-2021.xsd'; // phpcs:ignore

    protected const SUPPORTED_TAXONOMIES = [
        self::TAXONOMY_NT14,
        self::TAXONOMY_NT14_VA,
    ];
    public const NAMESPACE_BDI = 'http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-data';
}
