<?php

namespace App\Services\Parser\Xbrl;

use App\Exceptions\InvalidXbrlException;
use App\Logging\Channels\ServiceLog;
use DOMXPath;
use DOMNode;

trait XpathElementTrait
{
    public static function getElementValue(
        DOMXpath $xpath,
        string $query,
        ?DOMNode $contextNode = null,
        bool $logError = true
    ): ?string {
        try {
            $elements = $xpath->query($query, $contextNode);
        } catch (\Throwable  $e) {
            if ($logError) {
                ServiceLog::warning($e->getMessage() . '.  query: ' . $query);
            }
            return null;
        }
        if ($elements && $elements->count() === 1) {
            return $elements->item(0)->textContent;
        }
        return null;
    }

    public static function setElementValue(
        DOMXpath $xpath,
        string $query,
        string $value,
        ?DOMNode $contextNode = null,
        bool $logError = true
    ): ?string {
        try {
            $elements = $xpath->query($query, $contextNode);
        } catch (\Throwable $e) {
            if ($logError) {
                ServiceLog::warning($e->getMessage() . '.  query: ' . $query);
            }
            return null;
        }
        if ($elements && $elements->count() === 1) {
            $element = $elements->item(0);
            if ($element->nodeType === XML_ELEMENT_NODE) {
                return $elements->item(0)->textContent = $value;
            } elseif ($element->nodeType === XML_ATTRIBUTE_NODE) {
                $element->parentNode->setAttribute($element->nodeName, $value);
            }
        }
        return null;
    }

    public static function getElement(
        DOMXpath $xpath,
        string $query,
        ?DOMNode $contextNode = null,
        bool $logError = true
    ): ?\DOMNode {
        try {
            $elements = $xpath->query($query, $contextNode);
        } catch (\Throwable $e) {
            if ($logError) {
                ServiceLog::warning($e->getMessage() . '.  query: ' . $query);
            }
            return null;
        }
        if ($elements && $elements->count() === 1) {
            return $elements->item(0);
        }
        return null;
    }

    public static function verifyCurrency(DOMXPath $xpath, string $unitQuery): string
    {
        $unitValue = self::getElementValue($xpath, $unitQuery);
        if ($unitValue === 'iso4217:EUR') {
            return 'EUR';
        } else {
            throw new InvalidXbrlException('Invalid currency ' . $unitValue . '! Only iso4217:EUR is supported.');
        }
    }
}
