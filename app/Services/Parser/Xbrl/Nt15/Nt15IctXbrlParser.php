<?php

namespace App\Services\Parser\Xbrl\Nt15;

use App\Services\Parser\Xbrl\AbstractIctXbrlParser;

class Nt15IctXbrlParser extends AbstractIctXbrlParser
{
    public const TAXONOMY_NT15 = 'http://www.nltaxonomie.nl/nt15/bd/20201209/entrypoints/bd-rpt-icp-opgaaf-2021.xsd';
    public const NAMESPACE_BDT = 'http://www.nltaxonomie.nl/nt15/bd/20201209/dictionary/bd-tuples';

    public const SUPPORTED_TAXONOMIES = [
        self::TAXONOMY_NT15,
    ];

    public const NAMESPACE_BDI  = 'http://www.nltaxonomie.nl/nt15/bd/20201209/dictionary/bd-data';
}
