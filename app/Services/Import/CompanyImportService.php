<?php

namespace App\Services\Import;

use App\Account;
use App\Company;
use App\Exceptions\Company\DuplicateNameException;
use App\Exceptions\UnauthorizedException;
use App\Models\CompanyOcrEmail;
use App\Repositories\CompanyOcrEmailRepository;
use App\Services\CompanyIdentifierSyncService;
use App\Services\CompanyTagsService;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class CompanyImportService extends AbstractImportService
{
    //these columns will be merged with every CSV line so that every item will at least have these defaults.
    public array $columns = [
        'id' => null,
        'name' => null,
        'address' => null,
        'kvk_number' => null,
        'fiscal_number' => null,
        'vat_number' => null,
        'wage_tax_number' => null,
        'bsn_number' => null,
        'ocr_email_purchase' => null,
        'ocr_email_sale' => null,
        'internal_client_id' => null,
        'tags' => null
    ];

    public array $requiredHeaders = ['name'];
    private CompanyIdentifierSyncService $companySyncService;
    private CompanyTagsService $companyTagsService;
    private CompanyOcrEmailRepository $companyOcrEmailRepository;

    public function __construct(?Account $account = null)
    {
        if (!is_null($account)) {
            $this->setAccount($account);
        }
        $this->companySyncService = resolve(CompanyIdentifierSyncService::class);
        $this->companyTagsService = resolve(CompanyTagsService::class);
        $this->companyOcrEmailRepository = resolve(CompanyOcrEmailRepository::class);
    }

    /**
     * Import companies. On error it will keep going to the next one.
     * @return int Number of lines processed.
     */
    public function importLines(): int
    {
        $this->extractLinesFromFile();
        $c = 0;
        foreach ($this->lines as $line) {
            $c++;
            try {
                $company = $this->arrayToCompany($this->account, $line);
                $company->save();

                if (
                    !empty($line['ocr_email_purchase']) &&
                    (!$company->ocrEmailPurchase() || $company->ocrEmailPurchase()?->email !== $line['ocr_email_purchase']) //phpcs:ignore
                ) {
                    $this->companyOcrEmailRepository->updateOrCreate(
                        $company,
                        $line['ocr_email_purchase'],
                        CompanyOcrEmail::TYPE_PURCHASE
                    );
                }

                if (
                    !empty($line['ocr_email_sale']) &&
                    (!$company->ocrEmailSale() || $company->ocrEmailSale()?->email !== $line['ocr_email_sale'])
                ) {
                    $this->companyOcrEmailRepository->updateOrCreate(
                        $company,
                        $line['ocr_email_sale'],
                        CompanyOcrEmail::TYPE_SALE
                    );
                }

                // import company tags
                if (isset($line['tags'])) {
                    $this->companyTagsService->updateCompanyTags($company, explode(',', $line['tags']));
                }

                if (isset($line[Company::VAT_NUMBER])) {
                    $this->companySyncService->createOrSyncVatNumbers($company->id, $line[Company::VAT_NUMBER]);
                }
                if (isset($line[Company::FISCAL_NUMBER])) {
                    $this->companySyncService->createOrSyncFiscalNumbers($company->id, $line[Company::FISCAL_NUMBER]);
                }
                if (isset($line[Company::WAGE_TAX_NUMBER])) {
                    $this->companySyncService->createOrSyncWageTaxNumbers(
                        $company->id,
                        $line[Company::WAGE_TAX_NUMBER]
                    );
                }
                if (isset($line[Company::BSN_NUMBER])) {
                    $this->companySyncService->createOrSyncBsnNumbers(
                        $company->id,
                        $line[Company::BSN_NUMBER],
                        ignoreInvalid: true
                    );
                }
                if (isset($line[Company::KVK_NUMBER])) {
                    $this->companySyncService->createOrSyncKvkNumbers(
                        $company,
                        $line[Company::KVK_NUMBER],
                        ignoreInvalid: true
                    );
                }

                $this->info($c, $c . '. Imported company #' . $company->id . ' ' . $company->name);
                $message = trans('company.import.processed', ['id' => $company->id, 'name' => $company->name], $this->lang); //phpcs:ignore
                $this->notifyUser('info', $c, $c . '. ' . $message);
                $this->success++;
            } catch (\Throwable $e) {
                $this->warn($c, $c . '. ' . $e->getMessage());

                if ($e instanceof ModelNotFoundException) {
                    $errorMessage = trans('company.import.not_found', $line, $this->lang);
                } elseif (method_exists($e, 'getLocalizedMessage')) {
                    $errorMessage = $e->getLocalizedMessage();
                } else {
                    $errorMessage = $e->getMessage();
                }
                $message = trans('common.import.skipped', ['message' => $errorMessage], $this->lang);
                $this->notifyUser('warning', $c, $c . '. ' . $message);
                $this->warn++;
            }
        }

        $message = trans('common.import.done', ['success' => $this->success, 'warning' => $this->warn], $this->lang);
        $this->notifyUser('info', $c, $message);

        return $c;
    }

    /**
     * Get a company filled with data from array.
     * This method does not save the company!
     * @param Account $account
     * @param array $line With keys as defined in the $columns property.
     * @return Company Existing or newly created company.
     */
    public function arrayToCompany(Account $account, array $line): Company
    {
        $company = null;
        $name = $this->sanitize($line['name']);
        $existsQuery = Company::where(Company::ACCOUNT_ID, $account->id)->where('name', $name);
        if (isset($line['id'])) {
            $id = trim($line['id']);
            if ($id !== '') {
                $company = Company::findOrFail($id);
                if ($company->account_id != $account->id) {
                    throw new UnauthorizedException('Cannot load company #' . $id . ' from another account.');
                }
                $existsQuery->where(Company::ID, '!=', $id);
            }
        }

        if ($existsQuery->exists()) {
            throw new DuplicateNameException('Company with same name exists: ' . $name, params: ['name' => $name]); // phpcs:ignore
        }

        if (is_null($company)) {
            $company = new Company();
            $company->account_id = $account->id;
        }

        // These keys will always exist because they are in the $columns property
        $company->name = $name;
        $company->address = $this->sanitize($line['address']);
        $company->internal_client_id = $this->sanitize($line['internal_client_id']);

        //$ocrEmailPurchase = $this->sanitize(!empty($line['ocr_email_purchase']) ? $line['ocr_email_purchase'] : null);
        //if (!empty($ocrEmailPurchase)) {
        //    $this->companyOcrEmailRepository->updateOrCreate(
        //        $company,
        //        $ocrEmailPurchase,
        //        CompanyOcrEmail::TYPE_PURCHASE
        //    );
        //}
        //$ocrEmailSale = $this->sanitize(!empty($line['ocr_email_sale']) ? $line['ocr_email_sale'] : null);
        //if (!empty($ocrEmailSale)) {
        //    $this->companyOcrEmailRepository->updateOrCreate(
        //        $company,
        //        $ocrEmailSale,
        //        CompanyOcrEmail::TYPE_PURCHASE
        //    );
        //}

        return $company;
    }
}
