<?php

namespace App\Services\Import;

use App\Account;
use App\AccountService;
use App\Company;
use App\CompanyUser;
use App\Context;
use App\Factories\Models\UserFactory;
use App\Logging\Channel;
use App\Repositories\CompanyRepository;
use App\Repositories\UserRepository;
use App\Services\CompanyService;
use App\Services\CompanyTagsService;
use App\Services\CompanyUserService;
use App\Services\MembershipService;
use App\Services\RemoteWorker\Processor\AbstractAlureProcessor;
use App\Services\Service\Crm\AlureOnPremiseProvider;
use App\Services\Service\Crm\CrmStoreNewUserTrait;
use App\User;
use App\ValueObject\AlureOnPremise\EmailRecord;
use App\ValueObject\AlureOnPremise\PhoneRecord;
use App\ValueObject\AlureOnPremise\RelationConnectionRecord;
use App\ValueObject\AlureOnPremise\RelationRecord;

class AlureOnPremiseImportService extends AbstractAlureProcessor
{
    use CrmStoreNewUserTrait;

    public const ORIGIN_NAME = 'Alure on premise';

    /** @var EmailRecord[] */
    protected array $emailRecords;
    /** @var PhoneRecord[] */
    protected array $phoneRecords;
    /** @var RelationRecord[] */
    protected array $relationRecords;
    /** @var CompanyUser[] */
    protected array $updatedCompanyUsers;

    /** @var RelationRecord[] */
    protected array $skippedCompanies;

    /** @var RelationRecord[] */
    protected array $skippedUsers;

    protected Account $account;
    protected Context $context;
    protected ?AccountService $accountService;

    public function __construct(
        private readonly CompanyService $companyService,
        private readonly CompanyTagsService $companyTagsService,
        private readonly CompanyUserService $companyUserService,
        private readonly CompanyRepository $companyRepository,
        private readonly MembershipService $membershipService,
        private readonly UserRepository $userRepository
    ) {
    }

    public const FILE_RELINTERNET = 'alure-relinternet.json';
    public const FILE_RELTELEFOON = 'alure-reltelefoon.json';
    public const FILE_RELATIE = 'alure-relatie.json';
    public const FILE_RELCON = 'alure-relcon.json';
    public const FILE_RELATIE_VERVALLEN_USERS = 'alure-relatie-vervallen-users.json';
    public const FILE_RELATIE_VERVALLEN_COMPANIES = 'alure-relatie-vervallen-companies.json';

    public const REQUIRED_FILES = [
        self::FILE_RELINTERNET,
        self::FILE_RELTELEFOON,
        self::FILE_RELATIE,
        self::FILE_RELCON,
        self::FILE_RELATIE_VERVALLEN_USERS,
        self::FILE_RELATIE_VERVALLEN_COMPANIES,
    ];

    public function import(string $directory, Context $context, ?AccountService $accountService = null): void
    {
        $this->resetLists();
        $this->directory = $directory;
        $this->account = $context->account;
        $this->context = $context;
        $this->accountService = $accountService;

        if (!$this->checkRequirements()) {
            $this->log('Aborting import after failed requirements check');
            return;
        }

        $this->logConfig();

        $this->createRelationRecords();
        $this->log('Loaded ' . count($this->relationRecords) . ' relation records');

        $connections = $this->createRelationConnectionRecords();
        $this->log('Loaded ' . count($connections) . ' relation connection records');

        $success = $this->createEmailRecords();
        $this->log('Loaded ' . $success . ' out of ' . count($this->emailRecords) . ' email records');
        $success = $this->createPhoneRecords();
        $this->log('Loaded ' . $success . ' out of ' . count($this->phoneRecords) . ' phone records');

        $this->log('Preparing to store ' . count($this->relationRecords) . ' records');
        $this->storeRelationRecords();

        $this->cleanupSkippedCompanies();
        $this->cleanupSkippedUsers();
        $this->cleanupDeletedCompanies();
        $this->cleanupDeletedUsers();
        $this->cleanupCompanyUsers();
        $this->storeLogFile(Channel::ALURE_ON_PREMISE_IMPORT . '.log');
    }

    protected function logConfig(): void
    {
        $contypeIds = $this->accountService->getProperty(AlureOnPremiseProvider::PROPERTY_ALURE_CONTYPE_IDS);
        if (is_array($contypeIds)) {
            $this->log(AlureOnPremiseProvider::PROPERTY_ALURE_CONTYPE_IDS . ': ' . implode(',', $contypeIds));
        }
        $only = $this->accountService->getProperty(AlureOnPremiseProvider::PROPERTY_ALURE_CONTYPE_ONLY);
        $this->log(AlureOnPremiseProvider::PROPERTY_ALURE_CONTYPE_ONLY . ': ' . ($only ? 'true' : 'false'));
    }

    /**
     * Initialize arrays every time an import starts so that the instance of the class can be re-used.
     * @return void
     */
    protected function resetLists(): void
    {
        $this->emailRecords = [];
        $this->phoneRecords = [];
        $this->relationRecords = [];
        $this->updatedCompanyUsers = [];
        $this->skippedCompanies = [];
        $this->skippedUsers = [];
    }

    public function createRelationRecords(): void
    {
        $list = $this->decodeJsonFile(static::FILE_RELATIE);
        foreach ($list as $key => &$item) {
            try {
                $record = new RelationRecord($item, $this->accountService->properties);
                $this->relationRecords[$record->id] = $record;
            } catch (\Throwable $e) {
                $this->log('Error processing relation UUID ' . $item[RelationRecord::KEY_UUID] . ' ' . $e::class . ' ' . $e->getMessage(), $key); // phpcs:ignore
            }
        }
    }

    /**
     * Get data from the table "RELCON". Add references between loaded RelationRecord objects.
     * Some connections are to itself or to soft deleted, relations. Those will not be added.
     * @return RelationConnectionRecord[] List of RelationConnections that were used to connect RelationRecords.
     * @throws \JsonException
     */
    public function createRelationConnectionRecords(): array
    {
        $list = $this->decodeJsonFile(static::FILE_RELCON);
        $relconRecords = [];
        foreach ($list as $key => &$item) {
            try {
                $relcon = new RelationConnectionRecord($item, $this->accountService->properties);
                if (!$relcon->shouldBeUsed()) {
                    $this->log('Skipping relation connection ' . $relcon->from . ' > ' . $relcon->to . ' with type ' . $relcon->type); // phpcs:ignore
                    continue;
                }

                // also register contypes of "broken" connections where the "to" relation is not present.
                if (isset($this->relationRecords[$relcon->from])) {
                    $this->relationRecords[$relcon->from]->addContypeId($relcon->type);
                    $this->log('Registering contype ' . $relcon->type . ' for relnr ' . $relcon->from);
                }

                // also register contypes of "broken" connection where the "from" relation is not present.
                if (isset($this->relationRecords[$relcon->to])) {
                    $this->relationRecords[$relcon->to]->addContypeId($relcon->type);
                    $this->log('Registering contype ' . $relcon->type . ' for relnr ' . $relcon->to);
                }

                if (isset($this->relationRecords[$relcon->from]) && isset($this->relationRecords[$relcon->to])) {
                    if ($this->relationRecords[$relcon->from]->shouldHaveCompany() && $this->relationRecords[$relcon->to]->shouldHaveUser()) {  // phpcs:ignore
                        $this->log('Adding relation connection ' . $relcon->from . ' > ' . $relcon->to . ' is from company to user'); // phpcs:ignore
                        if ($this->relationRecords[$relcon->from]->addRelated($this->relationRecords[$relcon->to])) {
                            $relconRecords[] = $relcon;
                        }
                    } elseif ($this->relationRecords[$relcon->to]->shouldHaveCompany() && $this->relationRecords[$relcon->from]->shouldHaveUser()) { // phpcs:ignore
                        $this->log('Adding relation connection ' . $relcon->from . ' > ' . $relcon->to . ' is from user to company');  // phpcs:ignore
                        if ($this->relationRecords[$relcon->to]->addRelated($this->relationRecords[$relcon->from])) {
                            $relconRecords[] = $relcon;
                        }
                    }
                }
            } catch (\Throwable $e) {
                $this->log('Error processing relation connection ' . $item[RelationConnectionRecord::KEY_FROM] . '->' . $item[RelationConnectionRecord::KEY_TO] . ' ' . $e::class . ' ' . $e->getMessage(), $key); // phpcs:ignore
            }
        }
        return $relconRecords;
    }

    /**
     * @return int Number of email records that were added to an existing relation object
     * @throws \JsonException
     */
    public function createEmailRecords(): int
    {
        $list = $this->decodeJsonFile(static::FILE_RELINTERNET);
        $success = 0;
        foreach ($list as $key => $item) {
            try {
                $record = new EmailRecord($item);
                $this->emailRecords[] = $record;
                $success += $this->addEmailToRelation($record, $key);
            } catch (\Throwable $e) {
                $this->log('Error processing email UUID ' . $item[EmailRecord::KEY_UUID] . ' ' . $e::class . ' ' . $e->getMessage(), $key); // phpcs:ignore
            }
        }
        return $success;
    }

    /**
     * @return int Number of phone records that were added to an existing relation object
     * @throws \JsonException
     */
    public function createPhoneRecords(): int
    {
        $list = $this->decodeJsonFile(static::FILE_RELTELEFOON);
        $success = 0;
        foreach ($list as $key => $item) {
            try {
                $record = new PhoneRecord($item);
                $this->phoneRecords[] = $record;
                $success += $this->addPhoneToRelation($record, $key);
            } catch (\Throwable $e) {
                $this->log('Error processing phone UUID ' . $item[PhoneRecord::KEY_TELNR] . ' ' . $e::class . ' ' . $e->getMessage(), $key); // phpcs:ignore
            }
        }
        return $success;
    }

    /**
     * Add e-mail record to relation record if a RelationRecord with id that matches "relation" property of EmailRecord.
     * @param EmailRecord $record
     * @param int|null $key Value is only used to add som context to log message.
     * @return bool TRUE on success, FALSE on failure.
     */
    public function addEmailToRelation(EmailRecord $record, ?int $key = null): bool
    {
        $relation = $this->getRelationrecord($record->relation);
        if ($relation instanceof RelationRecord) {
            $relation->addEmail($record);
            return true;
        }
        $this->log('Cannot find relation ' . $record->relation . ' to add email UUID ' . $record->uuid . ' to. It is probably soft deleted.',  $key); // phpcs:ignore
        return false;
    }

    /**
     * Add phone record to relation record if a RelationRecord with id that matches "relation" property of PhoneRecord.
     * @param PhoneRecord $record
     * @param int|null $key Value is only used to add som context to log message.
     * @return bool TRUE on success, FALSE on failure.
     */
    public function addPhoneToRelation(PhoneRecord $record, ?int $key = null): bool
    {
        $relation = $this->getRelationrecord($record->relation);
        if ($relation instanceof RelationRecord) {
            $relation->addPhone($record);
            return true;
        }
        $this->log('Cannot find relation ' . $record->relation . ' to add phone UUID ' . $record->uuid . ' to. It is probably soft deleted.', $key); // phpcs:ignore
        return false;
    }

    /**
     * Find a RelationRecord that is currently loaded in memory
     * @param int $id
     * @return RelationRecord|null
     */
    public function getRelationrecord(int $id): ?RelationRecord
    {
        if (isset($this->relationRecords[$id])) {
            return $this->relationRecords[$id];
        }
        return null;
    }

    public function storeRelationRecords(): void
    {
        foreach ($this->relationRecords as $key => $item) {
            try {
                if ($item->shouldHaveUser()) {
                    $this->storeUser($item, $key);
                } else {
                    $this->log('Skipping storing user for (' . $item->id . ') ' . $item->uuid . ' with type ' . $item->type . ' contype IDs: ' . implode(',', $item->contypeIds)); // phpcs:ignore
                    $this->skippedUsers[] = $item;
                }
            } catch (\Throwable $e) {
                $this->log('Error storing user from relation (' . $item->id . ') UUID ' . $item->uuid . ' ' . $e::class . ' ' . $e->getMessage(), $key); // phpcs:ignore
            }

            try {
                if ($item->shouldHaveCompany()) {
                    $this->storeCompany($item, $key);
                } else {
                    $this->log('Skipping storing company for (' . $item->id . ') ' . $item->uuid . ' with type ' . $item->type . ' contype IDs: ' . implode(',', $item->contypeIds)); // phpcs:ignore
                    $this->skippedCompanies[] = $item;
                }
            } catch (\Throwable $e) {
                $this->log('Error storing company from relation (' . $item->id . ') UUID ' . $item->uuid . ' ' . $e::class . ' ' . $e->getMessage(), $key); // phpcs:ignore
            }
        }

        foreach ($this->relationRecords as $key => $item) {
            try {
                if ($item->shouldHaveCompany()) {
                    $this->storeCompanyUsers($item);
                }
            } catch (\Throwable $e) {
                $this->log('Error storing companyUser from relation (' . $item->id . ') UUID ' . $item->uuid . ' ' . $e::class . ' ' . $e->getMessage(), $key); // phpcs:ignore
            }
        }
    }

    private function storeUser(RelationRecord $record, ?int $key = null): ?User
    {
        $email = $record->email();
        // If the email is empty we move to the next entry because it is a mandatory field
        if (empty($email)) {
            $this->log('Skipping user for relation with UUID (' . $record->id . ') ' . $record->uuid . ' without email', $key); // phpcs:ignore
            return null;
        }

        if (empty($record->firstName)) {
            $this->log('Skipping user for relation with UUID (' . $record->id . ') ' . $record->uuid . ' without first name', $key); // phpcs:ignore
            return null;
        }

        $user = null;
        $userData = [
            User::ACCOUNT_ID => $this->account->id,
            User::EMAIL => $email,
            User::FIRST_NAME => $record->firstName,
            User::LAST_NAME => $record->lastName,
            User::MOBILE => $record->mobile(),
            User::EXTERNAL_ID => $record->uuid,
            User::INTERNAL_CLIENT_ID => $record->internalClientId
        ];

        try {
            $user = $this->userRepository->findByExternalId($this->account, $record->uuid);
            if ($user instanceof User && !$this->originIsAlure($user)) {
                $this->log('User #' . $user->id . ' matches UUID ' . $record->uuid . ' but origin does not match');
                $user = null;
            }
            if (is_null($user)) {
                // Auth ID, is_external & settings should only be set on create, not update
                $userData[User::AUTH_ID] = $record->authId();
                $userData[User::IS_EXTERNAL] = $record->isExternalUser;
                $userData[User::SETTINGS] = ['origin' => self::ORIGIN_NAME];
                $user = UserFactory::createFromArray($this->account, $userData);
                $user = $this->storeNewUser($user);
                $this->log('Created user #' . $user->id . ' from (' . $record->id . ') UUID ' . $record->uuid, $key); // phpcs:ignore
                $record->user = $user;
            } else {
                $this->userRepository->update($user, $userData);
                $this->log('Updated user #' . $user->id . ' from (' . $record->id . ') UUID ' . $record->uuid, $key);
                $record->user = $user;
            }

            // Creates membership if it doesn't exist yet
            $membership = $this->membershipService->findMembership($user, $this->context);
            if (empty($membership)) {
                $this->membershipService->createMembership($user->id, $this->context->id);
            }
        } catch (\Throwable $e) {
            $this->log('Error while processing user (' . $record->id . ') UUID ' . $record->uuid . ': ' . $e::class . ' ' . $e->getMessage(), $key); //phpcs:ignore
        }
        return $user;
    }

    private function storeCompany(RelationRecord $record, ?int $key = null): ?Company
    {
        $externalId = $record->uuid;
        try {
            if (!isset($externalId)) {
                throw new \UnexpectedValueException('Record customer ID not set.');
            }
            // Tries to find company by internal client ID. If it is not found creates one, else updates
            $company = $this->companyRepository->findByExternalId($this->account, $externalId);
            if (empty($company)) {
                $company = $this->companyService->storeCompany(
                    account: $this->account,
                    name: $record->companyName,
                    address: $record->address,
                    internalClientId: $record->internalClientId,
                    settings: ['origin' => self::ORIGIN_NAME],
                    externalId: $externalId
                );

                $this->companyTagsService->appendTags($company, [self::ORIGIN_NAME]);
                $this->log('Created company #' . $company->id . ' (' . $company->name . ') from (' . $record->id . ') UUID ' . $record->uuid, $key); // phpcs:ignore
            } else {
                $company = $this->companyService->updateCompany(
                    company: $company,
                    name: $record->companyName,
                    address: $record->address,
                    internalClientId: $record->internalClientId
                );
                $this->log('Updated company #' . $company->id . ' from (' . $record->id . ') UUID ' . $record->uuid, $key); // phpcs:ignore
            }

            try {
                // store identifiers separately so we can handle any exceptions better
                $this->companyService->updateCompanyFiscalIdentifierNumbers(
                    company: $company,
                    kvkNumber: $record->kvkNumber,
                    vatNumber: $record->vatNumber,
                    fiscalNumber: $record->fiscalNumber,
                    wageTaxNumber: $record->wageTaxNumber,
                    bsnNumber: $record->bsn,
                    accountService: $this->accountService
                );
            } catch (\Throwable $e) {
                $this->log('Error storing identifiers for company #' . $company->id . ' from (' . $record->id . ') UUID ' . $record->uuid . ' ' . $e::class . ' ' . $e->getMessage(), $key); // phpcs:ignore
            }

            $record->company = $company;
            return $company;
        } catch (\Throwable $e) {
            $this->log('Skipped storing company from (' . $record->id . ') UUID ' . $record->uuid . ' ' . $e::class . ' ' . $e->getMessage(), $key); // phpcs:ignore
        }
        return null;
    }

    /**
     * Connect users to companies after they have been saved in the database independently already.
     * @param RelationRecord $record
     * @return void
     */
    public function storeCompanyUsers(RelationRecord $record): void
    {
        $company = $record->company;
        if ($company instanceof Company) {
            $key = 1;
            foreach ($record->getRelatedUsers() as $user) {
                $companyUser = $this->companyUserService->addUserToCompany($user, $company, $this->accountService);
                if ($companyUser instanceof CompanyUser) {
                    $this->updatedCompanyUsers[] = $companyUser;
                    $this->log('Added user #' . $user->id . ' to company #' . $company->id . ' from (' . $record->id . ') UUID ' . $record->uuid, $key); // phpcs:ignore
                } else {
                    $this->log('Unable to store CompanyUser. Null returned for relation (' . $record->id . ') UUID ' . $record->uuid, $key); // phpcs:ignore
                }
                $key++;
            }
        } else {
            $this->log('Unable to store CompanyUser. Relation (' . $record->id . ') UUID ' . $record->uuid . ' has no company model set.'); // phpcs:ignore
        }
    }

    /**
     * Remove any users that were marked as "vervallen" in Alure.
     * @return void
     * @throws \JsonException
     */
    public function cleanupDeletedUsers(): void
    {
        $list = $this->decodeJsonFile(static::FILE_RELATIE_VERVALLEN_USERS);
        $this->log('Loaded ' . count($list) . ' soft deleted users to check');
        $c = 0;
        foreach ($list as $key => $item) {
            $user = $this->userRepository->findByExternalId($this->account, $item[RelationRecord::KEY_UUID]);
            if ($user instanceof User) {
                if ($this->originIsAlure($user)) {
                    $this->userRepository->delete($user);
                    $this->log('Deleted user #' . $user->id . ' because its origin and external ID match UUID ' . $item[RelationRecord::KEY_UUID], $key); // phpcs:ignore
                    $c++;
                } else {
                    $this->log('Did not archive/delete user #' . $user->id . '. UUID ' . $item[RelationRecord::KEY_UUID] . ' matched but origin not.'); // phpcs:ignore
                }
            }
        }
        $this->log('Deleted ' . $c . ' users.');
    }

    /**
     * Remove any companies that were marked as "vervallen" in Alure.
     * @return void
     * @throws \JsonException
     */
    public function cleanupDeletedCompanies(): void
    {
        $list = $this->decodeJsonFile(static::FILE_RELATIE_VERVALLEN_COMPANIES);
        $this->log('Loaded ' . count($list) . ' soft deleted companies to check.');
        $c = 0;
        foreach ($list as $key => $item) {
            $externalId = $item[RelationRecord::KEY_UUID];
            if (isset($externalId)) {
                $company = $this->companyRepository->findByExternalId($this->account, $externalId);
                if ($company instanceof Company) {
                    if ($this->originIsAlure($company)) {
                        if ($this->companyService->deleteOrArchive($company)) {
                            $this->log('Archived/deleted company #' . $company->id . ' because matches deleted ' . $externalId, $key); // phpcs:ignore
                            $c++;
                        }
                    } else {
                        $this->log('Did not archive/delete company #' . $company->id . '. UUID ' . $externalId . ' matched but origin not.'); // phpcs:ignore
                    }
                }
            }
        }
        $this->log('Archived/deleted ' . $c . ' companies.');
    }

    /**
     * Loop through all users that were skipped. Check if they exist in the database and delete them there too.
     * @return void
     */
    public function cleanupSkippedUsers(): void
    {
        $this->log('Loaded ' . count($this->skippedUsers) . ' skipped users to check');
        $c = 0;
        foreach ($this->skippedUsers as $r) {
            $user = $this->userRepository->findByExternalId($this->account, $r->uuid);
            if ($user instanceof User) {
                if ($this->originIsAlure($user)) {
                    $this->userRepository->delete($user);
                    $this->log('Deleted user #' . $user->id . ' because its origin and external ID match UUID ' . $r->uuid); // phpcs:ignore
                    $c++;
                } else {
                    $this->log('Did not archive/delete user #' . $user->id . '. UUID ' . $r->uuid . ' matched but origin not.'); // phpcs:ignore
                }
            }
        }
        $this->log('Deleted ' . $c . ' skipped users.');
    }

    /**
     * Loop through all companies that were skipped. Check if they exist in the database and delete them there too.
     * @return void
     */
    public function cleanupSkippedCompanies(): void
    {
        $this->log('Loaded ' . count($this->skippedCompanies) . ' soft deleted companies to check.');
        $c = 0;
        foreach ($this->skippedCompanies as $r) {
            $company = $this->companyRepository->findByExternalId($this->account, $r->uuid);
            if ($company instanceof Company) {
                if ($this->originIsAlure($company)) {
                    if ($this->companyService->deleteOrArchive($company)) {
                        $this->log('Archived/deleted company #' . $company->id . ' because ' . $r->uuid . ' was skipped'); // phpcs:ignore
                        $c++;
                    }
                } else {
                    $this->log('Did not archive/delete company #' . $company->id . '. UUID ' . $r->uuid . ' matched but origin not.'); // phpcs:ignore
                }
            }
        }
        $this->log('Archived/deleted ' . $c . ' skipped companies.');
    }

    public function cleanupCompanyUsers(): void
    {
        if (is_null($this->accountService)) {
            $this->log('Skipping cleaning up CompanyUsers because AccountService property is not set');
            return;
        }

        $this->log('Loaded ' . count($this->updatedCompanyUsers) . ' updated CompanyUsers to keep.');
        $removed = $this->companyUserService->cleanupAccountService($this->accountService, $this->updatedCompanyUsers);
        $this->log('Deleted ' . count($removed) . ' CompanyUsers from AccountService #' . $this->accountService->id);
    }

    private function originIsAlure(Company|User $model): bool
    {
        if ($model->settings instanceof \Illuminate\Database\Eloquent\Casts\ArrayObject) {
            $settings = $model->settings->toArray();
        } elseif (is_array($model->settings)) {
            $settings = $model->settings;
        } else {
            return false;
        }

        return isset($settings['origin'])
            && $settings['origin'] === self::ORIGIN_NAME;
    }
}
