<?php

namespace App\Services;

use App\AccountService;
use App\Company;
use App\Repositories\Redis\SyncLockRepository;
use App\User;
use App\Support\Carbon;

class SyncLockService
{
    private const KEY_PREFIX = 'sl:sync-lock:';
    private SyncLockRepository $syncLockRepository;

    public function __construct(SyncLockRepository $syncLockRepository)
    {
        $this->syncLockRepository = $syncLockRepository;
    }

    public function isLocked(AccountService $accountService, ?Company $company = null): bool
    {
        // If environment is local, staging or preprod, we don't want to wait to synchronize a service again
        if (app()->environment('local', 'staging', 'preprod')) {
            return false;
        }

        $key = $this->key($accountService, $company);
        $lock = $this->syncLockRepository->get($key);

        if (isset($company)) {
            //For companies, we also check need to check if account service is locked.
            $accountServiceLock = $this->syncLockRepository->get($this->key($accountService));
            if (!empty($lock) || !empty($accountServiceLock)) {
                return true;
            }
        }
        if (!empty($lock)) {
            return true;
        }
        return false;
    }

    /**
     * Sets a sync lock in redis for Account service and company if present, and returns the lock timestamp.
     * @param User $user
     * @param AccountService $accountService
     * @param Company|null $company
     * @return string|null
     */
    public function lock(User $user, AccountService $accountService, ?Company $company = null): ?string
    {
        $key = $this->key($accountService, $company);
        $ttl = $accountService->service->getMinutesBetweenSynchronization() * 60; //has to be in seconds.
        $timestamp = Carbon::now()->toDateTimeString();
        $data = [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
            ],
            'timestamp' => $timestamp,
        ];
        if ($this->syncLockRepository->set($key, $data, $ttl)) {
            return $timestamp;
        }
        return null;
    }

    /**
     * Returns array with keys: timestamp, user['id'] & user['name'].
     * @param AccountService $accountService
     * @param Company|null $company
     * @return array
     */
    public function getLockInfo(AccountService $accountService, ?Company $company = null): ?array
    {
        $key = $this->key($accountService, $company);
        $lock = $this->syncLockRepository->get($key);

        if (isset($company) && empty($lock)) {
            $lock = $this->syncLockRepository->get($this->key($accountService));
        }

        return json_decode($lock, true);
    }

    /** Returns a key for redis cache based on account_service_id and company_id if present.
     * Eg: sl:sync-lock:account_service_id:2907, sl:sync-lock:account_service_id:2907:company_id:1
     * @param AccountService $accountService
     * @param Company|null $company
     * @return string
     */
    public function key(AccountService $accountService, Company $company = null): string
    {
        $key = self::KEY_PREFIX . 'account_service_id:' . $accountService->id;
        if (isset($company)) {
            $key .= ':company_id:' . $company->id;
        }
        return $key;
    }

    /** Deletes sync locks
     * @param AccountService $accountService
     * @param Company|null $company
     * @return bool
     */
    public function unlock(AccountService $accountService, ?Company $company = null): bool
    {
        $key = $this->key($accountService, $company);
        return $this->syncLockRepository->remove($key);
    }

    public static function calculateMinutesBeforeNextSynchronization(
        string $timestamp,
        AccountService $accountService
    ): int {
        $seconds = Carbon::parse($timestamp)
            ->addMinutes($accountService->service->getMinutesBetweenSynchronization())
            ->diffInSeconds(Carbon::now(), true);
        return (int)ceil($seconds / 60);
    }
}
