<?php

namespace App\Services\Scim;

use App\Account;
use App\Context;
use App\Exceptions\Api\Exception;
use App\Exceptions\Api\Groups\GroupAlreadyExistsException;
use App\Exceptions\Api\NotFoundException;
use App\Exceptions\Api\UnauthorizedException;
use App\Exceptions\Context\ContextNotFoundException;
use App\Exceptions\Scim\MemberDoesNotExistsException;
use App\Exceptions\Scim\MutabilityException;
use App\Exceptions\Scim\UniquenessException;
use App\Exceptions\Users\UserNotFoundException;
use App\Logging\Channels\ScimLog;
use App\Repositories\ContextRepository;
use App\Repositories\MembershipsRepository;
use App\Repositories\UserRepository;
use App\User;
use App\ValueObject\Scim\ScimUpdateGroupOperation;
use Illuminate\Database\Eloquent\Collection;
use Log;

class ScimGroupService
{
    private ContextRepository $contextRepository;
    private MembershipsRepository $membershipsRepository;
    private UserRepository $userRepository;
    private ScimUserService $scimUsersService;

    public function __construct(
        ContextRepository $contextRepository,
        MembershipsRepository $membershipsRepository,
        UserRepository $userRepository,
        ScimUserService $scimUsersService
    ) {
        $this->contextRepository = $contextRepository;
        $this->membershipsRepository = $membershipsRepository;
        $this->userRepository = $userRepository;
        $this->scimUsersService = $scimUsersService;
    }

    public function getAll(Account $account, array $filter = null): Collection
    {
        return $this->contextRepository->getByAccount($account, $filter, true);
    }

    /**
     * Business Logic to create a group by SCIM.
     *
     * @param Account $account - Account of the Group.
     * @param array $attributes - Attributes of the Group.
     * @param array|null $membersExternalIds - ExternalIds of the members.
     *
     * @return Context
     * @throws GroupAlreadyExistsException
     * @throws MemberDoesNotExistsException
     * @throws UserNotFoundException
     */
    public function create(
        Account $account,
        array $attributes,
        array $membersExternalIds = null
    ): Context {
        // Get the Root context of the current account.
        $contextRoot = $this->contextRepository->getRoot($account);

        // Add the Root context to be the parent of the group that will be created and the scim_id.
        $attributes[Context::PARENT_ID] = $contextRoot->id;

        // Create the group.
        $context = $this->contextRepository->create($attributes);

        // Add members if value was provided.
        if ($membersExternalIds !== null) {
            $this->syncronizeMembers($context, $account, $membersExternalIds);
        }

        return $context;
    }

    /**
     * Retrieve a Context if possible.
     *
     * @param  string  $id
     * @param  Account  $account
     *
     * @return Context
     * @throws UnauthorizedException
     *
     * @throws ContextNotFoundException
     */
    public function show(string $id, Account $account): Context
    {
        if (!starts_with($id, 'Group-')) {
            throw new ContextNotFoundException();
        }

        $context = $this->contextRepository->getById((int)str_replace('Group-', '', $id));

        if ($context->account_id !== $account->id) {
            throw new UnauthorizedException('Group is not from the same account');
        }

        return $context;
    }

    /**
     * Updates a SCIM Group.
     *
     * @param string $id - ID of the Context.
     * @param Account $account - Account of the update.
     * @param ScimUpdateGroupOperation[] $operations - Operations
     *
     * @return Context
     * @throws ContextNotFoundException
     * @throws Exception
     * @throws MutabilityException
     * @throws UnauthorizedException
     * @throws UserNotFoundException
     */
    public function updateWithOperations(string $id, Account $account, array $operations): Context
    {
        $context = $this->show($id, $account);

        foreach ($operations as $operation) {
            switch ($operation->operation()) {
                case 'Replace':
                    $this->replaceOperation($context, $account, $operation);
                    break;
                case 'Add':
                    $this->addOperation($context, $account, $operation);
                    break;
                case 'Remove':
                    $this->removeOperation($context, $operation);
                    break;
                default:
                    throw new Exception('Operation ' . $operation->operation() . ' not found.');
            }
        }

        return $context;
    }

    /**
     * @throws UserNotFoundException
     * @throws MemberDoesNotExistsException
     * @throws UniquenessException
     * @throws UnauthorizedException
     * @throws ContextNotFoundException
     */
    public function update(
        string $id,
        Account $account,
        array $attributes,
        array $membersIds = null
    ): Context {
        $context = $this->show($id, $account);

        // Update attributes
        $this->contextRepository->update($context, $attributes);

        if ($membersIds !== null) {
            $this->syncronizeMembers($context, $account, $membersIds);
        }

        return $context;
    }

    /**
     * @param  Context  $context
     * @param  Account  $account
     * @param  array  $membersIds
     *
     * @throws MemberDoesNotExistsException
     * @throws UserNotFoundException|UnauthorizedException|UniquenessException
     */
    public function syncronizeMembers(Context $context, Account $account, array $membersIds): void
    {
        $users = [];
        $groups = [];

        foreach ($membersIds as $memberId) {
            try {
                // If the child is a context.
                if (starts_with($memberId, 'Group-')) {
                    $newChild = $this->show($memberId, $account);

                    $groups[$newChild->id] = $newChild;
                    continue;
                }

                if (starts_with($memberId, 'User-')) {
                    $newChild = $this->scimUsersService->show($memberId, $account);

                    $users[$newChild->id] = $newChild;
                    continue;
                }
            } catch (NotFoundException $e) {
                throw new MemberDoesNotExistsException($memberId);
            }

            throw new MemberDoesNotExistsException($memberId);
        }

        $this->synchronizeContexts($context, $account, $groups);
        $this->synchronizeUsers($context, $users);
    }

    /**
     * Synchronize contexts.
     *
     * @param Context $context
     * @param Account $account
     * @param array $members
     *
     * @return void
     * @throws UniquenessException
     */
    public function synchronizeContexts(Context $context, Account $account, array $members): void
    {
        // 1- Get all contexts users.
        $children = $this->contextRepository->getAllChildren($context);

        // 2- Remove contexts that are no longer present
        // BL - Move them to the root.
        foreach ($children as $child) {
            // If the id is in the current
            if (!array_key_exists($child->id, $members)) {
                $this->contextRepository->update(
                    $child,
                    [
                        'parent_id' => $this->contextRepository->getRoot($account)->id
                    ]
                );
            }
        }

        // 3- Add new ones.
        foreach ($members as $member) {
            if (!$children->has($member->id)) {
                $this->addGroupToGroup($member, $context);
            }
        }
    }

    /**
     * @param  Context  $context
     * @param  array  $members
     *
     * @throws UserNotFoundException|\Exception
     */
    public function synchronizeUsers(Context $context, array $members): void
    {
        // 1- Get all users.
        $children = $this->userRepository->getAllOfGivenContext($context);

        // 2- Remove contexts that are no longer present
        // BL - Move them to the root.
        foreach ($children as $user) {
            // If the id is in the current
            if (!array_key_exists($user->id, $members)) {
                $membership = $this->membershipsRepository->getByUserAndContext($user, $context);
                $this->membershipsRepository->delete($membership);
                $this->removeAzureInternalMembership($user);
            }
        }

        // 3- Add new ones.
        foreach ($members as $member) {
            if (!$children->has($member->id)) {
                $this->membershipsRepository->addMember($context, $member);
            }
        }
    }

    /**
     * Add operation to a context in SCIM.
     *
     * @param Context $context
     * @param Account $account
     * @param ScimUpdateGroupOperation $operation
     *
     * @return void
     * @throws ContextNotFoundException
     * @throws MutabilityException
     * @throws UnauthorizedException
     * @throws UniquenessException
     * @throws UserNotFoundException
     */
    private function addOperation(
        Context $context,
        Account $account,
        ScimUpdateGroupOperation $operation
    ): void {
        if ($operation->attribute() === 'members') {
            // If the child is a context.
            foreach ($operation->value() as $value) {
                $id = $value['value'];

                // If the child is a context.
                if (starts_with($id, 'Group-')) {
                    $newChild = $this->show($id, $account);

                    $this->addGroupToGroup($newChild, $context);
                }

                if (starts_with($id, 'User-')) {
                    $newChild = $this->scimUsersService->show($id, $account);

                    $this->addUserToGroup($newChild, $context);
                }
            }
        } else {
            if ($operation->attribute() === 'externalId') {
                throw new MutabilityException();
            }

            // Update just the attributes.
            $this->contextRepository->update(
                $context,
                [$operation->attribute() => $operation->value()]
            );
        }
    }

    private function addUserToGroup(User $user, Context $context): void
    {
        $this->membershipsRepository->addMember($context, $user);
    }

    /**
     * @param Context $subGroup
     * @param Context $context
     * @throws UniquenessException
     */
    private function addGroupToGroup(Context $subGroup, Context $context): void
    {
        if ($context->id !== $subGroup->parent_id) {
            if (!$subGroup->parent->isMain()) {
                Log::alert(
                    sprintf(
                        'SCIM 2.0: Multiple parent attempt: Child: %s(%d), Parents: %s(%d) and %s(%d)',
                        $subGroup->name,
                        $subGroup->id,
                        $subGroup->parent->name,
                        $subGroup->parent->id,
                        $context->name,
                        $context->id
                    )
                );
            }

            $this->contextRepository->update($subGroup, ['parent_id' => $context->id]);
        }
    }

    /**
     * Add operation to a context in SCIM.
     *
     * @param Context $context
     * @param Account $account
     * @param ScimUpdateGroupOperation $operation
     *
     * @return void
     * @throws MemberDoesNotExistsException
     * @throws UnauthorizedException
     * @throws UniquenessException
     * @throws UserNotFoundException
     */
    private function replaceOperation(
        Context $context,
        Account $account,
        ScimUpdateGroupOperation $operation
    ): void {
        if ($operation->attribute() === 'members') {
            $ids = [];

            foreach ($operation->value() as $member) {
                $ids[] = $member['value'];
            }

            $this->syncronizeMembers($context, $account, $ids);
        } else {
            // Update just the attributes.
            $this->contextRepository->update(
                $context,
                [$operation->attribute() => $operation->value()]
            );
        }
    }

    /**
     * Remove operation to a context in SCIM.
     * @param Context $context
     * @param ScimUpdateGroupOperation $operation
     * @return void
     */
    private function removeOperation(Context $context, ScimUpdateGroupOperation $operation): void
    {
        // Only used the remove operation for members in azure.
        // If values are not null, single removes will be done,
        // otherwise all members of the group are deleted, which also azure didn't use.
        if ($operation->attribute() === 'members' && $operation->value() !== null) {
            foreach ($operation->value() as $value) {
                // We will do nothing if is to remove a subgroup, since the schema is different.
                $newChild = $this->contextRepository->findByExternalId($value['value']);
                if ($newChild !== null && $newChild->account_id === $newChild->id) {
                    ScimLog::alert(
                        sprintf(
                            'SCIM 2.0: Remove parent attempt: Child: %s(%d), Parent: %s(%d)',
                            $newChild->name,
                            $newChild->id,
                            $context->name,
                            $context->id
                        )
                    );
                }

                // If it is a user.
                if (starts_with($value['value'], 'User-')) {
                    try {
                        $user = $this->userRepository->getById(substr($value['value'], 5));
                        $this->membershipsRepository->deleteMembership($context, $user);
                        $this->removeAzureInternalMembership($user);
                    } catch (\Exception $e) {
                        ScimLog::error('Error deleting user: ' . $e->getMessage());
                    }
                } elseif (starts_with($value['value'], 'Group-')) {
                    // If it is a group.
                    try {
                        $context = $this->contextRepository->getById(substr($value['value'], 6));
                        $this->contextRepository->delete($context);
                    } catch (\Throwable $e) {
                        ScimLog::error(
                            'Error deleting group. Exception: ' . get_class($e) . '. Message: ' . $e->getMessage()
                        );
                    }
                }
            }
        }
    }

    /**
     * Delete a SCIM.
     * @param string $id - The ID of the Group.
     * @param Account $account
     *
     * @return bool
     * @throws ContextNotFoundException
     * @throws UnauthorizedException - If the current account doesn't own the context.
     */
    public function delete(string $id, Account $account): bool
    {
        $context = $this->show($id, $account);

        return $this->contextRepository->delete($context);
    }

    /**
     * Removes user from Azure Internal group if it is the last membership
     * @param User $user
     * @return void
     * @throws \Exception
     */
    public function removeAzureInternalMembership(User $user): void
    {
        if ($user->memberships()->count() === 1) {
            $rootContext = $this->contextRepository->getRoot($user->account);
            $azureContext = $this->contextRepository->findByNameAndParentId(
                ScimUserService::CONTEXT_NAME,
                $rootContext->id
            );
            if (!empty($azureContext)) {
                $user->delete();
            } else {
                ScimLog::warning('SCIM user #' . $user->id . ' last membership is not Azure Internal');
            }
        }
    }
}
