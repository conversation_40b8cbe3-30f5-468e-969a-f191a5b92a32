<?php

namespace App\Services\System;

use App\Account;
use App\Logging\Channels\HeraldLog;
use App\Repositories\FileSystem\StorageRepository;
use App\Repositories\Services\AccountServiceRepository;
use App\Repositories\Services\ServiceRepository;
use App\Service;
use App\Services\Gateway\Caseware\CasewareService;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Support\Facades\File;
use Symfony\Component\Finder\SplFileInfo;

class HeraldService
{
    public function __construct(
        private readonly StorageRepository $storageRepository,
        private readonly ServiceRepository $serviceRepository,
        private readonly AccountServiceRepository $accountServiceRepository
    ) {
    }

    /**
     * Return list of most recent JSON application configuration files in account or resources folder
     *
     * @param Account $account
     * @return array
     */
    public function getTheMostRecentApplicationFiles(Account $account): array
    {
        $files = [];
        foreach ($this->storageRepository->getAllFiles($this->getApplicationsPathFromResources()) as $file) {
            $files[] = $this->getMostRecentFile($account, $file->getFilename());
        }

        return $files;
    }

    public function getApplicationConfigs(Account $account): array
    {
        $configs = [];
        foreach ($this->getTheMostRecentApplicationFiles($account) as $file) {
            $configs[$file->getBasename('.json')] = json_decode($this->storageRepository->getFile($file->getPathName()), true); // phpcs:ignore
        }
        return $configs;
    }

    /**
     * Return the file content from the most recent file
     *
     * @param Account $account
     * @param string $filename
     * @return string prettyfied JSON
     * @throws FileNotFoundException
     */
    public function getFile(Account $account, string $filename): string
    {
        $file = $this->getMostRecentFile($account, $filename);

        return $this->prettify(
            $this->storageRepository->getFile($file->getPathname())
        );
    }

    /**
     * Get the most recent file in resources folder or account folder
     *
     * @param Account $account
     * @param string $filename
     * @return SplFileInfo|null
     */
    protected function getMostRecentFile(Account $account, string $filename): ?SplFileInfo
    {
        $resourcesFile = $this->storageRepository->findFileByName(
            $this->getApplicationsPathFromResources(),
            $filename
        );

        $accountFile = $this->storageRepository->findFileByName(
            $this->getApplicationsPathFromAccount($account),
            $filename
        );

        // if user didnt save file in his account, return file in resources
        if ($accountFile === null) {
            return $resourcesFile;
        }

        // saved account file is the the most recent
        if ($accountFile->getMTime() >= $resourcesFile->getMTime()) {
            return $accountFile;
        }

        return $resourcesFile;
    }

    /**
     * Edit file in user account
     *
     * @param Account $account
     * @param string $filename
     * @param string $content
     */
    public function editFile(Account $account, string $filename, string $content): void
    {
        $this->editAccountFile($account, $filename, $content);
        // update settings file for account that is editing (admin) so it can be tested before commit to all accounts
        $configs = $this->getApplicationConfigs($account);
        $this->generateSettingsFile($account, $configs);
    }

    /**
     * Merge all applications files into settings.json file for all accounts from the main account
     *
     * @param Account $account
     * @param string $filename
     * @param string $content
     * @throws FileNotFoundException
     */
    public function commitFile(Account $account, string $filename, string $content): void
    {
        $this->editAccountFile($account, $filename, $content);
        $configs = $this->getApplicationConfigs($account);

        $c = 0;
        foreach (Account::cursor() as $account) {
            $c += $this->generateSettingsFile($account, $configs);
        }
        HeraldLog::info('User #' . auth()->user()->id . ' created settings.json file for ' . $c . ' accounts');
    }

    /*
     * updates settings file for a single account.
     */

    public function saveSettingsForAccount(Account $account): bool
    {
        $configs = $this->getApplicationConfigs($account);
        return $this->generateSettingsFile($account, $configs);
    }

    /**
     * Delete service and update settings
     * @param int $accountServiceId
     * @return bool
     */
    public function deleteService(int $accountServiceId): bool
    {
        $accountService = $this->accountServiceRepository->getById($accountServiceId);
        if ($accountService->delete() === false) {
            return false;
        }

        if (in_array($accountService->service->reference_name, Service::OPEN_QUESTIONS_SERVICES)) {
            if ($accountService->service->reference_name === Service::CASEWARE_SERVICE) {
                resolve(CasewareService::class)->deleteUploadTypes($accountService);
            }

            return $this->generateSettingsFile(
                $accountService->account,
                $this->getApplicationConfigs($accountService->account)
            );
        }

        return true;
    }

    /**
     * Edit file from account
     *
     * @param Account $account
     * @param string $filename
     * @param string $content
     */
    protected function editAccountFile(Account $account, string $filename, string $content): void
    {
        if (
            $this->storageRepository->findFileByName(
                $this->getApplicationsPathFromAccount($account),
                $filename
            ) === null
        ) {
            if (!File::isDirectory($this->getApplicationsPathFromAccount($account))) {
                File::makeDirectory($this->getApplicationsPathFromAccount($account), 0755, true);
                HeraldLog::info('User #' . auth()->user()->id . ' created browser extension directories in account #' . $account->id); //phpcs:ignore
            }
        }

        $this->storageRepository->editFile(
            $this->getApplicationsPathFromAccount($account) . $filename,
            $this->prettify($content)
        );
        HeraldLog::info('User #' . auth()->user()->id . ' modified file: ' . $filename);
    }

    /**
     * Generate settings.json file by account
     *
     * @param Account $account
     * @param array $files
     * @return bool
     */
    protected function generateSettingsFile(Account $account, array $files): bool
    {
        $settingsFile = [];
        $settingsFile['settings'] = [
            'services' => $this->handleInstalledServices($account, $files)
        ];

        $this->storageRepository->editFile(
            $this->getSettingsAbsolutePath($account),
            $this->prettify(json_encode($settingsFile))
        );

        return true;
    }

    /**
     * Handle installed account services
     *
     * @param \App\Account $account
     * @param array $files
     * @return array
     */
    protected function handleInstalledServices(Account $account, array $files): array
    {
        $demo = true;
        if (array_key_exists(Account::SETTING_BE_DEMO, $account->settings)) {
            // disable demo mode if account setting "be_demo" is set to false.
            $demo = $account->settings[Account::SETTING_BE_DEMO];
        }

        $serviceSettings = [];
        foreach ($this->serviceRepository->getAllOpenQuestionsAccountServices($account) as $accountService) {
            if (empty($files[$accountService->service->reference_name])) {
                continue;
            }
            $mode = $accountService->isEnabled() ? 'active' : 'demo';
            if ($mode === 'active' || $demo) {
                $serviceSettings[$accountService->service->reference_name] = [
                    'mode' => $mode,
                    'properties' => $this->filterBeProperties($accountService->properties),
                    'application' => $files[$accountService->service->reference_name]
                ];
            }
        }

        if ($demo) {
            $missingDemoModeServices = array_diff(Service::OPEN_QUESTIONS_SERVICES, array_keys($serviceSettings));
            foreach ($missingDemoModeServices as $demoService) {
                if (isset($files[$demoService])) {
                    $serviceSettings[$demoService] = [
                        'mode' => 'demo',
                        'application' => $files[$demoService]
                    ];
                }
            }
        }

        $belastingtoolService = $this->accountServiceRepository->firstEnabledByServiceName(
            $account,
            Service::BELASTINGTOOL
        );
        if ($belastingtoolService !== null && $belastingtoolService->getProvider()->isConfigured()) {
            foreach (array_keys($files) as $fileName) {
                if (str_contains($fileName, 'belastingtool')) {
                    $serviceSettings[$fileName] = [
                        'properties' => $this->filterBeProperties($belastingtoolService->properties),
                        'application' => $files[$fileName]
                    ];
                }
            }
        }

        return $serviceSettings;
    }

    /**
     * Filter properties so that only ones starting with be_ are exposed in settings.
     * This avoids sharing sensitive properties like API keys and reduces payload size.
     * @param array $properties Properties of AccountService model.
     * @return array Subset of properties where key starts with "be_"
     */
    public function filterBeProperties(array $properties): array
    {
        $out = [];
        foreach ($properties as $key => $value) {
            if (substr($key, 0, 3) === 'be_') {
                $out[$key] = $value;
            }
        }
        return $out;
    }

    /**
     * Return settings file
     *
     * @param Account $account
     * @return string|null JSON content of settings file
     */
    public function getSettingsFile(Account $account): ?string
    {
        $file = $this->storageRepository->findFileByName(
            $this->getBrowserExtensionPathFromAccount($account),
            'settings.json'
        );

        if ($file === null) {
            return null;
        }

        return $file->getContents();
    }

    /**
     * @param Account $account
     * @return int|null UNIX epoch timestamp when account settings file was last modified
     */
    public function getSettingsModificationDate(Account $account): ?int
    {
        $path = $this->getSettingsAbsolutePath($account);
        $time = filemtime($path);
        if ($time === false) {
            return null;
        }
        return $time;
    }

    /**
     * Return settings file properties
     *
     * @param Account $account
     * @return SplFileInfo|null
     */
    public function getSettingsFileProperties(Account $account): ?SplFileInfo
    {
        $file = $this->storageRepository->findFileByName(
            $this->getBrowserExtensionPathFromAccount($account),
            'settings.json'
        );

        if ($file === null) {
            return null;
        }

        return $file;
    }

    /**
     * Prettify json string (indentation & spacing)
     *
     * @param string $json
     * @return string
     */
    protected function prettify(string $json): string
    {
        $array = json_decode($json, true);
        return json_encode($array, JSON_PRETTY_PRINT);
    }

    /**
     * @return string Absolute local path of directory ending with slash as separator
     */
    protected function getBrowserExtensionPathFromResources(): string
    {
        return resource_path('json/browser_extension/');
    }

    /**
     * @return string Absolute local path of directory ending with slash as separator
     */
    protected function getApplicationsPathFromResources(): string
    {
        return $this->getBrowserExtensionPathFromResources() . 'applications/';
    }

    /**
     * @param Account $account
     * @return string Absolute local path of directory ending with slash as separator
     */
    protected function getBrowserExtensionPathFromAccount(Account $account): string
    {
        return storage_path('app/Account/' . $account->id . '/browser_extension/');
    }

    /**
     * @param Account $account
     * @return string Absolute local path of directory ending with slash as separator
     */
    protected function getApplicationsPathFromAccount(Account $account): string
    {
        return $this->getBrowserExtensionPathFromAccount($account) . 'applications/';
    }

    /**
     * @param Account $account
     * @return string Absolute local path of JSON settings file
     */
    protected function getSettingsAbsolutePath(Account $account): string
    {
        return $this->getBrowserExtensionPathFromAccount($account) . 'settings.json';
    }
}
