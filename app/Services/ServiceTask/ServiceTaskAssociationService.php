<?php

namespace App\Services\ServiceTask;

use App\Exceptions\Services\ServiceTaskCannotBeDeletedException;
use App\Helpers\TaskAuditLogHelper;
use App\Models\ServiceTask\ServiceTaskAssociation;
use App\Repositories\Services\ServiceTaskAssociationRepository;
use App\Repositories\Services\ServiceTaskRepository;
use App\ServiceTask;
use App\User;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Throwable;

class ServiceTaskAssociationService
{
    public function __construct(
        private readonly ServiceTaskAssociationRepository $repository,
        private readonly TaskSendService $taskSendService
    ) {
    }

    /**
     * @param ServiceTask $task
     * @param User $user
     * @param int $order
     * @param string $reference
     * @throws Throwable
     */
    public function store(ServiceTask $task, User $user, int $order, string $reference): ServiceTaskAssociation
    {
        if ($task->taskGroup !== null) {
            foreach ($task->taskGroup->service_tasks as $groupTask) {
                $association = $this->repository->updateOrCreate($groupTask, $user, $order, $reference);
                $groupTask->status = ServiceTask::STATUS_PENDING_ASSOCIATED_TASK;
                $groupTask->save();
                TaskAuditLogHelper::orderSet($association);
            }
        } else {
            $association = $this->repository->updateOrCreate($task, $user, $order, $reference);
            $task->status = ServiceTask::STATUS_PENDING_ASSOCIATED_TASK;
            $task->save();
            TaskAuditLogHelper::orderSet($association);
        }

        return $association;
    }

    public function sendAssociation(User $user, string $reference, int $order)
    {
        $account = $user->account;
        $associations = $this->repository->findByReferenceAndOrder($reference, $order);
        /** @var ServiceTaskAssociation $association */
        foreach ($associations as $association) {
            $association->refresh();
            if (!$association->sent) {
                $association->sent = true;
                $association->save();
                $task = $association->serviceTask;
                $this->taskSendService->send($account, $task, $user);
            }
        }
    }

    public function sendAssociatedTasksAfterComplete(ServiceTask $task): void
    {
        // Gather all tasks that are in the same association that are ready to be sent.
        $nextAssociations = $this->findNextTasksToBeSent($task);

        if ($nextAssociations !== null) {
            /** @var ServiceTaskAssociation $nextAssociation */
            foreach ($nextAssociations as $nextAssociation) {
                $this->sendAssociation($nextAssociation->user, $nextAssociation->reference, $nextAssociation->order);
            }
        }
    }

    private function findNextTasksToBeSent($task): ?Collection
    {
        $associations = $this->repository->getAssociatedToTask($task);

        /**
         * Loop through the 'sent' tasks to see if they are completed.
         * @var ServiceTaskAssociation $currentAssociatedTask
         */
        foreach ($associations->where(ServiceTaskAssociation::SENT, true) as $currentAssociatedTask) {
            // If any of the 'sent' tasks are not completed, we are not done, and should wait for these to finish.
            if (!$currentAssociatedTask->serviceTask->isCompleted()) {
                return null;
            }
        }

        $minOrder = $associations->where(ServiceTaskAssociation::SENT, false)
            ->min(ServiceTaskAssociation::ORDER);

        return $associations->where(ServiceTaskAssociation::ORDER, $minOrder);
    }

    public function getConnectedTaskMessages(ServiceTask $task): array
    {
        $associations = $this->findByReference($task->association->reference);
        $message = [];
        foreach ($associations as $association) {
            if ($association->service_task_id !== $task->id) {
                $currentTask = $association->serviceTask;
                $identifierLabel = $currentTask->getIdentifierLabel();
                if ($identifierLabel !== null) {
                    $message[] = $currentTask->title . ': ' . $identifierLabel . ' ' . $currentTask->getIdentifier();
                } else {
                    $message[] = $currentTask->title;
                }
            }
        }
        return $message;
    }

    /**
     * @param string $reference
     * @return Collection<ServiceTaskAssociation>
     */
    public function findByReference(string $reference): Collection
    {
        return $this->repository->findByReference($reference);
    }

    /**
     * @param ServiceTask $task
     * @return \Illuminate\Support\Collection
     * @throws ServiceTaskCannotBeDeletedException
     */
    public function deleteAssociation(ServiceTask $task): \Illuminate\Support\Collection
    {
        $associations = $this->findByReference($task->association->reference);
        $affected = collect();
        foreach ($associations as $association) {
            $task = $association->serviceTask;
            if (!$task->canBeDeleted()) {
                throw new ServiceTaskCannotBeDeletedException('Cannot delete task because it has responses');
            }
            $affected->push($task);
            $task->delete();
        }
        return $affected;
    }

    /**
     * @param ServiceTask $task
     * @param string $reason
     * @return \Illuminate\Support\Collection
     * @throws Exception
     */
    public function hardDeleteAssociation(ServiceTask $task, string $reason): \Illuminate\Support\Collection
    {
        $associations = $this->findByReference($task->association->reference);
        $affected = collect();
        $taskRepository = resolve(ServiceTaskRepository::class);
        foreach ($associations as $association) {
            $task = $association->serviceTask;
            $affected->push($task);
            $taskRepository->hardDelete($task, $reason);
        }
        return $affected;
    }
}
