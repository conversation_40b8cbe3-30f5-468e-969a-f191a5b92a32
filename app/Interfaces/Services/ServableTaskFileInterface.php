<?php

namespace App\Interfaces\Services;

use App\ServiceTask;

/**
 * A servable task file is a file that can be sent as a response to a HTTP request or to a DMS
 * (Document Management Service).
 * A servable file does not always exist in the database or local file system. It can also be passed on from an
 * external service or generated on the fly.
 * This interface can be used for any kind of file: ZIP, PDF, XBRL, XML, JPEG etc.
 */
interface ServableTaskFileInterface
{
    public function getTaskFileId(): ?int;

    public function getContent(): string;

    public function setFilename(string $filename): void;

    public function getFilename(): string;

    public function getMimeType(): string;

    public function getTask(): ?ServiceTask;

    public function getExtension(): string;

    public function getPreviewOrOriginal(): string;
}
