<?php

namespace App\DataTransferObject\Api;

use App\ServiceTaskResponse;

class ServiceTaskResponseDTO
{
    public function __construct(ServiceTaskResponse $response)
    {
        $this->user_id = $response->user_id;
        $this->permission = $response->permission;
    }

    public function getResponseData(): array
    {
        return [
            'user_id' => $this->user_id,
            'permission' => $this->permission,
            'message' => 'Sent to user ' . $this->user_id . ' with permission ' . $this->permission . '.'
        ];
    }
}
