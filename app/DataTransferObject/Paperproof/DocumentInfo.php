<?php

namespace App\DataTransferObject\Paperproof;

use App\Logging\Channels\ServiceLog;
use App\Models\TaskFile\Pdf;
use App\Support\Carbon;

class DocumentInfo
{
    public const AUTHORIZATION_TYPE_EMAIL = 'email_address';
    public const AUTHORIZATION_TYPE_PHONE = 'mobile_phone_number';
    public const AUTHORIZATION_TYPE_USER_UUID = 'hix_portal_user_uuid';

    public const AUTHORIZATION_TYPES = [
        self::AUTHORIZATION_TYPE_EMAIL,
        self::AUTHORIZATION_TYPE_PHONE,
        self::AUTHORIZATION_TYPE_USER_UUID
    ];

    public readonly string $uuid;
    public readonly string $filename;
    public readonly string $content;
    public readonly int $pageCount;
    public readonly string $language;
    public readonly string $ownerName;
    public readonly string $ownerUuid;
    public readonly Carbon $fileExpiresAt;
    private array $authorizations = [];

    /**
     * Add an authorization to the list. Anybody with access to the phone number or email address will be allowed to
     * download a copy of the document.
     * @param string $type "phone" or "email" are the only allowed values.
     * @param string $value phone number or email address
     * @return void
     */
    public function addAuthorization(string $type, string $value): void
    {
        if (!in_array($type, self::AUTHORIZATION_TYPES)) {
            throw new \UnexpectedValueException('Unknown authorization type');
        }

        if (strlen($value) == 0) {
            throw new \UnexpectedValueException('Authorization value not allowed to be empty');
        }

        $this->authorizations[] = ['type' => $type, 'value' => $value];
    }

    /**
     * Payload containing base64 encoded PDF content, metadata and authorizations to allow downloading the document.
     * @return array Payload that can be converted to JSON to post to Paperproof API.
     */
    public function getPayload(): array
    {
        return [
            'uuid' => $this->uuid,
            'filename' => $this->filename,
            'content' => base64_encode($this->content),
            'page_count' => $this->pageCount,
            'language' => $this->language,
            'owner_uuid' => $this->ownerUuid,
            'owner_name' => $this->ownerName,
            'file_expires_at' => $this->fileExpiresAt->isoFormat('YYYY-MM-DD'),
            'authorizations' => $this->authorizations
        ];
    }

    /**
     * Creaate new instance of DocumentInfo object which contains all information to send to Paperproof service.
     * @param Pdf $pdf
     * @param int $years Number of years the document should be kept.
     * @return self
     */
    public static function create(Pdf $pdf, int $years = 10): self
    {
        if ($years < 1) {
            throw new \UnexpectedValueException('Years has to be at least 1');
        }

        if (is_null($pdf->paperproof)) {
            throw new \UnexpectedValueException('Task file #' . $pdf->id . ' has no paperproof UUID associated');
        }

        $docInfo = new self();
        $docInfo->uuid = $pdf->paperproof->uuid;
        $docInfo->filename = $pdf->filename;
        $docInfo->content = $pdf->getContent();
        $docInfo->pageCount = static::getPageCount($docInfo->content);
        $docInfo->language = $pdf->account->language;
        $docInfo->ownerName = $pdf->account->name;
        $docInfo->ownerUuid = $pdf->account->uuid;
        $docInfo->fileExpiresAt = Carbon::now()->addYears($years);

        foreach ($pdf->task->taskResponses as $response) {
            try {
                $docInfo->addAuthorization(self::AUTHORIZATION_TYPE_EMAIL, $response->email);
                if (is_string($response->user->mobile)) {
                    $docInfo->addAuthorization(self::AUTHORIZATION_TYPE_PHONE, $response->user->mobile);
                }
                if (isset($response->user->uuid)) {
                    $docInfo->addAuthorization(self::AUTHORIZATION_TYPE_USER_UUID, $response->user->uuid);
                }
            } catch (\Throwable $e) {
                ServiceLog::error('Error while creating authorizations for Paperproof for task response #' . $response->id . ' : ' . $e::class . ' - ' . $e->getMessage()); // phpcs:ignore
            }
        }

        return $docInfo;
    }

    /**
     * @param string $content Raw PDF content.
     * @return int Number of pages in the PDF. Always at least 1.
     */
    private static function getPageCount(string $content): int
    {
        $doc = \SetaPDF_Core_Document::loadByString($content);
        $pages = $doc->getCatalog()->getPages();
        return $pages->count();
    }
}
