<?php

namespace App;

use App\Auth\Helpers\JwtHelper as Jwt;
use App\Auth\Helpers\PostAuthActionsHelper;
use App\Auth\IdentityIssuer;
use App\Auth\Providers\Jwt\Constants\JwtClaimTypes;
use App\Auth\SessionKey;
use App\Contracts\EmailRecipientContract;
use App\Contracts\EmailSenderContract;
use App\Contracts\NotificationOriginContract;
use App\Database\SoftDeletes;
use App\Events\AccountCreatedEvent;
use App\Events\AccountDeletedEvent;
use App\Events\AccountHardDeletedEvent;
use App\Exceptions\Account\AccountCannotBeDeletedException;
use App\Helpers\AccountHelper;
use App\Helpers\HixUuid;
use App\Helpers\Http\Host;
use App\Helpers\Http\RequestHelper;
use App\Helpers\Mail\TranslationHelper;
use App\Helpers\Meilisearch\TenantGenerator;
use App\Jobs\SendEmailJob;
use App\Models\Billing\AccountBilling;
use App\Models\ConsumerIssuer;
use App\Models\Dossiers\DossierFile;
use App\Models\Dossiers\DossierFolder;
use App\Models\Notification;
use App\Models\OcrTag;
use App\Models\OpenQuestions\Notifier\NotifierSettingAccount;
use App\Models\OpenQuestions\Notifier\NotifierSettingCompany;
use App\Models\OpenQuestions\Questions\Templates\Template;
use App\Models\Pdf\PdfBackground;
use App\Models\TaskFile;
use App\Models\UserReactivationRequest;
use App\Repositories\AccountRepository;
use App\Repositories\AccountUsageStatsRepository;
use Auth;
use Cache;
use App\Support\Carbon;
use Color;
use Crypt;
use Database\Factories\Account\AccountFactory;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Queue\QueueableEntity;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\Lang;
use Laravel\Sanctum\HasApiTokens;
use Log;
use Session;

/**
 * Class Account
 * @package App
 * @property string $meilisearch_tenant
 * @property string $name
 * @property string $uri
 * @property string|null $hostname
 * @property string|null $support_email
 * @property AccountKeychain|null $keychain
 * @property string $status
 * @property array $support_categories
 * @property bool $login_autocomplete
 * @property Context $mainContext
 * @property array $mainContexts
 * @property string $language
 * @property string $native_uri
 * @property string $trial_end_date
 * @property Carbon|null $contract_end_date
 * @property Carbon|null $delete_after
 * @property array $translations
 * @property array $login_options
 * @property array $settings
 * @property array $usage_types
 * @property array $auth_methods
 * @property Collection<int, License> $licenses
 * @property null|string $debtor_number
 * @property string $pricing_model
 * @property null|string $relation_manager
 * @property null|string $sales_manager
 * @property null|string $pipedrive_number
 * @property Collection<int, AccountLicense> $accountLicenses
 * @property Collection<int, Consumer> $identityIssuers
 * @property string $native_hostname
 * @property Collection<int, Company> $companies
 * @property Collection<int, Company> $activeCompanies
 * @property Collection<int, TaskFile> $taskFiles
 * @property Collection<int, Template> $templates
 * @property NotifierSettingAccount $notifierSetting
 * @property string $uuid
 * @method static Account findOrFail($id)
 * @method static Builder whereHostname(string $hostname) // uses scopeWhereHostname()
 * @property Collection<int, AccountBilling> accountBillings;
 * @property Color $primaryColorOnLight
 * @property Collection<int, AccountService> $accountServices
 * @property Collection<int, DossierFile> $dossierFiles
 * @property Collection<int, DossierFolder> $dossierFolders
 * @property Collection<int, UserReactivationRequest> $reactivationRequests
 * @property Color|null $primary_color
 * @property Color|null $secondary_color
 * @property Color $primary_color_contrast
 * @property string|null $logo_circle
 * @property string|null $email_header
 * @property string|null $background_img
 * @property Collection<int, AccountWidget> $accountWidgets
 * @property Collection<int, Consumer> $loginConsumers
 * @property Collection<int, ConsumerIssuer> $consumerIssuers
 * @property Carbon|null $activated_at
 * @property Collection<int, PdfBackground> $pdfBackgrounds
 * @property Collection<int, OcrTag> $ocrTags
 * @property Collection<int, Consumer> $consumers
 * @property string $type
 * @property string $feature_package
 * @property string|null $api_origins
 * @property string|null $embed_settings
 */
class Account extends OldModel implements
    EmailSenderContract,
    EmailRecipientContract,
    QueueableEntity,
    IdentityIssuer,
    NotificationOriginContract
{
    use SoftDeletes;
    use HasApiTokens;

    public static ?Account $sHostAccount = null;
    public const ID = 'id';
    public const MEILISEARCH_TENANT = 'meilisearch_tenant';
    public const NAME = 'name';
    public const HOSTNAME = 'hostname';
    public const PRIMARY_COLOR = 'primary_color';
    public const SECONDARY_COLOR = 'secondary_color';
    public const LOGO_WHITE = 'logo_white';
    public const LOGO_CIRCLE = 'logo_circle';
    public const EMAIL_HEADER = 'email_header';
    public const BACKGROUND_IMG = 'background_img';
    public const LANGUAGE = 'language';
    public const LANGUAGE_EN = 'en';
    public const LANGUAGE_NL = 'nl';
    public const TYPE = 'type';
    public const CATEGORY = 'category';
    public const USAGE_TYPES = 'usage_types';
    public const LOGIN_AUTOCOMPLETE = 'login_autocomplete';
    public const STATUS = 'status';
    public const SUPPORT_EMAIL = 'support_email';
    public const REMEMBER_ME = 'remember_me';
    public const USER_ID = 'user_id';
    public const FEATURE_PACKAGE = 'feature_package';
    public const PRICING_MODEL = 'pricing_model';
    public const DEBTOR_NUMBER = 'debtor_number';
    public const RELATION_MANAGER = 'relation_manager';
    public const SALES_MANAGER = 'sales_manager';
    public const PIPEDRIVE_NUMBER = 'pipedrive_number';
    public const API_ORIGINS = 'api_origins';
    public const ACTIVATED_AT = 'activated_at';
    public const CONTRACT_END_DATE = 'contract_end_date';
    public const DELETE_AFTER = 'delete_after';
    public const PRICING_SETTINGS = 'pricing_settings';
    public const SETTINGS = 'settings';
    public const UUID = 'uuid';
    public const TRIAL_EMAIL_LIMIT = 20;
    public const TRIAL_EMAIL_LIMIT_PER_MINUTES = 10;
    public const TRIAL_END_DATE = 'trial_end_date';

    protected $default_license_ids = [1];

    protected array $images = [self::LOGO_WHITE, self::LOGO_CIRCLE, self::BACKGROUND_IMG];
    public const DEFAULT_PRIMARY_COLOR = '#E9A9F0';
    public const DEFAULT_SECONDARY_COLOR = '#999999';
    public const LOGO_CIRCLE_IMG_PATH = '/images/account/logo_circle.png';
    public const LOGO_WHITE_IMG_PATH = '/images/account/logo_white.png';
    public const BACKGROUND_IMG_PATH = '/images/account/background_img.png';

    protected array $not_possible_to_load = ['managers'];
    protected $casts = [
        self::PRICING_SETTINGS => 'array',
        self::ACTIVATED_AT => 'datetime',
        self::CONTRACT_END_DATE => 'date',
        self::DELETE_AFTER => 'date',
    ];

    public const ACCOUNT_TYPE_CUSTOMER = 'customer';
    public const ACCOUNT_TYPE_PARTNER_ISV = 'partner_isv';
    public const ACCOUNT_TYPE_PARTNER_VAR = 'partner_var';
    public const ACCOUNT_TYPE_INTERNAL = 'internal';

    public const VALID_ACCOUNT_TYPES_LIST = [
        self::ACCOUNT_TYPE_CUSTOMER,
        self::ACCOUNT_TYPE_PARTNER_ISV,
        self::ACCOUNT_TYPE_PARTNER_VAR,
        self::ACCOUNT_TYPE_INTERNAL,
    ];

    public const VALID_ACCOUNT_TYPES = [
        self::ACCOUNT_TYPE_CUSTOMER => 'Customer',
        self::ACCOUNT_TYPE_PARTNER_ISV => 'Partner(ISV)',
        self::ACCOUNT_TYPE_PARTNER_VAR => 'Partner(VAR)',
        self::ACCOUNT_TYPE_INTERNAL => 'Internal',
    ];

    public const CLIENT_ACCOUNT_TYPES = [
        self::ACCOUNT_TYPE_CUSTOMER => 'Customer',
        self::ACCOUNT_TYPE_PARTNER_ISV => 'Partner(ISV)',
        self::ACCOUNT_TYPE_PARTNER_VAR => 'Partner(VAR)',
    ];

    public const ACCOUNT_CATEGORY_ACCOUNTANCY_NL = 'accountancy_nl';
    public const ACCOUNT_CATEGORY_ACCOUNTANCY_OTHER = 'accountancy_other';
    public const ACCOUNT_CATEGORY_HEALTH = 'health';
    public const ACCOUNT_CATEGORY_OTHER = 'other';

    public const VALID_ACCOUNT_CATEGORIES_LIST = [
        self::ACCOUNT_CATEGORY_ACCOUNTANCY_NL,
        self::ACCOUNT_CATEGORY_ACCOUNTANCY_OTHER,
        self::ACCOUNT_CATEGORY_HEALTH,
        self::ACCOUNT_CATEGORY_OTHER
    ];

    public const VALID_ACCOUNT_CATEGORIES = [
        self::ACCOUNT_CATEGORY_ACCOUNTANCY_NL => 'Accountancy NL',
        self::ACCOUNT_CATEGORY_ACCOUNTANCY_OTHER => 'Accountancy Other',
        self::ACCOUNT_CATEGORY_HEALTH => 'Health',
        self::ACCOUNT_CATEGORY_OTHER => 'Other',
    ];

    public const ACCOUNT_USAGE_TYPE_GUI = 'gui';
    public const ACCOUNT_USAGE_TYPE_API = 'api';

    public const VALID_ACCOUNT_USAGE_TYPES = [self::ACCOUNT_USAGE_TYPE_GUI, self::ACCOUNT_USAGE_TYPE_API];

    public const STATUS_TRIAL = 'trial'; //same as active but expires eventually and no invoices are sent.
    //legacy status, used when a quotation has been requested by the customer. Can be removed eventually. phpcs:ignore
    public const STATUS_PENDING = 'pending';
    //all users can login and do everything based on feature package and licenses. phpcs:ignore
    public const STATUS_ACTIVE = 'active';
    public const STATUS_BLOCKED = 'blocked'; //account is blocked, no one is allowed to log in.
    public const STATUS_ARCHIVED = 'archived'; //same as permanently blocked?
    //account is blocked and will be soft deleted after 7 days and hard deleted eventually by the system.
    public const STATUS_TOBEFORGOTTEN = 'tobeforgotten';

    public const BLOCKED_STATUSES = [
        self::STATUS_BLOCKED,
        self::STATUS_ARCHIVED,
        self::STATUS_TOBEFORGOTTEN
    ];

    public const VALID_STATUSES = [
        self::STATUS_TRIAL,
        self::STATUS_PENDING,
        self::STATUS_ACTIVE,
        self::STATUS_BLOCKED,
        self::STATUS_ARCHIVED,
        self::STATUS_TOBEFORGOTTEN
    ];

    // This is used when setting the front auth method to the default.
    public const DEFAULT_FRONT_AUTH_METHOD = 'default';

    public const AUTH_METHOD_SECRET = 'secret';
    public const AUTH_METHOD_TOTP = 'totp';
    public const AUTH_METHOD_SMS_OTP = 'sms_otp';
    public const AVAILABLE_AUTH_METHODS = [
        self::AUTH_METHOD_SECRET,
        self::AUTH_METHOD_SMS_OTP,
        self::AUTH_METHOD_TOTP
    ];

    public const ALLOWED_AUTH_METHODS = [
        self::AUTH_METHOD_SMS_OTP,
        self::AUTH_METHOD_TOTP
    ];

    // This auth method is only used for front auth when license 'no_front_auth' is installed.
    public const AUTH_METHOD_NONE = 'none';

    public const SETTING_BE_DEMO = 'be_demo'; // Browser extension demo mode enabled (default/TRUE) or disabled (FALSE)
    public const SETTING_DEFAULT_FRONT_AUTH_METHOD = 'default_front_auth_method';
    public const SETTING_ALLOW_COPY_PASSWORD = 'allow_copy_password';
    public const SETTING_ALLOW_CHANGE_PREFERRED_COMMUNICATION_CHANNEL = 'allow_change_preferred_communication_channel';
    public const SETTING_ALLOW_EMAIL_FRONT_AUTH = 'allow_email_front_auth';

    public const SECURELOGIN = 'SecureLogin';
    public const HIX = 'Hix';

    protected $fillable = [
        self::NAME,
        self::MEILISEARCH_TENANT,
        self::HOSTNAME,
        self::SUPPORT_EMAIL,
        self::TYPE,
        self::CATEGORY,
        self::LANGUAGE,
        self::RELATION_MANAGER,
        self::SALES_MANAGER,
        self::STATUS,
    ];

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return AccountFactory::new();
    }

    public function contexts(): HasMany
    {
        return $this->hasMany(Context::class);
    }

    public function mainContext(): BelongsTo
    {
        return $this->belongsTo(Context::class, 'id', Context::ACCOUNT_ID)->onlyMain()->withTrashed();
    }

    public function mainContexts()
    {
        return $this->contexts()->onlyMain();
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function taskFiles(): HasMany
    {
        return $this->hasMany(TaskFile::class);
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(ServiceTask::class);
    }

    public function dossierFolders(): HasMany
    {
        return $this->hasMany(DossierFolder::class);
    }

    public function dossierFiles(): HasMany
    {
        return $this->hasMany(DossierFile::class);
    }

    public function accountServices(): HasMany
    {
        return $this->hasMany(AccountService::class);
    }

    public function uploadTypes(): HasMany
    {
        return $this->hasMany(AccountServiceUploadType::class);
    }

    public function accountServiceCompanies(): HasMany
    {
        return $this->hasMany(AccountServiceCompany::class);
    }

    public function companies(): HasMany
    {
        return $this->hasMany(Company::class);
    }

    public function activeCompanies(): HasMany
    {
        return $this->companies()->whereNull(Company::ARCHIVED_AT);
    }

    public function templates(): HasMany
    {
        return $this->hasMany(Template::class);
    }

    public function accountBillings(): HasMany
    {
        return $this->hasMany(AccountBilling::class);
    }

    public function managers(): Collection
    {
        //This is a complex relation for which caching is needed (performance)
        //But this relation can ony be loaded after the full model is loaded

        if (empty($this->id)) {
            trigger_error("The relation managers of Account can only be loaded after the model is loaded");
        }

        return $this->mainContext->managers()->get();
    }

    public function translations(): HasMany
    {
        return $this->hasMany(Translation::class);
    }

    public function licenses(): HasManyThrough
    {
        return $this->hasManyThrough(
            License::class,
            AccountLicense::class,
            AccountLicense::ACCOUNT_ID,
            License::ID,
            Account::ID,
            AccountLicense::LICENSE_ID
        );
    }

    public function accountLicenses(): HasMany
    {
        return $this->hasMany(AccountLicense::class);
    }

    public function availableWidgets(): Builder
    {
        return GenericWidget::isAvailableForAccount($this);
    }

    public function accountWidgets(): HasMany
    {
        return $this->hasMany(AccountWidget::class)->whereNull('context_parent_id');
    }

    public function widgets(): HasMany
    {
        return $this->hasMany(AccountWidget::class)->whereNull('context_parent_id');
    }

    public function consumers(): HasMany
    {
        return $this->hasMany(Consumer::class);
    }

    public function loginConsumers(): HasMany
    {
        return $this->consumers()->where('login_button', true);
    }

    public function consumerIssuers(): HasMany
    {
        return $this->hasMany(ConsumerIssuer::class);
    }

    public function getAvailableUsageTypes(): array
    {
        $usage_types = [];
        foreach (self::VALID_ACCOUNT_USAGE_TYPES as $usage_type) {
            $usage_types[$usage_type] = trans('account.usage_types.' . $usage_type);
        }
        return $usage_types;
    }

    public function identityIssuers(): HasMany
    {
        return $this->hasMany(Consumer::class);
    }

    public function identityAttributes(): HasMany
    {
        return $this->hasMany(IdentityAttribute::class);
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class, 'origin_account_id');
    }

    public function events(): HasMany
    {
        return $this->hasMany(Event::class);
    }

    public function keychain(): HasOne
    {
        return $this->hasOne(AccountKeychain::class);
    }

    public function notifierSetting(): HasOne
    {
        return $this->hasOne(NotifierSettingAccount::class);
    }

    public function notifierSettingCompanies(): HasMany
    {
        return $this->hasMany(NotifierSettingCompany::class);
    }

    public function reactivationRequests(): HasMany
    {
        return $this->hasMany(UserReactivationRequest::class);
    }

    public function pdfBackgrounds(): HasMany
    {
        return $this->hasMany(PdfBackground::class);
    }

    public function ocrTags(): HasMany
    {
        return $this->hasMany(OcrTag::class);
    }

    /**
     * Accessor for $auth_methods
     * @return string[]
     */
    public function getAuthMethodsAttribute(): array
    {
        return self::AVAILABLE_AUTH_METHODS;
    }

    public function getAuthSecretAttribute(): ?string
    {
        return Crypt::encrypt(hash("sha256", $this->id));
    }

    /**
     * Accessor for $settings
     * @return array
     */
    public function getSettingsAttribute(): array
    {
        if (isset($this->attributes[self::SETTINGS])) {
            $value = $this->attributes[self::SETTINGS];

            if (is_string($value)) {
                $value = json_decode($value, true);
            }

            return (!empty($value) ? $value : []);
        } else {
            return [];
        }
    }

    /**
     * Mutator for $settings
     * @param $value
     * @return void
     */
    public function setSettingsAttribute($value)
    {
        if ($value == null) {
            $value = [];
        }

        $this->attributes[self::SETTINGS] = is_string($value) ? $value : json_encode($value);
    }

    /**
     * Accessor for $support_categories
     * @return array
     */
    public function getSupportCategoriesAttribute(): array
    {
        if (empty($this->attributes['support_categories'])) {
            return [];
        }

        $categories = json_decode($this->attributes['support_categories'], true);

        if (!is_array($categories)) {
            return [];
        }

        return $categories;
    }

    /**
     * Accessor for $support_categories_default
     * @property $support_categories_default
     * @return mixed|null
     */
    public function getSupportCategoriesDefaultAttribute()
    {
        foreach ($this->support_categories as $category) {
            if (isset($category['default']) && $category['default']) {
                return $category;
            }
        }

        return null;
    }

    /**
     * Accessor for $usage_types
     * Get usage types  When set to NULL the default returned will be 'gui'.
     * @return array List of usage types
     */
    public function getUsageTypesAttribute(): array
    {
        if (isset($this->attributes[self::USAGE_TYPES])) {
            return json_decode($this->attributes[self::USAGE_TYPES]);
        }
        //by default return both (API is needed for starting widgets in browser extension)
        return [self::ACCOUNT_USAGE_TYPE_GUI, self::ACCOUNT_USAGE_TYPE_API];
    }

    /**
     * Mutator for $usage_types
     * Set usage types. When set to NULL the default returned will be 'gui' and 'api'.
     * @param array|null $value
     * @return void
     */
    public function setUsageTypesAttribute(?array $value): void
    {
        if (is_null($value)) {
            $this->attributes[self::USAGE_TYPES] = null;
        } elseif (count(array_diff($value, self::VALID_ACCOUNT_USAGE_TYPES)) === 0) {
            $this->attributes[self::USAGE_TYPES] = json_encode($value);
        } else {
            throw new \UnexpectedValueException('Not all values are in the list of valid usage types.');
        }
    }

    public function getSupportCategory($reference)
    {
        if (empty($reference)) {
            return null;
        }

        foreach ($this->support_categories as $category) {
            if ($category['reference'] == $reference) {
                return $category;
            }
        }

        return null;
    }

    //This method is used to use an account as identity issuer (consumer)
    public function getConfiguration($key = null, $default = null)
    {
        if ($key == null) {
            return [];
        }

        return $default;
    }

    public function scopeWhereHasManager(Builder $query, User $user): Builder
    {
        return $query->whereHas('contexts', function ($query) use ($user) {
            $query->onlyMain()->whereHasManager($user);
        });
    }

    public function hasManager(User $user): bool
    {
        return $this->managers()->find($user->id) !== null;
    }

    public function isManagedBy(User $user, $direct = false): bool
    {
        $with_accounts = !$direct;

        return $this->contexts()->onlyMain()->whereManagedBy($user, $with_accounts)->exists();
    }

    public function isBlocked(): bool
    {
        return in_array(strtolower($this->status), self::BLOCKED_STATUSES) || $this->trashed();
    }

    public function scopeWhereHostname(Builder $query, string $hostname): Builder
    {
        return $query->where(function (Builder $query) use ($hostname) {
            $query->where(self::HOSTNAME, "like", $hostname)
                ->orWhere(self::HOSTNAME, "like", "$hostname %")
                ->orWhere(self::HOSTNAME, "like", "% $hostname")
                ->orWhere(self::HOSTNAME, "like", "% $hostname %");
        });
    }

    public function hasLicense($name): bool
    {
        return $this->licenses()->where(License::REFERENCE_NAME, $name)->exists();
    }

    public function isAdminAccount(): bool
    {
        return $this->id === 1 && ($this->name === self::SECURELOGIN || $this->name === self::HIX);
    }

    public function isHostAccount(): bool
    {
        return $this->id == static::getHostAccount()->id;
    }

    //Attention: the use of this method assumes max 1 main context per account
    public function getMainContext(): Context
    {
        return $this->contexts()->onlyMain()->firstOrFail();
    }

    public function beforeCreate(array $options = []): bool
    {
        if (!isset($this->status)) {
            $this->status = self::STATUS_TRIAL;
        }

        //Security by Default
        $this->login_autocomplete = false;

        return parent::beforeCreate($options);
    }

    public function afterCreate(array $options = []): bool
    {
        if (is_null($this->uuid) && isset($this->id)) {
            $this->uuid = HixUuid::generate($this->id);
            $this->save();
        }

        if (is_null($this->meilisearch_tenant) && config('scout.driver')) {
            $this->meilisearch_tenant = TenantGenerator::generate($this->id);
            $this->save();
        }

        //store the main context
        $context = new Context();
        $context->account_id = $this->id;
        $context->name = $this->name;

        //Make the main context of the SecureLogin account level -1
        if ($this->isAdminAccount()) {
            $context->level = -1;
        }

        $context->save();

        AccountCreatedEvent::fire($this);

        return parent::afterCreate($options);
    }

    public function beforeUpdate(array $options = [], array $originalValues = []): bool
    {
        if (isset($originalValues[self::UUID]) && $this->uuid !== $originalValues[self::UUID]) {
            throw new \UnexpectedValueException('Account UUID ' . $originalValues[self::UUID] . ' not allowed to change'); // phpcs:ignore
        }
        return parent::beforeUpdate($options, $originalValues);
    }

    public function afterUpdate(array $options = [], array $originalValues = []): bool
    {
        //Rename sub contexts
        if (isset($originalValues[self::NAME])) {
            foreach ($this->mainContexts as $mainContext) {
                $mainContext->name = $this->name;
                $mainContext->save();
            }
        }
        $this->deleteImages($originalValues);

        return parent::afterUpdate($options, $originalValues);
    }

    public function beforeDelete(array $options = []): bool
    {
        if ($this->id === 1) {
            dd(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS));
        }

        if (!$this->forceDeleting) {
            if ($this->status !== self::STATUS_TOBEFORGOTTEN) {
                throw new AccountCannotBeDeletedException('Account #' . $this->id . ' cannot be soft deleted because the status is not tobeforgotten'); //phpcs:ignore
            }
            $this->name = uniqid() . '_' . $this->name;
            $this->save();
        }

        return parent::beforeDelete($options);
    }

    public function afterDelete(array $options = []): bool
    {
        AccountDeletedEvent::fire($this);
        $this->accountServices()->delete();
        return parent::afterDelete();
    }

    public function forceDelete($options = ['commit' => false]): ?bool
    {
        //First delete all relations
        $this->hasMany(Token::class)->withTrashed()->get()->map(function ($token) {
            $token->forceDelete();
        });
        $this->users()->withTrashed()->get()->map(function ($user) {
            $user->forceDelete();
        });
        $this->accountWidgets()->withTrashed()->get()->map(function ($account_widget) {
            $account_widget->forceDelete();
        });
        $this->mainContexts()->withTrashed()->get()->map(function ($context) {
            $context->forceDelete();
        });
        $this->accountLicenses->map(function ($account_license) {
            $account_license->delete();
        });
        $this->consumers()->withTrashed()->get()->map(function ($consumer) {
            $consumer->forceDelete();
        });
        $this->notifications()->withTrashed()->get()->map(function ($notification) {
            $notification->forceDelete();
        });

        resolve(AccountUsageStatsRepository::class)->deleteAccountData($this->id);

        $result = parent::forceDelete($options);

        AccountHardDeletedEvent::fire($this);

        return $result;
    }

    public function createAccountWidgets(array $generic_widget_ids)
    {
        foreach ($generic_widget_ids as $generic_widget_id) {
            $generic_widget = GenericWidget::findOrFail($generic_widget_id);

            AccountWidget::createFromGenericWidget($generic_widget, $this);
        }
    }

    public function storeContexts(array $context_names)
    {
        foreach ($context_names as $context_name) {
            if (empty($context_name)) {
                continue;
            }
            $context = new Context();

            $context->parent_id = $this->getMainContext()->id;
            $context->name = trim($context_name);

            if ($context->name == $this->trans('common.external')) {
                $context->is_external = true;
            }

            $context->save();
        }
    }

    /**
     * Get translation
     * @param string $key
     * @param array $replace
     * @param $language
     * @return string
     */
    public function trans(string $key, array $replace = [], $language = null): string
    {
        if (is_null($language)) {
            $language = $this->language;
        }

        return Lang::get($key, $replace, $language);
    }

    /**
     * Get custom translation from database
     * "key" starts with group from column db and then key
     * Example: user.sms_account_name
     * @param string $key
     * @param array $replace
     * @param null $language
     * @param string $default
     * @return string
     */
    public function customTrans(string $key, array $replace = [], $language = null, string $default = ""): string
    {
        if (is_null($language)) {
            $language = $this->language;
        }
        $translations = $this->translations->toArray();
        return TranslationHelper::translate($translations, $key, $language, $replace, $default);
    }

    public function template($template)
    {
        if (view()->exists($template . "_" . str_clean_word($this->name))) {
            return $template . "_" . str_clean_word($this->name);
        }

        return $template;
    }

    public function showLabel($clickable = false, $popup = false, $extra_fields = array()): string
    {
        if ($this->trashed()) {
            return "*$this->name*";
        }

        return $this->name ?? '';
    }

    public function showImage(): string
    {
        return 'images/icons/securelogin.png';
    }

    public function showDefaultLabel(): string
    {
        return $this->showLabel();
    }

    public function showSenderLabel(): string
    {
        if ($this->isAdminAccount()) {
            return $this->showLabel();
        }

        return "Hix - " . $this->showLabel();
    }

    /**
     * @return array Single or multiple email addresses.
     */
    public function getEmailAddresses(): array
    {
        return explode(';', $this->attributes[self::SUPPORT_EMAIL]);
    }

    public function sendEmail(
        array $data = [],
        string $subject = 'Message from Hix',
        string $template = 'user.emails.default',
        EmailSenderContract $sender = null,
        ?string $recipient_email = null,
        ?string $recipient_name = null,
        ?array $cc = null,
        ?array $bcc = null,
        ?string $reply_to_email = null,
        ?string $reply_to_name = null
    ) {
        if ($sender == null) {
            $sender = Account::getAdminAccount();
        }
        $this->setRelations([]);
        dispatch(
            new SendEmailJob(
                $sender,
                $this,
                $subject,
                $template,
                $data,
                $recipient_email,
                $recipient_name,
                $cc,
                $bcc,
                $reply_to_email,
                $reply_to_name
            )
        );
    }

    public function getAccount(): Account
    {
        return $this;
    }

    public function getApiOriginsAttribute(): array
    {
        if (empty($this->attributes[self::API_ORIGINS])) {
            return [];
        }

        return explode(',', $this->attributes[self::API_ORIGINS]);
    }

    public function getPrimaryColorAttribute(): Color
    {
        if (empty($this->attributes[self::PRIMARY_COLOR])) {
            return new Color(self::DEFAULT_PRIMARY_COLOR);
        }

        return new Color($this->attributes[self::PRIMARY_COLOR]);
    }

    public function getSecondaryColorAttribute(): Color
    {
        if (empty($this->attributes[self::SECONDARY_COLOR])) {
            return new Color(self::DEFAULT_SECONDARY_COLOR);
        }

        return new Color($this->attributes[self::SECONDARY_COLOR]);
    }

    public function hasLightPrimaryColor(float $threshold = 0.75): bool
    {
        return $this->primary_color->getHsl()['L'] > $threshold;
    }

    public function hasLightSecondaryColor(float $threshold = 0.75): bool
    {
        return $this->secondary_color->getHsl()['L'] > $threshold;
    }

    public function getPrimaryColorContrastAttribute(): Color
    {
        return $this->hasLightPrimaryColor() ? Color::$BLACK : Color::$WHITE;
    }

    public function getSecondaryColorContrastAttribute(): Color
    {
        return $this->hasLightSecondaryColor() ? Color::$BLACK : Color::$WHITE;
    }

    public function getPrimaryColorLightAttribute(): Color
    {
        return !$this->hasLightPrimaryColor() ? $this->primary_color->setLightness(0.97) : Color::$LIGHTGRAY;
    }

    public function getSecondaryColorLightAttribute(): Color
    {
        return !$this->hasLightPrimaryColor() ? $this->secondary_color->setLightness(0.97) : Color::$LIGHTGRAY;
    }

    public function getPrimaryColorOnLightAttribute(): Color
    {
        return $this->hasLightPrimaryColor() ? Color::$BLACK : $this->primary_color;
    }

    public function getSecondaryColorOnLightAttribute(): Color
    {
        return $this->hasLightSecondaryColor() ? Color::$BLACK : $this->secondary_color;
    }

    public function getPrimaryColorOnLightContrastAttribute(): Color
    {
        return $this->hasLightPrimaryColor() ? $this->primary_color : Color::$WHITE;
    }

    public function getSecondaryColorOnLightContrastAttribute(): Color
    {
        return $this->hasLightSecondaryColor() ? $this->secondary_color : Color::$WHITE;
    }

    public function getHostnameAttribute(): string
    {
        if (isset($this->attributes[self::HOSTNAME])) {
            $hostnames = explode(" ", trim($this->attributes[self::HOSTNAME]));
            return reset($hostnames);
        }
        return '';
    }

    public function getHostnames(): string
    {
        return $this->attributes[self::HOSTNAME];
    }

    /**
     * @return string native host name ending with .securelogin.nu
     */
    public function getNativeHostnameAttribute(): string
    {
        return Host::firstNative(explode(' ', trim($this->attributes[self::HOSTNAME])));
    }

    public function getUriAttribute(): string
    {
        return RequestHelper::getBaseUrl($this->hostname);
    }

    public function getAuthIdAttribute(): string
    {
        return $this->uri;
    }

    public function getNativeUriAttribute(): string
    {
        return RequestHelper::getBaseUrl($this->native_hostname);
    }

    /**
     * Origins allowed include the host names and the API origins
     * Entries format: scheme://host or *
     * @return array
     */
    public function getAllowedOriginsAttribute(): array
    {
        $hostnames = isset($this->attributes[self::HOSTNAME]) ? array_map(
            function ($v) {
                return RequestHelper::getBaseUrl($v);
            },
            explode(' ', $this->attributes[self::HOSTNAME])
        ) : [];

        // Add admin account hostname, to make super admin login work.
        $hostnames[] = 'https://' . static::getAdminAccount()->hostname;

        // If the target account is Auth account, allow all origins on same server with wildcard origin.
        if (self::getHostAccount()->isAuthAccount()) {
            $hostnames[] = 'https://*.securelogin.nu';
            $hostnames[] = 'https://*.hixportal.com';
        }

        return array_merge($hostnames, $this->getApiOriginsAttribute());
    }

    public function getPubkeyPinsAttribute(): ?array
    {
        if (!isset($this->attributes['pubkey_pins'])) {
            return null;
        }

        return json_decode($this->attributes['pubkey_pins']);
    }

    public function getLoginOptionsAttribute(): ?array
    {
        if (!isset($this->attributes['login_options'])) {
            return null;
        }

        return json_decode($this->attributes['login_options'], true);
    }

    public function isDefaultLoginEnabled(): bool
    {
        return is_null($this->login_options) || in_array("default", $this->login_options);
    }

    public function getLoginAutocompleteAttribute()
    {
        if (!isset($this->attributes[self::LOGIN_AUTOCOMPLETE])) {
            return true;
        }

        return (bool)$this->attributes[self::LOGIN_AUTOCOMPLETE];
    }

    public function getEmbedSettingsAttribute(): ?array
    {
        if (!isset($this->attributes['embed_settings'])) {
            return null;
        }
        return json_decode($this->attributes['embed_settings'], true);
    }

    public function setEmbedSettingsAttribute($value)
    {
        $this->attributes['embed_settings'] = is_string($value) ? $value : json_encode($value);
    }

    public function setStatusAttribute($value)
    {
        if ($value === self::STATUS_TRIAL) {
            $this->trial_end_date = Carbon::now()->addDays(30);
        }

        $this->attributes[self::STATUS] = $value;
    }

    /**
     * Get the URL for a route for this specific account.
     * @param string $routeName Name of a route
     * @param array $routeArgs Parameters in route
     * @return string URL Absolute URL using the first hostname of this account.
     */
    public function route(string $routeName, $routeArgs = []): string
    {
        return AccountHelper::route($this->hostname, $routeName, $routeArgs);
    }

    public function nativeRoute($routeName, $routeArgs = [], $absolute = true): string
    {
        return ($absolute ? $this->native_uri : "") . route($routeName, $routeArgs, false);
    }

    public function redirectToNativeRoute($routeName, $routeArgs = []): Redirector|Application|RedirectResponse
    {
        return redirect($this->nativeRoute("idpAuth", [
            'id_token' => Jwt::generate($this, [
                JwtClaimTypes::SESSION_ID => Auth::getSessionId(),
                SessionKey::LOGIN_AMR_KEY => Session::get(SessionKey::LOGIN_AMR_KEY, [])
            ])->toString(),
            'return_url' => $this->nativeRoute($routeName, $routeArgs, false) . "?redirected"
        ]));
    }

    /**
     * Get Native Route Url
     *
     * @param string $routeName
     * @param array $routeArgs
     * @return string
     */
    public function getNativeRouteUrl(string $routeName, array $routeArgs = []): string
    {
        return $this->nativeRoute('idpAuth', [
            'id_token' => Jwt::generate($this, [
                JwtClaimTypes::SESSION_ID => Auth::getSessionId(),
                SessionKey::LOGIN_AMR_KEY => Session::get(SessionKey::LOGIN_AMR_KEY, [])
            ])->toString(),
            'return_url' => $this->nativeRoute($routeName, $routeArgs, false) . "?redirected"
        ]);
    }

    /**
     * @return bool TRUE if the status of this account is 'trial'
     */
    public function isTrial(): bool
    {
        return $this->status === self::STATUS_TRIAL;
    }

    public function autoStartWidgetEnabled(): bool
    {
        return $this->hasAttributeRecursive('settings[auto_start_widget]')
            && $this->getAttributeRecursive('settings[auto_start_widget]');
    }

    public static function issuerByScenarioKey(string $scenario_key = null)
    {
        $postAuthActions = Session::get(SessionKey::POST_AUTH_ACTIONS, []);
        if (empty($postAuthActions)) {
            $postAuthActions = Cache::get('post_auth_actions.' . $scenario_key, []);
        }

        $relevant_actions = array_filter(
            $postAuthActions,
            function ($v) use ($scenario_key) {
                return is_array($v) &&
                    isset($v['type']) && $v['type'] == "store_identity_attr" &&
                    (!isset($v['scenario_key']) || $v['scenario_key'] == $scenario_key);
            }
        );

        if (empty($relevant_actions)) {
            return false;
        }

        return PostAuthActionsHelper::getAttributeIssuer(reset($relevant_actions));
    }

    public function onboardUserEnabled(string $scenario_key = null): bool
    {
        $issuer = self::issuerByScenarioKey($scenario_key);

        return !empty($issuer->onboardContext);
    }

    public static function getHostAccount(?Request $request = null): ?Account
    {
        return static::$sHostAccount ?? (static::$sHostAccount = self::getFromRequest($request));
    }

    public static function setHostAccount(?Account $account): void
    {
        static::$sHostAccount = $account;
    }

    private static function getFromRequest(?Request $request = null): ?Account
    {
        $host = $request == null ? Host::createFromServer() : Host::createFromRequest($request);

        if (!$host->hasName()) {
            return null;
        }

        return self::getFromHostname($host->getName());
    }

    public static function getFromHostname(string $hostname): ?Account
    {
        return self::query()->whereHostname($hostname)->first();
    }

    /**
     * Get admin account for this environment.
     * @return Account
     */
    public static function getAdminAccount(): Account
    {
        $accountRepository = resolve(AccountRepository::class);
        return $accountRepository->getAdminAccount();
    }

    /**
     * Check if an account has API and/or GUI access enabled.
     * @param string $type either "gui" or "api".
     * @return bool TRUE if the account supports the usage type.
     */
    public function canUse(string $type): bool
    {
        return in_array($type, $this->usage_types);
    }

    /**
     * @return bool TRUE if any hostname does not belong to securelogin
     */
    public function hasCustomDomain(): bool
    {
        return collect(explode(' ', $this->hostname))
                ->map(function ($item) {
                    return trim($item);
                })
                ->filter(function ($item) {
                    return !str_ends_with($item, '.' . Host::SECURELOGIN_DOMAIN)
                        && !str_ends_with($item, '.' . Host::HIX_DOMAIN);
                })->count() > 0;
    }

    /**
     * Get the path to the logo image. Circle (for new Vue.js GUI) takes priority.
     * Uses the old logo (logo_white) as fallback.
     * @return string|null Relative path to image file.
     */
    public function getLogoUrl(): ?string
    {
        $url = $this->getFileRoute('logo_circle', null, self::LOGO_CIRCLE_IMG_PATH);
        if (empty($url)) {
            $url = $this->getFileRoute('logo_white', null, self::LOGO_WHITE_IMG_PATH);
        }

        return $url;
    }

    /**
     * Get the path to the logo image. Circle (for new Vue.js GUI) takes priority.
     * @return string|null Relative path to image file.
     */
    public function getLogoPath(): ?string
    {
        return $this->getFilePath('logo_circle', resource_path(self::LOGO_CIRCLE_IMG_PATH));
    }

    /**
     * If exists, return the path to the email header image.
     * @return string|null
     */
    public function getEmailHeaderPath(): ?string
    {
        return $this->getFilePath('email_header');
    }

    public function getBackgroundUrl(): ?string
    {
        return $this->getFileRoute('background_img', null, self::BACKGROUND_IMG_PATH);
    }

    public function getOldLogoUrl(): ?string
    {
        $url = $this->getFileRoute('logo_white');
        if (empty($url)) {
            $url = $this->getFileRoute('logo_circle');
        }

        return $url;
    }

    public function getUserWhitelist(): ?array
    {
        return [];
    }

    public function getMfaAttribute(): ?bool
    {
        return false;
    }

    public function getBypassIpCheckAttribute(): ?bool
    {
        return false;
    }

    public function companyManagers(): Collection
    {
        /** @var Role $role */
        $role = Role::where('name', Role::COMPANY_MANAGER)->first();

        $user_ids = $this->users()->pluck('id')->toArray();
        $company_managers_ids = $role->users()->whereIn('id', $user_ids)->pluck('id')->toArray();
        $account_managers_ids = $this->managers()->pluck('id')->toArray();
        $managers_ids = array_unique(array_merge($company_managers_ids, $account_managers_ids));

        return $this->users()->whereIn('id', $managers_ids)->orderBy(User::FIRST_NAME)->orderBy(User::LAST_NAME)->get();
    }

    public function disableAfasOpenId(bool $saveAfterUpdate = false)
    {
        $settings = $this->settings;
        $settings['afas_openid_enabled'] = false;
        $this->settings = $settings;
        $saveAfterUpdate && $this->save();
    }

    public function saveAfasEnvironmentNumber(
        ?string $afasEnvironmentNumber = null,
        bool $enable = false,
        ?bool $saveAfterUpdate = false
    ): void {
        $settings = $this->settings;

        if ($afasEnvironmentNumber !== null) {
            if (!is_numeric($afasEnvironmentNumber) || strlen($afasEnvironmentNumber) !== 5) {
                throw new \InvalidArgumentException('AFAS number should be exactly 5 digits');
            }
            $settings['afas_environment_number'] = $afasEnvironmentNumber;
        }

        // Only set to enabled when generating the openid, not always.
        if ($enable) {
            $settings['afas_openid_enabled'] = true;
        }

        $this->settings = $settings;
        $saveAfterUpdate && $this->save();
    }

    /**
     * @param string $reference_name
     * @return bool
     */
    public function hasService(string $reference_name): bool
    {
        $services = Service::where('reference_name', $reference_name)->get();
        foreach ($services as $service) {
            if (
                $this->accountServices()
                    ->where('service_id', $service->id)
                    ->where('enabled', true)
                    ->exists()
            ) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param string $reference_name
     * @return bool
     */
    public function hasEnabledService(string $reference_name): bool
    {
        return Service::query()
            ->join('account_services', 'account_services.service_id', '=', 'services.id')
            ->where([
                'services.reference_name' => $reference_name,
                'account_services.enabled' => true,
                'account_services.account_id' => $this->id,
                'account_services.deleted_at' => null
            ])
            ->exists();
    }

    /**
     * To prevent abusing Hix for sending many emails we limit the amount of users in the trial account.
     * Soft deleted users are also counted, otherwise the restriction can be circumvented by deleting the users.
     * @return bool TRUE if the maximum amount of users for this account has been reached.
     */
    public function userLimitReached(): bool
    {
        if ($this->isTrial()) {
            if (isset($this->settings['unlimited_trial_users']) && $this->settings['unlimited_trial_users']) {
                return false;
            }
            return $this->users()->withTrashed()->count() >= config('app.trial_user_limit');
        }

        return false;
    }

    /**
     * @return bool
     */
    public function showActivation(): bool
    {
        $hasAuthMethods = $this->mainContext()->whereNotNull(Context::AUTH_METHODS)->exists();
        return $this->isTrial() && !$hasAuthMethods;
    }

    public function getUnlimitedTrialUsersSetting(): bool
    {
        return (bool)($this->settings['unlimited_trial_users'] ?? false);
    }

    public function companyIds(): array
    {
        return $this->companies->pluck('id')->toArray();
    }

    public function activeCompanyIds(): array
    {
        return $this->activeCompanies->pluck('id')->toArray();
    }

    public function supportEmails(): array
    {
        if (empty($this->support_email)) {
            Log::Error('Support email for account #' . $this->id . ' is empty');
            return [];
        }

        $emails = explode(';', $this->support_email);

        foreach ($emails as &$email) {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                Log::Error('Support email "' . $email . '" of account #' . $this->id . ' is an invalid email address'); //phpcs:ignore
            }
        }

        return $emails;
    }

    public function hasAccountBillings(): bool
    {
        return $this->accountBillings()->exists();
    }

    public function isAuthAccount(): bool
    {
        return $this->name === 'Auth';
    }

    public function canUseNoFrontAuth(): bool
    {
        return $this->hasLicense('no_front_auth');
    }

    public function isExpired(): bool
    {
        return $this->isTrial() && $this->trial_end_date < Carbon::now();
    }

    public function getRedirectEmailsToMailtrapSetting(): bool
    {
        return (bool)($this->settings['redirect_emails_to_mailtrap'] ?? false);
    }

    public function getFaviconPath(): string
    {
        return $this->getImagesPath() . '/favicon.png';
    }

    public function getImagesPath(): string
    {
        return storage_path('app/Account/' . $this->id . '/images');
    }

    public function beforeSave(array $options = []): bool
    {
        $this->beforeSaveImageHandling();
        return parent::beforeSave($options);
    }

    public function allowsSendingFrontCodeByEmail(): bool
    {
        if (!$this->hasLicense(License::TOTP_SETUP_BY_EMAIL_LICENSE)) {
            return false;
        }
        return (bool)($this->settings[self::SETTING_ALLOW_EMAIL_FRONT_AUTH] ?? false);
    }

    /**
     * @param string|null $value
     */
    public function setSupportEmailAttribute(?string $value): void
    {
        if ($value !== null) {
            $this->attributes[self::SUPPORT_EMAIL] = str_replace(array(' ', "\r", "\n"), '', $value);
        }
    }

    public function hydrate(array $objects): Collection
    {
        return parent::hydrate(
            array_map(static function ($object) {
                foreach ($object as $k => $v) {
                    if ($k === self::SUPPORT_EMAIL) {
                        $object->$k = str_replace(array(' ', "\r", "\n"), '', $v);
                    }
                }

                return $object;
            }, $objects)
        );
    }
}
