<?php

namespace App\Helpers;

use App\ValueObject\MobileNumber;
use Config;
use Log;
use MessageBird\Client;
use MessageBird\Common\HttpClient;
use MessageBird\Exceptions\AuthenticateException;
use MessageBird\Exceptions\BalanceException;
use MessageBird\Exceptions\RequestException;
use MessageBird\Objects\BaseList;
use MessageBird\Objects\Message;
use MessageBird\Objects\VoiceMessage;
use Throwable;

class MessagebirdHelper
{
    public Client $client;
    public array $errorMessages = [];

    public function __construct(\MessageBird\Client $client = null)
    {
        if (!is_null($client)) {
            $this->client = $client;
        } else {
            if (!Config::get('app.debug')) {
                $key = Config::get('services.messagebird.key_prod');
            } else {
                $key = Config::get('services.messagebird.key_test');
            }

            // Change default connection timeout from 2 seconds to 10
            // Downside -> Voice and Chat API are not working
            $http_client = new HttpClient(Client::ENDPOINT, 10, 10);
            $this->client = new Client($key, $http_client);
        }

        $this->errorMessages = $this->getErrorMessages();
    }

    public function getErrorMessages(): array
    {
        return [
            'authentication' => 'Authentication failed, check your access key',
            'balance' => 'Not enough balance on your account',
            'recipient' => 'Invalid mobile number',
        ];
    }

    public function getBalance()
    {
        try {
            return $this->client->balance->read();
        } catch (AuthenticateException $e) {
            return $this->errorMessages['authentication'];
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function lookup($recipient)
    {
        $recipient = $this->normalizeRecipient($recipient);

        try {
            return $this->client->lookup->read($recipient);
        } catch (AuthenticateException $e) {
            return $this->errorMessages['authentication'];
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function createHlr($msisdn, $reference)
    {
        $hlr = new \MessageBird\Objects\Hlr();
        $hlr->msisdn = $msisdn;
        $hlr->reference = $reference;

        try {
            return $this->client->hlr->create($hlr);
        } catch (AuthenticateException $e) {
            return $this->errorMessages['authentication'];
        } catch (BalanceException $e) {
            return $this->errorMessages['balance'];
        } catch (Throwable $e) {
            return $e->getMessage();
        }
    }

    public function listHlrs($offset = 100, $limit = 30)
    {
        try {
            return $this->client->hlr->getList(['offset' => $offset, 'limit' => $limit]);
        } catch (AuthenticateException $e) {
            return $this->errorMessages['authentication'];
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function viewHlr($id)
    {
        try {
            return $this->client->hlr->read($id);
        } catch (AuthenticateException $e) {
            return $this->errorMessages['authentication'];
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function createMessage($originator, $recipient, $body)
    {
        $recipient = $this->normalizeRecipient($recipient);

        if (self::isUsaNumber($recipient)) {
            $originator = Config::get('services.messagebird.usa_originator');
        }

        $message = new Message();
        $message->originator = $originator;
        $message->recipients = [$recipient];
        $message->body = $body;

        try {
            return $this->client->messages->create($message);
        } catch (AuthenticateException $e) {
            Log::critical("AuthenticateException - " . $e->getMessage() . " - Message: " . serialize($message));
            return false;
        } catch (BalanceException $e) {
            Log::critical("BalanceException - " . $e->getMessage() . " - Message: " . serialize($message));
            return false;
        } catch (RequestException $e) {
            Log::critical("RequestException - " . $e->getMessage() . " - Message: " . serialize($message));
            //Assuming that an invalid request means an invalid recipient or invalid originator
            return false;
        } catch (Throwable $e) {
            Log::error("Retry after Exception - " . $e->getMessage() . " - Message: " . serialize($message));

            try {
                return $this->client->messages->create($message);
            } catch (Throwable $e) {
                Log::critical("Exception - " . $e->getMessage() . " - Message: " . serialize($message));
                return false;
            }
        }
    }

    /**
     * @param string $number
     * @return bool TRUE if number starts with +1
     */
    public static function isUsaNumber(string $number): bool
    {
        return (substr($number, 0, 2) == '+1');
    }

    /**
     * @deprecated We don't use this
     */
    public function deleteMessage($id)
    {
        try {
            return $this->client->messages->delete($id);
        } catch (AuthenticateException $e) {
            return $this->errorMessages['authentication'];
        } catch (Throwable $e) {
            return $e->getMessage();
        }
    }

    /**
     * Returns list of messages received on a virtual number.
     * @return BaseList|string Object containing items or string with error message.
     */
    public function listMessages($offset = 100, $limit = 30, $recipient = null)
    {
        try {
            return $this->client->messages
                ->getList(['offset' => $offset, 'limit' => $limit, 'recipient' => $recipient]);
        } catch (AuthenticateException $e) {
            return $this->errorMessages['authentication'];
        } catch (Throwable $e) {
            return $e->getMessage();
        }
    }

    public function viewMessage($id)
    {
        try {
            return $this->client->messages->read($id);
        } catch (AuthenticateException $e) {
            return $this->errorMessages['authentication'];
        } catch (Throwable $e) {
            return $e->getMessage();
        }
    }

    /**
     * @deprecated We don't use this
     */
    public function createVoiceMessage($body, $recipients = [], $language = 'en-gb', $voice = 'female', $ifMachine = 'continue') //phpcs:ignore
    {
        $voiceMessage = new VoiceMessage();
        $voiceMessage->recipients = $recipients;
        $voiceMessage->body = $body;
        $voiceMessage->language = $language;
        $voiceMessage->voice = $voice;
        $voiceMessage->ifMachine = $ifMachine;

        try {
            return $this->client->messages->voicemessages($voiceMessage);
        } catch (AuthenticateException $e) {
            return $this->errorMessages['authentication'];
        } catch (BalanceException $e) {
            return $this->errorMessages['balance'];
        } catch (Throwable $e) {
            return $e->getMessage();
        }
    }

    /**
     * @deprecated We don't use this
     */
    public function listVoiceMessages($offset = 100, $limit = 30)
    {
        try {
            return $this->client->voicemessages->getList(['offset' => $offset, 'limit' => $limit]);
        } catch (AuthenticateException $e) {
            return $this->errorMessages['authentication'];
        } catch (Throwable $e) {
            return $e->getMessage();
        }
    }

    /**
     * @deprecated We don't use this
     */
    public function viewVoiceMessage($id)
    {
        try {
            return $this->client->voicemessages->read($id);
        } catch (AuthenticateException $e) {
            return $this->errorMessages['authentication'];
        } catch (Throwable $e) {
            return $e->getMessage();
        }
    }

    protected function normalizeRecipient($recipient)
    {
        $recipient = preg_replace("/[^0-9]/", "", $recipient);

        if (substr($recipient, 0, 2) == "00") {
            $recipient = substr($recipient, 2);
        }

        return $recipient;
    }

    /**
     * @param string $number
     * @return bool TRUE if it is a valid number.
     * @deprecated Use MobileNumber instead
     * Number needs to start with "+" and followed by 9 to 19 numbers.
     * Dutch numbers that start with "+31" should start with "+316"
     */
    public static function isValidNumber(string $number): bool
    {
        return MobileNumber::isValid($number);
    }

    /**
     * Sanitize a mobile number
     *
     * @param string $number
     *
     * @return string
     *
     * @deprecated use function "sanitize" in MobileNumber.php instead
     */
    public static function sanitize(string $number): string
    {
        return (string)new MobileNumber($number);
    }
}
