<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Helpers\Http;

class WebExtractor
{
    private $cookies = [];

    public function requestWebDoc($url)
    {
        $content = $this->getUrl($url);

        $dom = new \DOMDocument();
        @$dom->loadHTML($content);

        return new WebDoc($dom, array());
    }

    private function getUrl($url, $max_redirects = 10)
    {
        if ($max_redirects < 1) {
            trigger_error("Max redirects reached for url $url");
        }

        (function_exists('curl_init')) ? '' : trigger_error('cURL Must be installed for geturl function to work. Ask your host to enable it or uncomment extension=php_curl.dll in php.ini');//phpcs:ignore

        $curl = curl_init();
        $header[0] = "Accept: text/xml,application/xml,application/xhtml+xml,";
        $header[0] .= "text/html;q=0.9,text/plain;q=0.8,image/png,*/*;q=0.5";
        $header[] = "Cache-Control: max-age=0";
        $header[] = "Connection: keep-alive";
        $header[] = "Keep-Alive: 300";
        $header[] = "Accept-Charset: ISO-8859-1,utf-8;q=0.7,*;q=0.7";
        $header[] = "Accept-Language: en-us,en;q=0.5";
        //$header[] = "Pragma: ";

        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/52.0.2743.116 Safari/537.36');//phpcs:ignore
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl, CURLOPT_HEADER, true);
        curl_setopt($curl, CURLOPT_REFERER, $url);
        curl_setopt($curl, CURLOPT_ENCODING, 'gzip,deflate');
        curl_setopt($curl, CURLOPT_AUTOREFERER, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        //curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 30);

        foreach ($this->cookies as $cookie) {
            curl_setopt($curl, CURLOPT_COOKIE, http_build_cookie($cookie));
        }

        $raw_content = curl_exec($curl);
        $status = curl_getinfo($curl);
        $curl_error = curl_error($curl);
        $curl_errno = curl_errno($curl);
        curl_close($curl);

        $header = substr($raw_content, 0, $status['header_size']);
        $body = trim(str_replace($header, '', $raw_content));

        if ($status['http_code'] != 200) {
            if ($status['http_code'] == 301 || $status['http_code'] == 302) {
                $matches = array();
                preg_match("/(Location:|URI:)[^(\n)]*/", $header, $matches);
                $redirect_url = trim(str_replace($matches[1], "", $matches[0]));
                $url_parsed = parse_url($url);

                if (strpos($redirect_url, "http") === false) {
                    $base_url = "$url_parsed[scheme]://$url_parsed[host]";
                    if (isset($url_parsed["port"])) {
                        $base_url .= ":$url_parsed[port]";
                    }
                    $redirect_url = "$base_url/$redirect_url";
                }
                return $this->geturl($redirect_url, $max_redirects - 1);
            }

            $oline = '';
            foreach ($status as $key => $eline) {
                if (is_array($eline)) {
                    $eline = implode(", ", $eline);
                }
                $oline .= '[' . $key . ']' . $eline . ' ';
            }
            $line = $oline . " \r\n " . $url . "\r\n-----------------\r\n";

            if ($status['http_code']) {
                trigger_error($status['http_code'] . " - " . $line);
            } else {
                trigger_error("Curl error($curl_errno) - $curl_error");
            }
            return false;
        }
        return $body;
    }

    /**
     * Query an HTTP(S) URL with the given request parameters and return the
     * response headers and status code. The socket is returned as well and
     * will point to the begining of the response payload (after all headers
     * have been read), and must be closed with fclose().
     * @param $request the request method may optionally be overridden.
     * @param $timeout connection and read timeout in seconds
     */
    public static function request($request, $timeout = 5)
    {
        $url = $request['url'];

        //echo $url."\n<br/>";
        // Extract the hostname from url
        $parts = parse_url($url);
        if (array_key_exists('host', $parts)) {
            $remote = $parts['host'];
        } else {
            return myErrorHandler("url ($url) has no host. Is it relative?");
        }
        if (array_key_exists('port', $parts)) {
            $port = $parts['port'];
        } else {
            $port = 0;
        }

        // Beware that RFC2616 (HTTP/1.1) defines header fields as case-insensitive entities.
        $request_headers = "";
        foreach ($request['headers'] as $name => $value) {
            switch (strtolower($name)) {
                //omit some headers
                case "keep-alive":
                case "connection":
                    //can cause a problem if client communication is already being
                    //compressed by the server/app that integrates this script
                    //(which would double compress the content, once from the remote
                    //server to us, and once from us to the client, but the client
                    //would de-compress only once).
                case "accept-encoding":
                    break;
                    // correct the host parameter
                case "host":
                    $host_info = $remote;
                    if ($port) {
                        $host_info .= ':' . $port;
                    }
                    $request_headers .= "$name: $host_info\r\n";
                    break;
                    // forward all other headers
                default:
                    $request_headers .= "$name: $value\r\n";
                    break;
            }
        }

        //set fsockopen transport scheme, and the default port
        switch (strtolower($parts['scheme'])) {
            case 'https':
                $scheme = 'ssl://';
                if (!$port) {
                    $port = 443;
                }
                break;
            case 'http':
                $scheme = '';
                if (!$port) {
                    $port = 80;
                }
                break;
            default:
                //some other transports are available but not really supported
                //by this script: http://php.net/manual/en/transports.inet.php
                $scheme = $parts['scheme'] . '://';
                if (!$port) {
                    return myErrorHandler("Unknown scheme ($scheme) and no port.");
                }
                break;
        }

        //we make the request with socket operations since we don't want to
        //depend on the curl extension, and the higher level wrappers don't
        //give us usable error information.

        $sock = @fsockopen("$scheme$remote", $port, $errno, $errstr, $timeout);
        if (!$sock) {
            return trigger_error("Unable to open URL ($url): $errstr");
        }

        //the timeout in fsockopen is only for the connection, the following
        //is for reading the content
        stream_set_timeout($sock, $timeout);

        //an absolute url should only be specified for proxy requests
        if (array_key_exists('path', $parts)) {
            $path_info = $parts['path'];
        } else {
            $path_info = '/';
        }

        if (array_key_exists('query', $parts)) {
            $path_info .= '?' . $parts['query'];
        }
        if (array_key_exists('fragment', $parts)) {
            $path_info .= '#' . $parts['fragment'];
        }

        $out = $request["method"] . " " . $path_info . " " . $request["protocol"] . "\r\n"
            . $request_headers
            . "Connection: close\r\n\r\n";
        fwrite($sock, $out);
        fwrite($sock, $request['payload']);

        $header_str = stream_get_line($sock, 1024 * 16, "\r\n\r\n");
        $headers = self::parse_headers($header_str);
        $status_line = array_shift($headers);

        // get http status
        preg_match('|HTTP/\d+\.\d+\s+(\d+)\s+.*|i', $status_line, $match);

        $status = '';
        if (isset($match[1])) {
            $status = $match[1];
        }

        return array('headers' => $headers, 'socket' => $sock, 'status' => $status);
    }

    /**
     * Parses a string containing multiple HTTP header lines into an array
     * of key => values.
     * Inspired by HttpHelper::Daemon (CPAN).
     */
    public static function parse_headers($header_str) //phpcs:ignore
    {
        $headers = array();

        //ignore leading blank lines
        $header_str = preg_replace("/^(?:\x0D?\x0A)+/", '', $header_str);

        while (preg_match("/^([^\x0A]*?)\x0D?(?:\x0A|\$)/", $header_str, $matches)) {
            $header_str = substr($header_str, strlen($matches[0]));
            $status_line = $matches[1];

            if (empty($headers)) {
                // the status line
                $headers[] = $status_line;
            } elseif (preg_match('/^([^:\s]+)\s*:\s*(.*)/', $status_line, $matches)) {
                if (isset($key)) {
                    //previous header is finished (was potentially multi-line)

                    if (!isset($headers[$key])) {
                        $headers[$key] = $val;
                    } elseif (!is_array($headers[$key])) {
                        $headers[$key] = array($headers[$key], $val);
                    } else {
                        $headers[$key][] = $val;
                    }
                }
                list(, $key, $val) = $matches;
            } elseif (preg_match('/^\s+(.*)/', $status_line, $matches)) {
                //continue a multi-line header
                $val .= " " . $matches[1];
            } else {
                //empty (possibly malformed) header signals the end of all headers
                break;
            }
        }
        if (isset($key)) {
            if (!isset($headers[$key])) {
                $headers[$key] = $val;
            } elseif (!is_array($headers[$key])) {
                $headers[$key] = array($headers[$key], $val);
            } else {
                $headers[$key][] = $val;
            }
        }
        return $headers;
    }

    public static function getallheaders()
    {
        $headers = '';
        foreach ($_SERVER as $name => $value) {
            if (substr($name, 0, 5) == 'HTTP_') {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value; //phpcs:ignore
            }
        }
        return $headers;
    }
}
