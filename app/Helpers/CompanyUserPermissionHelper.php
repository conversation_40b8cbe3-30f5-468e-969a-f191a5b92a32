<?php

namespace App\Helpers;

use App\Models\CompanyUserPermission;

/** @deprecated This class is not needed anymore  */
class CompanyUserPermissionHelper
{
    /**
     * Compare two permissions and return the dominant one.
     * @param string|null $a First permission to compare.
     * @param string|null $b Second permission to compare.
     * @return string Dominant permission value out of the two.
     */
    public static function dominant(?string $a, ?string $b): string
    {
        $key1 = array_search($a, CompanyUserPermission::ALLOWED_PERMISSIONS);
        if ($key1 === false) {
            $key1 = -1;
        }
        $key2 = array_search($b, CompanyUserPermission::ALLOWED_PERMISSIONS);
        if ($key2 === false) {
            $key2 = -1;
        }

        if ($key1 > $key2) {
            $key = $key1;
        } else {
            $key = $key2;
        }

        if ($key === -1) {
            return CompanyUserPermission::PERMISSION_NONE;
        }

        return CompanyUserPermission::ALLOWED_PERMISSIONS[$key];
    }
}
