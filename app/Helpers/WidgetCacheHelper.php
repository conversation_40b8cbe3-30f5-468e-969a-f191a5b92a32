<?php

namespace App\Helpers;

class WidgetCacheHelper
{
    public static function has(array $cache, int $max_age): bool
    {
        if (empty($cache)) {
            return false;
        }

        $timestamps = array_keys($cache);
        $created_at = reset($timestamps);

        if (time() > ($created_at + $max_age) || empty($cache[$created_at])) {
            return false;
        }

        return true;
    }

    public static function clean(array $cache, int $history): array
    {
        while (count($cache) > $history) {
            array_pop($cache);
        }
        return $cache;
    }
}
