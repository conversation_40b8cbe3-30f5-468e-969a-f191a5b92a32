<?php

namespace App\Helpers;

class WidgetInheritanceHelper
{
    public function mergeValue($local_value, $parent_value, $type)
    {
        if ($local_value == null || $type == "overwrite_child") {
            $merged = $parent_value;
        } elseif ($parent_value == null || $type == "overwrite_parent") {
            $merged = $local_value;
        } else {
            switch ($type) {
                case "csv":
                    if (!is_array($local_value)) {
                        $local_value = explode(",", $local_value);
                    }
                    if (!is_array($parent_value)) {
                        $parent_value = explode(",", $parent_value);
                    }

                    $merged = array_merge($local_value, $parent_value);
                    break;
                case "array_merge_overwrite_parent":
                    $merged = ArrayHelper::mergeRecursive($parent_value, $local_value);
                    break;
                case "array_merge_overwrite_child":
                    $merged = ArrayHelper::mergeRecursive($local_value, $parent_value);
                    break;
                case "concat":
                    $merged = $local_value . $parent_value;
                    break;
                default:
                    trigger_error("Unknown merge type $type", E_USER_ERROR);
                    $merged = $local_value;
            }
        }

        if (substr($type, 0, 6) == 'array_' && !is_array($merged)) {
            if (empty($merged)) {
                $merged = [];
            } else {
                trigger_error("Result for merge type $type should be an array. Value=" . serialize($merged));
            }
        }

        if ($type == 'csv' && !is_array($merged)) {
            $merged = explode(",", $merged);
        }

        return $merged;
    }
}
