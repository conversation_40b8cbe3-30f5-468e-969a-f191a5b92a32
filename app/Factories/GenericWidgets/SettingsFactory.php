<?php

namespace App\Factories\GenericWidgets;

class SettingsFactory
{
    public const PROPERTIES_USERNAME = 'properties[username]';
    public const PROPERTIES_PASSWORD = 'properties[password]';
    public const TYPE = 'type';
    public const TYPE_USERNAME = 'username';
    public const TYPE_EMAIL_AS_USERNAME = 'email_as_username';
    public const TYPE_PASSWORD = 'password';

    /**
     * @param array $selectors
     * @return array
     */
    public static function create(array $selectors): array
    {
        $settings = [];

        foreach ($selectors as $selector) {
            $setting = self::generateSetting($selector);
            if (!is_null($setting)) {
                $settings = array_merge($settings, $setting);
            }
        }

        return $settings;
    }

    /**
     * @param string $selector
     * @return \string[][]|null
     */
    private static function generateSetting(array $selector): ?array
    {
        if ($selector['type'] === self::TYPE_USERNAME) {
            return [
                self::PROPERTIES_USERNAME => [
                    self::TYPE => self::TYPE_USERNAME
                ]
            ];
        } elseif ($selector['type'] === self::TYPE_PASSWORD) {
            return [
                self::PROPERTIES_PASSWORD => [
                    self::TYPE => self::TYPE_PASSWORD
                ]
            ];
        }

        return null;
    }
}
