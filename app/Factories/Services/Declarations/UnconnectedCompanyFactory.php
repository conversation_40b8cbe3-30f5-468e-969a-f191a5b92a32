<?php

namespace App\Factories\Services\Declarations;

use App\ValueObject\Repositories\Soap\Nmbrs\Company;
use App\ValueObject\Services\Declarations\UnconnectedCompany;

class UnconnectedCompanyFactory
{
    /**
     * Create an UnconnectedCompany object from a NmbrsCompany.
     *
     * @param  Company  $company
     *
     * @return UnconnectedCompany
     */
    public static function createFromNmbrsCompany(Company $company): UnconnectedCompany
    {
        return new UnconnectedCompany(
            $company->getID(),
            $company->getName(),
            $company->getID(),
            '',
            $company->getKvkNr()
        );
    }
}
