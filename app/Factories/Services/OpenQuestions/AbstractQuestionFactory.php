<?php

namespace App\Factories\Services\OpenQuestions;

use App\Helpers\CurrencyHelper;
use App\Support\CarbonImmutable;

class AbstractQuestionFactory
{
    protected string $title;
    protected string $subtitle;

    protected string $name;
    protected string $amount;
    protected string $currency;
    protected string $invoiceNumber;
    protected string $bookkeepingNumber;
    protected string $description;
    protected CarbonImmutable $transactionDate;

    public function setTransactionDate(string|\DateTimeInterface $transactionDate): void
    {
        $this->transactionDate = CarbonImmutable::parse($transactionDate);
    }

    public function setAndFormatAmount(string $amount): void
    {
        $this->amount = CurrencyHelper::formatString($amount);
    }

    public function setCurrency(string $currency): void
    {
        $this->currency = $currency;
    }

    public function setTitle(string $title): void
    {
        $this->title = $title;
    }

    public function setSubtitle(string $subtitle): void
    {
        $this->subtitle = $subtitle;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function setInvoiceNumber(string $invoiceNumber): void
    {
        $this->invoiceNumber = $invoiceNumber;
    }

    public function setBookkeepingNumber(string $bookkeepingNumber): void
    {
        $this->bookkeepingNumber = $bookkeepingNumber;
    }

    public function setDescription(string $descrption): void
    {
        $this->description = $descrption;
    }
}
