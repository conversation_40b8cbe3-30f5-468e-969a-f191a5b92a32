<?php

namespace App\Factories\Services\OpenQuestions;

use App\Exceptions\BadRequestException;
use App\Models\OpenQuestions\OpenQuestionCategory;
use App\Models\OpenQuestions\OpenQuestionRelationType;
use App\Models\OpenQuestions\OpenQuestionType;
use App\Models\OpenQuestions\Questions\Bookkeeping;
use App\Models\OpenQuestions\Questions\Client;
use App\Models\OpenQuestions\Questions\Declaration;
use App\Models\OpenQuestions\Questions\Fiscal;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Models\OpenQuestions\Questions\OpticalCharacterRecognition;
use App\Models\OpenQuestions\Questions\Other;
use App\Models\OpenQuestions\Questions\Templates\TemplateEntry;
use App\Models\OpenQuestions\Questions\Wage;
use App\Models\OpenQuestions\Questions\Yearwork;

class RelationFactory
{
    public static function createFromRelationType(
        string $relationType
    ): string {
        return match ($relationType) {
            OpenQuestionRelationType::BOOKKEEPING => Bookkeeping::class,
            OpenQuestionRelationType::WAGE => Wage::class,
            OpenQuestionRelationType::OCR => OpticalCharacterRecognition::class,
            OpenQuestionRelationType::FISCAL => Fiscal::class,
            OpenQuestionRelationType::OTHER => Other::class,
            OpenQuestionRelationType::CLIENT => Client::class,
            OpenQuestionRelationType::YEARWORK => Yearwork::class,
            OpenQuestionRelationType::DECLARATION => Declaration::class,
            OpenQuestionRelationType::TEMPLATE => TemplateEntry::class,
            default => throw new BadRequestException('Relation Type unknown: ' . $relationType),
        };
    }

    public static function determineRelationType(OpenQuestion $openQuestion): string
    {
        $questionType = $openQuestion->questionType;

        if (isset($questionType)) {
            if ($questionType->subject === OpenQuestionType::SUBJECT_DECLARATION) {
                return OpenQuestionRelationType::DECLARATION;
            } elseif ($questionType->key === OpenQuestion::TYPE_TEMPLATE) {
                return OpenQuestionRelationType::TEMPLATE;
            }
        }

        return match ($openQuestion->category) {
            OpenQuestionCategory::BOOKKEEPING => OpenQuestionRelationType::BOOKKEEPING,
            OpenQuestionCategory::WAGE => OpenQuestionRelationType::WAGE,
            OpenQuestionCategory::FISCAL => OpenQuestionRelationType::FISCAL,
            OpenQuestionCategory::OCR => OpenQuestionRelationType::OCR,
            OpenQuestionCategory::OTHER => OpenQuestionRelationType::OTHER,
            OpenQuestionCategory::YEARWORK => OpenQuestionRelationType::YEARWORK,
            OpenQuestionCategory::CLIENT => OpenQuestionRelationType::CLIENT,
            default => throw new BadRequestException('Could not determine relation type for question type: ' . $openQuestion->category), //phpcs:ignore
        };
    }
}
