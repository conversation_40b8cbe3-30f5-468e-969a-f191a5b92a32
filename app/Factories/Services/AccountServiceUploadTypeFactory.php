<?php

namespace App\Factories\Services;

use App\AccountService;
use App\AccountServiceUploadType;
use UnexpectedValueException;

class AccountServiceUploadTypeFactory
{
    public static function createFromArray(AccountService $accountService, array $data): AccountServiceUploadType
    {
        if (!isset($data['title'])) {
            throw new UnexpectedValueException('Missing upload type title');
        }

        $title = trim($data['title']);
        if (strlen($title) < 1 || strlen($title) > 40) {
            throw new UnexpectedValueException('Upload type title too long or short');
        }

        $model = new AccountServiceUploadType();

        $model->title = $title;
        if (isset($data['id']) && $data['id'] !== '') {
            $model->id = intval($data['id']);
        }
        $model->account_service_id = $accountService->id;
        $model->account_id = $accountService->account_id;

        $permission = 'inherit';
        if (isset($data['permission'])) {
            $permission = $data['permission'];
        }

        $model->permission = $permission;

        return $model;
    }
}
