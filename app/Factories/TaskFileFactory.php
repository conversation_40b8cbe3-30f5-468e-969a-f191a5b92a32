<?php

namespace App\Factories;

use App\AccountService;
use App\Helpers\FilenameHelper;
use App\Helpers\PdfDetectorHelper;
use App\Helpers\XmlDetectorHelper;
use App\Models\TaskFile;
use App\Models\TaskFile\Pdf;
use App\Models\TaskFile\Xbrl;
use App\Models\TaskFile\Xml;
use App\Services\Parser\Xbrl\AbstractXbrlParser;
use App\ValueObject\Declarations\DeclarationData;

class TaskFileFactory
{
    /**
     * @param AccountService $accountService
     * @param string $content Content of file (XML/XBRL/PDF)
     * @param string $filename Filename including extension
     * @param string|null $type File type. Should be a TaskFile TYPE_ constant
     * @param TaskFile|null $belongsTo File this one belongs to. If this is a PDF, usually it can be an XML/XBRL
     * @param int|null $uploadTypeId
     * @param int $order
     * @return Pdf|Xbrl|Xml New instance of object. Not saved in database.
     */
    public static function create(
        AccountService $accountService,
        string $content,
        string $filename,
        string $type = null,
        TaskFile $belongsTo = null,
        int $uploadTypeId = null,
        int $order = 0
    ): Xbrl|Pdf|Xml {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        if ($accountService->service->isManual()) {
            $filename = FilenameHelper::sanitize($filename);
        }

        switch ($extension) {
            case TaskFile::EXTENSION_XBRL:
                $taskFile = self::createXbrl($accountService, $content, $filename);
                break;
            case TaskFile::EXTENSION_XML:
                if (in_array($type, [TaskFile::TYPE_SEPA_SALARY, TaskFile::TYPE_SEPA_TAX])) {
                    $taskFile = self::createSepaXml($accountService, $content, $filename, $type);
                } else {
                    $taskFile = self::createXml($accountService, $content, $filename);
                }
                break;
            case TaskFile::EXTENSION_PDF:
                $taskFile = self::createPdf($accountService, $content, $filename, $type);
                break;
            default:
                throw new \UnexpectedValueException('Incorrect extension type: ' . $extension);
        }

        if (isset($belongsTo)) {
            $taskFile->belongs_to_uuid = $belongsTo->uuid;
        }

        $taskFile->order = $order;

        if (
            !empty($uploadTypeId)
            && $accountService->uploadTypes()->where('id', $uploadTypeId)->exists()
        ) {
            $taskFile->account_service_upload_type_id = $uploadTypeId;
        }

        $parsedData = $taskFile->getParsedDataAttribute();
        // If declaration is IHZ, and it has a partner we group both declarations
        if (isset($parsedData[DeclarationData::BSNNUMBER_PARTNER])) {
            $mainBsn = $parsedData[DeclarationData::BSNNUMBER_FIELD];
            $partnerBsn = $parsedData[DeclarationData::BSNNUMBER_PARTNER];

            // In order to have always the same group reference name we save the bsns sorted
            // from the lowest to the greatest
            $bsns = $mainBsn < $partnerBsn ? $mainBsn . '_' . $partnerBsn : $partnerBsn . '_' . $mainBsn;

            $taskFile->group_reference_name = 'IHZ_' . $bsns . '_' . $parsedData[DeclarationData::YEAR_FIELD];
        }

        return $taskFile;
    }

    public static function createXbrl(AccountService $accountService, string $content, string $filename): Xbrl
    {
        $taskFile = new Xbrl();
        $taskFile->account_id = $accountService->account_id;
        $taskFile->account_service_id = $accountService->id;
        $taskFile->filename = $filename;
        $taskFile->extension = TaskFile::EXTENSION_XBRL;
        $taskFile->setContent($content);
        $parser = XmlDetectorHelper::detectParser($content, $filename);
        $taskFile->type = $parser->getTaskFileType();
        $taskFile->parsed_data = $parser->getParsedResult();
        $taskFile->date_start = $taskFile->parsed_data[TaskFile::DATE_START] ?? null;
        $taskFile->date_end = $taskFile->parsed_data[TaskFile::DATE_END] ?? null;
        $taskFile->title = $parser->getFileTitle($taskFile->parsed_data, $accountService->account->language);
        $taskFile->subtitle = $parser->getFileSubtitle($taskFile->parsed_data, $accountService->account->language);
        return $taskFile;
    }

    public static function createXml(AccountService $accountService, string $content, string $filename): Xml
    {
        $parser = XmlDetectorHelper::detectParser($content, $filename);
        if ($parser instanceof AbstractXbrlParser) {
            // handle situation where XBRL was uploaded with XML extension.
            $filename = pathinfo($filename, PATHINFO_FILENAME) . '.' . TaskFile::EXTENSION_XBRL;
            return self::createXbrl($accountService, $content, $filename);
        }

        $taskFile = new Xml();
        $taskFile->account()->associate($accountService->account);
        $taskFile->accountService()->associate($accountService);
        $taskFile->filename = $filename;
        $taskFile->extension = TaskFile::EXTENSION_XML;
        $taskFile->setContent($content);

        $taskFile->type = $parser->getTaskFileType();
        $taskFile->parsed_data = $parser->getParsedResult();
        $taskFile->date_start = $taskFile->parsed_data[TaskFile::DATE_START] ?? null;
        $taskFile->date_end = $taskFile->parsed_data[TaskFile::DATE_END] ?? null;
        $taskFile->title = $parser->getFileTitle($taskFile->parsed_data, $accountService->account->language);
        $taskFile->subtitle = $parser->getFileSubtitle($taskFile->parsed_data, $accountService->account->language);
        return $taskFile;
    }

    public static function createPdf(
        AccountService $accountService,
        string $content,
        string $filename,
        string $type = null,
        ?string $title = null,
        ?string $subtitle = null,
        ?string $identifier = null,
        ?string $identifierType = null,
        ?int $uploadTypeId = null
    ): Pdf {
        $taskFile = new Pdf();
        $taskFile->account()->associate($accountService->account);
        $taskFile->accountService()->associate($accountService);
        $taskFile->filename = $filename;
        $taskFile->extension = TaskFile::EXTENSION_PDF;
        $taskFile->setContent($content);
        $parser = PdfDetectorHelper::detectParser($content, $filename, $type);
        $taskFile->parsed_data = $parser->getParsedResult($filename, $content);
        $parsedData = $parser->getParsedResult($filename, $content);
        $taskFile->title = $title;
        if ($taskFile->title === null) {
            $taskFile->title = $parser->getFileTitle($parsedData, $accountService->account->language);
        }
        $taskFile->subtitle = $subtitle;
        if ($taskFile->subtitle === null) {
            $taskFile->subtitle = $parser->getFileSubtitle($parsedData, $accountService->account->language);
        }

        if (isset($type)) {
            $taskFile->type = $type;
        } else {
            $taskFile->type = $parser->getTaskFileType();
        }

        if (
            !empty($uploadTypeId)
            && $accountService->uploadTypes()->where('id', $uploadTypeId)->exists()
        ) {
            $taskFile->account_service_upload_type_id = $uploadTypeId;
        }
        $taskFile->identifier = $identifier;
        $taskFile->identifier_type = $identifierType;

        return $taskFile;
    }

    public static function createSepaXml(
        AccountService $accountService,
        string $content,
        string $filename,
        string $type
    ): Xml {
        $taskFile = new Xml();
        $taskFile->account()->associate($accountService->account);
        $taskFile->accountService()->associate($accountService);
        $taskFile->filename = $filename;
        $taskFile->extension = TaskFile::EXTENSION_XML;
        $taskFile->setContent($content);

        $taskFile->type = $type;
        $taskFile->title = pathinfo($filename, PATHINFO_FILENAME);
        return $taskFile;
    }
}
