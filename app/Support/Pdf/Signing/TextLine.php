<?php

namespace App\Support\Pdf\Signing;

/**
 * This class is used to place text without line wrapping because the Text Block does not support it
 */
class TextLine extends \SetaPDF_Core_Text_Block
{
    /**
     * Draws the text onto the canvas in a single line.
     *
     * @param \SetaPDF_Core_Canvas $canvas
     * @param int|float $x The lower left x-value of the text block
     * @param int|float $y The lower left y-value of the text block
     * @throws \SetaPDF_Core_Exception
     * @throws \SetaPDF_Core_Font_Exception
     * @throws \SetaPDF_Core_Type_Exception
     * @throws \SetaPDF_Core_Type_IndirectReference_Exception
     * @throws \SetaPDF_Exception_NotImplemented
     */
    protected function _drawText(\SetaPDF_Core_Canvas $canvas, $x, $y) // phpcs:ignore
    {
        $x = $this->_fixX($x);
        $line = implode('', $this->_getLines());

        $text = $canvas->text();

        $text->setCharacterSpacing($this->_charSpacing);

        $textWidth = $this->getTextWidth();

        if (($this->_align !== \SetaPDF_Core_Text::ALIGN_LEFT && $line) || $this->_underline) {
            $glyphWidth = $this->_font->getGlyphsWidth($line) / 1000;
            // calculate the total string width
            $stringWidth = $glyphWidth * $this->getFontSize();

            if ($this->_charSpacing != 0) {
                $stringWidth += ($this->_charSpacing * (\SetaPDF_Core_Encoding::strlen($line, 'UTF-16BE') - 1));
            }

            switch ($this->_align) {
                case \SetaPDF_Core_Text::ALIGN_CENTER:
                    $x = ($textWidth / 2) - ($stringWidth / 2);
                    break;
                case \SetaPDF_Core_Text::ALIGN_RIGHT:
                    $x = $x + $textWidth - $stringWidth;
                    break;
            }
        }

        $text->begin()
            ->setFont($this->_font, $this->getFontSize())
            ->setLeading($this->getLineHeight())
            ->moveToNextLine($x, $y);

        $charCodes = $this->_font->getCharCodes($line);
        $text->showText($charCodes);
        $text->moveToStartOfNextLine();
        $text->end();
    }
}
