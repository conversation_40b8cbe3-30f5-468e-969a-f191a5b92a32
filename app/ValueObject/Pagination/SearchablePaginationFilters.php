<?php

namespace App\ValueObject\Pagination;

class SearchablePaginationFilters extends PaginationFilters
{
    private string $search = '';

    public function __construct(string $search = '', int $page = 1, int $limit = 20)
    {
        parent::__construct($page, $limit);

        $this->search = $search;
    }

    /**
     * Returns the search value.
     *
     * @return string
     */
    public function search(): string
    {
        return $this->search;
    }
}
