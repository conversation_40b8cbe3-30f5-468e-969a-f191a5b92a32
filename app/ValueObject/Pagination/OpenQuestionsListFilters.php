<?php

namespace App\ValueObject\Pagination;

use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\ValueObject\Filter;

/**
 * Class OpenQuestionsCompaniesFilters.
 *
 * Attributes of the filters for the companies list in open questions
 *
 * @package App\ValueObject
 */
class OpenQuestionsListFilters extends PaginationFilters
{
    private array $categories;
    private array $status;
    private array $years;
    private array $months;
    private string $orderBy;
    private string $orderDir;
    private ?int $initialId;

    public function __construct(
        int $page,
        int $limit,
        array $categories,
        array $status,
        array $years,
        array $months,
        string $sortBy,
        string $sortDir,
        ?int $initialId
    ) {
        parent::__construct($page, $limit);
        $this->categories = $categories;
        $this->status = $status;
        $this->years = $years;
        $this->months = $months;
        $this->orderBy = $sortBy;
        $this->orderDir = $sortDir;
        $this->initialId = $initialId;
    }

    public function categories(): array
    {
        return $this->categories;
    }

    public function status(): array
    {
        return $this->status;
    }

    public function years(): array
    {
        return $this->years;
    }

    public function months(): array
    {
        return $this->months;
    }

    public function orderBy(): string
    {
        return $this->orderBy;
    }

    public function orderDir(): string
    {
        return $this->orderDir;
    }

    public function initialId(): ?int
    {
        return $this->initialId;
    }

    public function toArray(): array
    {
        return [
            'limit' => $this->limit(),
            'status' => $this->status(),
            'years' => $this->years(),
            'months' => $this->months(),
            'categories' => $this->categories(),
            'orderBy' => $this->orderBy(),
            'orderDir' => $this->orderDir(),
            'initialId' => $this->initialId(),
        ];
    }
}
