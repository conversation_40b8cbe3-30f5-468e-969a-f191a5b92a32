<?php

namespace App\ValueObject;

class AuthenticationId
{
    public string $identification;
    protected const WHITE_LIST_USERS = [
        'life&gardenhulst;',
        'oovb\janm',
        'bjö************'
    ];

    public function __construct(string $identification)
    {
        $this->identification = $identification;
    }

    /**
     * @param string|null $value
     * @return bool
     */
    public static function isValid(?string $value): bool
    {
        // for legacy reason we allow some existing production users with names that do not pass.
        if (in_array($value, self::WHITE_LIST_USERS)) {
            return true;
        }

        // check min and max length
        $len = mb_strlen($value);
        if ($len < 2 || $len > 80) {
            return false;
        }

        // check if it is a valid email
        if (strpos($value, '@') !== false) {
            // allow _ (underscore) in the hostname and allow dotless domain names. That is why we add an extension.
            $value = str_replace('_', '-', $value) . '.com';
            return filter_var($value, FILTER_VALIDATE_EMAIL);
        }

        // is not an email, only these characters are allowed.
        $chars = 'abcdefghijklmnopqrstuwvxyzABCDEFGHIJKLMNOPQRSTUWVXYZ0123456789-_öëé.@# ';
        for ($i = 0; $i < strlen($value); $i++) {
            if (strpos($chars, $value[$i]) === false) {
                return false;
            }
        }

        // check for leading and trailing spaces and other special characters.
        if (trim($value, " \t\n\r\0\x0B-_@#") !== $value) {
            return false;
        }

        // do not allow double (or more) spaces because it would allow having users with names that look too much alike.
        if (strpos($value, '  ') !== false || strpos($value, '..') !== false || strpos($value, '###') !== false) {
            return false;
        }

        return true;
    }

    public function __toString()
    {
        return $this->identification;
    }
}
