<?php

namespace App\ValueObject\ServiceTask;

use App\Exceptions\InvalidValueException;
use App\Helpers\WageTaxNumberHelper;
use App\ValueObject\Identifier\VatNumber;
use App\ValueObject\Identifier\WageTaxNumber;

/**
 * Class RSIN
 * @package App\ValueObject\ServiceTask
 */
class RSIN implements \JsonSerializable
{
    private $rsinString;

    /**
     * RSIN constructor.
     * @param $rsinString
     * @throws InvalidValueException
     */
    public function __construct(string $rsinString)
    {
        self::assertValid($rsinString);
        $this->rsinString = $rsinString;
    }

    /**
     * Parses the RSIN from the identification number.
     * The Identifiation number can be either a fiscal, VAT or a WAGE TAX number.
     *
     * @param string $identificationNumber
     * @return RSIN
     * @throws InvalidValueException
     */
    public static function parseFromIdentificationNumber(string $identificationNumber): self
    {
        if (ctype_digit($identificationNumber) && strlen($identificationNumber) == 9) {
            return new self($identificationNumber);
        } elseif (VatNumber::isValid($identificationNumber)) {
            return new self((new VatNumber($identificationNumber))->getFiscalNumber());
        } elseif (WageTaxNumber::isValid($identificationNumber)) {
            return new self((new WageTaxNumber($identificationNumber))->getFiscalNumber());
        }

        throw new InvalidValueException("Invalid RSIN provided ($identificationNumber)");
    }

    /**
     * @return string
     */
    public function getRsinString(): string
    {
        return $this->rsinString;
    }

    public function __toString(): string
    {
        return $this->rsinString;
    }

    public function equals(RSIN $rsin): bool
    {
        return $this->rsinString === $rsin->rsinString;
    }

    /**
     * @param string $rsin
     * @return void
     * @throws InvalidValueException
     */
    public static function assertValid(string $rsin): void
    {
        if (!self::isValid($rsin)) {
            throw new InvalidValueException("Invalid RSIN provided ($rsin)");
        }
    }

    public static function isValid(string $rsin): bool
    {
        if (is_numeric($rsin) && strlen($rsin) == 9) {
            return true;
        }
        return false;
    }

    /**
     * @inheritDoc
     */
    public function jsonSerialize()
    {
        return $this->rsinString;
    }
}
