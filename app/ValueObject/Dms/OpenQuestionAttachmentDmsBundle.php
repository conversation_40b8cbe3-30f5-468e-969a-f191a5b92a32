<?php

namespace App\ValueObject\Dms;

use App;
use App\Account;
use App\AccountService;
use App\Company;
use App\Interfaces\Services\Dms\CanSendToDmsInterface;
use App\Models\OpenQuestions\OpenQuestionAttachment;
use App\Objects\DmsPreferenceAwareInterface;
use App\Repositories\OpenQuestions\OpenQuestionAttachmentRepository;
use App\Repositories\Services\AccountServiceRepository;
use App\Service;
use App\User;
use App\Support\Carbon;
use Illuminate\Support\Collection;
use RuntimeException;

class OpenQuestionAttachmentDmsBundle implements CanSendToDmsInterface
{
    public int $id;
    private array $attachmentIds;
    private string $title;
    public OpenQuestionAttachment $firstAttachment;
    private Company $company;

    public function __construct(array $attachmentIds, Company $company, string $title)
    {
        $this->id = random_int(0, 1000000);
        $this->attachmentIds = $attachmentIds;
        $this->company = $company;
        $this->firstAttachment = resolve(OpenQuestionAttachmentRepository::class)
            ->getByCompanyAndId($this->company, $this->attachmentIds[0]);

        if (empty($this->firstAttachment)) {
            throw new RuntimeException('OpenQuestionAttachment #' . $this->attachmentIds[0] . ' missing');
        }
        $this->title = $title;
    }

    public function getDmsDisplayName(): string
    {
        return $this->title;
    }

    public function getDmsDate(): Carbon
    {
        return Carbon::now();
    }

    public function dmsParentPreferences(): Collection
    {
        return $this->firstAttachment->dmsParentPreferences();
    }

    public function dmsPreferences(): Collection
    {
        throw new RuntimeException('Method dmsPreferences should not be called on FileUploadBundle.');
    }

    public function getDmsRecipients(): Collection
    {
        return $this->firstAttachment->getDmsRecipients();
    }

    public function getDmsPreferences(AccountService $accountService): ?DmsPreferenceAwareInterface
    {
        return $this->firstAttachment->getDmsPreferences($accountService);
    }

    public function getClientId(): ?string
    {
        return $this->firstAttachment->getClientId();
    }

    public function getDmsType(): ?string
    {
        return $this->firstAttachment->getDmsType();
    }

    public function getAccount(): Account
    {
        return $this->firstAttachment->getAccount();
    }

    public function sendDmsSuccessMessage(string $message, array $replaces, ?User $user, ?string $filePath = null): void
    {
        // If filePath is present it means it's from a dms where files are sent separately.
        // We create a dms entry per file, based on the filePath because this is all we know at the time of sending.
        if ($filePath !== null) {
            /** @var OpenQuestionAttachment $attachment */
            $attachment = $this->firstAttachment
                ->openQuestion
                ->attachments()
                ->where(OpenQuestionAttachment::PATH, $filePath)
                ->firstOrFail();
            $attachment->sendDmsSuccessMessage($message, $replaces, $user, $filePath);
        } else {
            foreach ($this->attachmentIds as $attachmentId) {
                $openQuestionAttachment = OpenQuestionAttachment::findOrFail($attachmentId);
                $openQuestionAttachment->sendDmsSuccessMessage($message, $replaces, $user);
            }
        }
    }

    public function sendDmsErrorMessage(string $message, array $replaces, ?User $user, ?string $filePath = null): void
    {
        // If filePath is present it means it's from a dms where files are sent separately.
        // We create a dms entry per file, based on the filePath because this is all we know at the time of sending.
        if ($filePath !== null) {
            /** @var OpenQuestionAttachment $attachment */
            $attachment = $this->firstAttachment
                ->openQuestion
                ->attachments()
                ->where(OpenQuestionAttachment::PATH, $filePath)
                ->firstOrFail();
            $attachment->sendDmsErrorMessage($message, $replaces, $user, $filePath);
        } else {
            foreach ($this->attachmentIds as $attachmentId) {
                $openQuestionAttachment = OpenQuestionAttachment::findOrFail($attachmentId);
                $openQuestionAttachment->sendDmsErrorMessage($message, $replaces, $user);
            }
        }
    }

    public function handleSendingToDms(?User $user): void
    {
        throw new RuntimeException('Method handleSendingToDms should not be called on OpenQuestionAttachmentDmsBundle.'); //phpcs:ignore
    }

    public function getDmsFile(): DmsFile
    {
        throw new RuntimeException('Method getDmsFile should not be called on OpenQuestionAttachmentDmsBundle.');
    }

    public function allowResend(): bool
    {
        return false;
    }

    public function getFilePaths(): array
    {
        return OpenQuestionAttachment::query()
            ->whereIn('id', $this->attachmentIds)
            ->pluck('path')
            ->toArray();
    }

    public function getAccountService(): AccountService
    {
        $accountServiceRepository = resolve(AccountServiceRepository::class);
        return $accountServiceRepository->firstEnabledByServiceName(
            $this->getAccount(),
            Service::MANUAL_QUESTIONS_SERVICE
        );
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }
}
