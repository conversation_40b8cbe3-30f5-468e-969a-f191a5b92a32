<?php

namespace App\ValueObject\Declarations\Manual;

use App\ValueObject\Declarations\Manual\Dividend\QdRevenueEntitlement;
use App\ValueObject\Declarations\Manual\Dividend\RevenueEntitlement;
use App\Support\Carbon;

class DividendDeclaration
{
    public const PARTICIPATION_EXEMPTION = 'participation_exemption';
    public const FISCAL_UNIT = 'fiscal_unit';
    public const ASSIGNED_COMPANY = 'assigned_company';
    public const EXEMPT_SHARE_REPURCHASE = 'exempt_share_repurchase';
    public const PAYMENTS_BY_INVESTMENT_INSTITUTIONS = 'payments_by_investment_institutions';
    public const PURCHASE_OF_OWN_SHARES = 'purchase_of_own_shares';
    public const TRANSFER_SHARES_OR_PROFIT_PROOF = 'transfer_shares_or_profit_proof';
    public const PARTICIPATION_EXEMPTION_BY_INVESTMENT_INSTITUTIONS = 'participation_exemption_by_investment_institutions'; //phpcs:ignore
    public const INHERITED_SUBSTANTIAL_INTEREST = 'inherited_substantial_interest';

    public const REVENUE_ENTITLEMENT = 'revenue_entitlement';
    public const QD_REVENUE_ENTITLEMENT = 'qd_revenue_entitlement'; // QD = Qualification Disposal
    public const SI_REVENUE_ENTITLEMENT = 'si_revenue_entitlement'; // SI = Significant Interest

    public const OPTIONS_DISTRIBUTIONS_NO_WITHHOLDING_C = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    public const ALLOWED_OPTIONS_PROFIT_OVER_MONEY_DISTRIBUTIONS_NO_WITHHOLDING_C = [1, 2, 3, 4, 5, 6, 7];

    public const OPTION_PROFIT_AS_MONEY = 1;
    public const OPTION_LIQUIDATION_DISTRIBUTIONS = 3;
    public const OPTIONS_DISTRIBUTIONS_WITHHOLDING_15_B = [
        self::OPTION_PROFIT_AS_MONEY,
        2,
        self::OPTION_LIQUIDATION_DISTRIBUTIONS,
        4,
        5,
        6,
        7,
        9,
        10
    ];

    public const SECTION_DECLARATION = 'section_declaration';
    public const SECTION_PAYMENTS = 'section_payments';
    public const SECTION_DISTRIBUTIONS_NO_WITHHOLDING = 'section_distributions_no_withholding';
    public const SECTION_DISTRIBUTIONS_WITHHOLDING_15 = 'section_distributions_withholding_15';
    public const SECTION_DISTRIBUTIONS_WITHHOLDING_BRK = 'section_distributions_withholding_brk';
    public const SECTION_DIVIDEND_TAX_SPECIAL = 'section_dividend_tax_special';
    public const SECTION_TAX_REDUCTIONS = 'section_tax_reductions';
    public const SECTION_COLLECTION_LIST = 'section_collection_list';

    public const SECTION_NUMBERS = [
        self::SECTION_PAYMENTS => 1,
        self::SECTION_DISTRIBUTIONS_NO_WITHHOLDING => 2,
        self::SECTION_DISTRIBUTIONS_WITHHOLDING_15 => 3,
        self::SECTION_TAX_REDUCTIONS => 4,
        self::SECTION_COLLECTION_LIST => 5,
    ];

    public readonly string $contactPhoneNumber;
    public readonly ?string $contactInitials;
    public readonly string $contactFirstname;
    public readonly string $contactSurname; // "surname" instead of "last name" to match the XBRL element name
    public readonly int $serialNumber;
    public readonly string $fiscalNumber;
    public readonly string $companyName;
    public readonly string $year;
    public readonly string $startDate;
    public readonly string $endDate;
    public readonly string $dividendAvailabilityDate;
    public array $summary;
    public readonly bool $hasTaxReductions;
    public readonly bool $isFiscalInvestmentInstitution;

    public array $revenueEntitlements = [];
    public array $qdRevenueEntitlements = [];
    public array $siRevenueEntitlements = [];

    public readonly int $withholdingExemption;
    public ?int $withholdingExemptionType = null;

    public readonly int $paymentOrdinaryRate;

    public readonly int $paymentForm; // number 1 - 10

    // used when payment form = liquidation distribution
    public readonly int $liquidationPaymentTotal;
    public readonly int $shareCapitalAveragePaidUp;
    public readonly int $untaxedProhibitionExtraterritorialLevy;

    public readonly int $paymentTotal;

    public readonly int $exemptedDividendPaymentTotal;
    public readonly int $nominalPaidUpCapitalTotal;
    public readonly int $votingSharesNumberTotal;

    public readonly int $dividendTaxReductionArticle11; // value of field Tax Reductions B
    public readonly int $dividendTaxWithheldArticle12; // value of field Tax Reductions C


    public function __construct(array $data)
    {
        $this->contactPhoneNumber = $data['contact_phone'];
        $this->contactInitials = $data['contact_initials'];
        $this->contactFirstname = $data['contact_firstname'];
        $this->contactSurname = $data['contact_surname'];
        $this->companyName = $data['company_name'];

        $this->serialNumber = $data['serial_number'];
        $this->fiscalNumber = $data['fiscal_number'];
        $this->year = $data['financial_year'];
        $date = Carbon::createFromDate($data['financial_year']);
        $this->startDate = $date->startOfYear()->format('Y-m-d');
        $this->endDate = $date->endOfYear()->format('Y-m-d');
        $this->dividendAvailabilityDate = Carbon::parse($data['dividend_availability_date'])->format('Y-m-d'); //phpcs:ignore
        $this->summary = $data['summary'];

        if (
            isset($this->summary[self::SECTION_DECLARATION]['a']['value'])
            && $this->summary[self::SECTION_DECLARATION]['a']['value'] === 'yes'
        ) {
            $this->exemptedDividendPaymentTotal = $this->summary[self::SECTION_DECLARATION]['b']['value'];
            $this->nominalPaidUpCapitalTotal = $this->summary[self::SECTION_DECLARATION]['c']['value'];
            $this->votingSharesNumberTotal = $this->summary[self::SECTION_DECLARATION]['d']['value'];
        }

        $this->paymentTotal = $this->summary[DividendDeclaration::SECTION_PAYMENTS]['b']['value'];

        if (
            isset($this->summary[self::SECTION_DISTRIBUTIONS_NO_WITHHOLDING]['a']['value'])
            && $this->summary[self::SECTION_DISTRIBUTIONS_NO_WITHHOLDING]['a']['value'] === 'yes'
        ) {
            $this->withholdingExemption = $this->summary[self::SECTION_DISTRIBUTIONS_NO_WITHHOLDING]['b']['value'];
            $this->setWithholdingExemptionType((int)$this->summary[self::SECTION_DISTRIBUTIONS_NO_WITHHOLDING]['c']['value']); //phpcs:ignore
            $this->setRevenueEntitlements($this->summary[self::SECTION_DISTRIBUTIONS_NO_WITHHOLDING]['c']);
        } else {
            $this->withholdingExemption = 0;
        }
        $this->setRevenueEntitlements($this->summary[self::SECTION_DISTRIBUTIONS_WITHHOLDING_15]['b']);

        if (
            isset($this->summary[self::SECTION_DISTRIBUTIONS_WITHHOLDING_15]['a']['value'])
            && $this->summary[self::SECTION_DISTRIBUTIONS_WITHHOLDING_15]['a']['value'] === 'yes'
        ) {
            $this->setPaymentForm((int)$this->summary[self::SECTION_DISTRIBUTIONS_WITHHOLDING_15]['b']['value']);
            $this->paymentOrdinaryRate = (int)$this->summary[self::SECTION_DISTRIBUTIONS_WITHHOLDING_15]['c']['value'];

            if (isset($this->summary[self::SECTION_DISTRIBUTIONS_WITHHOLDING_15]['e']['value'])) {
                $this->untaxedProhibitionExtraterritorialLevy = (int)$this->summary[self::SECTION_DISTRIBUTIONS_WITHHOLDING_15]['e']['value']; //phpcs:ignore
            }

            if ($this->paymentForm === self::OPTION_LIQUIDATION_DISTRIBUTIONS) {
                $this->liquidationPaymentTotal = (int)$this->summary[self::SECTION_DISTRIBUTIONS_WITHHOLDING_15]['b']['liquidation_payment_total']; //phpcs:ignore
                $this->shareCapitalAveragePaidUp = (int)$this->summary[self::SECTION_DISTRIBUTIONS_WITHHOLDING_15]['b']['share_capital_average_paid_up']; //phpcs:ignore
            }
        } else {
            $this->setPaymentForm(1);
            $this->paymentOrdinaryRate = 0;
        }

        if (
            isset($this->summary[self::SECTION_TAX_REDUCTIONS]['b']['value'])
            && (int)$this->summary[self::SECTION_TAX_REDUCTIONS]['b']['value'] > 0
        ) {
            $this->dividendTaxReductionArticle11 = (int)$this->summary[self::SECTION_TAX_REDUCTIONS]['b']['value'];
        }

        if (
            isset($this->summary[self::SECTION_TAX_REDUCTIONS]['c']['value'])
            && (int)$this->summary[self::SECTION_TAX_REDUCTIONS]['c']['value'] > 0
        ) {
            $this->dividendTaxWithheldArticle12 = (int)$this->summary[self::SECTION_TAX_REDUCTIONS]['c']['value'];
        }

        $this->hasTaxReductions =
            $this->summary[self::SECTION_TAX_REDUCTIONS]['b']['value'] > 0
            || $this->summary[self::SECTION_TAX_REDUCTIONS]['c']['value'] > 0
            || $this->summary[self::SECTION_TAX_REDUCTIONS]['e']['value'] > 0
            || $this->summary[self::SECTION_TAX_REDUCTIONS]['f']['value'] > 0
        ;
        $this->isFiscalInvestmentInstitution = (int)$this->summary[self::SECTION_TAX_REDUCTIONS]['e']['value'] > 0 || (int)$this->summary[self::SECTION_TAX_REDUCTIONS]['f']['value'] > 0; //phpcs:ignore
    }

    public function setPaymentForm(int $value): void
    {
        if (!in_array($value, self::OPTIONS_DISTRIBUTIONS_WITHHOLDING_15_B)) {
            throw new \UnexpectedValueException('Unexpected value for ' . self::SECTION_NUMBERS[self::SECTION_DISTRIBUTIONS_WITHHOLDING_15] . 'B: ' . $value); //phpcs:ignore
        }
        $this->paymentForm = $value;
    }

    public function setWithholdingExemptionType(int $value): void
    {
        if (!in_array($value, self::OPTIONS_DISTRIBUTIONS_NO_WITHHOLDING_C)) {
            throw new \UnexpectedValueException('Unexpected value for ' . self::SECTION_NUMBERS[self::SECTION_DISTRIBUTIONS_NO_WITHHOLDING] . 'C: ' . $value); //phpcs:ignore
        }
        $this->withholdingExemptionType = $value;
    }

    public function setRevenueEntitlements(array $section): void
    {
        // exempted entitlements (section distributions no withholding C)
        if (isset($section[self::REVENUE_ENTITLEMENT]) && is_array($section[self::REVENUE_ENTITLEMENT])) {
            $this->revenueEntitlements = $this->parseRevenueEntitlements($section[self::REVENUE_ENTITLEMENT]);
        }
        // Qualification Disposal revenue entitlement (WithholdingExemptionTypeDomain = 6)
        if (isset($section[self::QD_REVENUE_ENTITLEMENT]) && is_array($section[self::QD_REVENUE_ENTITLEMENT])) {
            $this->qdRevenueEntitlements = $this->parseQdRevenueEntitlements($section[self::QD_REVENUE_ENTITLEMENT]);
        }
        // Significant Interest revenue entitlement
        // if dividend is paid in currency (section distributions no withholding B)
        if (isset($section[self::SI_REVENUE_ENTITLEMENT]) && is_array($section[self::SI_REVENUE_ENTITLEMENT])) {
            $this->siRevenueEntitlements = $this->parseRevenueEntitlements($section[self::SI_REVENUE_ENTITLEMENT]);
        }
    }

    /**
     * @param array $array
     * @return RevenueEntitlement[]
     */
    protected function parseExemptedRevenueEntitlements(array $array): array
    {
        $entitlements = [];
        $serialNumber = 1;
        foreach ($array as $data) {
            $entitlements[] = new RevenueEntitlement(
                $data['name'],
                $data['identification'],
                $data['interest_size'],
                (int)$data['payment'],
                $serialNumber
            );
            $serialNumber++;
        }
        return $entitlements;
    }

    /**
     * @param array $array
     * @return RevenueEntitlement[]
     */
    protected function parseRevenueEntitlements(array $array): array
    {
        $entitlements = [];
        $serialNumber = 1;
        foreach ($array as $data) {
            $entitlements[] = new RevenueEntitlement(
                $data['name'],
                $data['identification'],
                $data['interest_size'],
                (int)$data['payment'],
                $serialNumber
            );
            $serialNumber++;
        }
        return $entitlements;
    }

    /**
     * @param array $array
     * @return QdRevenueEntitlement[]
     */
    protected function parseQdRevenueEntitlements(array $array): array
    {
        $entitlements = [];
        $serialNumber = 1;
        foreach ($array as $data) {
            $entitlements[] = new QdRevenueEntitlement(
                $data['name'],
                $data['identification'],
                (int)$data['shares_number'],
                $data['payment_per_share'],
                (int)$data['payment'],
                $serialNumber
            );
            $serialNumber++;
        }
        return $entitlements;
    }

    public function isFiscalInvestmentInstitution(): string
    {
        return $this->isFiscalInvestmentInstitution ? 'true' : 'false';
    }

    public function getSerialNumberString(): string
    {
        return str_pad($this->serialNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Position 1 to 9 = Fiscal number
     * Position 10 to 12 = 'DIB'
     * Position 13 and 14 = last two positions of year
     * Position 15 and 16 = month
     * Position 17 = '0' for MBKz and '1' for XBRL
     * Position 18 to 20 = numerical sequence number
     * Good example: 800001709DIB24011001
     * @return string
     */
    public function getTaxReturnNumber(): string
    {
        $availabilityYearMonth = Carbon::parse($this->dividendAvailabilityDate)->format('ym');
        return $this->fiscalNumber . 'DIB' . $availabilityYearMonth . '1' . $this->getSerialNumberString();
    }

    public function filename(): string
    {
        return 'hix-div-' . $this->fiscalNumber . '-' . $this->startDate . '-' . $this->endDate . '.xbrl';
    }

    /**
     * BR-BD-19.4.63:
     * De waarde van element 'bd-i:DividendPaymentFormAmount' MOET gelijk zijn aan de waarde van element
     * 'bd-i:UntaxedProhibitionExtraterritorialLevy'
     * PLUS de som van de waarden van element 'bd-i:WithholdingExemption'
     * PLUS de som van de waarden van element 'bd-i:ConventionApplicationAmount'
     * PLUS de waarde van element 'bd-i:PaymentOrdinaryRate' met dezelfde overeenkomstige dimensiewaarden
     * INDIEN element 'bd-i:DividendPaymentFormAmount' is opgenomen.
     * @return int
     */
    public function getPaymentFormAmount(): int
    {
        $amount = $this->paymentOrdinaryRate;
        if (isset($this->withholdingExemption)) {
            $amount += $this->withholdingExemption;
        }
        if (isset($this->untaxedProhibitionExtraterritorialLevy)) {
            $amount += $this->untaxedProhibitionExtraterritorialLevy;
        }
        return $amount;
    }

    public function hasRevenueEntitlements(): bool
    {
        return count($this->revenueEntitlements) > 0
            || count($this->qdRevenueEntitlements) > 0
            || count($this->siRevenueEntitlements) > 0;
    }

    /**
     * Get total of all si revenue entitlement amounts for all beneficiaries
     * @return int
     */
    public function getTotalSiRevenueEntitlementsAmount(): int
    {
        $total = 0;
        /** @var RevenueEntitlement $entitlement */
        foreach ($this->siRevenueEntitlements as $entitlement) {
            $total += $entitlement->payment;
        }
        return $total;
    }


    /**
     * Get total of all exempted revenue entitlement amounts for all beneficiaries
     * to be used in element ExemptedDividendPaymentTotal.
     * @return int
     */
    public function getTotalExemptedRevenueEntitlements(): int
    {
        $total = 0;
        /** @var RevenueEntitlement $entitlement */
        foreach ($this->revenueEntitlements as $entitlement) {
            $total += $entitlement->payment;
        }
        return $total;
    }
}
