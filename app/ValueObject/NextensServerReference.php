<?php

namespace App\ValueObject;

/**
 * Nextens Servere Reference is a string generated by Securelogin returned to Nextens in a response to a file upload.
 * Nextens will use the reference later to check the status of the task/file.
 * To prevent problems with IDs for task/file and giving the wrong status back we include the account service ID and
 * several other characters like "F" to indicate that it is a file ID and not a task ID.
 * Class NextensServerReference
 * @package App\ValueObject
 */
class NextensServerReference
{
    private string $value;
    private ?int $accountServiceId = null;
    private ?int $fileId = null;

    public function __construct(string $value)
    {
        $this->value = $value;
        $matches = self::parse($value);
        if (count($matches) === 3) {
            $this->accountServiceId = intval($matches[1]);
            $this->fileId = intval($matches[2]);
        }
    }

    /**
     * Generate a reference in a format that the parse method can process.
     * @param int $accountServiceId
     * @param int $fileId
     * @return string
     */
    public static function generate(int $accountServiceId, int $fileId): string
    {
        return 'SL-AS' . $accountServiceId . '-F' . $fileId;
    }

    /**
     * Parse reference. Example of expected string: "SL-AS123-F456".
     * SL = SecureLogin, AS = Account Service, F = file. 123 = account service ID, 456 = task file ID
     * @param string $value Reference to parse
     * @return array Containing 3 elements: complete string, account service ID, file ID
     */
    public static function parse(string $value): array
    {
        preg_match('/^SL-AS([0-9]+)\-F([0-9]+)$/', $value, $matches);
        return $matches;
    }

    /**
     * @return string Original value, also returned if unable to parse.
     */
    public function __toString(): string
    {
        return $this->value;
    }

    /**
     * @return int|null Positive int or NULL if unable to parse reference
     */
    public function fileId(): ?int
    {
        return $this->fileId;
    }

    /**
     * @return int|null Positive int or NULL if unable to parse reference
     */
    public function accountServiceId(): ?int
    {
        return $this->accountServiceId;
    }
}
