<?php

namespace App\ValueObject\Services\Signing\PkiSigning\Interactive;

use Illuminate\Contracts\Support\Arrayable;

class DocumentsToSign implements Arrayable
{
    /**
     * @var DocumentToSign[] $documents - An array of documents to sign to start a session.
     */
    private array $documentsToSign;

    public function __construct(array $documentsToSign)
    {
        $this->documentsToSign = $documentsToSign;
    }

    public function toArray(): array
    {
        $array = [];

        foreach ($this->documentsToSign as $documentToSign) {
            $array['documentsToSign'][] = $documentToSign->toArray();
        }

        return $array;
    }
}
