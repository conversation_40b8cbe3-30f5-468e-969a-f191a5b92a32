<?php

namespace App\ValueObject\Services\Crm\Azure;

use Illuminate\Contracts\Support\Arrayable;

class Configuration implements Arrayable
{
    public const EMAIL = 'email';
    public const EXTERNAL_ID = 'external_id';

    public function __construct(
        public readonly bool $notifyOnCreate,
        public readonly string $authIdReference
    ) {
    }

    public function toArray(): array
    {
        return [
            'notify_on_create' => $this->notifyOnCreate,
            'auth_id_reference' => $this->authIdReference,
        ];
    }
}
