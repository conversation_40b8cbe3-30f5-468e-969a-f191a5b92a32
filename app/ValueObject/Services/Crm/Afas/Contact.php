<?php

namespace App\ValueObject\Services\Crm\Afas;

use App\Helpers\UserHelper;
use App\Services\Service\Crm\AbstractCrmService;
use App\ValueObject\MobileNumber;

class Contact
{
    protected string $id;
    protected ?string $name; // Naam
    protected ?string $address; // AdresOrganisatie
    protected ?string $fiscalNumber; // Naam_2
    protected ?string $kvkNumber; // Nummer
    protected ?string $contactType; // Soort_contact
    protected ?string $externalId; // Organisatie
    protected ?string $person; // Persoon
    protected ?string $function;
    protected ?string $mobileWork;
    protected ?string $mobilePrivate;
    protected ?string $emailWork;
    protected ?string $emailPrivate;
    protected ?string $firstName; // Department
    protected ?string $middleName;
    protected ?string $lastName;
    protected ?string $bsnNumber; // Client (Omschrijving)
    protected ?string $clientIB; // Client_IB
    protected ?string $vatNumber; // Btw-identificatienummer
    protected array $properties;

    /**
     * Configurations constructor.
     * @param string $id
     * @param string|null $name
     * @param string|null $address
     * @param string|null $fiscalNumber
     * @param string|null $kvkNumber
     * @param string|null $contactType
     * @param string|null $externalId
     * @param string|null $person
     * @param string|null $function
     * @param string|null $mobileWork
     * @param string|null $mobilePrivate
     * @param string|null $emailWork
     * @param string|null $emailPrivate
     * @param string|null $firstName
     * @param string|null $middleName
     * @param string|null $lastName
     * @param string|null $bsnNumber
     * @param string|null $clientIB
     * @param string|null $vatNumber
     * @param array $properties
     */
    public function __construct(
        string $id,
        ?string $name,
        ?string $address,
        ?string $fiscalNumber,
        ?string $kvkNumber,
        ?string $contactType,
        ?string $externalId,
        ?string $person,
        ?string $function,
        ?string $mobileWork,
        ?string $mobilePrivate,
        ?string $emailWork,
        ?string $emailPrivate,
        ?string $firstName,
        ?string $middleName,
        ?string $lastName,
        ?string $bsnNumber,
        ?string $clientIB,
        ?string $vatNumber,
        array $properties = []
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->address = $address;
        $this->fiscalNumber = $fiscalNumber;
        $this->kvkNumber = $kvkNumber;
        $this->contactType = $contactType;
        $this->externalId = $externalId;
        $this->person = $person;
        $this->function = $function;
        $this->mobileWork = $mobileWork;
        $this->mobilePrivate = $mobilePrivate;
        $this->emailWork = $emailWork;
        $this->emailPrivate = $emailPrivate;
        $this->firstName = $firstName;
        $this->middleName = $middleName;
        $this->lastName = $lastName;
        $this->bsnNumber = $bsnNumber;
        $this->clientIB = $clientIB;
        $this->vatNumber = $vatNumber;
        $this->properties = $properties;
    }

    /**
     * @return string
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @return string
     */
    public function getInfix(): ?string
    {
        return trim($this->middleName . ' ' . $this->lastName);
    }

    /**
     * @return string
     */
    public function getAddress(): ?string
    {
        if (isset($this->address)) {
            return $this->address;
        }

        return '';
    }

    /**
     * @return string
     */
    public function getFiscalNumber(): ?string
    {
        return $this->fiscalNumber;
    }

    /**
     * @return string|null
     */
    public function getVatNumber(): ?string
    {
        return $this->vatNumber;
    }

    /**
     * @return string
     */
    public function getKvkNumber(): ?string
    {
        return $this->kvkNumber;
    }

    /**
     * We are using this contact types to fetch Afas:
     * Persoon bij organisatie // Organisatie // Persoon
     * @return string
     */
    public function getContactType(): ?string
    {
        return $this->contactType;
    }

    /**
     * @return string
     */
    public function getExternalId(): ?string
    {
        // If user is client IB we use his ID as external ID because it can happen that Organisatie
        // is null and is not unique
        if ($this->isClientIb()) {
            return $this->getId() . '-IB';
        }
        return $this->externalId ?? '';
    }

    /**
     * Used by Afas as an ID that relates with a Company
     * @return string
     */
    public function getPerson(): ?string
    {
        return $this->person;
    }

    /**
     * @return string
     */
    public function getMobileWork(): ?string
    {
        return $this->mobileWork;
    }

    /**
     * @return string
     */
    public function getMobileNumber(): ?string
    {
        $mobileNumber = $this->mobileWork;
        if ($mobileNumber === null) {
            $mobileNumber = $this->mobilePrivate;
        }

        if ($mobileNumber === null) {
            return '';
        }

        return MobileNumber::sanitize($mobileNumber);
    }

    /**
     * @return string|null
     */
    public function getEmail(): ?string
    {
        if ($this->isClientIb()) {
            $email = $this->emailPrivate;
            if ($email === null) {
                $email = $this->emailWork;
            }
        } else {
            $email = $this->emailWork;
            if ($email === null) {
                $email = $this->emailPrivate;
            }
        }

        return $email;
    }

    /**
     * @return string|null
     */
    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    /**
     * @return string|null
     */
    public function getMiddleName(): ?string
    {
        return $this->middleName;
    }

    /**
     * @return string|null
     */
    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    /**
     * @return string|null
     */
    public function getBsnNumber(): ?string
    {
        return $this->bsnNumber;
    }

    /**
     * Checks whether contact type is "Persoon bij organisatie"
     * @return bool
     */
    public function isClientWithCompany(): bool
    {
        return isset($this->contactType) && $this->contactType === 'Persoon bij organisatie';
    }

    /**
     * Checks whether contact is IB client
     * @return bool
     */
    public function isClientIb(): bool
    {
        return isset($this->clientIB) && $this->clientIB;
    }

    /**
     * Checks whether contact is NOT IB client and has NOT contact type "Persoon bij organisatie"
     * @return bool
     */
    public function isClientWithoutCompany(): bool
    {
        return !$this->isClientIb() && !$this->isClientWithCompany();
    }

    public function authId(): ?string
    {
        if (
            isset($this->properties[AbstractCrmService::PROPERTY_AUTH_ID_STRATEGY])
            && $this->properties[AbstractCrmService::PROPERTY_AUTH_ID_STRATEGY] === AbstractCrmService::AUTH_ID_STRATEGY_NAME // phpcs:ignore
        ) {
            if (is_string($this->firstName) && is_string($this->lastName)) {
                return UserHelper::nameToAuthId($this->firstName, $this->lastName);
            }
            return null;
        }
        return $this->getEmail();
    }

    public function getId(): string
    {
        return $this->id;
    }
}
