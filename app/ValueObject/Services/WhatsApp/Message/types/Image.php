<?php

namespace App\ValueObject\Services\WhatsApp\Message\types;

/**
 * @link https://developers.facebook.com/docs/whatsapp/cloud-api/webhooks/payload-examples#media-messages
 */
class Image extends MessageContent
{
    public const TYPE = 'image';
    public const ID = 'id';
    public const MIME_TYPE = 'mime_type';
    public const CAPTION = 'caption';
    public const LOGO = 'logo';

    protected string $id;
    protected ?string $caption;
    protected string $mimeType;

    /**
     * @param array $attributes
     */
    public function __construct(
        array $attributes,
    ) {
        parent::__construct($attributes);

        $this->id = $attributes[self::TYPE][self::ID];
        $this->mimeType = $attributes[self::TYPE][self::MIME_TYPE];
        $this->caption = $attributes[self::TYPE][self::CAPTION] ?? null;
    }

    /**
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getMimeType(): string
    {
        return $this->mimeType;
    }

    /**
     * @return string|null
     */
    public function getCaption(): ?string
    {
        return $this->caption;
    }

    /**
     * @return array
     */
    public function getContent(): array
    {
        $data = [
            self::ID => $this->id,
            self::MIME_TYPE => $this->mimeType,
            self::LOGO => '/images/logos/' . strtolower(explode('/', $this->mimeType)[1]) . '.png'
        ];

        if ($this->getCaption()) {
            $data[self::CAPTION] = $this->getCaption();
        }

        return $data;
    }
}
