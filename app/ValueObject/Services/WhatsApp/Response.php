<?php

namespace App\ValueObject\Services\WhatsApp;

class Response
{
    protected string $messagingProduct;
    protected array $contacts;
    protected array $messages;

    /**
     * Configurations constructor.
     * @param string $messagingProduct
     * @param array $contacts
     * @param array $messages
     */
    public function __construct(
        string $messagingProduct,
        array $contacts,
        array $messages
    ) {
        $this->messagingProduct = $messagingProduct;
        $this->contacts = $contacts;
        $this->messages = $messages;
    }

    /**
     * @return string
     */
    public function getMessagingProduct(): string
    {
        return $this->messagingProduct;
    }

    /**
     * @return array
     */
    public function getContacts(): array
    {
        return $this->contacts;
    }

    /**
     * @return array
     */
    public function getMessages(): array
    {
        return $this->messages;
    }

    /**
     * WhatsApp message ID. You can use the ID listed after "wamid." to track your message status.
     * Example: wamid.HBgLMTY1MDM4Nzk0MzkVAgARGBI3N0EyQUJDMjFEQzZCQUMzODMA
     * @return string|null
     */
    public function getMessageWhatsAppId(): ?string
    {
        return !empty($this->messages) ? $this->messages[0]['id'] : null;
    }

    public function getWhatsAppId(): ?string
    {
        return !empty($this->contacts) ? $this->contacts[0]['wa_id'] : null;
    }
}
