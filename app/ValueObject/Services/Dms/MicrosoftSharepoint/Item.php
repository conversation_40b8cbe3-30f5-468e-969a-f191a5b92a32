<?php

namespace App\ValueObject\Services\Dms\MicrosoftSharepoint;

class Item
{
    private string $id;
    private array $parentReference;
    private string $name;
    private ?array $folder;

    public function __construct(
        string $id,
        string $name,
        array $parentReference,
        array $folder = null
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->parentReference = $parentReference;
        $this->folder = $folder;
    }

    public function id(): string
    {
        return $this->id;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function isFolder(): bool
    {
        return $this->folder !== null;
    }

    public function isRoot(): bool
    {
        return $this->parentReference !== null && !isset($this->parentReference['id']);
    }

    public function parentId(): ?string
    {
        if ($this->isRoot()) {
            return null;
        }

        return $this->parentReference['id'];
    }

    public function hasParentPath(): bool
    {
        return !empty($this->parentReference['path']);
    }

    public function parentPath(): string
    {
        return explode('root:', $this->parentReference['path'])[1];
    }
}
