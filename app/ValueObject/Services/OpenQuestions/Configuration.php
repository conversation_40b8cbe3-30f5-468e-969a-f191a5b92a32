<?php

namespace App\ValueObject\Services\OpenQuestions;

use App\Services\FileUploadService;
use App\Services\OpenQuestions\ManualQuestionsService;
use Illuminate\Contracts\Support\Arrayable;

class Configuration implements Arrayable
{
    public function __construct(private array $properties)
    {
    }

    /**
     * @return string
     */
    public function getQuestionUrl(): string
    {
        return $this->properties[ManualQuestionsService::PROPERTY_QUESTION_URL];
    }

    /**
     * @return bool
     */
    public function getAllowedUnknownEmails(): bool
    {
        return $this->properties[ManualQuestionsService::PROPERTY_UNKNOWN_MAIL_ADDRESS] ?? false;
    }

    public function isEnabledForAllCompanies(): bool
    {
        if (!isset($this->properties[FileUploadService::PROPERTY_ENABLE_FOR_ALL_COMPANIES])) {
            return false;
        }
        return $this->properties[FileUploadService::PROPERTY_ENABLE_FOR_ALL_COMPANIES];
    }

    public function sendAllToDms(): bool
    {
        if (!isset($this->properties[FileUploadService::PROPERTY_SEND_ALL_TO_DMS])) {
            return false;
        }
        return $this->properties[FileUploadService::PROPERTY_SEND_ALL_TO_DMS];
    }

    public function toArray(): array
    {
        return $this->properties;
    }
}
