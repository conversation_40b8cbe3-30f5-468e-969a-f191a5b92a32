<?php

namespace App\ValueObject\Services\Declarations\LoketApi;

use Illuminate\Contracts\Support\Arrayable;

readonly class Administration implements Arrayable
{
    public const CLIENT_NUMBER = 'clientNumber';
    public const EMPLOYER_ID = 'employerId';
    public const WAGE_TAX_NUMBER = 'wageTaxNumber';
    public const DESCRIPTION = 'description';
    public const START_DATE = 'startDate';
    public const END_DATE = 'endDate';
    public const IS_PAYROLL_ADMINISTRATION = 'isPayrollAdministration';
    public const ID = 'id';

    public function __construct(
        public int $clientNumber,
        public string $employerId,
        public ?string $wageTaxNumber,
        public string $description,
        public string $startDate,
        public ?string $endDate,
        public bool $isPayrollAdministration,
        public string $id
    ) {
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->description,
            'wage_tax_number' => $this->wageTaxNumber,
            'client_id' => $this->clientNumber
        ];
    }
}
