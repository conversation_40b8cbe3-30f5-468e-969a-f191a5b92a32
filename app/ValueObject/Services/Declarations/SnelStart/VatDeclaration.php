<?php

namespace App\ValueObject\Services\Declarations\SnelStart;

use App\ServiceTask;
use App\ValueObject\Declarations\DeclarationData;
use App\ValueObject\Identifier\VatNumber;
use App\Support\Carbon;

class VatDeclaration
{
    public const TAXABLE_AMOUNT = 'taxable_amount';
    public const VAT = 'vat';

    public function __construct(
        public readonly string $id,
        public readonly Carbon $startDate,
        public readonly Carbon $endDate,
        public readonly bool $isSupplementation,
        public readonly VatNumber $vatNumber,
        public readonly array $summary,
        public readonly ?Carbon $modifiedDate = null
    ) {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            DeclarationData::OBNUMBER_FIELD => $this->vatNumber->__toString(),
            DeclarationData::TYPE_FIELD => ServiceTask::TYPE_VAT_APPROVAL,
            DeclarationData::DATE_START_FIELD => $this->startDate,
            DeclarationData::DATE_END_FIELD => $this->endDate,
            DeclarationData::SUPPLETION => $this->isSupplementation,
            DeclarationData::SUMMARY_FIELD => $this->summary,
            DeclarationData::SOURCE_MODIFIED_DATE => $this->modifiedDate
        ];
    }
}
