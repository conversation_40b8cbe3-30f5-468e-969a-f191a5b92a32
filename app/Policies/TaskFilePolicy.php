<?php

namespace App\Policies;

use App\User;
use Gate;
use Illuminate\Auth\Access\HandlesAuthorization;

class TaskFilePolicy
{
    use HandlesAuthorization;

    public function isUserAllowed(User $user): bool
    {
        return Gate::allows('use_companies', $user);
    }

    public function index(User $user): bool
    {
        return $this->isUserAllowed($user);
    }

    public function delete(User $user): bool
    {
        return $this->isUserAllowed($user);
    }
}
