<?php

namespace App\Policies;

use App\Account;
use App\Http\Requests\UpdateVerifiedUsersRequest;
use App\Role;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use View;

class AccountPolicy
{
    use HandlesAuthorization;

    public function manage(User $user, Account $account): bool
    {
      //only if user is manager of the account
        return $account->hasManager($user);
    }

    public function create(User $user): bool
    {
      //only if user is manager of context 1 (Hix)
        return Account::getAdminAccount()->hasManager($user);
    }

    public function store(User $user, Account $account): bool
    {
      //only if user is manager of context 1 (Hix)
        return Account::getAdminAccount()->hasManager($user);
    }

    public function createManager(User $user, Account $account): bool
    {
      //only if user is manager of the account
        return $account->hasManager($user);
    }

    public function createAccountWidget(User $user, Account $account): bool
    {
        return $account->isManagedBy($user, true) || $user->isAdminUser(true);
    }

    public function editSettings(User $user, Account $account): bool
    {
      //only if user is manager of the account
        return $account->hasManager($user);
    }

    public function editBranding(User $user, Account $account): bool
    {
      //only if user is manager of the account
        return ($account->hasLicense('branding') || $account->hasLicense('custom_domain'))
            && $account->hasManager($user);
    }

    public function editCommunication(User $user, Account $account): bool
    {
      //only if user is manager of the account and there is no custom template

        return $account->hasManager($user)
            && !View::exists("user.emails.invitation_default_" . str_clean_word($account->name));
    }

    public function updateSettings(User $user): bool
    {
        $account = Account::getHostAccount();

      //only if user is manager of the account
        return $account->hasManager($user) || $user->isAdminUser();
    }

    public function updateEmbedSettings(User $user): bool
    {
        $account = Account::getHostAccount();

      //only if user is manager of the account
        return $account->hasManager($user) || $user->isAdminUser();
    }

    public function overviewAccountWidgets(User $user, Account $account, Request $request): bool
    {
        return true;
    }

    public function sendSupportMessage(): bool
    {
        return true;
    }

    public function requestSSO(User $user): bool
    {
        return Account::getHostAccount()->hasManager($user);
    }

    public function block(User $user, Account $account): bool
    {
        return in_array($account->status, ['trial','pending']) && $user->isAdminUser(false);
    }

    public function activate(User $user, Account $account): bool
    {
        return in_array($account->status, ['trial','pending']) && $user->isAdminUser(false);
    }

    public function createConsumer(User $user, Account $account): bool
    {
        return $account->isManagedBy($user) && $user->isAdminUser(false);
    }

    public function exportWeeklyUserData(User $user, Account $account): bool
    {
        return Account::getHostAccount()->isAdminAccount() && $user->isAdminUser();
    }

    public function getAfasOpenId(User $user, Account $account): bool
    {
        $account = Account::getHostAccount();
        return $account->isManagedBy($user);
    }

    public function generateAfasOpenId(User $user, Account $account): bool
    {
        return $user->isManagerOfHostAccount();
    }

    public function removeAfasOpenId(User $user, Account $account): bool
    {
        return $user->isManagerOfHostAccount();
    }

    public function sendSupportRequest(User $user, Account $account): bool
    {
        return true;
    }

    public function sendSSORequest(User $user): bool
    {
        return $user->isManagerOfHostAccount();
    }

    public function apiIndexCompanies(User $user): bool
    {
        return $user->isManagerOfOwnAccount() || $user->hasRole(Role::COMPANY_MANAGER);
    }

  /**
   * Check if the user can retrieve a list of groups that he manages.
   */
    public function managedContexts(User $user, Account $account): bool
    {
        return $user->isManager();
    }

    public function addAccountManagers(User $user): bool
    {
        return $user->isManagerOfHostAccount() || $user->isAdminUser();
    }

    public function addCompanyManager(User $user): bool
    {
        return $user->isManagerOfHostAccount() || $user->isAdminUser();
    }

    public function deleteCompanyManager(User $user): bool
    {
        return $user->isManagerOfHostAccount() || $user->isAdminUser();
    }

    public function addCertificate(User $user): bool
    {
        $account = Account::getHostAccount();
       //only if user is manager of the account
        return $account->hasManager($user) && $account->hasLicense('companies');
    }

    public function index(User $user): bool
    {
        return $user->isAdminUser();
    }

    public function updateAccount(User $user): bool
    {
        return $user->isAdminUser();
    }

    public function loginConsumers(User $user): bool
    {
        return $user->isAdminUser();
    }
}
