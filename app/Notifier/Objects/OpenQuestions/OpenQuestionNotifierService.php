<?php

namespace App\Notifier\Objects\OpenQuestions;

use App\Logging\Channels\OpenQuestionNotifierLog;
use App\Mail\AbstractMail;
use App\Models\CommunicationChannels\CommunicationChannel;
use App\Notifier\Factories\NotifierMessageFactory;
use App\Notifier\Factories\NotifierServiceFactory;
use App\Notifier\Interfaces\EmailNotifierInterface;
use App\Notifier\Interfaces\WhatsAppNotifierInterface;
use App\Notifier\Objects\AbstractNotifier;

class OpenQuestionNotifierService
{
    public function send(array $channels, AbstractNotifier $notifierType)
    {
        $notifierMessages = collect();

        foreach ($channels as $channel) {
            if ($this->canBeSentThroughChannel($channel, $notifierType)) {
                $notifierMessage = NotifierMessageFactory::create($notifierType, $channel);
                $notifierMessages->push($notifierMessage);
            }
        }

        if ($notifierMessages->isEmpty()) {
            $notifierMessages->push($notifierType->buildDefaultMessage());
        }

        foreach ($notifierMessages as $notifierMessage) {
            $notifierService = NotifierServiceFactory::create($notifierMessage);
            if ($notifierMessage instanceof AbstractMail) {
                $this->handleEmailEventAndLog($notifierMessage);
            }
            $notifierService->send($notifierMessage);
        }
    }

    private function canBeSentThroughChannel(string $channel, AbstractNotifier $notifier): bool
    {
        return match ($channel) {
            CommunicationChannel::EMAIL => $notifier instanceof EmailNotifierInterface,
            CommunicationChannel::WHATSAPP => $notifier instanceof WhatsAppNotifierInterface,
            default => false
        };
    }

    private function handleEmailEventAndLog(AbstractMail $notifierMessage): void
    {
        OpenQuestionNotifierLog::debug('Sent open questions ' . $notifierMessage::class . ' to ' . $notifierMessage->recipient?->email); //phpcs:ignore
    }
}
