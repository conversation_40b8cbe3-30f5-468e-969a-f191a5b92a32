<?php

namespace App\Notifier\Objects\ServiceTask\Types\DividendTax;

use App\Mail\AbstractBaseMail;
use App\Mail\ServiceTask\DividendTax\DividendTaxApprovedMail;
use App\Notifier\Objects\ServiceTask\Types\BaseNotifier;
use App\WhatsApp\AbstractWhatsApp;
use App\WhatsApp\ServiceTask\TaskCompletedWhatsApp;

class DividendTaxApprovedNotifier extends BaseNotifier
{
    public function buildEmailMessage(): AbstractBaseMail
    {
        return new DividendTaxApprovedMail($this->response);
    }

    public function buildWhatsAppMessage(): AbstractWhatsApp
    {
        return new TaskCompletedWhatsApp($this->response);
    }
}
