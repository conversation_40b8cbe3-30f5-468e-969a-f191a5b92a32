<?php

namespace App\Notifier\Objects\ServiceTask\Types\VatApproval;

use App\Mail\AbstractBaseMail;
use App\Mail\ServiceTask\VatApproval\VatApprovedMail;
use App\Notifier\Objects\ServiceTask\Types\BaseNotifier;
use App\WhatsApp\AbstractWhatsApp;
use App\WhatsApp\ServiceTask\PaymentWhatsApp;

class VatApprovedNotifier extends BaseNotifier
{
    public function buildEmailMessage(): AbstractBaseMail
    {
        return new VatApprovedMail($this->response);
    }

    public function buildWhatsAppMessage(): AbstractWhatsApp
    {
        return new PaymentWhatsApp($this->response);
    }
}
