<?php

namespace App\Notifier\Objects\ServiceTask\Types\VpbApproval;

use App\Mail\AbstractBaseMail;
use App\Mail\ServiceTask\VpbApproval\VpbApprovalForcedMail;
use App\Notifier\Objects\ServiceTask\Types\BaseNotifier;
use App\WhatsApp\AbstractWhatsApp;
use App\WhatsApp\ServiceTask\ForceApprovalWhatsApp;

class VpbApprovalForcedNotifier extends BaseNotifier
{
    public function buildEmailMessage(): AbstractBaseMail
    {
        return new VpbApprovalForcedMail($this->response);
    }

    public function buildWhatsAppMessage(): AbstractWhatsApp
    {
        return new ForceApprovalWhatsApp($this->response);
    }
}
