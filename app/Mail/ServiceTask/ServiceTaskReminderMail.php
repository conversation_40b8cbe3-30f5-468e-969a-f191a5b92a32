<?php

namespace App\Mail\ServiceTask;

use App\Interfaces\EmailTemplate\CustomizableMail;
use App\Mail\MailKey;
use App\Objects\EmailTemplate\Content\TaskCustomerFinalPayDateReminder;
use App\Objects\EmailTemplate\Content\TaskCustomerReminder;
use App\ServiceTask;
use App\ServiceTaskResponse;
use App\User;

class ServiceTaskReminderMail extends AbstractServiceTaskMail
{
    public ?string $click_button_below = null;
    public ?string $details = null;

    public function __construct(ServiceTaskResponse $response)
    {
        $this->response = $response;
        $this->taskResponseId = $response->id;
        $this->taskId = $response->task_id;
        $this->task = $response->task;

        parent::__construct(
            $response->account,
            'mail.task.task_approval_reminder',
            $response->user->trans('mail.task.task_approval_reminder.subject', [
                'title' => $response->task->label($response->user->language)
            ]),
            $response->user,
            User::find($response->created_by)
        );

        $this->button = $this->getButtonText();
        $this->url = $this->getButtonUrl();
        $this->intro = $this->getIntro();
        $this->details = $this->getDetails();
        $this->click_button_below = $this->recipient->trans('mail.click_button_below');

        if ($this instanceof CustomizableMail) {
            $this->findAndApplyTemplate();
        }
    }

    /**
     * HTML content
     *
     * @return string
     */
    public function getIntro(): string
    {
        $translationKey = $this->getTask()?->getReminderInfo()['intro'] ?? null;
        if (empty($translationKey)) {
            return '';
        }

        return $this->recipient->trans($translationKey, $this->getReplacements());
    }

    /**
     * Gets payment details
     * In case the task has no final pay date, we cannot tell them when to do anything.
     * There shouldn't be a date for submit in the reminder mails for IT and CT.
     * IT = IHZ (income tax)
     * CT = VPB (corporate tax)
     */
    public function getDetails(): string
    {
        $task = $this->getTask();
        if (!$task->canBePaid() || empty($task->data[ServiceTask::DATE_END])) {
            return '';
        }

        $translationKey = $task->getReminderInfo()['details'] ?? null;
        if (empty($translationKey)) {
            return '';
        }

        return $this->recipient->trans($translationKey, $this->getReplacements());
    }

    public function getReplacements(): array
    {
        $task = $this->getTask();

        return [
            TaskCustomerReminder::TAG_FIRSTNAME => $this->recipient->firstname,
            TaskCustomerReminder::TAG_LASTNAME => $this->recipient->lastname,
            TaskCustomerReminder::TAG_NAME => $this->recipient->name,
            TaskCustomerReminder::TAG_ACCOUNT => $this->recipient->getAccount()->name,
            TaskCustomerReminder::TAG_TITLE => htmlentities($task->label($this->recipient->language)),
            TaskCustomerFinalPayDateReminder::TAG_FINAL_PAY_DATE => $task->getFinalPayDate(),
            TaskCustomerReminder::TAG_COMPANY => $this->getResponse()?->task->company->baseName(),
        ];
    }

    public function getMailKey(): string
    {
        return MailKey::SERVICE_TASK_APPROVAL_REMINDER;
    }
}
