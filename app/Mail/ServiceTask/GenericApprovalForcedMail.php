<?php

namespace App\Mail\ServiceTask;

use App\Interfaces\EmailTemplate\CustomizableMail;
use App\Mail\MailKey;
use App\Objects\EmailTemplate\Content\TaskForceApprovedContent;
use App\ServiceTaskResponse;
use App\Traits\EmailTemplate\CustomizableMailTrait;
use App\ValueObject\Mail\TinyAccount;
use App\ValueObject\Mail\TinyUser;

abstract class GenericApprovalForcedMail extends AbstractServiceTaskMail implements CustomizableMail
{
    use CustomizableMailTrait;

    protected ?array $replacementsCache = null;

    public function __construct(ServiceTaskResponse $response)
    {
        $this->response = $response; // is needed for previews
        $this->task = $response->task; // is needed for previews
        $this->recipient = $response->user; // is needed for replacing text in subject
        $this->sender = new TinyUser(\Auth::user()); // is needed for replacing text in subject
        $this->account = new TinyAccount($response->user->account); // is needed for replacing text in subject
        $subject = $response->user->trans('mail.task.generic_approval_forced.subject', $this->getReplacements());
        parent::__construct(
            $response->account,
            'mail.task.generic_approval_forced',
            $subject,
            $response->user,
            $this->sender
        );
        $this->intro = $this->getIntro();
        $this->taskResponseId = $response->id;
        $this->taskId = $response->task_id;

        $this->task = null; // remove reference to reduce job size

        $this->findAndApplyTemplate();
    }

    /**
     * Get replacements. Cache results in array so we can unset objects to prevent them from making mail job very big.
     * @return array
     */
    public function getReplacements(): array
    {
        if (is_null($this->replacementsCache)) {
            $task = $this->getTask();
            $this->replacementsCache = array_merge(
                parent::getReplacements(),
                [
                    TaskForceApprovedContent::TAG_TITLE => $task->label($this->recipient->language),
                    TaskForceApprovedContent::TAG_APPROVER => $this->sender->name,
                    TaskForceApprovedContent::TAG_COMPANY => $task->company->baseName()
                ]
            );
        }
        return $this->replacementsCache;
    }

    public function getIntro(): string
    {
        return $this->recipient->trans('mail.task.generic_approval_forced.intro', $this->getReplacements());
    }

    public function getRegardsText(): string
    {
        // If recipient is set, we use that as language for translations.
        if (!empty($this->recipient)) {
            // If sender is set we use that as regard text.
            if (!empty($this->sender)) {
                if ($this->sender instanceof TinyUser && $this->recipient instanceof TinyUser) {
                    $recipient = $this->recipient->trans('user.msg_regards', $this->getReplacements());
                    return nl2br(htmlentities($recipient), false);
                }
                $recipient = $this->recipient->trans('user.msg_regards_with_account', $this->getReplacements());
                return nl2br(htmlentities($recipient), false);
            }
            // If not set we use Hix as sender
            return $this->recipient->trans('user.msg_regards_hix');
        }
        return $this->account->trans('user.msg_regards_hix');
    }

    public function getEmailTemplateEvent(): ?string
    {
        return TaskForceApprovedContent::EVENT_TASK_DOCUMENT_FORCE_APPROVED;
    }

    public function getMailKey(): string
    {
        return MailKey::SERVICE_TASK_APPROVAL_FORCED;
    }
}
