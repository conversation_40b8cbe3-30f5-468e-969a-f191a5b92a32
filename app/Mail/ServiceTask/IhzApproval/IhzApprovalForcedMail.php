<?php

namespace App\Mail\ServiceTask\IhzApproval;

use App\Interfaces\EmailTemplate\CustomizableMail;
use App\Mail\ServiceTask\GenericApprovalForcedMail;
use App\Objects\EmailTemplate\Content\TaskForceApprovedContent;
use App\Traits\EmailTemplate\CustomizableMailTrait;
use App\ValueObject\Declarations\DeclarationData;

class IhzApprovalForcedMail extends GenericApprovalForcedMail implements CustomizableMail
{
    use CustomizableMailTrait;

    public function getEmailTemplateEvent(): ?string
    {
        return TaskForceApprovedContent::EVENT_TASK_IHZ_FORCE_APPROVED;
    }

    /**
     * Replace company with name of natural person if found.
     * @return array
     */
    public function getReplacements(): array
    {
        if (is_null($this->replacementsCache)) {
            $this->replacementsCache = parent::getReplacements();
            $name = array_get($this->getTask()->data, DeclarationData::NATURAL_PERSON_NAME);
            if (isset($name) && strlen($name)) {
                $this->replacementsCache['company'] = $name;
            }
        }
        return $this->replacementsCache;
    }
}
