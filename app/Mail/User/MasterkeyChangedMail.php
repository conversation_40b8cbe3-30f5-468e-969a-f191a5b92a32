<?php

namespace App\Mail\User;

use App\Mail\AbstractMail;
use App\Mail\MailKey;
use App\User;

class MasterkeyChangedMail extends AbstractMail
{
    public string $masterkey;

    public function __construct(User $recipient, string $masterkey)
    {
        parent::__construct(
            $recipient->account,
            'mail.user.masterkey_changed',
            $recipient->trans('user.msg_masterkey_changed.subject'),
            $recipient,
            $recipient->account // mail is sent by account
        );

        $this->masterkey = $masterkey;
    }

    public function getMailKey(): string
    {
        return MailKey::USER_MASTERKEY_CHANGED;
    }
}
