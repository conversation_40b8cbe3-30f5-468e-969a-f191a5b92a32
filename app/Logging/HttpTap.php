<?php

namespace App\Logging;

use App;
use App\Logging\Processors\HttpRequestProcessor;
use Illuminate\Log\Logger;

/**
 * Adds logging processors with relevant HTTP information
 */
class HttpTap extends BaseTap
{
    private static $instanceProcessors = null;

    public function __invoke(Logger $logger): void
    {
        $this->pushProcessors($logger, array_merge(self::getSharedProcessors(), $this->getAdditionalProcessors()));
    }

    protected function getAdditionalProcessors(): array
    {
        return self::$instanceProcessors ??
            (self::$instanceProcessors = App::runningInConsole() ? [] : [new HttpRequestProcessor()]);
    }
}
