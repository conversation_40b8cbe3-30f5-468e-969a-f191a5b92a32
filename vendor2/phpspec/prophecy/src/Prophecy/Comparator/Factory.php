<?php

/*
 * This file is part of the Prophecy.
 * (c) <PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Prophecy\Comparator;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Comparator\Factory as BaseFactory;

/**
 * Prophecy comparator factory.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @deprecated Use "Prophecy\Comparator\FactoryProvider" instead to get a "Sebastian<PERSON><PERSON><PERSON>n\Comparator\Factory" instance.
 */
final class Factory extends BaseFactory
{
    /**
     * @var Factory
     */
    private static $instance;

    public function __construct()
    {
        parent::__construct();

        $this->register(new ClosureComparator());
        $this->register(new ProphecyComparator());
    }

    /**
     * @return Factory
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new Factory();
        }

        return self::$instance;
    }
}
