<?php

/*
 * This file is part of PhpSpec, A php toolset to drive emergent
 * design by specification.
 *
 * (c) <PERSON><PERSON> <<EMAIL>>
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PhpSpec\Locator;

interface ResourceManager
{
    /**
     * @return \PhpSpec\Locator\Resource[]
     */
    public function locateResources(string $query): array;

    
    public function createResource(string $classname): \PhpSpec\Locator\Resource;
}
