# This file is for unifying the coding style for different editors and IDEs
# editorconfig.org

root = true

[*]
indent_style = space
end_of_line = lf
charset = utf-8
# (Please don't specify an indent_size here; that has too many unintended consequences.)

[*.js]
indent_size = 4

[*.{less, sass, css}]
indent_size = 4

# PHP PSR-2 Coding Standards
# http://www.php-fig.org/psr/psr-2/
[*.{php, phpt}]
indent_size = 4
trim_trailing_whitespace = true
insert_final_newline = true

[*.{html,xml}]
indent_size = 4

[*.json]
indent_size = 4

[*.{yml, m4, sh}]
indent_size = 4
trim_trailing_whitespace = true
insert_final_newline = true

[*.md]
trim_trailing_whitespace = false
insert_final_newline = true
