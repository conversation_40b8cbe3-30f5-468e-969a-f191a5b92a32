<?php

namespace Tests\Unit\Helpers;

use App\Helpers\IdGeneratorHelper;
use App\Support\Carbon;
use Tests\Support\UnitTestCase;

class IdGeneratorHelperTest extends UnitTestCase
{
    /**
     * Test if constant values always generate the same ID
     */
    public function testIdGenerationWithConstantValues()
    {
        $value1 = 'sample';
        $value2 = 77;
        $value3 = [2, "rgb3", [111, "rgb3"]];

        $generatedIds = [];
        $generatedIds[] = IdGeneratorHelper::generateStringIdFromArgs($value1, $value2, $value3);
        $generatedIds[] = IdGeneratorHelper::generateStringIdFromArgs($value1, $value2, $value3);
        $generatedIds[] = IdGeneratorHelper::generateStringIdFromArgs($value1, $value2, $value3);
        $generatedIds[] = IdGeneratorHelper::generateStringIdFromArgs($value1, $value2, $value3);
        $generatedIds[] = IdGeneratorHelper::generateStringIdFromArgs($value1, $value2, $value3);

        $uniqueIds = array_unique($generatedIds);

        $this->assertTrue(count($uniqueIds) == 1);
    }

    /**
     * Test if changing values always generates a different ID
     */
    public function testIdGenerationWithChangingValues()
    {
        $generatedIds = [];
        $generatedIds[] = IdGeneratorHelper::generateStringIdFromArgs(Carbon::now());
        $generatedIds[] = IdGeneratorHelper::generateStringIdFromArgs(Carbon::now());
        $generatedIds[] = IdGeneratorHelper::generateStringIdFromArgs(Carbon::now());
        $generatedIds[] = IdGeneratorHelper::generateStringIdFromArgs(Carbon::now());
        $generatedIds[] = IdGeneratorHelper::generateStringIdFromArgs(Carbon::now());

        $uniqueIds = array_unique($generatedIds);

        $this->assertTrue(count($uniqueIds) == count($generatedIds));
    }
}
