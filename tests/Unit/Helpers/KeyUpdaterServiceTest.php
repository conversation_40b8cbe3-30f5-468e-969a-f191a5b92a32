<?php

namespace Tests\Unit\Helpers;

use App\Account;
use App\Auth\Contracts\IdentityClaimTokenHandler;
use App\Auth\Helpers\KeyUpdaterService;
use App\Auth\IdentityIssuer;
use App\Auth\Models\KeysCollection;
use App\Support\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use RuntimeException;
use Tests\Helper\DataCreator;
use Tests\Support\OldTestCase;

class KeyUpdaterServiceTest extends OldTestCase
{

  public function testIssuerNotSupportingUpdates()
  {
    $this->dataCreatorParameters->setConsumerAuthId(Str::random(10));
    $this->dataCreatorParameters->setConsumerMetadataUrl(null);
    $dataCreator = new DataCreator($this->dataCreatorParameters);

    $issuer = $dataCreator->getConsumer();

    $keyUpdater = new KeyUpdaterService();
    $handler = $this->getHandlerMock();
    $updated = $keyUpdater->updateKeys($issuer, $handler);
    $this->assertFalse($updated);
  }

  public function testIssuerSupportingUpdates()
  {
    $this->dataCreatorParameters->setConsumerAuthId(Str::random(10));
    $this->dataCreatorParameters->setConsumerMetadataUrl("http://metadata.url");
    $this->dataCreatorParameters->setConsumerVerifyKeys([]);
    $dataCreator = new DataCreator($this->dataCreatorParameters);

    /** @var IdentityIssuer $issuer */
    $issuer = $dataCreator->getConsumer();
    $updatedAt = Carbon::now()->subMinutes(2);
    $issuer->updated_at = $updatedAt;
    $issuer->save();

    $keyUpdater = new KeyUpdaterService();

    $handler = $this->getHandlerMock();

    $handler->expects($this->any())
      ->method('getKeys')
      ->will($this->returnValue($this->getTestKeysCollection()));

    // first try succeeds because we've updated the keys a while ago
    $updated = $keyUpdater->updateKeys($issuer, $handler);
    $this->assertTrue($updated);
    $this->assertTrue($issuer->updated_at > $updatedAt);
    $keys = new KeysCollection($issuer->verify_keys);
    $this->assertTrue($keys->valid()->count() > 0);

    // retry fails because we've just updated the keys
    $updated = $keyUpdater->updateKeys($issuer, $handler);
    $this->assertFalse($updated);

    // when an exception is triggered, the updated_at property is refreshed to prevent too many errors/retries
    $newUpdatedAt = Carbon::now()->subMinutes(2);
    $issuer->updated_at = $newUpdatedAt;
    $issuer->save();

    $handler = $this->getHandlerMock();
    $handler->expects($this->any())
      ->method('getKeys')
      ->will($this->throwException(new RuntimeException()));

    $updated = $keyUpdater->updateKeys($issuer, $handler);
    $this->assertFalse($updated);
    $this->assertTrue($issuer->updated_at > $newUpdatedAt);
  }

  /**
   * @return IdentityClaimTokenHandler|\PHPUnit\Framework\MockObject\MockObject
   */
  private function getHandlerMock()
  {
    $account = new Account();
    $request = new Request();
    $handler = $this->getMockForAbstractClass(IdentityClaimTokenHandler::class, [$account, $request]);
    return $handler;
  }

  private function getTestKeysCollection(): KeysCollection
  {
    return new KeysCollection(json_decode('[{"value":"-----BEGIN PUBLIC KEY-----\nMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAwBAWEF11aVkQf1t95rFk\nKEOxlNC55Snq8dqeMCrmhQBnyGYibgiOXOyI7nr6+OHtzPQBQ8XaOho2LvHOqWSc\nKswxxoulrtVDYJzXSA3BaoivO38Xfzs85VHZpS7EYcdVAcIJL78PYg6iqNjgFf84\nTx2JroiBRT5gXDI7U3I2jLDqQR1VY/T1I/gfiKFO+aUYBzXeDLlEY2FH0j/g6W5a\nJI1nVJtf+oriDL8sQQ2nkcWxho4uLQnLkiv7lTrBYp5Fiz2ztATUmeN/yVqYYLLS\nUISwb/s9CPL+X+hIHr5tolHFN/qWheEittTzpbhScDrEhT+lMDIshX4BuoW8Kw3W\nzFV/HvczPULQME2PL7Ln/khTHIXMWZIOJoi+rqB4fktsxnVOY4x7qh/AeAn/sC/N\njVSeSarfrO3puz9OYjNOunJ7zVR/rsjq+Mxg5QoG1m7p/SVkqwAD9+vQtzkL4sUZ\nSOr4Dg1VziXQZHdpb6cLf+LsSZFgtURVWCs/iXGCIFYWUgT4BaufzbJo++zTmG9e\nrmfiOsg591/9mH/UOBpB6MTCxxoQEojAaLKEiFGP23LWJ+SFGkokDJwhydmRBbEk\nAj9wcXb9xj6zZIxBBmy6CrZ1iVfsWW4tgzRzInnHZ89VsWVoa0l/+JXzgI82Hphd\nF4EB74Gno/IXo6XvLtEPeacCAwEAAQ==\n-----END PUBLIC KEY-----"}]', true));
  }

}