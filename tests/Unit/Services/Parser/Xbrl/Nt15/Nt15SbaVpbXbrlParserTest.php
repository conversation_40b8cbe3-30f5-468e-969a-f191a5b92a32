<?php

namespace Tests\Unit\Services\Parser\Xbrl\Nt15;

use App\Helpers\XmlDetectorHelper;
use Tests\Support\UnitTestCase;

class Nt15SbaVpbXbrlParserTest extends UnitTestCase
{
    public function testParse()
    {
        $xbrl = $this->asset('logius/xbrl/NT15/SBA-VPB2020/VB-01_bd-rpt-vpb-sba-2020.xbrl');
        $parser = XmlDetectorHelper::detectParser($xbrl);
        $data = $parser->getParsedResult();

        $this->assertEquals('2020-11-30', $data['NoticeOfAssessmentDate']);
        $this->assertEquals('http://www.nltaxonomie.nl/nt15/bd/20210217/entrypoints/bd-rpt-vpb-sba-2020.xsd', $data['taxonomy_url']);
        $this->assertEquals('2020', $data['year']);
        $this->assertEquals('2020-06-01', $data['date_start']);
        $this->assertEquals('2021-05-31', $data['date_end']);
    }
}
