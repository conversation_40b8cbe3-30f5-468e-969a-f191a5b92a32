<?php

namespace Tests\Unit\Services\Parser\Xbrl\Nt13;

use App\Helpers\XmlDetectorHelper;
use Tests\Support\UnitTestCase;

class Nt13SbaObXbrlParserTest extends UnitTestCase
{
    public function testParse()
    {
        $xbrl = $this->asset('logius/xbrl/NT13/SBAOB/VB-01_bd-rpt-ob-sba.xbrl');
        $parser = XmlDetectorHelper::detectParser($xbrl);
        $data = $parser->getParsedResult();

        $this->assertEquals('2019-03-30', $data['NoticeOfAssessmentDate']);
        $this->assertEquals('http://www.nltaxonomie.nl/nt13/bd/20181212/entrypoints/bd-rpt-ob-sba.xsd', $data['taxonomy_url']);
        $this->assertEquals('2019', $data['year']);
        $this->assertEquals('2019-12-01', $data['date_start']);
        $this->assertEquals('2019-12-31', $data['date_end']);
    }
}
