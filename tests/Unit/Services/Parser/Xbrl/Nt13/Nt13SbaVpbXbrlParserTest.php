<?php

namespace Tests\Unit\Services\Parser\Xbrl\Nt13;

use App\Helpers\XmlDetectorHelper;
use Tests\Support\UnitTestCase;

class Nt13SbaVpbXbrlParserTest extends UnitTestCase
{
    public function testParse()
    {
        $xbrl = $this->asset('logius/xbrl/NT13/SBAVPB/VB-02_bd-rpt-vpb-sba-2018.xbrl');
        $parser = XmlDetectorHelper::detectParser($xbrl);
        $data = $parser->getParsedResult();

        $this->assertEquals('2018-06-28', $data['NoticeOfAssessmentDate']);
        $this->assertEquals('http://www.nltaxonomie.nl/nt13/bd/20181212/entrypoints/bd-rpt-vpb-sba-2018.xsd', $data['taxonomy_url']);
        $this->assertEquals('2018', $data['year']);
        $this->assertEquals('2018-01-01', $data['date_start']);
        $this->assertEquals('2018-12-31', $data['date_end']);
    }
}
