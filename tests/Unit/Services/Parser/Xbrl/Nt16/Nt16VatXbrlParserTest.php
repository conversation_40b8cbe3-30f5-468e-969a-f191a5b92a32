<?php

namespace Tests\Unit\Services\Parser\Xbrl\Nt16;

use App\Helpers\XmlDetectorHelper;
use App\Services\Parser\Xbrl\Nt16\Nt16VatXbrlParser;
use Tests\Support\UnitTestCase;

class Nt16VatXbrlParserTest extends UnitTestCase
{
    private ?string $vatXbrl;
    private ?string $vatSupXbrl;

    protected function setUp(): void
    {
        $this->vatXbrl = $this->asset(
            'logius/xbrl/NT16/OB-2022/VB-01_bd-rpt-ob-aangifte-2022.xbrl'
        );
        $this->vatSupXbrl = $this->asset(
            'logius/xbrl/NT16/OBSUP-2022/VB-01_bd-rpt-ob-suppletie-2022.xbrl'
        );
        parent::setUp();
    }

    protected function tearDown(): void
    {
        $this->vatXbrl = null;
        $this->vatSupXbrl = null;
        parent::tearDown();
    }

    public function testDetectN16()
    {
        $parser = XmlDetectorHelper::detectParser($this->vatXbrl);
        $this->assertInstanceOf(Nt16VatXbrlParser::class, $parser);
        $parser = XmlDetectorHelper::detectParser($this->vatSupXbrl);
        $this->assertInstanceOf(Nt16VatXbrlParser::class, $parser);
    }

    public function testNt16VatData()
    {
        $parser = XmlDetectorHelper::detectParser($this->vatXbrl);
        $data = $parser->getParsedResult();

        $this->assertFalse($data['suppletion']);
        $this->assertEquals('001000044B37', $data['obnumber']);
        $this->assertEquals('2022-10-01', $data['date_start']);
        $this->assertEquals('2022-10-31', $data['date_end']);
        $this->assertEquals('3240', $data['summary']['5g']['vat']);
        $this->assertEquals('3150', $data['summary']['1a']['vat']);
        $this->assertEquals('90', $data['summary']['1b']['vat']);
        $this->assertEquals('4000', $data['summary']['3a']['taxable_amount']);
        $this->assertEquals('1200', $data['summary']['4a']['taxable_amount']);
        $this->assertEquals('EUR', $data['currency']);
        $this->assertEquals('2022', $data['year']);
    }

    public function testNt16VatSupplietionData()
    {
        $parser = XmlDetectorHelper::detectParser($this->vatSupXbrl);
        $data = $parser->getParsedResult();

        $this->assertTrue($data['suppletion']);
        $this->assertEquals('001001735B02', $data['obnumber']);
        $this->assertEquals('2022-07-01', $data['date_start']);
        $this->assertEquals('2022-09-30', $data['date_end']);
        $this->assertEquals('2000', $data['summary']['1a']['taxable_amount']);
        $this->assertEquals('420', $data['summary']['1a']['vat']);
        $this->assertEquals('8000', $data['summary']['1b']['taxable_amount']);
        $this->assertEquals('480', $data['summary']['1b']['vat']);
        $this->assertNull($data['summary']['1c']['taxable_amount']);
        $this->assertNull($data['summary']['1c']['vat']);
        $this->assertNull($data['summary']['1d']['taxable_amount']);
        $this->assertNull($data['summary']['1d']['vat']);
        $this->assertNull($data['summary']['1e']['taxable_amount']);
        $this->assertNull($data['summary']['1e']['vat']);
        $this->assertNull($data['summary']['2a']['taxable_amount']);
        $this->assertNull($data['summary']['2a']['vat']);
        $this->assertNull($data['summary']['3a']['taxable_amount']);
        $this->assertNull($data['summary']['3a']['vat']);
        $this->assertNull($data['summary']['3b']['taxable_amount']);
        $this->assertNull($data['summary']['3b']['vat']);
        $this->assertNull($data['summary']['3c']['taxable_amount']);
        $this->assertNull($data['summary']['3c']['vat']);
        $this->assertNull($data['summary']['4a']['taxable_amount']);
        $this->assertNull($data['summary']['4a']['vat']);
        $this->assertNull($data['summary']['4b']['taxable_amount']);
        $this->assertNull($data['summary']['4b']['vat']);
        $this->assertEquals('900', $data['summary']['5a']['vat']);
        $this->assertEquals('900', $data['summary']['5c']['vat']);
        $this->assertEquals('900', $data['summary']['5e']['vat']);
        $this->assertEquals('200', $data['summary']['5f']['vat']);
        $this->assertEquals('700', $data['summary']['5g']['vat']);
    }
}
