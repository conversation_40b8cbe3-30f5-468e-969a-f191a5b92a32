<?php

namespace Tests\Unit\Services\Parser\Xbrl\Nt11;

use App\Helpers\XmlDetectorHelper;
use Tests\Support\UnitTestCase;

class Nt11SbaVpbXbrlParserTest extends UnitTestCase
{
    public function testParse()
    {
        $xbrl = $this->asset('logius/xbrl/NT11/SBAVPB2018/VB-01_bd-rpt-vpb-sba-2018.xbrl');
        $parser = XmlDetectorHelper::detectParser($xbrl);
        $data = $parser->getParsedResult();

        $this->assertEquals('2018-01-31', $data['NoticeOfAssessmentDate']);
        $this->assertEquals('http://www.nltaxonomie.nl/nt11/bd/20170920/entrypoints/bd-rpt-vpb-sba-2018.xsd', $data['taxonomy_url']);
        $this->assertEquals('2018', $data['year']);
        $this->assertEquals('2018-01-01', $data['date_start']);
        $this->assertEquals('2018-12-31', $data['date_end']);
    }
}
