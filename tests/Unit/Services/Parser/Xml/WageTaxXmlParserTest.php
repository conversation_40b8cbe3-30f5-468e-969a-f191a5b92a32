<?php

namespace Tests\Unit\Services\Parser\Xml;

use App\Helpers\XmlDetectorHelper;
use App\Services\Parser\Xml\WageTaxXmlParser;
use Tests\Support\UnitTestCase;

class WageTaxXmlParserTest extends UnitTestCase
{
    public function testGetIdentifier()
    {
        $xml = $this->asset('logius/xml/nmbrs-wage_tax-data_from_prod.xml');
        $parser = XmlDetectorHelper::detectParser($xml);
        $this->assertInstanceOf(WageTaxXmlParser::class, $parser);
        $this->assertEquals('859651903L01', $parser->getDomIdentifier());
    }
}
