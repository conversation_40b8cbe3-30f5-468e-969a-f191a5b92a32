<?php

namespace Tests\Unit\Services\Azure;

use App\Services\Azure\AzureBlobStorageService;
use Tests\Support\UnitTestCase;

class AzureBlobStorageServiceTest extends UnitTestCase
{
    public function testUploadFile(): void
    {
        $service = new AzureBlobStorageService();
        $service->uploadFile(base_path('tests/Support/assets/csv/user/10-users.csv'), '10-users.csv');
        $this->assertTrue($service->fileExists('10-users.csv'));
        $this->assertTrue($service->deleteFile('10-users.csv'));
    }
}
