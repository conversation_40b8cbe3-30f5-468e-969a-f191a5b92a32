<?php

namespace Tests\Unit\Services\Webhook;

use App\Models\Webhooks\Webhook;
use App\Services\Webhook\WebhookService;
use PHPUnit\Framework\TestCase;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\Webhook\WebhookFactory;

class WebhookServiceTest extends TestCase
{
    public function testGetByCondition()
    {
        $account = AccountFactory::create();

        $webhook = WebhookFactory::create($account);
        $anotherWebhook = WebhookFactory::create($account, false);
        WebhookFactory::createCondition($anotherWebhook, Webhook::LOGIUS_STATUS_CHANGE);
        $thirdWebhook = WebhookFactory::create($account, false);
        WebhookFactory::createCondition($thirdWebhook, Webhook::SERVICE_TASK_RESPONSE_STATUS_CHANGE);

        WebhookFactory::create(); // another webhook in a separate account.

        $service = resolve(WebhookService::class);
        $webhooks = $service->getByCondition($account, Webhook::SERVICE_TASK_STATUS_CHANGE);
        $this->assertCount(1, $webhooks);

        $webhooks = $service->getByCondition($account, Webhook::SERVICE_TASK_RESPONSE_STATUS_CHANGE);
        $this->assertCount(2, $webhooks);

        $webhooks = $service->getByCondition($account, Webhook::TASK_FILE_PLACEHOLDER_APPLIED);
        $this->assertCount(1, $webhooks);

        $webhooks = $service->getByCondition($account, Webhook::LOGIUS_STATUS_CHANGE);
        $this->assertCount(2, $webhooks);
    }

    public function testGetToken()
    {
        $account = AccountFactory::create();
        $service = resolve(WebhookService::class);
        $token = $service->getToken($account);
        $this->assertNotNull($token);
        $this->assertNotNull($token->token);

        $sameToken = $service->getToken($account);
        $this->assertNotNull($sameToken);
        $this->assertNotNull($sameToken->token);
        $this->assertEquals($token->token, $sameToken->token, 'same token returned after creating once');
    }
}
