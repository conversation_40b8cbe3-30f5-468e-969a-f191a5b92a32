<?php

namespace Tests\Unit\Services;

use App\Factories\Models\LogiusRequestFactory;
use App\LogiusRequest;
use App\Repositories\AuditLogRepository;
use App\Services\LogiusRequestService;
use App\TaskAuditLog;
use App\ValueObject\ServiceTask\TaskAuditLogFilter;
use Tests\Support\Factories\ServiceTaskFactory;
use Tests\Support\UnitTestCase;

class LogiusRequestServiceTest extends UnitTestCase
{
    public function testStoreFaultDetails(): void
    {
        $task = ServiceTaskFactory::createVatApproval();
        $request = LogiusRequestFactory::createForTask($task);
        $service = resolve(LogiusRequestService::class);

        $code = 'ERROR-54321';
        $descr = 'TEST-DESCR';
        $details = ['code' => $code, 'descr' => $descr];

        $this->assertNull($request->response_code);
        $this->assertNull($request->error_description);

        $service->storeFaultDetails($request, $details);

        $this->assertEquals($code, $request->response_code);
        $this->assertEquals($descr, $request->error_description);
    }

    public function testFinalizeSupplyError(): void
    {
        $task = ServiceTaskFactory::createVatApproval();
        $request = LogiusRequestFactory::createForTask($task);
        $service = resolve(LogiusRequestService::class);

        $this->assertNull($request->response_code);
        $this->assertNull($request->error_description);

        $service->finalizeSupplyError($request);

        $this->assertEquals(LogiusRequest::STATUS_ERROR, $request->status);

        $repo = resolve(AuditLogRepository::class);
        $filter = new TaskAuditLogFilter(['actions' => [TaskAuditLog::XBRL_ERROR]]);
        $logs = $repo->getLog($task, filter: $filter);
        $this->assertCount(1, $logs);

        // finalizing more than once is not possible
        $this->expectException(\UnexpectedValueException::class);
        $service->finalizeSupplyError($request);
    }
}
