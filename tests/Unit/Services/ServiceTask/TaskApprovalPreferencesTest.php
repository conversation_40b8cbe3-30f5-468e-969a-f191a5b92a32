<?php

namespace Tests\Unit\Services\ServiceTask;

use App\Models\ServiceTask\VatApprovalTask;
use App\Objects\Service\ApprovalPreferencesAwareInterface;
use App\ServiceTask;
use App\ServiceTaskResponse;
use PHPUnit\Framework\TestCase;
use Tests\Support\Builders\ServiceTaskResponseBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\ServiceTaskFactory;

class TaskApprovalPreferencesTest extends TestCase
{
    public function testTaskNotWaitingForApprovalWithOneApproval()
    {
        $account = AccountFactory::create();
        $company = CompanyFactory::create($account);
        $accountService = AccountServiceFactory::create($account);
        $task = ServiceTaskFactory::createVatApproval($accountService, $company);
        $task->status = ServiceTask::STATUS_SENT;
        $task->setApprovalPreferences(ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES_ONE);
        $task->save();

        $taskResponseApproved = ServiceTaskResponseBuilder::new()
            ->setServiceTask($task)
            ->setPermission(ServiceTaskResponse::PERMISSION_APPROVE)
            ->setStatus(ServiceTaskResponse::STATUS_APPROVED)
            ->build();
        $taskResponseAwaiting = ServiceTaskResponseBuilder::new()
            ->setServiceTask($task)
            ->setPermission(ServiceTaskResponse::PERMISSION_APPROVE)
            ->build();

        $this->assertFalse($task->isTaskWaitingForApproval([$taskResponseApproved, $taskResponseAwaiting]));
    }

    public function testTaskWaitingForApprovalWithOneApproval()
    {
        $account = AccountFactory::create();
        $company = CompanyFactory::create($account);
        $accountService = AccountServiceFactory::create($account);
        $task = ServiceTaskFactory::createVatApproval($accountService, $company);
        $task->status = ServiceTask::STATUS_SENT;
        $task->setApprovalPreferences(ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES_ALL);
        $task->save();

        $taskResponseApproved = ServiceTaskResponseBuilder::new()
            ->setServiceTask($task)
            ->setPermission(ServiceTaskResponse::PERMISSION_APPROVE)
            ->setStatus(ServiceTaskResponse::STATUS_APPROVED)
            ->build();
        $taskResponseAwaiting = ServiceTaskResponseBuilder::new()
            ->setServiceTask($task)
            ->setPermission(ServiceTaskResponse::PERMISSION_APPROVE)
            ->build();

        $this->assertTrue($task->isTaskWaitingForApproval([$taskResponseApproved, $taskResponseAwaiting]));
    }

    public function testTaskNotWaitingForApprovalWithAllApproval()
    {
        $account = AccountFactory::create();
        $company = CompanyFactory::create($account);
        $accountService = AccountServiceFactory::create($account);
        $task = ServiceTaskFactory::createVatApproval($accountService, $company);
        $task->status = ServiceTask::STATUS_SENT;
        $task->setApprovalPreferences(ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES_ALL);
        $task->save();

        $taskResponseApproved = ServiceTaskResponseBuilder::new()
            ->setServiceTask($task)
            ->setPermission(ServiceTaskResponse::PERMISSION_APPROVE)
            ->setStatus(ServiceTaskResponse::STATUS_APPROVED)
            ->build();
        $taskResponseApproved2 = ServiceTaskResponseBuilder::new()
            ->setServiceTask($task)
            ->setPermission(ServiceTaskResponse::PERMISSION_APPROVE)
            ->setStatus(ServiceTaskResponse::STATUS_APPROVED)
            ->build();

        $this->assertFalse($task->isTaskWaitingForApproval([$taskResponseApproved, $taskResponseApproved2]));
    }

    public function testTaskWaitingForApprovalWithNoApproval()
    {
        $account = AccountFactory::create();
        $company = CompanyFactory::create($account);
        $accountService = AccountServiceFactory::create($account);
        $task = ServiceTaskFactory::createVatApproval($accountService, $company);
        $task->status = ServiceTask::STATUS_SENT;
        $task->setApprovalPreferences(ApprovalPreferencesAwareInterface::APPROVAL_PREFERENCES_ALL);
        $task->save();

        $taskResponseAwaiting = ServiceTaskResponseBuilder::new()
            ->setServiceTask($task)
            ->setPermission(ServiceTaskResponse::PERMISSION_APPROVE)
            ->build();
        $taskResponseAwaiting2 = ServiceTaskResponseBuilder::new()
            ->setServiceTask($task)
            ->setPermission(ServiceTaskResponse::PERMISSION_APPROVE)
            ->build();

        $this->assertTrue($task->isTaskWaitingForApproval([$taskResponseAwaiting, $taskResponseAwaiting2]));
    }
}