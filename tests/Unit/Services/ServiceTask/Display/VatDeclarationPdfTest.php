<?php

namespace Tests\Unit\Services\ServiceTask\Display;

use App\Account;
use App\Company;
use App\Helpers\XmlDetectorHelper;
use App\Interfaces\Services\ServableTaskFileInterface;
use App\Models\ServiceTask\VatApprovalTask;
use App\Services\Generator\Pdf\VatDeclarationPdf;
use App\ServiceTask;
use Tests\Support\Builders\ServiceTaskBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\UnitTestCase;

class VatDeclarationPdfTest extends UnitTestCase
{
    /**
     * We do not have the tools to compare the visual output of a PDF but this test will verify that the code generating
     * the PDF does not throw any errors and that PDF file size is within an expected range.
     */
    public function testGeneratingPdf()
    {
        $account = AccountFactory::create();
        $company = CompanyFactory::create($account);
        $accountService = AccountServiceFactory::createManualTasks($account);

        $task = ServiceTaskBuilder::new()
            ->setCompany($company)
            ->setType(ServiceTask::TYPE_VAT_APPROVAL)
            ->setAccountService($accountService)
            ->build();
        $pdf = new VatDeclarationPdf($task);

        $this->assertInstanceOf(ServableTaskFileInterface::class, $pdf);
        $size = strlen($pdf->getContent());
        $this->assertGreaterThan(18000, $size, 'Generated PDF should be > 17 KB');
        $this->assertLessThan(19000, $size, 'Generated PDF should be < 18 KB');
    }
}
