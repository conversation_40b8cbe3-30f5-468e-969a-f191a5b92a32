<?php

namespace Tests\Unit\Services\ServiceTask\Display;

use App\Account;
use App\Company;
use App\Factories\Services\ServiceTaskFactory;
use App\Factories\TaskFileFactory;
use App\Helpers\XmlDetectorHelper;
use App\Interfaces\Services\ServableTaskFileInterface;
use App\Models\ServiceTask\WageTaxApprovalTask;
use App\Service;
use App\Services\Generator\Pdf\WageTaxDeclarationPdf;
use App\ValueObject\Declarations\DeclarationData;
use Tests\Support\Builders\AccountServiceBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\UnitTestCase;

class WageTaxDeclarationPdfTest extends UnitTestCase
{
    /**
     * We do not have the tools to compare the visual output of a PDF but this test will verify that the code generating
     * the PDF does not throw any errors and that PDF file size is within an expected range.
     */
    public function testGeneratingPdf()
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_TASKS_PROVIDER)
            ->build();

        $xml = $this->asset('logius/xml/nmbrs-wage_tax-data_from_prod.xml');
        $file = TaskFileFactory::create($accountService, $xml, 'nmbrs-wage_tax-data_from_prod.xml');

        $declarationData = new DeclarationData($file, $file->parsed_data);

        $company = new Company();
        $company->name = 'Neo Tokyo';

        $task = ServiceTaskFactory::create($accountService, $declarationData, $company);
        $pdf = new WageTaxDeclarationPdf($task);

        $this->assertInstanceOf(ServableTaskFileInterface::class, $pdf);
        $size = strlen($pdf->getContent());
        $this->assertGreaterThan(16500, $size, 'Generated PDF should be > 15 KB');
        $this->assertLessThan(17500, $size, 'Generated PDF should be < 16 KB');
    }
}
