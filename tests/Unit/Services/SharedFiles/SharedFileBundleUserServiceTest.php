<?php

namespace Tests\Unit\Services\SharedFiles;

use App\Models\SharedFiles\SharedFileAuditLog;
use App\Models\SharedFiles\SharedFileBundleUser;
use App\Services\SharedFiles\SharedFileBundleUserService;
use Illuminate\Http\UploadedFile;
use Tests\Support\Builders\SharedFiles\SharedFileAuditLogBuilder;
use Tests\Support\Builders\SharedFiles\SharedFileBuilder;
use Tests\Support\Builders\SharedFiles\SharedFileBundleBuilder;
use Tests\Support\Builders\SharedFiles\SharedFileBundleUserBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class SharedFileBundleUserServiceTest extends TestCase
{
    private ?SharedFileBundleUserService $service;

    protected function setUp(): void
    {
        $this->service = resolve(SharedFileBundleUserService::class);
        parent::setUp();
    }

    public function testGet()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $bundle = SharedFileBundleBuilder::new()->setAccount($account)->setCreatedBy($user)->build();
        SharedFileBundleUserBuilder::new()->setSharedFileBundle($bundle)->setUser($user)->build();
        $bundleUser = $this->service->get($bundle, $user);
        $this->assertEquals($bundle->uuid, $bundleUser->shared_bundle_uuid);
        $this->assertEquals($user->id, $bundleUser->user_id);
    }

    public function testUpdateStatusNotDownloaded()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $bundle = SharedFileBundleBuilder::new()->setAccount($account)->setCreatedBy($user)->build();

        $pdfPath = base_path('tests/Support/assets/pdf/document/laboratory report.pdf');
        $pdfFile = new UploadedFile($pdfPath, 'test.pdf', 'application/pdf', null, true);
        SharedFileBuilder::new()->setBundle($bundle)->setFile($pdfFile)->build();

        $bundleUser = SharedFileBundleUserBuilder::new()
            ->setSharedFileBundle($bundle)
            ->setUser($user)
            ->setStatus(SharedFileBundleUser::STATUS_UNOPENED)
            ->build();

        $this->service->updateStatus($bundleUser);

        $bundleUser->refresh();
        $this->assertEquals(SharedFileBundleUser::STATUS_UNOPENED, $bundleUser->status);
    }

    public function testUpdateStatus1File()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $bundle = SharedFileBundleBuilder::new()->setAccount($account)->setCreatedBy($user)->build();

        $pdfPath = base_path('tests/Support/assets/pdf/document/laboratory report.pdf');
        $pdfFile = new UploadedFile($pdfPath, 'test.pdf', 'application/pdf', null, true);
        $file = SharedFileBuilder::new()->setBundle($bundle)->setFile($pdfFile)->build();

        SharedFileAuditLogBuilder::new()
            ->setSharedFileBundle($bundle)
            ->setSharedFile($file)
            ->setAction(SharedFileAuditLog::FILE_DOWNLOADED_ACTION)
            ->setCreatedBy(UserFactory::createClientUser($account))
            ->build();

        $bundleUser = SharedFileBundleUserBuilder::new()
            ->setSharedFileBundle($bundle)
            ->setUser($user)
            ->setStatus(SharedFileBundleUser::STATUS_UNOPENED)
            ->build();

        $this->service->updateStatus($bundleUser);

        $bundleUser->refresh();
        $this->assertEquals(SharedFileBundleUser::STATUS_OPENED, $bundleUser->status);
    }

    public function testUpdateStatusFileDownloadedMultipleTimes()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $bundle = SharedFileBundleBuilder::new()->setAccount($account)->setCreatedBy($user)->build();

        $pdfPath = base_path('tests/Support/assets/pdf/document/laboratory report.pdf');
        $pdfFile = new UploadedFile($pdfPath, 'test.pdf', 'application/pdf', null, true);
        $file = SharedFileBuilder::new()->setBundle($bundle)->setFile($pdfFile)->build();

        SharedFileAuditLogBuilder::new()
            ->setSharedFileBundle($bundle)
            ->setSharedFile($file)
            ->setAction(SharedFileAuditLog::FILE_DOWNLOADED_ACTION)
            ->setCreatedBy(UserFactory::createClientUser($account))
            ->build();
        SharedFileAuditLogBuilder::new()
            ->setSharedFileBundle($bundle)
            ->setSharedFile($file)
            ->setAction(SharedFileAuditLog::FILE_DOWNLOADED_ACTION)
            ->setCreatedBy(UserFactory::createClientUser($account))
            ->build();

        $bundleUser = SharedFileBundleUserBuilder::new()
            ->setSharedFileBundle($bundle)
            ->setUser($user)
            ->setStatus(SharedFileBundleUser::STATUS_UNOPENED)
            ->build();

        $this->service->updateStatus($bundleUser);

        $bundleUser->refresh();
        $this->assertEquals(SharedFileBundleUser::STATUS_OPENED, $bundleUser->status);
    }

    public function testUpdateStatusMultipleFilesDownloaded()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $bundle = SharedFileBundleBuilder::new()->setAccount($account)->setCreatedBy($user)->build();

        $pdfPath = base_path('tests/Support/assets/pdf/document/laboratory report.pdf');
        $pdfFile = new UploadedFile($pdfPath, 'test.pdf', 'application/pdf', null, true);
        $file = SharedFileBuilder::new()->setBundle($bundle)->setFile($pdfFile)->build();
        $file2 = SharedFileBuilder::new()->setBundle($bundle)->setFile($pdfFile)->build();

        SharedFileAuditLogBuilder::new()
            ->setSharedFileBundle($bundle)
            ->setSharedFile($file)
            ->setAction(SharedFileAuditLog::FILE_DOWNLOADED_ACTION)
            ->setCreatedBy(UserFactory::createClientUser($account))
            ->build();
        SharedFileAuditLogBuilder::new()
            ->setSharedFileBundle($bundle)
            ->setSharedFile($file2)
            ->setAction(SharedFileAuditLog::FILE_DOWNLOADED_ACTION)
            ->setCreatedBy(UserFactory::createClientUser($account))
            ->build();

        $bundleUser = SharedFileBundleUserBuilder::new()
            ->setSharedFileBundle($bundle)
            ->setUser($user)
            ->setStatus(SharedFileBundleUser::STATUS_UNOPENED)
            ->build();

        $this->service->updateStatus($bundleUser);

        $bundleUser->refresh();
        $this->assertEquals(SharedFileBundleUser::STATUS_OPENED, $bundleUser->status);
    }

    public function testUpdateStatusMultipleFilesNotDownloaded()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $bundle = SharedFileBundleBuilder::new()->setAccount($account)->setCreatedBy($user)->build();

        $pdfPath = base_path('tests/Support/assets/pdf/document/laboratory report.pdf');
        $pdfFile = new UploadedFile($pdfPath, 'test.pdf', 'application/pdf', null, true);
        $file = SharedFileBuilder::new()->setBundle($bundle)->setFile($pdfFile)->build();
        SharedFileBuilder::new()->setBundle($bundle)->setFile($pdfFile)->build();

        SharedFileAuditLogBuilder::new()
            ->setSharedFileBundle($bundle)
            ->setSharedFile($file)
            ->setAction(SharedFileAuditLog::FILE_DOWNLOADED_ACTION)
            ->setCreatedBy(UserFactory::createClientUser($account))
            ->build();

        $bundleUser = SharedFileBundleUserBuilder::new()
            ->setSharedFileBundle($bundle)
            ->setUser($user)
            ->setStatus(SharedFileBundleUser::STATUS_UNOPENED)
            ->build();

        $this->service->updateStatus($bundleUser);

        $bundleUser->refresh();
        $this->assertEquals(SharedFileBundleUser::STATUS_UNOPENED, $bundleUser->status);
    }
}
