<?php

namespace Tests\Unit\Services\Gateway\Exact;

use App\Models\CustomRules\CustomRule;
use App\Models\CustomRules\CustomRule\CustomRuleServiceSync;
use App\Services\CustomRule\CustomRuleService;
use App\Services\Gateway\Exact\ExactTaskProvider;
use App\Support\Carbon;
use Tests\Support\Builders\CustomRules\CustomRuleConditionBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\UnitTestCase;

class ExactTaskProviderCustomRuleTest extends UnitTestCase
{
    public function testRelevantDeclarationCodesAllInRange()
    {
        Carbon::setTestNow(Carbon::parse('2024-01-20'));

        $account = AccountFactory::create();
        $service = AccountServiceFactory::createExactDeclarations($account);
        /** @var ExactTaskProvider $provider */
        $provider = $service->getProvider();

        $this->assertCount(3, $provider->getRelevantDeclarationCodes());

        $customRule = CustomRule::factory()->create(
            [
                CustomRule::ACCOUNT_ID => $account->id,
                CustomRule::ACTION => CustomRule::ACTION_SERVICE_SYNC_SCHEDULE,
                CustomRule::PARAMETERS => [
                    CustomRuleServiceSync::PARAM_FROM_DAY => 20,
                    CustomRuleServiceSync::PARAM_TO_DAY => 23
                ]
            ]
        );

        CustomRuleConditionBuilder::new()
            ->setCustomRule($customRule)
            ->setEvent(CustomRuleService::SERVICE_SYNC_VAT)
            ->build();

        $this->assertCount(3, $provider->getRelevantDeclarationCodes());

        CustomRuleConditionBuilder::new()
            ->setCustomRule($customRule)
            ->setEvent(CustomRuleService::SERVICE_SYNC_ICT)
            ->build();

        $this->assertCount(3, $provider->getRelevantDeclarationCodes());

        CustomRuleConditionBuilder::new()
            ->setCustomRule($customRule)
            ->setEvent(CustomRuleService::SERVICE_SYNC_WAGE_TAX)
            ->build();

        $this->assertCount(3, $provider->getRelevantDeclarationCodes());
    }

    public function testRelevantDeclarationCodesNotInRange()
    {
        Carbon::setTestNow(Carbon::parse('2024-01-20'));

        $account = AccountFactory::create();
        $service = AccountServiceFactory::createExactDeclarations($account);
        /** @var ExactTaskProvider $provider */
        $provider = $service->getProvider();

        $this->assertCount(3, $provider->getRelevantDeclarationCodes());

        $customRule = CustomRule::factory()->create(
            [
                CustomRule::ACCOUNT_ID => $account->id,
                CustomRule::ACTION => CustomRule::ACTION_SERVICE_SYNC_SCHEDULE,
                CustomRule::PARAMETERS => [
                    CustomRuleServiceSync::PARAM_FROM_DAY => 10,
                    CustomRuleServiceSync::PARAM_TO_DAY => 13
                ]
            ]
        );

        CustomRuleConditionBuilder::new()
            ->setCustomRule($customRule)
            ->setEvent(CustomRuleService::SERVICE_SYNC_VAT)
            ->build();

        $this->assertCount(2, $provider->getRelevantDeclarationCodes());

        CustomRuleConditionBuilder::new()
            ->setCustomRule($customRule)
            ->setEvent(CustomRuleService::SERVICE_SYNC_ICT)
            ->build();

        $this->assertCount(1, $provider->getRelevantDeclarationCodes());

        CustomRuleConditionBuilder::new()
            ->setCustomRule($customRule)
            ->setEvent(CustomRuleService::SERVICE_SYNC_WAGE_TAX)
            ->build();

        $this->assertCount(0, $provider->getRelevantDeclarationCodes());
    }
}
