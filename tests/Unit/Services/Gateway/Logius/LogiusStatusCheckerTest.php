<?php

namespace Tests\Unit\Services\Gateway\Logius;

use App\Services\Gateway\Logius\LogiusStatus;
use Tests\Support\UnitTestCase;
use App\Services\Gateway\Logius\LogiusStatusChecker;

class LogiusStatusCheckerTest extends UnitTestCase
{
    /**
     * Contains a call to retrieve WSDL from Logius, that currently fail in pipelines
     *
     *
     * public function testSupplyRequest()
     * {
     * $xbrl = file_get_contents(self::getAssetsPath('logius/xbrl/twinfield-vat.xbrl.xml'));
     * $sender = new VatDeclarationSender($xbrl);
     * $sender->parseXbrl($xbrl);
     * $sender->setRawResponse(file_get_contents(self::getAssetsPath('logius/aanleverresponse.soap.xml')));
     * $this->assertEquals('061ccb78-073b-490f-2d2d-90f672asd12c', $sender->getMessageID());
     * }
     */
    public function testStatusRequest()
    {
        $checker = new LogiusStatusChecker('test-id');
        $checker->response = $this->asset('logius/statusresponse.soap.xml');
        $status = $checker->getActualStatus();
        $this->assertFalse($status->isError());
        $this->assertFalse($status->isSuccess());
        $this->assertTrue($status->isProcessing());
    }

    /**
     * Test parsing a response with two 301 statuses where the timestamp is later than the 405 status and a 500 status
     * that is before that. The 500 should still be considered the last one.
     */
    public function testStatusRequestTimestampsOutOfOrder()
    {
        $checker = new LogiusStatusChecker('test-id');
        $checker->response = $this->asset('logius/statusresponse5.soap.xml');
        $status = $checker->getActualStatus();
        $this->assertFalse($status->isError());
        $this->assertFalse($status->isProcessing());
        $this->assertTrue($status->isSuccess());
    }

    /**
     * Test parsing a response with a lot of warnings but eventually end up with 500 as the final resulting code.
     */
    public function testStatusRequestWithWarningsAndSuccess()
    {
        $checker = new LogiusStatusChecker('test-id');
        $checker->response = $this->asset('logius/statusresponse-500-with-warnings.soap.xml');
        $status = $checker->getActualStatus();
        $this->assertEquals(LogiusStatus::STATUS_CODE_DELIVERED_AND_VALIDATED, $status->getCode());
        $this->assertFalse($status->isError());
        $this->assertFalse($status->isProcessing());
        $this->assertTrue($status->isSuccess());
    }
}