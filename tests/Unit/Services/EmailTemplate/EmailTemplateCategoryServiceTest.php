<?php

namespace Tests\Unit\Services\EmailTemplate;

use App\Services\EmailTemplate\EmailTemplateTypeService;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\UnitTestCase;

class EmailTemplateCategoryServiceTest extends UnitTestCase
{
    public function testSectionList()
    {
        $account =  AccountFactory::create();
        $service = resolve(EmailTemplateTypeService::class);

        $list = $service->getSectionList($account, 'nl');

        $this->assertCount(3, $list);
        $this->assertArrayHas<PERSON>ey('name', $list[0]);
        $this->assertArrayHas<PERSON>ey('key', $list[0]);
        $this->assertArray<PERSON>as<PERSON>ey('categories', $list[0]);

        $this->assertArrayHas<PERSON>ey('name', $list[0]['categories'][0]);
        $this->assertArrayHas<PERSON>ey('key', $list[0]['categories'][0]);
        $this->assertArray<PERSON><PERSON><PERSON>ey('customized', $list[0]['categories'][0]);
    }
}
