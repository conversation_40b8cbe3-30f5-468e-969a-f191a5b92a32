<?php

namespace Tests\Unit\ValueObject\Placeholder\Signing;

use App\Models\TaskFile\Placeholder;
use App\Services\Pdf\PdfSigningService;
use App\ValueObject\Placeholder\Signing\InitialsPlaceholder;
use Tests\Support\UnitTestCase;

class InitialsPlaceholderTest extends UnitTestCase
{
    public function testAspectRatio()
    {
        require_once('vendor-local/setasign/SetaPDF/Autoload.php');

        $content = $this->asset('pdf/document/laboratory report.pdf');
        $service = new PdfSigningService();
        $service->setContent($content);
        $doc = $service->getDocument();

        $ph = new Placeholder();
        $ph->left = 15;
        $ph->right = 60;
        $ph->top = 3;
        $ph->bottom = 80;
        $ph->page = 1;
        $ph->filled_data = "Line 1\nLine 2\nLine 3";
        $ph->status = Placeholder::STATUS_FILLED;
        $ph->type = Placeholder::TYPE_INITIALS_SIGNATURE;

        $visualPlaceholder = $service->setPlaceholder($ph);
        $xObject = $visualPlaceholder->getPdfObject($doc);

        $this->assertTrue($visualPlaceholder instanceof InitialsPlaceholder);
        $this->assertEquals(148.819, $visualPlaceholder->getWidth());
        $this->assertEquals(99.21266666666665, $visualPlaceholder->getHeight());
        $this->assertEquals(1.6535444444444443, $visualPlaceholder->ratio);
        $this->assertEquals(20000, $visualPlaceholder->getSize());

        $this->assertEquals(148.819, $xObject->getWidth());
        $this->assertEquals(99.21266666666665, $xObject->getHeight());
    }
}
