<?php

namespace Tests\Unit;

use App\User;
use App\Account;
use Tests\Support\UnitTestCase;

class UserUsageTypeTest extends UnitTestCase
{
  /**
   * @test for user usage types which are used to enable or disable API access.
   */
  public function testUserUsageTypesAttribute(): void
  {
    $user = new User();
    $user->auth_id = 'test';
    $user->account = new Account();

    //default, NULL is GUI and API.
    $this->assertTrue($user->canUse('gui'));
    $this->assertTrue($user->canUse('api'));
    $this->assertFalse($user->canUse('invalid'));

    //API set means API only
    $user->account->usage_types = ['api'];
    $this->assertTrue($user->canUse('api'));
    $this->assertFalse($user->canUse('gui'));

    //set API only on user level.
    $user->account->usage_types = ['api', 'gui'];
    $user->usage_types = ['api'];
    $this->assertTrue($user->canUse('api'));
    $this->assertFalse($user->canUse('gui'));

    //Try user value that is not allowed by account.
    try
    {
      $user->account->usage_types = ['api'];
      $user->usage_types = ['gui'];
    } catch(\Exception $e)
    {
      $this->assertTrue($e instanceof \UnexpectedValueException);
    }
  }
}
