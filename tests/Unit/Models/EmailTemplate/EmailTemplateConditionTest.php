<?php

namespace Tests\Unit\Models\EmailTemplate;

use App\Models\EmailTemplate\EmailTemplateCondition;
use App\Objects\EmailTemplate\Content\ClientUserInvitationContent;
use App\Objects\EmailTemplate\Content\OpenQuestion\OpenQuestionReminderContent;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceUploadTypeFactory;
use Tests\Support\Factories\EmailTemplate\EmailTemplateTestFactory;
use Tests\Support\UnitTestCase;

class EmailTemplateConditionTest extends UnitTestCase
{
    public function testUpdatingImmutablePropertyAccountId()
    {
        $account = AccountFactory::create();
        $condition = new EmailTemplateCondition();
        $condition->account_id = $account->id;
        $condition->template_id = EmailTemplateTestFactory::create($account)->id;
        $condition->language = 'nl';
        $condition->event = OpenQuestionReminderContent::EVENT_OPEN_QUESTION_REMINDER_DEFAULT;
        $condition->save();
        $condition->account_id = 2;
        $this->expectException(\UnexpectedValueException::class);
        $condition->save();
    }

    public function testUpdatingImmutablePropertyTemplateId()
    {
        $account = AccountFactory::create();
        $condition = new EmailTemplateCondition();
        $condition->account_id = $account->id;
        $condition->template_id = EmailTemplateTestFactory::create($account)->id;
        $condition->language = 'nl';
        $condition->event = OpenQuestionReminderContent::EVENT_OPEN_QUESTION_REMINDER_DEFAULT;
        $condition->save();
        $condition->template_id = 3;
        $this->expectException(\UnexpectedValueException::class);
        $condition->save();
    }

    public function testUpdatingImmutablePropertyLanguage()
    {
        $account = AccountFactory::create();
        $condition = new EmailTemplateCondition();
        $condition->account_id = $account->id;
        $condition->template_id = EmailTemplateTestFactory::create($account)->id;
        $condition->language = 'nl';
        $condition->event = OpenQuestionReminderContent::EVENT_OPEN_QUESTION_REMINDER_DEFAULT;
        $condition->save();
        $condition->language = 'en';
        $this->expectException(\UnexpectedValueException::class);
        $condition->save();
    }

    public function testUpdatingImmutablePropertyEvent()
    {
        $account = AccountFactory::create();
        $condition = new EmailTemplateCondition();
        $condition->account_id = $account->id;
        $condition->template_id = EmailTemplateTestFactory::create($account)->id;
        $condition->language = 'nl';
        $condition->event = OpenQuestionReminderContent::EVENT_OPEN_QUESTION_REMINDER_URGENT;
        $condition->save();
        $condition->event = OpenQuestionReminderContent::EVENT_OPEN_QUESTION_REMINDER_DEFAULT;
        $this->expectException(\UnexpectedValueException::class);
        $condition->save();
    }

    public function testUpdatingImmutablePropertyUploadTypeId()
    {
        $account = AccountFactory::create();
        $uploadType = AccountServiceUploadTypeFactory::create('test type');

        $condition = new EmailTemplateCondition();
        $condition->account_id = $account->id;
        $condition->template_id = EmailTemplateTestFactory::create($account)->id;
        $condition->language = 'nl';
        $condition->event = OpenQuestionReminderContent::EVENT_OPEN_QUESTION_REMINDER_DEFAULT;
        $condition->upload_type_id = null;
        $condition->save();
        $condition->upload_type_id = $uploadType->id;
        $this->expectException(\UnexpectedValueException::class);
        $condition->save();
    }
}
