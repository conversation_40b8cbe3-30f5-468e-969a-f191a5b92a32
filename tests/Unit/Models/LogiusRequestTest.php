<?php

namespace Tests\Unit\Models;

use App\Factories\Models\LogiusRequestFactory;
use App\Support\Carbon;
use Tests\Support\Factories\ServiceTaskFactory;
use Tests\Support\UnitTestCase;

class LogiusRequestTest extends UnitTestCase
{
    public function testNotReadyToGiveUp(): void
    {
        $task = ServiceTaskFactory::createVatApproval();
        $request = LogiusRequestFactory::createForTask($task);
        $this->assertEquals(0, $request->tries);
        $this->assertEquals(0, $request->supply_attempts);
        $request->response_code = 'ERROR';
        $request->supply_attempts = 9;
        $request->created_at = Carbon::now()->subHours(47);
        $this->assertFalse($request->readyToGiveUp());
    }

    public function testReadyToGiveUp(): void
    {
        $task = ServiceTaskFactory::createVatApproval();
        $request = LogiusRequestFactory::createForTask($task);
        $this->assertEquals(0, $request->tries);
        $this->assertEquals(0, $request->supply_attempts);
        $request->response_code = 'ERROR';
        $request->supply_attempts = 11;
        $request->created_at = Carbon::now()->subHours(49);

        $this->assertTrue($request->readyToGiveUp());
    }
}
