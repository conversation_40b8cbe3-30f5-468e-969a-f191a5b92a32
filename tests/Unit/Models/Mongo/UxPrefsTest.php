<?php

namespace Tests\Unit\Models\Mongo;

use App\User;
use App\Models\Mongo\UxPrefs;
use Tests\Support\TestCase;

class UxPrefsTest extends TestCase
{
    public function testCreate()
    {
        $prefs = '{
  "user-open-questions-overview": {
    "tabs": [
      {
        "id": "my-id",
        "title": "Favorites",
        "columns": [
          {
            "name": "id",
            "width": "4%",
            "visible": true
          },
          {
            "name": "title",
            "width": "auto",
            "visible": true
          },
          {
            "name": "status",
            "width": "auto",
            "visible": false
          }
        ],
        "pagination": {
          "limit": 50
        }
      }
    ]
  },
  "other-screen": {
      "tabs": [
        {
        	"title": "invalid"  
        }
      ]  
  }
}';

        $user = User::findOrFail(1);

        $uxprefs = new UxPrefs();
        $uxprefs->setClientDatabaseConnection($user->account_id);
        $uxprefs->user_uuid = $user->uuid;
        $uxprefs->prefs = json_decode($prefs);
        $result = $uxprefs->save();

        $this->assertTrue($result);
    }

}
