<?php

namespace Tests\Unit\Factories;

use App\Factories\Services\Declarations\VatXbrlFactory;
use App\Helpers\XmlDetectorHelper;
use App\ValueObject\Declarations\Manual\VatDeclaration;
use App\ValueObject\Identifier\VatNumber;
use App\ValueObject\Services\Declarations\SnelStart\VatDeclaration as SnelstartVatDeclaration;
use App\Support\Carbon;
use Tests\Support\UnitTestCase;

class VatXbrlFactoryTest extends UnitTestCase
{
    /**
     * @todo make SnelStart use a common data format so we don't need an additional method & test
     */
    public function testSnelstartNt14()
    {
        $id = '123456789';
        $dateStart = Carbon::parse('2020-01-01');
        $dateEnd = Carbon::parse('2020-01-31');
        $vatNumber = new VatNumber('NL001001735B02');
        $summary = [
            '5a' => [
                'vat' => '123'
            ],
            '5b' => [
                'vat' => '546',
                'sign' => '-'
            ]
        ];
        $vat = new SnelstartVatDeclaration($id, $dateStart, $dateEnd, false, $vatNumber, $summary);
        $xbrl = VatXbrlFactory::generateXbrlFromSnelStart($vat);
        $parser = XmlDetectorHelper::detectParser($xbrl);
        $parsed = $parser->getParsedResult();

        $this->assertEquals('001001735B02', $parsed['obnumber']);
        $this->assertEquals('EUR', $parsed['currency']);
        $this->assertFalse($parsed['suppletion']);
        $this->assertNull($parsed['contact_initials']);
        $this->assertNull($parsed['contact_surname']);
        $this->assertNull($parsed['contact_phone']);
        $this->assertEquals('-423', $parsed['summary']['5c']['vat']);
    }

    public function testFileIsValidNt14()
    {
        // We will use a base file to compare this with the generated one
        $xbrl = $this->asset('logius/xbrl/NT14/OB2020/VB-01_bd-rpt-ob-aangifte-2020.xbrl');
        $parser = XmlDetectorHelper::detectParser($xbrl);
        $parsed = $parser->getParsedResult();

        // Generate a XBRL file based on the serviceTaskData from the base file
        $vat = new VatDeclaration($parsed);
        $generatedXbrl = VatXbrlFactory::generateXbrl($vat);
        $parser2 = XmlDetectorHelper::detectParser($generatedXbrl);
        $parsed2 = $parser2->getParsedResult();
        $result = $this->recursive_array_diff($parsed, $parsed2);

        // If only the created or the created and year attributes are different it means that the file generated is
        //equal to the one we use as base
        $this->assertCount(1, $result);
        foreach ($result as $key => $value) {
            $this->assertTrue(in_array($key, ['created']));
        }
    }

    public function testSuppletionNt11()
    {
        // We will use a base file to compare this with the generated one
        $xbrl = $this->asset('logius/xbrl/NT11/OBSUP2017/fiscaal-gemak-vat-suppletion-nt11.xbrl');
        $parser = XmlDetectorHelper::detectParser($xbrl);
        $parsed = $parser->getParsedResult();

        // Generate a XBRL file based on the serviceTaskData from the base file
        $vat = new VatDeclaration($parsed);
        $generatedXbrl = VatXbrlFactory::generateXbrl($vat);

        $parser2 = XmlDetectorHelper::detectParser($generatedXbrl);
        $parsed2 = $parser2->getParsedResult();
        $result = $this->recursive_array_diff($parsed, $parsed2);

        // If only the created or the created and year attributes are different it means that the file generated is
        //equal to the one we use as base
        $this->assertCount(1, $result);
        foreach ($result as $key => $value) {
            $this->assertTrue(in_array($key, ['created']));
        }
    }

    public function testSuppletionNt10()
    {
        // We will use a base file to compare this with the generated one
        $xbrl = $this->asset('logius/xbrl/NT10/OBSUP2016/VB_ob-suppletie-2016-jaar.xbrl');
        $parser = XmlDetectorHelper::detectParser($xbrl);
        $parsed = $parser->getParsedResult();
        // Generate a XBRL file based on the serviceTaskData from the base file
        $vat = new VatDeclaration($parsed);
        $generatedXbrl = VatXbrlFactory::generateXbrl($vat);
        $parser2 = XmlDetectorHelper::detectParser($generatedXbrl);
        $parsed2 = $parser2->getParsedResult();
        $result = $this->recursive_array_diff($parsed, $parsed2);

        // If only the created or the created and year attributes are different it means that the file generated is
        //equal to the one we use as base
        $this->assertCount(1, $result);
        foreach ($result as $key => $value) {
            $this->assertTrue(in_array($key, ['created']));
        }
    }

    // Function to compare arrays recursively
    private function recursive_array_diff($a1, $a2)
    {
        $r = array();
        foreach ($a1 as $k => $v) {
            if (array_key_exists($k, $a2)) {
                if (is_array($v)) {
                    $rad = $this->recursive_array_diff($v, $a2[$k]);
                    if (count($rad)) {
                        $r[$k] = $rad;
                    }
                } else {
                    if ($v != $a2[$k]) {
                        $r[$k] = $v;
                    }
                }
            } else {
                $r[$k] = $v;
            }
        }
        return $r;
    }
}