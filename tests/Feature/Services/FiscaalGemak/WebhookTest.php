<?php

namespace Tests\Feature\Services\FiscaalGemak;

use App\Account;
use App\CompanyIdentifier;
use App\Models\Webhooks\Webhook;
use App\Repositories\Http\Postbode\PostbodeApiRepository;
use App\Repositories\Http\Services\FiscaalGemakRepository;
use App\Services\Gateway\FiscaalGemak\FiscaalGemakService;
use App\Services\ServableTaskFileService;
use App\Token;
use App\ValueObject\Postbode\LetterResponse;
use Database\Factories\Token\TokenFactory;
use Faker\Factory;
use Mockery;
use Symfony\Component\HttpFoundation\Response;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyIdentifierFactory;
use Tests\Support\Factories\ServiceTaskFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;
use ZipArchive;

class WebhookTest extends TestCase
{
    public const WEBHOOK_ENDPOINT = 'api.private.fiscaal_gemak.webhook';

    public function testInvalidScope(): void
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createFiscaalGemak($account);
        $data = [
            'Content' => [
                'Scope' => 'WrongScope',
                'Topic' => 'NewDocument',
                'TenantId' => '********-1234-1234-1234-************',
                'EndpointUrl' => 'https://fiscaal-gemak.nl/********-1234-1234-1234-************'
            ]
        ];

        $response = $this->post(
            $account->route(self::WEBHOOK_ENDPOINT),
            $data,
        );
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $this->assertStringContainsString('Scope', $response->json('original.message'));
    }

    public function testInvalidTopic(): void
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createFiscaalGemak($account);
        $data = [
            'Content' => [
                'Scope' => 'DocumentApproval',
                'Topic' => 'WrongTopic',
                'TenantId' => '********-1234-1234-1234-************',
                'EndpointUrl' => 'https://fiscaal-gemak.nl/********-1234-1234-1234-************'
            ]
        ];

        $response = $this->post(
            $account->route(self::WEBHOOK_ENDPOINT),
            $data,
        );
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $this->assertStringContainsString('Topic', $response->json('original.message'));
    }

    public function testInvalidTenantIdFormat(): void
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createFiscaalGemak($account);
        $data = [
            'Content' => [
                'Scope' => 'DocumentApproval',
                'Topic' => 'NewDocument',
                'TenantId' => '********',
                'EndpointUrl' => 'https://fiscaal-gemak.nl/********-1234-1234-1234-************'
            ]
        ];

        $response = $this->post(
            $account->route(self::WEBHOOK_ENDPOINT),
            $data,
        );
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $this->assertStringContainsString('TenantId field must be a valid UUID', $response->json('original.message'));
    }

    public function testInvalidEndpointUrlFormat(): void
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createFiscaalGemak($account);
        $data = [
            'Content' => [
                'Scope' => 'DocumentApproval',
                'Topic' => 'NewDocument',
                'TenantId' => '********-1234-1234-1234-************',
                'EndpointUrl' => 'this-aint-no-url'
            ]
        ];

        $response = $this->post(
            $account->route(self::WEBHOOK_ENDPOINT),
            $data,
        );
        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $this->assertStringContainsString('URL', $response->json('original.message'));
    }

    public function testInvalidEndpointUrl(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createFiscaalGemak($account);
        $data = [
            'Content' => [
                'Scope' => 'DocumentApproval',
                'Topic' => 'NewDocument',
                'TenantId' => $accountService->getProperty('tenant_id'),
                'EndpointUrl' => 'https://fiscaalgemak.nl/'
            ]
        ];

        $response = $this->post(
            $account->route(self::WEBHOOK_ENDPOINT),
            $data,
        );
        $this->assertEquals(Response::HTTP_PRECONDITION_FAILED, $response->getStatusCode());
    }

    public function testNoTokenForAccountService(): void
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createFiscaalGemak($account);
        $data = [
            'Content' => [
                'Scope' => 'DocumentApproval',
                'Topic' => 'NewDocument',
                'TenantId' => '********-1234-1234-1234-************',
                'EndpointUrl' => 'https://fiscaal-gemak.nl/********-1234-1234-1234-************'
            ]
        ];

        $response = $this->post(
            $account->route(self::WEBHOOK_ENDPOINT),
            $data,
        );
        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
    }

    public function testValidWebhookUnconnected(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createFiscaalGemak($account);

        $token = new Token();
        $token->token = random_string(16);
        $token->type = Token::TYPE_OAUTH2_ACCESS_TOKEN;
        $token->expiration_date = now()->addDay();
        $token->account_service_id = $accountService->id;
        $token->account_id = $account->id;
        $token->save();

        $zipContent = file_get_contents(base_path('tests/Support/assets/zip/vat.zip'));

        $fiscaalGemakRepository = Mockery::mock(FiscaalGemakRepository::class);
        $fiscaalGemakRepository->shouldReceive('getDocumentZip')->andReturn($zipContent);
        $this->app->instance(FiscaalGemakRepository::class, $fiscaalGemakRepository);

        $data = [
            'Content' => [
                'Scope' => 'DocumentApproval',
                'Topic' => 'NewDocument',
                'TenantId' => $accountService->getProperty('tenant_id'),
                'EndpointUrl' => 'https://public-api.fiscaalgemak.nl/api/document/2203f0fe-b22c-4ef1-98a3-62e405551678'
            ]
        ];

        $response = $this->post(
            $account->route(self::WEBHOOK_ENDPOINT),
            $data,
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $accountService->refresh();
        $this->assertCount(0, $accountService->tasks);
        $this->assertCount(1, $accountService->taskFiles()->get());
    }

    public function testValidWebhookWithCompany(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createFiscaalGemak($account);

        $company = CompanyFactory::create($account);
        CompanyIdentifierFactory::create(
            $company,
            'NL399786776B53',
            CompanyIdentifier::TYPE_VAT,
            $accountService
        );

        $token = new Token();
        $token->token = random_string(16);
        $token->type = Token::TYPE_OAUTH2_ACCESS_TOKEN;
        $token->expiration_date = now()->addDay();
        $token->account_service_id = $accountService->id;
        $token->account_id = $account->id;
        $token->save();

        $zipContent = file_get_contents(base_path('tests/Support/assets/zip/vat.zip'));

        $fiscaalGemakRepository = Mockery::mock(FiscaalGemakRepository::class);
        $fiscaalGemakRepository->shouldReceive('getDocumentZip')->andReturn($zipContent);
        $this->app->instance(FiscaalGemakRepository::class, $fiscaalGemakRepository);

        $data = [
            'Content' => [
                'Scope' => 'DocumentApproval',
                'Topic' => 'NewDocument',
                'TenantId' => $accountService->getProperty('tenant_id'),
                'EndpointUrl' => 'https://public-api.fiscaalgemak.nl/api/document/2203f0fe-b22c-4ef1-98a3-62e405551678'
            ]
        ];

        $response = $this->post(
            $account->route(self::WEBHOOK_ENDPOINT),
            $data,
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $accountService->refresh();
        $this->assertCount(1, $accountService->tasks);
        $this->assertCount(1, $accountService->taskFiles()->get());
    }

    public function testValidWebhookUpdateWithCompany(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createFiscaalGemak($account);

        $company = CompanyFactory::create($account);
        CompanyIdentifierFactory::create(
            $company,
            'NL399786776B53',
            CompanyIdentifier::TYPE_VAT,
            $accountService
        );

        $token = new Token();
        $token->token = random_string(16);
        $token->type = Token::TYPE_OAUTH2_ACCESS_TOKEN;
        $token->expiration_date = now()->addDay();
        $token->account_service_id = $accountService->id;
        $token->account_id = $account->id;
        $token->save();

        $zipContent = file_get_contents(base_path('tests/Support/assets/zip/vat.zip'));

        $fiscaalGemakRepository = Mockery::mock(FiscaalGemakRepository::class);
        $fiscaalGemakRepository->shouldReceive('getDocumentZip')->andReturn($zipContent);
        $this->app->instance(FiscaalGemakRepository::class, $fiscaalGemakRepository);

        $data = [
            'Content' => [
                'Scope' => 'DocumentApproval',
                'Topic' => 'NewDocument',
                'TenantId' => $accountService->getProperty('tenant_id'),
                'EndpointUrl' => 'https://public-api.fiscaalgemak.nl/api/document/2203f0fe-b22c-4ef1-98a3-62e405551678'
            ]
        ];

        $response = $this->post(
            $account->route(self::WEBHOOK_ENDPOINT),
            $data,
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $accountService->refresh();
        $this->assertCount(1, $accountService->tasks);
        $this->assertCount(1, $accountService->taskFiles()->get());

        $data = [
            'Content' => [
                'Scope' => 'DocumentApproval',
                'Topic' => 'UpdatedDocument',
                'TenantId' => $accountService->getProperty('tenant_id'),
                'EndpointUrl' => 'https://public-api.fiscaalgemak.nl/api/document/2203f0fe-b22c-4ef1-98a3-62e405551678'
            ]
        ];

        $response = $this->post(
            $account->route(self::WEBHOOK_ENDPOINT),
            $data,
        );
        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $accountService->refresh();
        $this->assertCount(1, $accountService->tasks);
        $this->assertCount(1, $accountService->taskFiles()->get());
    }
}
