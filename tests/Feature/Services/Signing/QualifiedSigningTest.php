<?php

namespace Tests\Feature\Services\Signing;

use App\Http\Responses\Response;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class QualifiedSigningTest extends TestCase
{
    public function testNonQualifiedSigningUsers()
    {
        $manager = UserFactory::createManager();
        $accountService = AccountServiceFactory::createQualifiedSigning($manager->account);

        $this->be($manager);

        $route = $manager->getAccount()->route(
            'qualified_signing.non_qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
            ]
        );
        $response = $this->get($route);

        $response->assertStatus(Response::HTTP_OK);
    }

    public function testFailedNonQualifiedSigningUsers()
    {
        $manager = UserFactory::createManager();
        $accountService = AccountServiceFactory::createQualifiedSigning($manager->account);

        $this->be($manager);

        $route = $manager->getAccount()->route(
            'qualified_signing.non_qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
                'page' => 'xxx'
            ]
        );
        $response = $this->get($route);

        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    public function testUnauthorizedColleagueNonQualifiedSigningUsers()
    {
        $user = UserFactory::createColleague();
        $accountService = AccountServiceFactory::createQualifiedSigning($user->account);

        $this->be($user);

        $route = $user->getAccount()->route(
            'qualified_signing.non_qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
            ]
        );
        $response = $this->get($route);

        $response->assertStatus(Response::HTTP_FORBIDDEN);
    }

    public function testUnauthorizedClientNonQualifiedSigningUsers()
    {
        $user = UserFactory::createClientUser();
        $accountService = AccountServiceFactory::createQualifiedSigning($user->account);

        $this->be($user);

        $route = $user->getAccount()->route(
            'qualified_signing.non_qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
            ]
        );
        $response = $this->get($route);

        $response->assertStatus(Response::HTTP_FORBIDDEN);
    }

    public function testQualifiedSigningUsers()
    {
        $manager = UserFactory::createManager();
        $accountService = AccountServiceFactory::createQualifiedSigning($manager->account);

        $this->be($manager);

        $route = $manager->getAccount()->route(
            'qualified_signing.qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
            ]
        );
        $response = $this->get($route);

        $response->assertStatus(Response::HTTP_OK);
    }

    public function testUnauthorizedColleagueQualifiedSigningUsers()
    {
        $user = UserFactory::createColleague();
        $accountService = AccountServiceFactory::createQualifiedSigning($user->account);

        $this->be($user);

        $route = $user->getAccount()->route(
            'qualified_signing.qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
            ]
        );
        $response = $this->get($route);

        $response->assertStatus(Response::HTTP_FORBIDDEN);
    }

    public function testUnauthorizedClientQualifiedSigningUsers()
    {
        $user = UserFactory::createClientUser();
        $accountService = AccountServiceFactory::createQualifiedSigning($user->account);

        $this->be($user);

        $route = $user->getAccount()->route(
            'qualified_signing.qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
            ]
        );
        $response = $this->get($route);

        $response->assertStatus(Response::HTTP_FORBIDDEN);
    }

    public function testAddQualifiedSigningUsers()
    {
        $manager = UserFactory::createManager();
        $accountService = AccountServiceFactory::createQualifiedSigning($manager->account);
        $this->be($manager);

        $user1Id = UserFactory::createColleague()->id;
        $user2Id = UserFactory::createColleague()->id;
        $user3Id = UserFactory::createColleague()->id;
        $user4Id = UserFactory::createColleague()->id;

        $getQualifiedUsers = function () use ($manager, $accountService) {
            $route = $manager->getAccount()->route(
                'qualified_signing.qualified_signing_users',
                ['account_service_id' => $accountService->id]
            );
            $response = $this->get($route);
            $response->assertStatus(Response::HTTP_OK);
            return collect(json_decode($response->getContent(), true))->pluck('user_id')->toArray();
        };

        $addQualifiedUsers = function (array $userIds) use ($manager, $accountService) {
            $route = $manager->getAccount()->route(
                'qualified_signing.add_qualified_signing_users',
                [
                    'account_service_id' => $accountService->id,
                    'users_ids' => $userIds
                ]
            );
            $response = $this->post($route);
            $response->assertStatus(Response::HTTP_OK);
            return $response;
        };

        $initialUsers = [$user1Id, $user2Id];
        $addQualifiedUsers($initialUsers);
        $currentUsers = $getQualifiedUsers();
        $this->assertCount(2, $currentUsers);
        $this->assertContains($user1Id, $currentUsers);
        $this->assertContains($user2Id, $currentUsers);

        $expandedUsers = [$user1Id, $user2Id, $user3Id];
        $addQualifiedUsers($expandedUsers);
        $currentUsers = $getQualifiedUsers();
        $this->assertCount(3, $currentUsers);
        $this->assertContains($user1Id, $currentUsers);
        $this->assertContains($user2Id, $currentUsers);
        $this->assertContains($user3Id, $currentUsers);

        $reducedUsers = [$user2Id, $user3Id];
        $addQualifiedUsers($reducedUsers);
        $currentUsers = $getQualifiedUsers();
        $this->assertCount(2, $currentUsers);
        $this->assertNotContains($user1Id, $currentUsers); // user1 should be removed
        $this->assertContains($user2Id, $currentUsers);
        $this->assertContains($user3Id, $currentUsers);

        $replacementUsers = [$user4Id];
        $addQualifiedUsers($replacementUsers);
        $currentUsers = $getQualifiedUsers();
        $this->assertCount(1, $currentUsers);
        $this->assertNotContains($user2Id, $currentUsers); // user2 should be removed
        $this->assertNotContains($user3Id, $currentUsers); // user3 should be removed
        $this->assertContains($user4Id, $currentUsers);

        $addQualifiedUsers([]);
        $currentUsers = $getQualifiedUsers();
        $this->assertCount(0, $currentUsers);

        $finalUsers = [$user1Id, $user4Id];
        $addQualifiedUsers($finalUsers);
        $currentUsers = $getQualifiedUsers();
        $this->assertCount(2, $currentUsers);
        $this->assertContains($user1Id, $currentUsers);
        $this->assertContains($user4Id, $currentUsers);
    }

    public function testAddQualifiedSigningUsersPreventsDuplicates()
    {
        $manager = UserFactory::createManager();
        $accountService = AccountServiceFactory::createQualifiedSigning($manager->account);
        $this->be($manager);

        $user1Id = UserFactory::createColleague()->id;
        $user2Id = UserFactory::createColleague()->id;

        $getQualifiedUsers = function () use ($manager, $accountService) {
            $route = $manager->getAccount()->route(
                'qualified_signing.qualified_signing_users',
                ['account_service_id' => $accountService->id]
            );
            $response = $this->get($route);
            $response->assertStatus(Response::HTTP_OK);
            return collect(json_decode($response->getContent(), true))->pluck('user_id')->toArray();
        };

        $addQualifiedUsers = function (array $userIds) use ($manager, $accountService) {
            $route = $manager->getAccount()->route(
                'qualified_signing.add_qualified_signing_users',
                [
                    'account_service_id' => $accountService->id
                ]
            );
            $response = $this->post($route, ['users_ids' => $userIds]);
            $response->assertStatus(Response::HTTP_OK);
            return $response;
        };

        $addQualifiedUsers([$user1Id, $user2Id]);
        $this->assertCount(2, $getQualifiedUsers());

        $addQualifiedUsers([$user1Id, $user2Id]);
        $currentUsers = $getQualifiedUsers();
        $this->assertCount(2, $currentUsers);
        $this->assertContains($user1Id, $currentUsers);
        $this->assertContains($user2Id, $currentUsers);

        $user3Id = UserFactory::createColleague()->id;
        $addQualifiedUsers([$user1Id, $user3Id]);
        $currentUsers = $getQualifiedUsers();
        $this->assertCount(2, $currentUsers);
        $this->assertContains($user1Id, $currentUsers);
        $this->assertContains($user3Id, $currentUsers);
        $this->assertNotContains($user2Id, $currentUsers);
    }

    public function testFailedAddQualifiedSigningUsers()
    {
        $manager = UserFactory::createManager();
        $accountService = AccountServiceFactory::createQualifiedSigning($manager->account);

        $this->be($manager);

        $route = $manager->getAccount()->route(
            'qualified_signing.add_qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
                'users_ids' => 'xxx'
            ]
        );
        $response = $this->post($route);

        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    public function testUnauthorizedColleagueAddQualifiedSigningUsers()
    {
        $user = UserFactory::createColleague();
        $accountService = AccountServiceFactory::createQualifiedSigning($user->account);

        $this->be($user);

        $route = $user->getAccount()->route(
            'qualified_signing.add_qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
            ]
        );
        $response = $this->get($route);

        $response->assertStatus(Response::HTTP_FORBIDDEN);
    }

    public function testUnauthorizedClientAddQualifiedSigningUsers()
    {
        $user = UserFactory::createClientUser();
        $accountService = AccountServiceFactory::createQualifiedSigning($user->account);

        $this->be($user);

        $route = $user->getAccount()->route(
            'qualified_signing.add_qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
            ]
        );
        $response = $this->get($route);

        $response->assertStatus(Response::HTTP_FORBIDDEN);
    }

    public function testRemoveQualifiedSigningUsers()
    {
        $manager = UserFactory::createManager();
        $accountService = AccountServiceFactory::createQualifiedSigning($manager->account);
        $this->be($manager);

        $user = UserFactory::createColleague();

        $route = $manager->getAccount()->route(
            'qualified_signing.add_qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
                'users_ids' => [
                    $user->id
                ]
            ]
        );

        $response = $this->post($route);
        $response->assertStatus(Response::HTTP_OK);

        $route = $manager->getAccount()->route(
            'qualified_signing.qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
            ]
        );
        $response = $this->get($route);
        $content = json_decode($response->getContent(), true);

        $route = $manager->getAccount()->route(
            'qualified_signing.remove_qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
                'user_id' => $content[0]['id']
            ]
        );
        $response = $this->delete($route);
        $response->assertStatus(Response::HTTP_OK);

        $route = $manager->getAccount()->route(
            'qualified_signing.qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
            ]
        );
        $response = $this->get($route);
        $response->assertStatus(Response::HTTP_OK);

        $content = json_decode($response->getContent(), true);
        $this->assertCount(0, $content);
    }

    public function testFailedRemoveQualifiedSigningUsers()
    {
        $user = UserFactory::createManager();
        $accountService = AccountServiceFactory::createQualifiedSigning($user->account);

        $this->be($user);

        $route = $user->getAccount()->route(
            'qualified_signing.remove_qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
            ]
        );
        $response = $this->delete($route);

        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    public function testUnauthorizedRemoveQualifiedSigningUsers()
    {
        $user = UserFactory::createClientUser();
        $accountService = AccountServiceFactory::createQualifiedSigning($user->account);

        $this->be($user);

        $route = $user->getAccount()->route(
            'qualified_signing.remove_qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
            ]
        );
        $response = $this->get($route);

        $response->assertStatus(Response::HTTP_FORBIDDEN);
    }

    public function testPreconditionFailedForAddingClientUserAsQualifiedSigner()
    {
        $manager = UserFactory::createManager();
        $accountService = AccountServiceFactory::createQualifiedSigning($manager->account);
        $this->be($manager);

        $userIds = [
            UserFactory::createClientUser()->id,
        ];

        $route = $manager->getAccount()->route(
            'qualified_signing.add_qualified_signing_users',
            [
                'account_service_id' => $accountService->id,
                'users_ids' => $userIds
            ]
        );

        $response = $this->post($route);
        $response->assertStatus(Response::HTTP_PRECONDITION_FAILED);
    }
}
