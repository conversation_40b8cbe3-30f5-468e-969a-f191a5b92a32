<?php

namespace Tests\Feature\Services\Dms;

use App\Exceptions\Services\Dms\Afas\ClientIdDoesNotExistException;
use App\Http\Responses\Response;
use App\Repositories\Http\Services\Dms\AfasDmsRepository;
use App\Services\Dms\DmsService;
use App\Services\Service\Dms\AfasDmsService;
use App\Services\ServiceTaskService;
use App\ValueObject\ServiceTask\ServiceTaskPreferences;
use GuzzleHttp\Client;
use Tests\Support\Builders\ServiceTaskBuilder;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class AfasDmsTest extends TestCase
{
    public function testGetConnectors()
    {
        // Test setup
        $accountService = AccountServiceFactory::createAfasDms();
        $company = CompanyFactory::create($accountService->account);
        $serviceTask = ServiceTaskBuilder::new()->setCompany($company)->setAccountService($accountService)->setVat()->build();
        $user = UserFactory::createColleague($serviceTask->account);
        CompanyUserFactory::create($serviceTask->company, $user);

        // Authenticate user
        $this->be($user);

        // Perform test
        $response = $this->post(
            $accountService->account->route('afas_dms.get_connectors_for_task', [$accountService->id]),
            ['task_id' => $serviceTask->id]
        );

        // Make assertions
        $this->assertEquals(Response::HTTP_OK, $response->status());
    }

    public function testGetConnectorsWithMissingPreferences()
    {
        // Test setup
        $accountService = AccountServiceFactory::createAfasDms();
        // Remove account service preferences
        $accountService->update(['properties' => []]);
        $company = CompanyFactory::create($accountService->account);
        $serviceTask = ServiceTaskBuilder::new()->setCompany($company)->setAccountService($accountService)->setVat()->build();
        $user = UserFactory::createColleague($serviceTask->account);
        CompanyUserFactory::create($serviceTask->company, $user);

        // Authenticate user
        $this->be($user);

        // Perform test
        $response = $this->post(
            $accountService->account->route('afas_dms.get_connectors_for_task', [$accountService->id]),
            ['task_id' => $serviceTask->id]
        );

        // Make assertions
        $this->assertEquals(Response::HTTP_PRECONDITION_FAILED, $response->status());
    }

    public function testGetConnectorsWithWrongConfiguration()
    {
        // Test setup
        $accountService = AccountServiceFactory::createAfasDms();
        // Use invalid Client ID
        $accountService->setProperty(AfasDmsService::PROPERTY_CLIENT_ID, $this->faker->randomNumber(3));
        $accountService->save();
        $company = CompanyFactory::create($accountService->account);
        $serviceTask = ServiceTaskBuilder::new()->setCompany($company)->setAccountService($accountService)->setVat()->build();
        $user = UserFactory::createColleague($serviceTask->account);
        CompanyUserFactory::create($serviceTask->company, $user);

        // Authenticate user
        $this->be($user);

        // Perform test
        $response = $this->post(
            $accountService->account->route('afas_dms.get_connectors_for_task', [$accountService->id]),
            ['task_id' => $serviceTask->id]
        );

        // Make assertions
        $this->assertEquals(Response::HTTP_PRECONDITION_FAILED, $response->status());
        $this->assertInstanceOf(ClientIdDoesNotExistException::class, $response->exception);
    }

    public function testGetConnectorsUnauthorized()
    {
        // Test setup
        $accountService = AccountServiceFactory::createAfasDms();
        $company = CompanyFactory::create($accountService->account);
        $serviceTask = ServiceTaskBuilder::new()->setCompany($company)->setAccountService($accountService)->setVat()->build();
        $user = UserFactory::createColleague($serviceTask->account);

        // Authenticate user without company triggering unauthorized exception
        $this->be($user);

        // Perform test
        $response = $this->post(
            $accountService->account->route('afas_dms.get_connectors_for_task', [$accountService->id]),
            ['task_id' => $serviceTask->id]
        );

        // Make assertions
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->status());
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    public function testSendDocuments()
    {
        $client = resolve(Client::class);
        $mock = \Mockery::mock(AfasDmsRepository::class . '[updateConnectors]', [$client]);
        $mock->makePartial();
        $mock->shouldReceive('updateConnectors')->once()->andReturns();
        $this->app->instance('App\Repositories\Http\Services\Dms\AfasDmsRepository', $mock);
        // Test setup
        $afasDmsService = \App::make(DmsService::class);

        $accountService = AccountServiceFactory::createAfasDms();
        $company = CompanyFactory::create($accountService->account);
        $serviceTask = ServiceTaskBuilder::new()->setCompany($company)->setAccountService($accountService)->setVat()->build();
        $user = UserFactory::createColleague($serviceTask->account);
        CompanyUserFactory::create($serviceTask->company, $user);

        // Authenticate user
        $this->be($user);

        // Get connectors
        $response = $this->post(
            $accountService->account->route('afas_dms.get_connectors_for_task', [$accountService->id]),
            ['task_id' => $serviceTask->id]
        );
        $connectorsList = json_decode($response->getContent(), 1, JSON_THROW_ON_ERROR);

        // Set task property
        $serviceTaskService = \App::make(ServiceTaskService::class);
        $serviceTaskPreferences = new ServiceTaskPreferences(
            [
                'afas_destination_id' => $connectorsList[$this->faker->numberBetween(0, count($connectorsList) - 1)]['id'],
                'afas_characteristic1' => 123
            ]
        );
        $serviceTaskService->updatePreferences($serviceTaskPreferences, $serviceTask);

        // Perform test
        $afasDmsService->sendServiceTaskDocuments($serviceTask);
    }

    public function testGetDossierCharacteristics()
    {
        // Test setup
        $accountService = AccountServiceFactory::createAfasDms();
        $user = UserFactory::createColleague($accountService->account);

        // Authenticate user
        $this->be($user);

        // Perform test
        $response = $this->post(
            $accountService->account->route(
                'afas_dms.get_dossier_characteristics',
                //phpcs:ignore
                [$accountService->id]
            ),
            [
                'dossier_type' => $accountService->properties[AfasDmsService::PROPERTY_PREFERENCES][0][AfasDmsService::PROPERTY_DOSSIER_ITEM] //phpcs:ignore
            ]
        );

        // Make assertions
        $response->assertOk();
        $response->assertJsonStructure(
            [
                'characteristic1',
                'characteristic2',
                'characteristic3'
            ]
        );
        $this->assertGreaterThan(0, $response->json('characteristic1'));
    }
}
