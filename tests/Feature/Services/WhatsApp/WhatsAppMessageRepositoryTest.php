<?php

namespace Tests\Feature\Services\WhatsApp;

use App\Models\Mongo\WhatsApp\WhatsAppMessage;
use App\Repositories\WhatsApp\WhatsAppMessageRepository;
use App\User;
use App\ValueObject\Services\WhatsApp\Message\Message;
use App\ValueObject\Services\WhatsApp\Message\types\Audio;
use App\ValueObject\Services\WhatsApp\Message\types\Document;
use App\ValueObject\Services\WhatsApp\Message\types\Image;
use App\ValueObject\Services\WhatsApp\Message\types\MessageContent;
use App\ValueObject\Services\WhatsApp\Message\types\Template;
use App\ValueObject\Services\WhatsApp\Message\types\Text;
use App\ValueObject\Services\WhatsApp\Message\types\Unknown;
use App\ValueObject\Services\WhatsApp\Message\types\UnsupportedType;
use Mockery;
use Tests\Support\Factories\UserFactory;
use Tests\Support\MongoTestCase;

class WhatsAppMessageRepositoryTest extends MongoTestCase
{
    protected WhatsAppMessageRepository $whatsAppMessageRepository;

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     * @throws \Throwable
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->whatsAppMessageRepository = Mockery::mock(WhatsAppMessageRepository::class);
        $this->whatsAppMessageRepository->makePartial();
        $this->whatsAppMessageRepository->shouldAllowMockingProtectedMethods();
        $this->whatsAppMessageRepository->shouldReceive('getDatabaseName')->andReturn('test_account'); // phpcs:ignore
    }

    public function testCreateWhatsAppMessage(): void
    {
        $user = UserFactory::createClientUser($this->account);

        $whatsAppMessage = $this->whatsAppMessageRepository->create(
            $user->getAccount(),
            $this->getMessageAttributes($user)
        );

        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);

        $whatsAppMessage = $this->whatsAppMessageRepository->getMessageByWhatsAppMessageId(
            $user,
            $whatsAppMessage->getWhatsAppMessageId()
        );

        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);
        $this->assertTrue($whatsAppMessage->sent_by_service);
    }

    public function testCreateWhatsAppMessageWithContext(): void
    {
        $user = UserFactory::createClientUser($this->account);
        $user->mobile = '+*********';
        $user->save();

        $whatsAppMessage = $this->whatsAppMessageRepository->create(
            $user->getAccount(),
            $this->getMessageAttributes($user)
        );

        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);

        $whatsAppMessage = $this->whatsAppMessageRepository->getMessageByWhatsAppMessageId(
            $user,
            $whatsAppMessage->getWhatsAppMessageId()
        );

        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);
        $this->assertInstanceOf(Message::class, $whatsAppMessage->getRepliedMessage());
        $this->assertTrue($whatsAppMessage->sent_by_service);
    }

    public function testWhatsAppMessagesFromUser(): void
    {
        $user = UserFactory::createClientUser($this->account);

        $whatsAppMessage = $this->whatsAppMessageRepository->create(
            $user->getAccount(),
            $this->getMessageAttributes($user)
        );
        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);

        $whatsAppMessage = $this->whatsAppMessageRepository->create(
            $user->getAccount(),
            $this->getMessageAttributes($user)
        );
        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);

        $whatsAppMessages = $this->whatsAppMessageRepository->getMessagesByUser(
            $user
        );

        $this->assertCount(2, $whatsAppMessages);
        $this->assertEquals($user->id, $whatsAppMessage->user->id);
    }

    public function testUpdateWhatsAppMessageStatus(): void
    {
        $user = UserFactory::createClientUser($this->account);

        $whatsAppMessage = $this->whatsAppMessageRepository->create(
            $user->getAccount(),
            $this->getMessageAttributes($user)
        );

        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);

        $updateStatus = $this->whatsAppMessageRepository->updateMessageStatus(
            $user,
            $whatsAppMessage->getWhatsAppMessageId(),
            WhatsAppMessage::STATUS_READ
        );

        $this->assertEquals(true, $updateStatus);

        $whatsAppMessage = $this->whatsAppMessageRepository->getMessageByWhatsAppMessageId(
            $user,
            $whatsAppMessage->getWhatsAppMessageId()
        );

        $this->assertEquals(WhatsAppMessage::STATUS_READ, $whatsAppMessage->status);
    }

    public function testDeleteWhatsAppMessage(): void
    {
        $user = UserFactory::createClientUser($this->account);

        $whatsAppMessage = $this->whatsAppMessageRepository->create(
            $user->getAccount(),
            $this->getMessageAttributes($user)
        );

        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);

        $delete = $this->whatsAppMessageRepository->delete(
            $user,
            $whatsAppMessage->getWhatsAppMessageId()
        );

        $this->assertTrue($delete);

        $whatsAppMessage = $this->whatsAppMessageRepository->getMessageByWhatsAppMessageId(
            $user,
            $whatsAppMessage->getWhatsAppMessageId()
        );

        $this->assertNull($whatsAppMessage);
    }

    public function testWhatsAppMessageDecrypt(): void
    {
        $user = UserFactory::createClientUser($this->account);

        $whatsAppMessage = $this->whatsAppMessageRepository->create(
            $user->getAccount(),
            $this->getMessageAttributes($user)
        );

        $this->assertInstanceOf(WhatsAppMessage::class, $whatsAppMessage);

        $whatsAppMessage = $this->whatsAppMessageRepository->getMessageByWhatsAppMessageId(
            $user,
            $whatsAppMessage->getWhatsAppMessageId()
        );

        $this->assertInstanceOf(Message::class, $whatsAppMessage->getMessage());
    }

    public function testGetMessagesFromUser(): void
    {
        $user = UserFactory::createColleague($this->account);
        $this->be($user);

        $response = $this->get($user->account->route('whatsapp_business.get.user.messages', [
            'user_id' => $user,
            'page' => 1,
            'limit' => 10
        ]));

        $this->assertCount(0, json_decode($response->getContent()));

        // Add 2 messages
        $this->whatsAppMessageRepository->create(
            $user->getAccount(),
            $this->getMessageAttributes($user)
        );
        $this->whatsAppMessageRepository->create(
            $user->getAccount(),
            $this->getMessageAttributes($user)
        );

        $response = $this->get($user->account->route('whatsapp_business.get.user.messages', [
            'user_id' => $user,
            'page' => 1,
            'limit' => 10
        ]));

        $this->assertCount(2, json_decode($response->getContent()));
    }

    public function testTextMessageType()
    {
        $text = new Text(
            [
                Text::TYPE => [
                    Text::BODY => 'hello im a body'
                ],
                MessageContent::CONTEXT => [
                    MessageContent::FROM => '*********',
                    MessageContent::ID => 'wamid.************************************************************',
                ]
            ],
        );
        $this->assertEquals('hello im a body', $text->getBody());
        $this->assertIsArray($text->getContext());
        $this->assertArrayHasKey(Text::BODY, $text->getContent());
        $this->assertArrayHasKey(MessageContent::CONTEXT, $text->getContent());

        $text = new Text(
            [
                Text::TYPE => [
                    Text::BODY => 'hello im a body'
                ]
            ],
        );
        $this->assertEquals('hello im a body', $text->getBody());
        $this->assertIsArray($text->getContext());
        $this->assertArrayHasKey(Text::BODY, $text->getContent());
        $this->assertArrayNotHasKey(MessageContent::CONTEXT, $text->getContent());
    }

    public function testAudioMessageType()
    {
        $audio = new Audio([
            Audio::TYPE => [
                Audio::ID => '5852828181484136',
                Audio::MIME_TYPE => 'audio/ogg; codecs=opus',
                Audio::VOICE => true,
            ],
            MessageContent::CONTEXT => [
                MessageContent::FROM => '*********',
                MessageContent::ID => 'wamid.************************************************************',
            ]
        ]);
        $this->assertEquals('5852828181484136', $audio->getId());
        $this->assertEquals('audio/ogg; codecs=opus', $audio->getMimeType());
        $this->assertTrue($audio->getVoice());
        $this->assertArrayHasKey(Audio::ID, $audio->getContent());
        $this->assertArrayHasKey(Audio::MIME_TYPE, $audio->getContent());
        $this->assertArrayHasKey(Audio::VOICE, $audio->getContent());
        $this->assertArrayHasKey(MessageContent::FROM, $audio->getContext());
        $this->assertArrayHasKey(MessageContent::ID, $audio->getContext());

        $audio = new Audio([
            Audio::TYPE => [
                Audio::ID => '5852828181484136',
                Audio::MIME_TYPE => 'audio/ogg; codecs=opus',
                Audio::VOICE => false,
            ]
        ]);
        $this->assertEquals('5852828181484136', $audio->getId());
        $this->assertEquals('audio/ogg; codecs=opus', $audio->getMimeType());
        $this->assertFalse($audio->getVoice());
        $this->assertArrayNotHasKey(MessageContent::CONTEXT, $audio->getContent());
    }

    public function testDocumentMessageType()
    {
        $document = new Document([
            Document::TYPE => [
                Document::ID => '279797131467494',
                Document::MIME_TYPE => 'image/jpeg',
                Document::FILENAME => '57267_3.jpeg'
            ],
            MessageContent::CONTEXT => [
                MessageContent::FROM => '*********',
                MessageContent::ID => 'wamid.************************************************************',
            ]
        ]);

        $this->assertEquals('279797131467494', $document->getId());
        $this->assertEquals('image/jpeg', $document->getMimeType());
        $this->assertEquals('57267_3.jpeg', $document->getFilename());
        $this->assertArrayHasKey(Document::ID, $document->getContent());
        $this->assertArrayHasKey(Document::MIME_TYPE, $document->getContent());
        $this->assertArrayHasKey(Document::FILENAME, $document->getContent());
        $this->assertArrayHasKey(Document::LOGO, $document->getContent());
        $this->assertArrayHasKey(MessageContent::FROM, $document->getContext());
        $this->assertArrayHasKey(MessageContent::ID, $document->getContext());

        $document = new Document([
            Document::TYPE => [
                Document::ID => '279797131467494',
                Document::MIME_TYPE => 'image/jpeg',
                Document::FILENAME => '57267_3.jpeg'
            ]
        ]);

        $this->assertArrayNotHasKey(MessageContent::CONTEXT, $document->getContent());
    }

    public function testImageMessageType()
    {
        $image = new Image([
            Image::TYPE => [
                Image::ID => '279797131467494',
                Image::MIME_TYPE => 'image/jpeg',
                Image::CAPTION => 'hellloooo'
            ],
            MessageContent::CONTEXT => [
                MessageContent::FROM => '*********',
                MessageContent::ID => 'wamid.************************************************************',
            ]
        ]);

        $this->assertEquals('279797131467494', $image->getId());
        $this->assertEquals('image/jpeg', $image->getMimeType());
        $this->assertEquals('hellloooo', $image->getCaption());
        $this->assertArrayHasKey(Image::ID, $image->getContent());
        $this->assertArrayHasKey(Image::MIME_TYPE, $image->getContent());
        $this->assertArrayHasKey(Image::CAPTION, $image->getContent());
        $this->assertArrayHasKey(Image::LOGO, $image->getContent());
        $this->assertArrayHasKey(MessageContent::FROM, $image->getContext());
        $this->assertArrayHasKey(MessageContent::ID, $image->getContext());

        $image = new Image([
            Image::TYPE => [
                Image::ID => '279797131467494',
                Image::MIME_TYPE => 'image/jpeg'
            ]
        ]);

        $this->assertEquals('279797131467494', $image->getId());
        $this->assertEquals('image/jpeg', $image->getMimeType());
        $this->assertEquals(null, $image->getCaption());
        $this->assertArrayHasKey(Image::ID, $image->getContent());
        $this->assertArrayHasKey(Image::MIME_TYPE, $image->getContent());
        $this->assertArrayNotHasKey(Image::CAPTION, $image->getContent());
        $this->assertArrayHasKey(Image::LOGO, $image->getContent());
        $this->assertArrayNotHasKey(MessageContent::CONTEXT, $image->getContent());

    }

    public function testUnknownMessageType()
    {
        $unknown = new Unknown([
            Unknown::ERRORS => [
                [
                    Unknown::CODE => 131051,
                    Unknown::DETAILS => 'Message type is not currently supported',
                    Unknown::MESSAGE => 'Message type is not currently supported',
                    Unknown::TITLE => 'Unsupported message type'
                ]
            ]
        ]);
        $this->assertArrayHasKey(Unknown::ERRORS, $unknown->getContent());
        $this->assertArrayHasKey(Unknown::DETAILS, $unknown->getContent()[Unknown::ERRORS][0]);
        $this->assertArrayHasKey(Unknown::MESSAGE, $unknown->getContent()[Unknown::ERRORS][0]);

        $unknown = new Unknown([
            Unknown::ERRORS => [
                [
                    Unknown::CODE => 131051,
                    Unknown::TITLE => 'Unsupported message type'
                ]
            ]
        ]);
        $this->assertArrayHasKey(Unknown::ERRORS, $unknown->getContent());
        $this->assertArrayNotHasKey(Unknown::DETAILS, $unknown->getContent()[Unknown::ERRORS][0]);
        $this->assertArrayNotHasKey(Unknown::MESSAGE, $unknown->getContent()[Unknown::ERRORS][0]);

        $unknown = new Unknown([
            Unknown::ERRORS => [
                [
                    Unknown::CODE => 131051,
                    Unknown::TITLE => 'Unsupported message type',
                    Unknown::ERROR_DATA => [
                        Unknown::DETAILS => 'Message failed to send because of bla bla'
                    ]
                ]
            ]
        ]);

        $this->assertArrayHasKey(Unknown::ERRORS, $unknown->getContent());
        $this->assertArrayHasKey(Unknown::DETAILS, $unknown->getContent()[Unknown::ERRORS][0]);
        $this->assertEquals('Message failed to send because of bla bla', $unknown->getContent()[Unknown::ERRORS][0][Unknown::DETAILS]); // phpcs:ignore
    }

    public function testUnsupportedMessageType()
    {
        $unknown = new UnsupportedType();
        $this->assertArrayHasKey(UnsupportedType::BODY, $unknown->getContent());
    }

    public function testTemplateType()
    {
        $message = new Message([
            Message::FROM => '*********',
            Message::ID => 'wam_id',
            Message::TIMESTAMP => time(),
            Message::TYPE => WhatsAppMessage::TYPE_TEMPLATE,
            WhatsAppMessage::TYPE_TEMPLATE => [ // mocked data from external
                [
                    'type' => 'HEADER',
                    'format' => 'IMAGE',
                    'image' => [
                        'link' => 'https://www.image.com/image.png'
                    ]
                ],
                [
                    'type' => 'BODY',
                    'text' => 'hello',
                ]
            ]
        ]);

        $this->assertInstanceOf(Template::class, $message->getMessageContent());
        $this->assertArrayHasKey('header', $message->getMessageContent()->getContent());
        $this->assertArrayHasKey('body', $message->getMessageContent()->getContent());
    }

    /**
     * Message attributes to create message
     * @param User $user
     * @return array
     */
    protected function getMessageAttributes(User $user): array
    {
        return [
            WhatsAppMessage::USER_ID => $user->id,
            WhatsAppMessage::WA_ID => $user->getFilteredMobile(),
            WhatsAppMessage::WAM_ID => 'wamid.HBgMMzUxOTEzMjk3MjA5FQIAEhgWM0VCMDMxREFEOTk1NDgyRjg0MDYzRAA=',
            WhatsAppMessage::TYPE => WhatsAppMessage::TYPE_TEXT,
            WhatsAppMessage::MESSAGE => $this->getExampleMessage(),
            WhatsAppMessage::CREATED_BY => $user->id
        ];
    }


    /**
     * Message content example
     * @return array
     */
    protected function getExampleMessage(): array
    {
        return [
            'from' => '*********',
            'id' => 'wamid.HBgMMzUxOTEzMjk3MjA5FQIAEhgWM0VCMDMxREFEOTk1NDgyRjg0MDYzRAA=',
            'timestamp' => '1690210600',
            'text' => [
                'body' => 'hello world'
            ],
            'type' => 'text',
            'context' => [ // replied itself
                'from' => '*********',
                'id' => 'wamid.HBgMMzUxOTEzMjk3MjA5FQIAEhgWM0VCMDMxREFEOTk1NDgyRjg0MDYzRAA=',
            ]
        ];
    }
}
