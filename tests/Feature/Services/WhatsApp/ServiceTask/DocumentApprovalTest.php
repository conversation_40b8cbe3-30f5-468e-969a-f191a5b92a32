<?php

namespace Tests\Feature\Services\WhatsApp\ServiceTask;

use App;
use App\Mail\ServiceTask\ServiceTaskNotifierFactory;
use App\Mail\ServiceTask\ServiceTaskReminderNotifierFactory;
use App\Notifier\Objects\ServiceTask\ServiceTaskNotifierService;
use App\Notifier\Objects\ServiceTask\Types\DocumentApproval\DocumentApprovalForcedNotifier;
use App\Notifier\Objects\ServiceTask\Types\DocumentApproval\DocumentApprovalNotifier;
use App\Notifier\Objects\ServiceTask\Types\DocumentApproval\DocumentApprovalReminderNotifier;
use App\Notifier\Objects\ServiceTask\Types\DocumentApproval\DocumentApprovedNotifier;
use App\ServiceTask;
use App\ServiceTaskResponse;
use App\WhatsApp\ServiceTask\ForceApprovalWhatsApp;
use App\WhatsApp\ServiceTask\ReminderWhatsApp;
use App\WhatsApp\ServiceTask\TaskCompletedWhatsApp;
use App\WhatsApp\ServiceTask\TaskSentWhatsApp;
use JsonException;
use Tests\Feature\Services\WhatsApp\WhatsAppTestTrait;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\ServiceTaskFactory;
use Tests\Support\Factories\ServiceTaskResponseFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\MongoTestCase;
use Throwable;

class DocumentApprovalTest extends MongoTestCase
{
    use WhatsAppTestTrait;

    protected function setUp(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');
        parent::setUp();
        $this->handleMockCloudApiService();
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     * @throws JsonException|Throwable
     */
    public function testSendDocumentApprovalToWhatsApp(): void
    {
        $user = UserFactory::createClientUser($this->account);
        $company = CompanyFactory::create($this->account);
        $accountService = AccountServiceFactory::createManualTasks($this->account);
        $serviceTask = ServiceTaskFactory::createDocumentApproval($accountService, $company);
        $serviceTask->status = ServiceTask::STATUS_SENT;
        $serviceTask->save();
        $serviceTaskResponse = ServiceTaskResponseFactory::create($serviceTask, $user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_APPROVE;
        $serviceTaskResponse->save();

        $notifier = ServiceTaskNotifierFactory::build($serviceTask, $serviceTaskResponse);
        $this->assertInstanceOf(DocumentApprovalNotifier::class, $notifier);
        $this->assertInstanceOf(TaskSentWhatsApp::class, $notifier->buildWhatsAppMessage());
        (App::make(ServiceTaskNotifierService::class))->send(['whatsapp'], $notifier, $serviceTaskResponse);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     * @throws JsonException|Throwable
     */
    public function testSendDocumentInformToWhatsApp(): void
    {
        $user = UserFactory::createClientUser($this->account);
        $company = CompanyFactory::create($this->account);
        $accountService = AccountServiceFactory::createManualTasks($this->account);
        $serviceTask = ServiceTaskFactory::createDocumentApproval($accountService, $company);
        $serviceTask->status = ServiceTask::STATUS_COMPLETED;
        $serviceTask->save();
        $serviceTaskResponse = ServiceTaskResponseFactory::create($serviceTask, $user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_INFORM;
        $serviceTaskResponse->save();

        $notifier = ServiceTaskNotifierFactory::build($serviceTask, $serviceTaskResponse);
        $this->assertInstanceOf(DocumentApprovalNotifier::class, $notifier);
        $this->assertInstanceOf(TaskSentWhatsApp::class, $notifier->buildWhatsAppMessage());
        (App::make(ServiceTaskNotifierService::class))->send(['whatsapp'], $notifier, $serviceTaskResponse);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     * @throws JsonException|Throwable
     */
    public function testSendDocumentForceApprovalToWhatsApp(): void
    {
        $user = UserFactory::createClientUser($this->account);
        $company = CompanyFactory::create($this->account);
        $accountService = AccountServiceFactory::createManualTasks($this->account);
        $serviceTask = ServiceTaskFactory::createDocumentApproval($accountService, $company);
        $serviceTask->status = ServiceTask::STATUS_SENT;
        $serviceTask->save();
        // Do the setApprove as colleague to trigger force approve;
        $colleague = UserFactory::createColleague($this->account);
        $this->be($colleague);

        $serviceTaskResponse = ServiceTaskResponseFactory::create($serviceTask, $user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_APPROVE;
        $serviceTaskResponse->setApproved();
        $serviceTaskResponse->save();

        $notifier = ServiceTaskNotifierFactory::build($serviceTask, $serviceTaskResponse);
        $this->assertInstanceOf(DocumentApprovalForcedNotifier::class, $notifier);
        $this->assertInstanceOf(ForceApprovalWhatsApp::class, $notifier->buildWhatsAppMessage());
        (App::make(ServiceTaskNotifierService::class))->send(['whatsapp'], $notifier, $serviceTaskResponse);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     * @throws JsonException|Throwable
     */
    public function testSendDocumentApprovedToWhatsApp(): void
    {
        $user = UserFactory::createClientUser($this->account);
        $company = CompanyFactory::create($this->account);
        $accountService = AccountServiceFactory::createManualTasks($this->account);
        $serviceTask = ServiceTaskFactory::createDocumentApproval($accountService, $company);
        $serviceTask->status = ServiceTask::STATUS_COMPLETED;
        $serviceTask->save();
        $serviceTaskResponse = ServiceTaskResponseFactory::create($serviceTask, $user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_APPROVE;
        $serviceTaskResponse->save();

        $notifier = ServiceTaskNotifierFactory::build($serviceTask, $serviceTaskResponse);
        $this->assertInstanceOf(DocumentApprovedNotifier::class, $notifier);
        $this->assertInstanceOf(TaskCompletedWhatsApp::class, $notifier->buildWhatsAppMessage());
        (App::make(ServiceTaskNotifierService::class))->send(['whatsapp'], $notifier, $serviceTaskResponse);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     * @throws JsonException|Throwable
     */
    public function testSendDocumentApprovalReminderToWhatsApp(): void
    {
        $user = UserFactory::createClientUser($this->account);
        $company = CompanyFactory::create($this->account);
        $accountService = AccountServiceFactory::createManualTasks($this->account);
        $serviceTask = ServiceTaskFactory::createDocumentApproval($accountService, $company);
        $serviceTask->status = ServiceTask::STATUS_SENT;
        $serviceTask->save();
        $serviceTaskResponse = ServiceTaskResponseFactory::create($serviceTask, $user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_APPROVE;
        $serviceTaskResponse->save();

        $notifier = ServiceTaskReminderNotifierFactory::create($serviceTaskResponse);
        $this->assertInstanceOf(DocumentApprovalReminderNotifier::class, $notifier);
        $this->assertInstanceOf(ReminderWhatsApp::class, $notifier->buildWhatsAppMessage());
        (App::make(ServiceTaskNotifierService::class))->send(['whatsapp'], $notifier, $serviceTaskResponse);
    }
}
