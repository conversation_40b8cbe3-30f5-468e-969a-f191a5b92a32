<?php

namespace Tests\Feature\User;

use Symfony\Component\HttpFoundation\Response;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class UserSignatureTest extends TestCase
{
    private const SIGNATURE_STRING_PNG = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAA1EAAAFTCAYAAADY0oaIAAAgAElEQVR4XuydBZTexNfGB+ru7k5daQtFikuB4u7wx929aCla3N2huLtD3Z0adXenBb77pF+WbPq+b2biu/vcc/YsdCfJ5JfJZJ6Ze+/s8K+YopEACZAACZAACZAACZAACZAACWgR2IEiSosTC5EACZAACZAACZAACZAACZCARYAiig2BBEiABEiABEiABEiABEiABAwIUEQZwGJREiABEiABEiABEiABEiABEqCIYhsgARIgARIgARIgARIgARIgAQMCFFEGsFiUBEiABEiABEiABEiABEiABCii2AZIgARIgARIgARIgARIgARIwIAARZQBLBYlARIgARIgARIgARIgARIgAYootgESIAESIAESIAESIAESIAESMCBAEWUAi0VJgARIgARIgARIgARIgARIgCKKbYAESIAESIAESIAESIAESIAEDAhQRBnAYlESIAESIAESIAESIAESIAESoIhiGyABEiABEiABEiABEiABEiABAwIUUQawWJQESIAESIAESIAESIAESIAEKKLYBkiABEiABEiABEiABEiABEjAgABFlAEsFiUBEiABEiABEiABEiABEiABiii2ARIgARIgARIgARIgARIgARIwIEARZQCLRUmABEiABEiABEiABEiABEiAIoptgARIgARIgARIgARIgARIgAQMCFBEGcBiURIgARIgARIgARIgARIgARKgiGIbIAESIAESIAESIAESIAESIAEDAhRRBrBYlARIgARIgARIgARIgARIgAQootgGSIAESIAESIAESIAESIAESMCAAEWUASwWJQESIAESIAESIAESIAESIAGKKLYBEiABEiABEiABEiABEiABEjAgQBFlAItFSYAESIAESIAESIAESIAESIAiim2ABEiABEiABEiABEiABEiABAwIUEQZwGJREiABEiABEiABEiABEiABEqCIYhsgARIgARIgARIgARIgARIgAQMCFFEGsFiUBEiABEiABEiABEiABEiABCii2AZIgARIgARIgARIgARIgARIwIAARZQBLBYlARIgARIgARIgARIgARIgAYootgESIAESIAESIAESIAESIAESMCBAEWUAi0VJgARIgARIgARIgARIgARIgCKKbYAESIAESIAESIAESIAESIAEDAhQRBnAYlESIAESIAESIAESIAESIAESoIhiGyABEiABEiABEiABEiABEiABAwIUUQawWJQESIAESIAESIAESIAESIAEKKLYBkiABEiABEiABEiABEiABEjAgABFlAEsFiUBEiABEiABEiABEiABEiABiii2ARIgARIgARIgARIgARIgARIwIEARZQCLRUmABEiABEiABEiABEiABEiAIoptgARIgARIgARIgARIgARIgAQMCFBEGcBiURIgARIgARIgARIgARIgARKgiGIbIAESIAESIAESIAESIAESIAEDAhRRBrBYlARIgARIgARIgARIgARIgAQootgGSIAESIAESIAESIAESIAESMCAAEWUASwWJQESIAESIAESIAESIAESIAGKKLYBEiABEiABEiABEiABEiABEjAgQBFlAItFSYAESIAESIAESIAESIAESIAiim2ABEiABEiABEiABEiABEiABAwIUEQZwGJREiABEiABEiABEiABEiABEqCIYhsgARIgARIgARIgARIgARIgAQMCFFEGsFiUBEiABEiABEiABEiABEiABCii2AZIgARIgARIgARIgARIgARIwIAARZQBLBYlARIgARIgARIgARIgARIgAYootgESIAESIAESIAESIAESIAESMCBAEWUAi0VJgARIgARIgARIgARIgARIgCKKbYAESIAESIAESIAESIAESIAEDAhQRBnAYlESIAESIAESIAESIAESIAESoIhiGyABEiABEiABEiABEiABEiABAwIUUQawWJQESIAESIAESIAESIAESIAEKKLYBkiABEiABEiABEiABEiABEjAgABFlAEsFiUBEiABEiABEiABEiABEiABiii2ARIgARIgARIgARIgARIgARIwIEARZQCLRUmABEiABEiABEiABEiABEiAIoptgARIgARIgARIgARIgARIgAQMCFBEGcBiURIgARIgARIgARIgARIgARKgiGIbIAESIAESIAESIAESIAESIAEDAhRRBrBYlARIgARIgARIgARIgARIgAQootgGSIAESIAESIAESIAESIAESMCAAEWUASwWJQESIAESIAESIAESIAESIAGKKLYBEiABEiABEiABEiABEiABEjAgQBFlAItFSYAESIAESIAESIAESIAESIAiim2ABEiABEiABEiABEiABEiABAwIUEQZwGJREiABEiABEiABEiABEiABEghNRP39j1ITF21Qw+asU+VL7ag61i2nWtYoo4rtSMhpJsDnluanw7qRAAmQAAmQAAmQAAmkkUAoIurx3xap6z6drdb9JUrKYeVL7qjuOrihuni3OmqHHdJ4+0W7TnxuRfv58+5JgARIgARIgARIgAT8EQgkotZt/ked+fZ0NWjs8pxX37dFJfXGyS1VjfLF/dWSR4VKgM8tVJw8GQmQAAmQAAmQAAmQQBEjEEhEnf32DPX8sCVayI5oV1W9f0YrrbIsFC0BPrdo+fLsJEACJEACJEACJEAChZuAbxH104w1qvcTE43ovH1KS3Vsp2pGx7BwuAT43MLlybORAAmQAAmQAAmQAAkUPQK+RVS3gePUyHnrjYh1qltWjb6yo9ExLBwuAT63cHnybCRAAiRAAiRAAiRAAkWPgC8RtWnrP6r8dUPV3/+aASux4w5q3YAeqmQxZpkwIxdOaT63cDjyLCSQNIGXhi9VZ0k8ahRWvmQxNfzyDpJdtXQUp/c8Z8mrh8i3xfDj4nlW/QIVSxVTK+/qrn8AS5IACZAACRRJAr5EFFagsKLhx0Zf0UF1qlfOz6E8JiABPreAAHk4CaSEwIsSi3qmxKRGZZ2ljx58SXtVqnj8E17FrxpsPEEXJgeIqNX9KaLCZMpzkQAJkEBhJOBLRH0ycaU67IUpvnhMv6GzalYtmRlOXxUuRAfxuRWih8lbKdIEohZRgHtRr9rq0SObxM6ZIip25LwgCZAACZCADwK+RNTM5ZtVs/6jjC9XTvaNWtO/hxKvPloCBPjcEoDOS5JABATiEFGo9vunt1JHtK8awR1kPyVFVKy4eTESIAESIAGfBHyJKLirV7lpmFq96W+jy3ZvUF4Nvay90TEsHB4BPrfwWPJMJJAkgY1b/lETFm1QExdtVL/NWqM+mrBCLV2/NfQqVS1TXJIBdVANq5QK/dzZTjhrxWY1dclGubcNCtlEf5m5Rq0y/NaYVLZJ1VJq18YVrJ92dcqqJlVLqwaVS5qcgmVJgARIgASKIAFfIgqcLnhvpnry98VGyN48uYU6vnN1o2NYOFwCfG7h8uTZSCANBP7+R6mfRWy8OmKJen3UMvWXadafHDfRS8TFjxe2VcUTciH4RybtRs5bp94ft0I2dl+mZognRFDbo2lF1bddFXVY26qqeXW6lwflyeNJgARIoCgS8C2i1sjMYNt7x6h5q//S4rZvi0rqm/PaaJVloegI8LlFx5ZnJoE0EJgvffLAnxaqpwcvUuv+EnUVgl2/Tz3V/+CGIZwp+CmGz12n7v52vvpAVt9M7UhxTey3fwPVQbbboJEACZAACZBAEAK+RRQu+tXUVarv81PUZo9Zz7oVS6gfL2inWiSUMjcIoMJ4LJ9bYXyqhfuehs1Zp14buVTrJh86vAnjLoUUxNTJr09TP4pLXFDDItSX57RR+7WsFPRUoR0/ZPY6tf/TE9XazXpC8egO1dQ7p7ZUOzAmN7RnwBORAAmQQFEmEEhEAdyY+evVsa/8oaYt25SR4z6yAvWGuPHVLF+iKHNO3b3zuaXukbBCOQi8MmKpOu1NvX2RttzXMzHXs7Q9RLjC9f92nrr5y7mBq1a7Qgk1RjZLryW/02IfymrUkS9OVV67SnWUlaffL26vykpyIxoJkAAJkAAJhEEgsIhCJdbJTOBzQxerIbPXqhHialGpdHHVvWF51atJBXVilxqcFQ7jSUVwDj63CKDylJEQoIgKhvX6z+aoAd/PD3YSOXp/WYn6QlakEgqPylj/E1+bpt4cvSznvb10fHN12s41At8/T0ACJEACJEACNoFQRBRxkgAJkECUBCiigtHFitSej09Qv85aG+xEcjRioxAjlRb7fPIq1ee5yVmrU0zc9xbftrOqVq54WqrMepAACZAACRQCAhRRheAh8hZIoLAToIgK/oQRC3ngM9nFhu4ViotH3E8XtrNSgqfBtopCrHPrCLUsS4p3ZOL7SbIL0kiABEiABEggTAIUUWHS5LlIgAQiIUARFQ7WjvePVeMWbgh8skaybxT2j6oi+0ilwc58a7p6cXjmxCO37Fdf3XZggzRUk3UgARIgARIoRAQoogrRw+StkEBhJUARFc6Tve3reerWr4InmUBtjmhXVb1/RqtwKhbwLE/8tkhd+P6sjGd54bhm6ozuNQNegYeTAAmQAAmQQH4CFFFsESRAAqknQBEVziNCTNTuj00I52RylkePaKIu2q12aOfze6Jc9/XNuW3UvilKze73HnkcCZAACZBAughQRKXrebA2JEACGQhQRIXTLLbInn5lrxuitrq2VqoqbnmrNm1VSEBhYqUka8PgS9urzvXKmRwWetm5q/5SDe8YmfG8U6/rrFpyj8LQmfOEJEACJFDUCVBEFfUWwPsngQJAgCIqvIeEJAyL1m7Jd8JekiSid/OK6q5vzdOgtxKBMuLyjqp8qeT2YPpbRGGpawarTPu+bxjQQ5UpkVzdwntyPBMJkAAJkECaCFBEpelpsC4kQAIZCVBEhdcwMiWXgIhCxr3eT/hLg35qtxrq5ROah1dJH2eqectwtdSVoa+krJRtvrenj7PxEBIgARIgARLITYAiii2EBEgg9QQoosJ7RLs9OkH99mf+/aIgon69uJ2aJ25xnR4Yq5Zv2Gp8wRdlQ9vTE9zQtkX/0Wr68k356l2xVDG1un9343vhASRAAiRAAiTgRYAiyosQ/04CJJA4AYqo8B5BLhGFq3w6aaU69PkpxhcsX3JHNfzyDmqnmmWMjw3jgG4Dx6mR89bnO1XN8sWtjXZpJEACJEACJBA2AYqosInyfCRAAqEToIgKD6mXiMKVrvz4T/XgTwuNL9qpblkr0URp7MgbsyHrILL0Oa1h5ZJq9s1dY64JL0cCJEACJFAUCFBEFYWnzHskgQJOgCIqvAeoI6KQxa+XuP0Nn7vO+MLn71pLPXFUU+Pjgh6w1xMT1Y8z1uQ7DbLyITsfjQRIgARIgATCJkARFTZRno8ESCB0AhRR4SHVEVG42qwVm1VniY9avelv44u/e1pLdVSHasbHBTlgv6cmqW+nrc53iva1y6pxV3cMcloeSwIkQAIkQAIZCVBEGTaMP2VgMWzOOhlgbFIzl2+2fq/b/LeqV6mUqi+uIw3kp778N/ZNacG9SQzpmhfH/jAjZLYcAfHzVm+2fs9f/ZekcP5LUi4XUzXLl7B+avz/76bVSqn9WlZWFUsXM79YAT5i4ZotarAkExg6Z63FZ9XGrWrlxr/VVllxqC5xIxajciVU5/rl1AGtKquqZYun6m4posJ7HLoiClccNHa5OvaVP4wvXqVMMTXqio6qcdVSxsf6PeCgZyarL6euynd4hzpl1dir0iuisC/XxEUb1G/ihjhq/jq1RFLPL5MMg8vWb1HYsgup2fFTVn4qli5u8WxcpZRqUq20wr3Fydfvc4nquAVr/rL6NHyP0aetkGQoK6VfK1lsR9Wiemnr+9uiehnrN+L0Skimxihsk2y6NnLuejV49lo1efFGtfL/67FWxgVVZP816xtUoYTqVr+8OnCnyqpauXT1rX6YjF2wQe51g3xz5bv7/99e+xlUk2+H/b21v71oq7s3rRjZM/BzDzyGBMIgYCSi/pIBFwZfq2Twte33tv9Gx5Xp37F5o/V36VQ6iaj4+KydpIMLvyNDvXANe2Bod2Lb/l9+NvxXx23/v20ACfHz9DFNPWdMN2/9V30wfrl6bugS9b3MdOruR9mjYXl1iqT+Pa5TdVU9wY4zrc/NbwPGx+ldGdy9OnKp+kncd0w3CC2x4w5qz2YV1aFtq6hD2lRVEFZx24a//rHapv3ht9uk3Xbt3/n/vlXtLwLn1RNbaFUXH/dXRyxVD/28UE2Sj7uu4RXt2aiCZFqrqc7sUVMJrshs45Z/FPb48bI3Ri1V574706uY9feVd3ZXxQNUuqwkSAhwuFYdkyxkIqJQz/OF+1ODFxtXeZdG5dXPF7UL9CxMLlpQRBT6408nrlSvSf/1/fTVvlb6bC5YaTusXRXrG5b0hsdrZMXS+X3N68vyvrn/fXud/dvZPWuqAX0aaT1qrI5iQuU1+XFnYsx1AsTG3bhvfXVG95qhDeSRxGTAd/PVRxNWqC2aHyH0K+hbL9+jjjq6Y7wrtVqAcxTChOXr0mbx3TX5ntinrCQTlxCRh8o396DW6ZuoC8qHxxdNAllF1HNDlqgXhy/JJ5Y2yIAniM2RAF+s1ASx70TE3PrV3G2C6f/FUZB6nSWd6nPHNctYJQzW7/h6nnph2BJfKX/tk2LQ3qdNFXV3n4aRZ65K63ML8sztYzHjCEHw4fgVaqMIhLBs3xaV1MDDG6t2MiCJwl4evlQ9PXiRJdztwQMGUn7t3wd2yXnocpnJfuL3ReqxXxeqJevMU1U7T95REgU81LeJtRFrFLaHJAP4xZUMIIrrmJxzwtWdVNvayWSYM6mn37KmIgpivMdD49W4hRuML3nNXnXVPYfoDZCNT+46IO0iavbKzere7xeoN0cvtfqCsO1YGZTfd2gj1VBWqqK2qz+ZrX6WCSx7IgjfY79dWv1KJdXcW3In//hj6SZ17aezLcHiv+dUqoms5N28X311areaShasfBkmUgd8P19980d+11HTk2GSYaD0rT3kd5rtnTHLZRJlkfpx+ppA7J33iLwzZ/eope44qGGiE8xp5s66FQwCWUUUOsn7f1wQ6l2EIaIws37qm9NDq1c2EfWxzBRe+N5Ma7k6LCslU/w3SQd+7d71QpsNc9ctrc8tCEOs2tz0xRz18C8LjVeddK+L1Zdzd6mtbj+wQejuFqj7Xd/O162KZ7lsIgorOv2+mqMGSla1IBMLmSqAwdlVvet61s20AEWUKbHg5U1FFK44dclG1VVSiK+Xd9HEsIj5xTmtLRfRqC2tImqRuOjd+c089eyQxSrI5IkOvzIyOsU35oZ96+kU910mUxIPvyfLJaKwwIP+8/4fFmiv9ujUo5u4LX95Thujvh6ZH6/46E9fyVay1QkTrC/JJtUndqmuU+1Yy8wR0X/OoJnqK5eLbJiVqCyrU/0OaKAu7FU7sjFRmPXluUjATYAiyrUShdiRSz6Ypd4dtzyy1gIXjOePb6Z2bhD+DFRhE1FYefzfOzOsIPdchsEaZvTg+46YNLhPLpS4qFkStzZDNuAcJa4XOjOYiOV46YQW6jBx9QvL4hBRWH06/tU/tgusD+secJ57ZUXhallZCNMoosKkqXcuPyIKZzaJS3PWpJbEhIy+sqOqU7GEXgV9lkqjiPpAVs3PfGu6WuWRnAMrvvu2qCyrSCWtGJodd9hBzVm1Wc2Vn0mLNir0gzr9l43uvF22ZUiU00RicYgoJDQ5Qfq0L6bkj3ML64bayWrzt+e1VbUkXimXYXLq9q/nykTYPN+rbV7frgf7NlaXiYtfGuxfaWiP/7ZIXf/ZbLXOY9KkdHG4xleyYvMghuEKPWelfHclVhztVtflEt/tj87cSSGbJo0EChIBiiiHiBozf7064JlJgV2gdBoAlrMR23J853BnoAqLiMKH6+IPZqonf88di4EB2qXy8cFMXqMcbizjxRXpbvFff2fMMs8PIZ7NK/JsTgjp2UQtohDke8SLUzyFpk679CrzxsnhccG1KKK8iIf/d78iCjU5XbwAXhZvAFODy+xX57aJNNYsTSIKK05Xf/KneuSXRVlRYRb+ot3qqPN71VJ1K+Z2c0cMyr3iQoZ4FM3wG3WmfNuePbZZJMyjFlHTxH3vsBemqCmyAhqlYdD+nQgpJIXKZIhJPfLFqeqnmflT50dRpy9jWrHNVfel4v595EtTtttvzX0M9oO7ePc6ViweYp2y2WeycTe+u79JAhAvw7f82/PbROZW73V9/p0E/BDIKqLwEUBHhgws6MiQAQ2zYV4zE7kqEYY7Hz4gmJ2bsWyTtcIwdPY6K4PUGolf8mO2Ox+y/Bz87OSMM4boJBpWLqUayWwLfiM49E9Z6v5TZluQrc8vE7iQYSn/5K41/FQ94zFpfW4mNwgBdeob09Qbo5flPOxEETmPHNHEyCXjd3nOfbI8Z+fF8IyfOaaZOksSKwQ17LmDWWW02WnyM0h8zH+Wj7LJzLKzDk53Psx0n/z6NC33PUxK+72mff0assI3RfbdCSt7H0VU0NZlfnwQEQV3vm7i1udncHuHuMrC1SwqS4uI2iofqaNemqrgEp7NDm9XVb14fHNVWVa+TQwJdU6S913XLfDugxuq6/YJ37Vv3eZ/1OQlG6zVhjEL1iskfvEbf+l25/tBYm+OlEkhr9U7cAujT2sq3/WxV3WSbK75g6SQffegZycpxGN5WRj1qCsrtROu6WRl9EvCIKD2fnKCmiDPNJshMVi//Ruoa/aua5Qw5rFfF1kePl7fH2T2g5jsFoGXThJMec3CT8AoOx+y1A0au0z1+3KumunhXpUJXRgiKtN5keHrQwk4verjP9UCccczMYgorAYdLp22098fAqevfOgwU7iXR1A9XMUe/GmBrHIsN/bbxmD9+eOaSya08ISU+/7T+twyPSeIZAio10dlF1D4YD0vyUCQacmPjZOVm/2fnqQWr8vdVnCd105qEYm/OiYoLvtolvp8srmrii2iXh+5TJ325rSMK2tdJBvmvi0rqX1kBaBJ1dJWyllMBiBjJoQ/BnivSOIYP+9xrmQsps/jPXGbRWpcL3tb3i0IYB27RQbqQdIIY1IjLJGoU9+4ywQRUagr3p8eD49Tm+R7YGJY4f3hgnZqtyYVTA7TLpsGEYX+6xQROV4TQAtv7aZqe7iRZbtxJDQ4WAb3Orl1Kogw+OP6Lr6vpQsfog7vMpI+6YgO53mdIupLcd3Dqrq7beF7vFfzbf3ZHpJZFcegT0M688Xr/lLwNMD3F2LOtF2iLtdJnDISP9mGGMC9n5yYcTyB9OmI8UN92knqbriOQ/is++tvy5Xtiykr1cvSt+YSI9m4YvUQ37a4DWn195bNqsdLuv1sVlXu8Zvz2qguEk/mx/C9Ov2taZ7ttqJsTTL0svaRJ+Hycw88hgTcBIxElH0w9mfY9ZEJCtmGTCwqEWXXYbS442Fm22RlCPtvYObQObN3hIinh2WFwzSTIAaD9/4wP6cLRyZeGKy/fWpLdUzEKU/T+txsJhiAQBS8Jp1tNgOrp45uqs4Rn/8ghtTocEnxGgbC5WbStZ0jieeA+EcdhkrWQRODiEKQ+nmSetrt2oNBxl2S8Ugn4xPE9T3iIoSgd90UvagnhP+sG7vEkgXM5nKuBDg/I/esYzOlbsjCRctMIKiIwlmflpTnaH+mhj51jMRHRSFS0yCiEDdz0xdzPbEMk0FikJjY2yRrLASLjp0hE3QvyKpXHIZvDNqXVwyrsy62iMLEzjEvT91ule3I9lVVf1lRayVxM14G75RzJIb2e1nNMjGssEyUVaDmIpAmiJDY98ntJ9nQp9xxYEN1griOe22BgH4Z2VGv+3SOUSZZ1GN+v26xZqzTEVCYgPtWBFTQFaI75Ftzi0zCexkyF/56cXtPzl7n4d9JIGoCvkQUKoXZpl6Pjrc2BtS1qEUU6gEf3L7iS+033SoykCGAPkhALmbEEDtgkoYbg/Xxklo5m2+2LmOvcml9bqj3DZ/Psfyncxmy5yFFbRh2gWRf9Iq5wnWQZAJBr1HYElkN2+Xh8UYrQhhQgJXTdm1cQd0p4slr1TTTPXzy/4OXzQYvza2SUanf/uE8Bx2uFFE6lPTKhCGicCVswgtXalPrK+/ThxG8T0mLKAiIlneP1spgiImgcwNMBGHir8sD43KuHNjPBQP+abIaFdd+eBAyaGPISqhjdWRF7l7J/nmGJOBwrq7tKZuzIj2+zoSQ8zpgc9Jr09Q7hm2zj+xdhJTb+z01Kd+WJojVgRsqnpfphr1IC37I85O12oR9Dw8c1khdsWe4CXyyPQeIvT0fn5AzBgrt5/vz21p7KwY1uLbDHVhnu4QHD2usLt8zHck2gt43jy+8BHyLKCBB6ssDZZd4XYtDRKEuJgMuu+5wF3jsyKbqvF2DrXDY50MMGcSciXshgq+/luDrIAJO51mk8blh48IeD43LKX6xmSRmcINspOrkg33A6t82Uiue7k1JqBB2EhC7LnDp6/Oc/nvkvAe4SD13bHN1WkB30JdkLysMYnQNSTyw4uM1I6t7Pq9yJu80V6Jy0wxLRCF7WpcHxhpNANg1e0iykSEhTJiWtIhCFlFsyK5j5/SsZW30HsQekC1IrpKtSHTssSObWGmk4zJMzCAxhB8LI+034mp3lu8JvFNMDJ4pzu0h4JXyqrh0l5Osc37tLYntPUFEna4hU93kazvpFg9U7mHZd/EySdueyy7erbYVexyWIR54z8cnep4Oz2Lc1R1Vs2rM2OcJiwUSIxBIRCEVZou7R0mCBz23vrhE1HARMN1lc0hdg9/4O6e2snbTDtOQ7W+XR8Yb+Wg/LJu+XiJZb6K0tD03uFJ2ezD3rCpE7rDLOvj2x87G85xBM8Q1znvg01o+bJMi+rBh5rTurSPUUoNVXdwP9h1757RWoaRjx4xkZxkQ68wQ2ixHXMsdvmUAACAASURBVN5BdfXpH2/avimiTIllLx+WiMIV0Nf2EtduE3dQu+3+fkn7UN/npEVU4ztHabu4w+0W7lFBDPv44JpeLsm4RpSr6ZnuASKmwe0jZJsJvdUo+xyYFHrrlJZW1reg9oVMTh3sc3IK14Z7PbKRBp20w/e2y4NjJQGH/mbV8/t19czYGJQPVgw73Dc2Z1KihuJ+O/Gaztsl3Qh6bazYIsmSlyFV/5OyaksjgbQSCCSicFOnSBKAXDEszhuPS0Rhybj0tUO0UsHWLC/Bkue2VR0kZWcU9pSk6D5f3MZ0DZsljrmqY+T7JaTpuSFRye3iK53LTutWw8pkGLYhWUEvcT3RsZ8vbKt2FxeTKAxBzMhKpWvlZWYULlEYjIVlpoOOJ2UfmrBWbr3ugSLKi5D+38MUUbgqNne+QpL6mBoC9Ede0UFVkEDyMCxJEYVVuco3DtO+DcTXYMU0qGGSbohkqPUyTBQuv6O7sTua13lz/f0ASd7ztSTB0DUIqDdODjc2OFNb16kPtrfAFiSStyIUQ5jBIc/rr8x9LH37oSHuVei+CQg7xON6pW5HBskokl71l83nb5RNlL0M7XbhrTsHWgn0ugb/TgJBCAQWUTpxLHYF4xJRuF4dmdnX8cneXzKYYf+SKO04iR0w8c+OSjA47zEtz22ipFPFCojXTPZgmbXuKcGmUViNW4ZrxfadJAHFyNYXhZmIWlx/kCQiOTqCRCQm+7/EmUmKIiq8Vhe2iELNDpMB4icyUDS1k7tuG6yGYUmKKMSathowWvs2IBg23tMz8CrHvd8vUNfKpqg6NvTS9qp7w2j60EzXN91TLAovDCQQ6i1iwcSQ2XSIsDKNf/K6BibrdDOMIt4UcadR2TOSGOZcj8QwiNNeIFkky4hbXdiG7367+8ZonfZZ2Wrk7J7+MvFqXYCFSCAAgcAiChmCkClIx+IUUYh1mS+Bvl4Wh4iCmGt8x0ilG7iPDD1/3tQ1koxwNo+0PDfE4SAeJ5d1kDSyY2V1LipDPJJOqnHszo7MSVFkFjvr7RnqhWHeboVgcFynapbLSxT2hqSWxz40OoYNF0dLprU4jCIqPMpRiKjl4oraSSZD5mmkq3ffCVI6Q5AHtSRFFDJdlr1Oz/vBvs/Ft3VTNSVpQRAziT368IxW1rYdcZlJn4akDp+e3TqSqjWUb+/cVd5jAVwcffzIKzqqNrW8MwGaVtbEK+XQNlXUx2dFk8wIrtst+o/yjGW8ZPfa6uHDw4uFcvJCHSrdMFQrkzKyWCIWmkYCaSRAERXDShQe/Pky6/OUzP7o2vWyQSKysEVlaRBR2NwPfvNe4jLqbEUm6YKjWgEyGXAsu33nQPsg5WpTwyTdeg/JFqhjyFq1SAaCcRhFVHiUoxBRqN2vs9aq3pLpyyDJo3VTCNofLvGOrQMOXJMUUbiPFv1Hq+kSZ6JjyEiHWf6ghj27Oop41bGwxKrOtVDGpE9bctvOsu9TNJvM9pYkBl5ua/Y9XSTJNx6VJBxRmElCpyiFg67wHikxr373hNLhZ+L1sFS+ediPi0YCaSNAERWTiMK+GS0lCYfOBoloJFVkJ/u5t3SLzBc4DSIqrj1VvF467E+CTIo6djVS4Es63rDNZMBhb7Ybdh1wPmxZAPdGHUNii0339tQpGrgMRVRghHkniEpE4QK6sQ7uu8FqMzbYLA0/N5+WtIi674cF6ppP9VzrwhqsI7lEI0kuoWP3Sb+FLTzisrT0aTreDjaTa/euqwb0Cb9/x/lNXD5b1iitpl7XOZJHhRTu307LHauGWKRVd/WINPvqFZIVcKBkB9Sxz2SV8mBZraSRQNoIUETFJKLw4E33VXlcZsQuiCgtbdIiChnpGt8xytPlEmlOV/fvHjh2INeLN1QCs3tKgLaO7SGJJX6SBBNhW1oGHLiviuJmsXazpNfSsE0S11FKXGCiNoqo8AhHKaLgpoOEAl6DtEx3g314sH+SX0taRGHLhKZ3jfKMr4QbLDIThhFrAjc1uKvp2HV711N394nOu8Fdh7T0abdLuEE/zY2JoxRRcPksIwmvdLIp1paVyoUhrFS6n8nkxRtVm3u9Y5H2k3ERtluJ0rDR+3WfeSeXQB1ukX26bpM9ImkkkDYCFFExiqjXRy5TJ0s2Q13DzAtmYKKwpEXUe+OWq6Nf/sPz1rDh4o8RiBbnhWdKiv5m4iOuY3A9Wi0zdGFlbbKvmZYBB+qD2Jaxmul4w4jr0OFOEaVDSa9MlCIKNVgsMaAd7x+rFstG0qb2jiRMQWppP5a0iEKdJ8kg9aBnJqk5WWJwkJzmftlEFIPkMMxERAUVqab1TUuf9uqIperUN/X2wItSRIGfbqw2svRuuKeHKXLP8he/P0s99tsiz3JRJ7ZABRADjDaiYwe2qqy+OCeasZDO9VmGBLIRoIiKUUQhBqj2rcO1Uq/jgVWU1L8r7uwe+oAd505aROluTBmW20uuLmCNpCeuZJCeGBshYkPEMC0tAw7c06GSae1TzUxrcexngjpRRIXX2qIWUajpt5La+gARE1iZMjFkBBslyUqQAtzU0iCiUGckEsIGq9hwHW7cjWVj6hbinnXgTlVCzzBqIqLO7lFTPXtsM1OsvsunpU/7TlzX9hUXNh2LWkRh/0rsraZj/9y/i9oh5EV+3bi9d08LZ6+uXPdp4kYfZ/ytzrNhGRKwCVBExSiiAN2kE0X5qDY0TVpE6W62d+dBDdSN+9aP/I0tdc0QhU1/deyXi9qp3ZpU0CmqXSYtAw5U+IgXp6oPJ6zQqjtFlBamVBWKQ0Thhm+SfWDukv1gTK2HpOHGO2aaYjotIsr0foOUNxFR/5M00c9Iuui4LC19Gvbfwz58Oha1iOopSXuGSvIeHQtbRC2QbMX1JGuxjv0q71+vkL9x7uua7NGIUMkt9+2iU3WWIYFYCVBExSyibv5irrrzW72U8GgJUWWmS1JELVyzRdW9bYRWQ39OZk7PkhnUqK2u7Cu2UGaQdSyKjRDTMuCgiNJpAQW7TFwi6m8Jq+v9xAQra5+pIQECEiGYGEVUblrn9Kylnj7Gf8yZybNA2bT0aRRR257cm7I6euJreuEE02/orJpVK236yI3KmyTawInXSGx0WBtzG1WUhUkgBwGKqJhF1DtjlqvjXvWOBbKf2WGya/lHsnt52JakiIKrywmanfmnsldGH9kzI2pDDMe4hRu0LvPyCc3Vqd1qaJXVLZSWAQdFlO4TK7jl4hJRIDRPYoMQY7d8w1YjYPBiQjzoQQYZuYqSiPpXFs2/mLJK3fnNXDVYEuPo2HmSuOPJAIk7dK7hLJOWPo0iattTOU+2WXlac5uVdXf3iCwzsN1GVm7cqqrepJcJFsfMvqmLaiiusTQSSBMBiqiYRdR4Gah3kAG7roW1n4j7ekmKqAvem6me/F1vz6xdG1eIZHNbN4/fZq1RKzf+rfVYHj68sbpk9zpaZXULpWXAQRGl+8QKbrk4RRQoIb4OcXamVkP2hRkjm2zXrVhS69CiIKKwqfHLI5YobNw6bZnenlQ2vPN3raWeOIorUbkaU2F252tzzxg1eclGz3cJExhxTFz+IzMBOpvc2xUeK7GSHSSzJY0E0kSAIipmEbVJNooqd91Q7YDrEjvuoP66L/y9eJIUUSab7KXpZbHrEkWcFkVU7ifNxBLhvQlxiyjU/MqP/1QP/qS3J4zzTvduXlF9c15brf1qCquI2iKxmp9PXqleHr5UfSa/dWM33S3mgl1rq8ePimYj2UytMy19GleilDXeKHH1YO1xR3i9TXhniiNOK7za8kxFhQBFVMwiCg2rmewlMlOyNuna6ru6q4qStSpMS1JEtb9vjJqwyHtGLMz7DfNcd8h+FTfJvhVhWloGHLgnJpYI88mm71xJiCgIgV6PTtDOTOakdtsBDdQt+3u/b4VJRCGe7McZq60sf++PW6FWiOtTUIsj06mzjmnp0yiilMIKZnXNTdSDtrOojo8ioVNUdeV5iw4BiqgERNTuj5kFW8+6sYtq7CPlb65mnKSIqt1vhK89ZNLyWlJE/fckmJ0vLa1Svx5JiCjUDvuxdXlwrFotWwqYWDHxL/r+grYKG13nsoIuorBa8Ku4FSNu9t2xy7X6SGxGvmGL3sbYl+xeWz18OFeicrWhwurON0Xc+FqLO19BNoqogvz0Cm/dKaISEFEHPzvZCgrWtSjSnCclohAQXfKawUq8GgusUURRRBXYxisVT0pEgdkgEQfHvqKfWMfmXL9SSTVGYiKqSZxUNiuIIgr94eDZa9XbY5apQSKedDKE1ixfXB3apqrq266q2qt5JVXhhqFazfHKPevKRr9mGQ+1TpylEFeitgeTVIpzZMjE5G1BNoqogvz0Cm/dKaISEFHHS3a+t+WDqWtfn9tG7Sf1DNOSElGmGXk+PKOVbFAZ7p5MQTmWl02Qy5WUjStCtLQMOHBLdOcL8cGm8FRJiijgOF+yhD2lmSXMie9QydKJTKXZNiAtSCJq9Pz16s1RyyzxNEcyGHoZkmwc26m6Or5zdYVkOxIqa9nmrf+q0tcO8Trc+vt1e9dTd/dpqFU2jEJp6dPozqfUB+NXqCNfmqr1WNG25vfrpkLe51fr2rkKVS1b3HjvuMAX5QlIwIMARVQCIuqcQTPUs0OWaDfOt09pKR/QatrldQomJaJmr9ysGt85SqeKVpnPDdMca584ZQXTMuAAFoqolDWOkKuTtIhCcp0eD43X3lLAefsPHtZYXb5n5syYaRdR2B/vxWFL1Ksjlyq4V3kZ3BghnLCdwr7ynSpuKyfHges2/6O9EnWTbFp+h2xeHpelpU+jiFJWUpLT35qu/eg3DOihyoirKI0ESCA3AYqoBETUhe/NUk/8vki7bUaxV1JSImqVpBGvctMw7XuPQkBqXzzGgmkZcFBExfjQE7pU0iIKtz1VRETXgePU+r/M/HpLirL47eJ2qluD8tvRS6uImisrTfd8P189P3Sx2iQrR14G8XRCl+rqlv0aqBY1cm94atKf6ibo8Kqf7t/T0qdRRCn14YQV1uSYri2+rZuqWb6EbnGWI4EiS4AiKgERddqb09UrI5ZqN7phl7VXO2cYNGifIEPBpEQUYgCKG6Rafe7YZuqsHjWD3GqBODYtAw6KqALRXAJVMg0iCjeAPhB9oak1r1Zajbyiw3YZS9MmomZJBtb+386z7lMnLTkWmo6XlSdkImxVs4wWlqXrtqqa/fQ2LO1/cEN1/T71tM4bRqG09GkUUUr9NGON6v3ERO3HOv2GzqqZvGc0EiCB3AQoohIQUUfKjNAHMjOka3/KTt2NQt6pOykRhXuuJruU66bsHdi3sbpsj3A3ttXlHme5tAw4KKLifOrJXCstIgp3f7qIqJcNJpRsYidKbNDrJ7fIBzAtIgpZ9h7+eaG64fPZWitPEE/Hdqwm4qmBal1LTzzZNw4Xwbq3jdBqSPcd2khd1buuVtkwCqWlT6OIUmr8wg2qw/1jtR/raJmk6FSvnHZ5FiSBokqAIioBEbX/05PUN3+s1m5zUfgnJymimvcfpWZIumMdu1n2Y7pd9mUq7JaWAQdFVGFvaclm53PThTtfN3Hr04kRch/77DHN1Nk9/1ulToOIQozS4S9OUd9N0+vfq0mw/Hunt1J7Nsudvj1bq5wjMaaNNGNMH5IJqUtjnJBKS59GEaXUPHEpbXDHSO3O7bvz2qi9W4SbzEr74ixIAgWIAEVUAiJql0fGqyGz12k1kwqldlRr+vfQKmtSKEkR1V2CyofP1bv/oztUU4NOa2lyawWybFoGHBRRBbL5GFU600oUMr4h1igJG7dgg+rx8DitVRtn/bBH0rDLOqi2tbet3iQtotbI/lcHyfYVv/+5VgtjW1l1+vis1qpptVJa5TMVwt5bzWRSSsceP7KJuqBXbZ2ioZRJS59GEaXUBpmsKHe9Xip8PPzHpK1cGGNbCaXB8SQkkAABiqgERFTbe8eoSYu9szOhPTSTD+z0G7qE3jSSFFF9X5iiPp64Uuue2shAY+I1nbTKFuRCaRlwUEQV5FakV/ddZRJnsGsSp7vEXA6V2Muk7KnfF6vz35tpfPl2IqAgpJBJLGkRdbj0ax9p9ms7SczTkEvbq0qlixnfs/OAP5ZuUq0GjNY6xzPHNFX/61lLq2wYhdLSp1FEbXualW4YptZs1tvo+oJda6vHj4pvY+Yw2hvPQQJJEKCIillEwV++/HVD1UbN3WaxN8rHZ+0UettIUkTd+c08dfOXc7XuqYQEDKyXdKslkLKqEFtaBhwUUYW4kf3/rSG9+DDXSnAXiX9AsoYkDZvwYjNeU/ufuPQ9I659SYqox35dpC7+YJZW1eFdAOEHIRXUJi7aqNrdN0brNC8e31ydvnMNrbJhFEpLn0YRte1p7vvUJG030z2bVlQ/Xtg2jGbAc5BAoSZAERWziDJxv0DLe0Jmg86XWaGwLUkRhXgwxIXpGlaisCJVmC0tAw6KqMLcyrbdW9cHx6lRstmr09rXLqvGXd0x0ZtfLe5wXR4Yq2ZKVjtTe0v20ntJ9mD6cuqqfId2qFNWjb0q2vtasm6LanzHKO2JsXdObamOkUQSYdhYcYXsJMx07LUTW6iTulbXKRpKmbT0aRRR2x7njZ/PUf2/m6/1bLG585Lbd9Yqy0IkUJQJUETFLKI+nbRSHfr8FO02F0VmPlw8SRGFwVKVG4cp7x1TtmFyB5BrwytABdMy4AAybrZbgBqOj6pi0I3Bt9MaS/bPWZIFNGlDrGSvRyaoLViyNzC4xdWrVHI7N+k4RJTJ4BRxUBNCdE8eIbx2lpVFHYt7z7209GkUUdtaxyfianqYuJzq2rTrO6vm1ZnmXJcXyxVNAhRRMYuoe79foK79bLZWa2st7h6Tro0mHihJEYWbb3PPGDVZNtzUsYN2qqw+/19rnaIFtkxaBhyFQUTx45/7NWh592g1bdmmfIWqlCmmVtzZPRXvz8CfFqorPv4zlLpELaI2iVt2nX4j1CqZGNKxp45uqs7dJby4JCQoQqIiHXtfsgAe0b6qTtFQyqSlT6OI2vY4sWJaS9qqrt3Tp5G6Zu/4UuLr1ovlSCBNBCiiYhZRp74xXb06Um+j3Sv3rKvuP6xRJO0laRF15lvT1YvD9TiUlHiopeJaUDFgEHYkIEM6aVoGHIVBRE2SmX7T/XZCeowF4jR1bx2hFq7dkq+uCDn8675dFPYsSoNhtR6r9kEtahFlMkAvU3xHteyOnVXZkjsGva2843+ZuUbt8bjeJqofntFK9W1HEZUL/rUiGgaIeIjKej48Xg2do5eZ9p/7d1E7hPw+Nr1rlMIm0DrWo2F5K/kJjQRIIDsBiqgYRRQ8VOrcOlxmhLZqtcko92pIWkR9JJsNHy6bDuva6ye1UCd2ic+fX7deYZWjiMpN8txBM9UzQxZr4R57ZUfVoW5ZrbJFsVDFG4aqtbKfkdsW3tpN1a5QIhVIlq/fasX6zFv9V6D6RC2i+kmCnNslUY6OReFZYCLiPj5zJ3Vo2yo6VQ2lTFr6NBNGhV1EXf7Rn+oh2Qhax6Df5t7S1XKTpZEACWQmQBEVo4j6ddZatftjE7TaIj648J2PamY4aRG1VRRlI9n8b8Ga/DPi2eAc2Kqy+uKc5Fz68OxWbtga2SAkLQMO8C/oMVG/y35Hu8i+R7TtCWze+q8qfe2QjGiGSYrznSXVeVoM71zvxyeov83Co/JVP2oRdbDsC/XFlPzJLOLsw76WRBoHPDNZ65F9IlleD5Fsr3FZWvo0iqj/nji2VsEWK7o2oE9Dde3e9XSLh17ulRFLVdf65fP2ggv9AjwhCQQkQBEVo4i66uPZ6oGfFmg9MmSbOq5TOBmcMl0waRGFOt38xVx157d6s7go/8tF7dRuTeIfHG+RUVyH+8eqKRLD1Vdmch85oolqKIH4YVpaBhyFQUR9KoPFPjEOFsNsB1Gfa87KzarRnZk3Z03jau9d0j/cJP2EX4taRO08cJwaMS9/psNsdUUsFGKiwrTPJ69SfZ7TE1Gfnd1aHdy6cpiXz3mutPRpFFH5HxMmcjFBoWNVyxRXMyXhTND9zHSu5S4zXeI224nggwfP5XvWUf32bxCqK6yfOvEYEnAToIiKUUQ1E39knfS92EBy7FXRrUKhEaRBRM2WAR18tHUTce0qqwu/ySpD3DZA0sJeL+lhbSsnMQ33Hdoo1NTzaRlwFAYR9ZLsh3NajPvhxN0eg1wv12r41b3rqnulXafJ0DccINshfDttta9qRS2iTGJMzpA2+YK0zTANm5Zj83Id+1xE1EEUUTlRFXZ3Ptz8q7K6c+qb03WajFXmhn3qqbsObqhdPqyCB8oK61eOLQsaVi6pXhO3/t1lDysaCaSFAEVUTCLKZG+kQbKPyNEh7SOSraGlQUShbpk2yMz1cnwkfv2HxejXD/eHbrKvjntz5DdObqFO6BxejBZFVO4u8YL3Zqonf9eLiTqnZy319DHhzvinpcMOWo9nBi9W5747M+Np4MoHl7602WJJgtFRVoIXS3YxU4taRNXqpx/jGsUk0HvjlqujX/5DC8uX4g59gLhFx2Vp6dO4EpX/iSOjJJLLrNyol1GybIkd1fQbuqg6FeOLl3xJkk6dIcmnnIbJyzES78q063G9wbyODoEiL6JqlS+hZtzYReEFjdJ6yF4ew2RPDy/DRx8dRdhZedzXTYuI+v3PtWq3Rydo7xnVQGajhl7aIZYOfeOWfxTcdSaKkHIaNsrEhplhWloGHLinNMZEmbh11atYUs3r1zXMx1NoznXKG9PUayOXZb2f6Td0Vs2qpW9vmG9lg+4DnpmkvWpt32DUIspkq4ZqZYtb2fnCtAfFPfxKcRPXsa9ERO1PEZUTVVFYiQKAu8W74gaHd4VX+0FM8idn76SKRxWk7ajAZExcynd3g3x/nfb4kU3UBb1qe1WVfyeBWAnEKqKi2jg2E7H6t41U89foZXbqL0vV18uSdVSmm4kOqbx/vrCd6tEo+uBuExEV9XO7+P1Z6rHfFmnj71KvnPpJOJUvFa3wPU1cHhDY6jRkL5twdSdVTXZ0D9NMRFQUqW+d95JGEfW6DPxPFgGga6Ou6KA6Szuh/UdgnWTkw8qJe3DiZHTHgQ3UTfvVTyW2m76Yo+76dr5R3aIWUb0lvfhPkmZc15aLiKoqYioM+1ISWsCV7y/NzBvvntZSHdUhujhb9z2Z9Gl/SzrvqMbnBXUlKkomSOyECcIxrk23c7XLOFb418h+a70eHa8mLMo/cbkfPIbOaRP55HIY7yXPUbQIBBZR13wyW933o16yhDgDvk1EVGXZfwjBk1UkiDJsg09/J3FFGb9og+epHzm8ibp493hmWtL03DC4a3ffGIUYKV3rI779H53ZWhWLSEddKZt9PiibfrotqgxXJ7z6h3przHKt258oWRvb1CqjVdZPIcSgfC0z/zo25JL2sYj+3yQQejfNzJao9+0iBm5OqRjQ4RpFmUwuMu7rNJaEKeNlkiDqCQo/9/e3TEz3fkI/KB7XiFpEZZpoyXVvA/s2VpftUcfP7ec7BrFteE9zCWL3RR6VhDgX7RbP9wXXNunTRsukR6eIJj0+k/3GDpF9x3TsaBGZg0RsRmUd7tMbC+D6UW8aPnr+etX9oXFKvPu07W6ZcL4uognnDX/9Y602u5NeYHyGPqm+eKHQSCBtBAKLKJOZuOskVebdkjIzDjMRUajPqd1qqJdPCDfoF+e99tPZ6t4fvEXm8ZKJ703JyBeXpe25IYAUgaQmtnfziuqlE1oouPiFZRC9eGb3Z5gYOLN7TfX8cc3CulS+8+gmHcFBUbo1/Cv3X+3mYdr+8g8f3lhdsnvwQaEX1PmyX1D920d6Fcv7e5UyxdSIyzuqptXCzaKoXYGUFVwi8USYeZ6zynt1/kSJ9XtdYv7SaPOk/tg/arlsN6BjUYso0xVSxJdgQBikXb4mm7WfJ3Ft62XQaWJHyEa778uGu3GZSZ/2kIjLS0MQl5nuzWQvr0YyiQDPiygMk4WVbxyqnbI/jmyZ1382Rw34Xn91F3tHQYjfc0gjVUbacli2cuNWdfRLU9X307df1X1FxmWnyPiMRgJpJBBIRCH1c6Ubhm0XdJ/tRneX9NQ/S5rqOMxURKFOYYs83Q/sTrIn1PDLOsQ2+5vW54aBwdMS+G5imKV6/KimoWzEiwD2k1+fljETWJOqpSRjYkdVoVQxk+pplV0oe2XVvW2EVlkUiiImy7646T4iJ8kGyMiYFLVB3JaR/Y10XZdQn7ayWvfu6a0U3q+ibNgbai9ZwRk82zsm0+bUb//6lltfHDEQps/mU1lZOFRzZSFqEbVUNk6Hi6TJVlaY/Pn2vLbGrklICHDJB7PUs0OW5CHDoFb32iXEX27ydZ1iiXkz7dOiFHj7PjVJfWeQ3XHxbd1UTYmVDttQB9RF17BiiZXLKA1tqufD49VYA7c+1Ad9Kvr9rvWDu0yPkFjxY1/5Q81asb0nStQrg1Gy5bmLBoFAIsrUxUZCftQHZ8Sza7ofEYVHHpa7xY8yo3LQs5PUJhnA5DLMfH19bhvVskZ8wdxpfW4YKJ8pGXledsUh6byKB+1UWf1PsrJhfyDElpnYRPG/fujnBRJwvzTj82pfu6z6/H+tI3MneHP0MnXia/rxPri/xyTIFvcbtj0hsWkXSoyarmHFZ/jlHWIZmPWSBCRIRGJipYTVxbJShliQ7g3LZ427wAocVmv+lA/5yHnrVDfJVIfyBdlwTxAcN0oAuY47sfteIUIHyIwzMrqVMHynouZ2xUd/qoE/b+9u675u1CIK1zvyxanqgwkrjG65u7Svx45qorW5MQa6r41YJm7z89UfSzflXQfJkJCtdKAkl/hM9ovSMWyf8eMF7UKP6XRf27RPQ/N64LDwV6OQHKjmLcPVOoNVu7AnU202pvsi1pH42xHi5lhXEuVEacvXxDcHDgAAIABJREFUbxVxN9EoPgr1gSg/s0dNy4sHmSdNDd4nA8VlHhtGZxolwTsHnialipt9z03rwfIkEISAbxGFmaY9JEZh+vL/OnWdisCdATOc2DytdPHwloPd1/YrovC6IgPMnQc1VJVlgGhqGLhgefxmCYL2ivftJrM4n8jeHUhWEJel/blBSJ07aIZ6buh/s60mbLA54LHS+R7evqpCfEfdSiXzrR7h/Asl4QgGyzOk7UI4If18Ntu3RSX1nqxmVJQVryhsqmzgi80Pl8qHzNTO37WWelji6MIa4I6STUP3emKiWrNZL/WtXV+s0mET0agzf5lsLJqJJTaMxLtWvVwJWfUtpjbJAGv1pq1qmbBfKgJqs+OFhWsvBgcFzbDq9IskOvhg/Ar5Wa4WyupqUMM71bddFdl2obrq3axiKja8xGo6RPVwj4yncYgoZBNrLzGdXv29+zngW3OGuAgfLm52rUWwNqla2orxRND/XHFbnCX9008z1khq/0Xb9Q9YgcfEzi4yeEUfdsob+vv+IHvlE/K+9mldJZKY0iB92v961hR35aah9GlYtT5OVjg+NBS4eE7YM+0W2dw1rNhArEL1eXZyvj5G573ERMYzxzbzJVJ0zm+XgTvd/rJKprtxtPvczcRt+pRuNdU+8r2sJ99cCD+n+MH7OmfVZuu7O1Hiw+FxAq+HbHbNXnXVgD6NjFdrTe6ZZUkgDAJGImqBDD7HzN+gfpi+Wj0lHbvJ7I67svgIYKB7jHyYMeNbPeRsZ35FlF3PmuWLy4aqjY0GUvh4XPrhn/k2iMv2kPrKXkdvnNwylgFJQXpu4AUheuH7+vsCeb0IFSSLHzp1BKajI9d1CcMg+jn5gIUlUux6IoHGGAnqRXatF4Yt0a5PpvtEOz2yfTVrpaVrg3JGyVHAY9qyjWr8wg3q88krrcGYSZCxuz495D22BGy7aqqxCKsosm3pbhXg1Sa8/l5QRNTM5ZtlMLJBVs/Wqx+lXx4ye63n6rfXvef6O2af0c72FDHVo2EFy62nZY0ykQzGveqJe+/y4FgRwtlFfxwiCvW89MNZ6pFf9DOMZro3rJrWEDcyTPLkEmR4578+t63qWLesdRrE2mC1xb2XnRc/rCJDuCFbINw2i8v18d9+YoPD7NMw0YF+5FgZG3SsW85IyCA5wZgF660V60d/WagVA5iNE8YkR8gYBS5lPRpVUJiE0TV8w6Yv26RGST+PvbzeHbtc2+3SfQ2IbYjlQ8XL4kDxuNhJhFUUE9B4j5CsZOgcfbffXDyQ0h97S62S/agwBsEEppdhRfJR8bI4f9f4EqB41Yl/J4FcBDxFFNyrkCIUri4mmYBMsaPDgivdyV3Dmf0NKqLs+veSzgtuU9jpPZOfNDrtr/9YpR7/dZHld+3VT6BDxCocBFoUg0y73gX1uTnbDfZAQVBwELFu2g7t8sjshgxvYRk2FZ4og12seHi5eAa5JgZGmN2Ga0wmQ+aj82SlD6svyzdsCSSactUTLodI+IFkKdjENSyD8DxIZnSjtoIgojAoKXbV4KhReJ4fG/SG+Yw9L+goMEgGp4inyGZxiSisHh0s7TLXqrbJfWUr20rcvj8+q/V27t/nyDvtjJXyey18h5ferreXVVx9GkTV/fK9PKlr5s3Np4mL4xEvTrE2Y4Zrmtc32C8brMgiwcFDkkwnm8GrYJaI+6XrtwSaHMs5aJM/QpzcvF8DdZ54I4Rpa8ULAZlpn5O4u6g4ZqsvvJTeku/FoTLBTCOBgkLAU0Tt+sh4o4DkIDf+wGGN1BV71g1yirxjdUUUOmjMME11+JpnqgDED+KXMFOIDw32M4A72CIDdxn4wT8saWZ7xrAPVEF9bm72yMp2ucQ/YLAUh2H2DGI+7GxADSSz3Dy5lzjsSJk9hQtiJvtk4kp1mOwtE5d9e14by8UjTNvnyYkZsziFdQ24prx6Ygtr9jfNRhG17emcLwlpnsqSkCYuEYV6YCYfLlE6m6qbtit4vl+zVz11syT8yLQKgbgpZFwNuopgIqLi7NNyjQ2wqt/5wXGmSH2VP0wG+IhDy2ZItGXqDu2rInJQ2BN9znoMlSQ05783UyENehyGLTswcYVYVBoJFCQCRV5E7S+buH0qcUlY9bj963mRrLbVlVmju8W/9xRZZdshphjJwiKi7JcJwacXScKDaeIiEYXBTQmpW+EH7ycWzqtOcQ44CruIQmrnI2XmWXcvK69ng79jIgVZD0/buabaTbKIFgSjiNr2lCAg4OY5TtxS3RaniMK1EZOGDHrPDDHLMpqrvSED2vPHNc9z38tWFtlFe0imNZP99tznqiEThEs0V6Li7NMoorZ/6lGKKFwN7t6Ix8Mm17lcZoP0lRDttx3QQJ27S+1EXIKD1J3HkgAIUERhJ2zJjgdD0gXEeuEDaLLClKkpQSsh1gu+3eggkEkpTitsIgrsEMsEMQX/8o8nrtDeyygXd6xEQtyeK24RzapFlyExzgFHYRdReJ4IVH7014Xq3u8XWG48fgz+9/u1rCzCqYYkTqga6r4nfupjegxF1H/Epkg8ajfZB8u9d1LcIsquEZIZ3PrVXOPU0fbxWHlC2zxRthA4oXMN7QEmhNQ9ktjoqd8Xa8dI4VvVQ7wjEIN0tEwk6O67F2efRhEVv4iyr4ikEx9KohrEdsFdVTemOFd/hiyRcDs/s3utSCYtTftSlicBvwQ8RZTfEyd9nK47H1aibBFl1xkDNLg9IYHGbxKgOk4CVXUyL2FQ1lMCUPEhQqC/7scoaVYF8fp4Rng+741bIQkaVlqBqzpJETA4aV+nnATFl7fSoSNQN4174RTEZ5JEnRGT+K583JFU4WfJSjdD4hGyTWpgtamDBKrj2WPQuFuTiqpWjJkxk+BTlK75xqhllkeB01pULx3rJubOayO5wCeSYv6dMcuseFmvibmKkjGyc71y6gQRTkhmUC1AsiVcC6nPkXAEWf6QUAf9IwQT4mmayoQRkkp0kVUufquK0lsS7F4RxoCxEQTVr7PWWLG1OrFT5WUSuWv98qpn4/JWMrEw9pcKdic8mgTCIUARlUFEudEi+xEyYGFGZrVkmlmF39KZIP0psr5ZKT3lp1b5ktozhuE8Pp7FJoABCzr0RWv/sgYri0RUYYUCCQ6qSEBwFYl1QmKQ9nXKFrgVBz5lfQJwQcF7unLDVsvNC6npK5Uubv2OMpGLfg1ZsqgSQPZWxHhiOwN8Q5AJr458PyBqalcoGam3At4L9I0QZlFkdiuqz7So3zcmM5F0zP7m4jeSe2BshG8uvr31JcFQ65plOTYq6o2lkN4/RZSGiCqkz563RQIkQAIkQAIkQAIkQAIk4IMARRRFlI9mw0NIgARIgARIgARIgARIoOgSoIiiiCq6rZ93TgIkQAIkQAIkQAIkQAI+CFBEUUT5aDY8hARIgARIgARIgARIgASKLgGKKIqootv6eeckQAIkQAIkQAIkQAIk4IMARRRFlI9mw0NIgARIgARIgARIgARIoOgSoIiiiCq6rZ93TgIkQAIkQAIkQAIkQAI+CFBEUUT5aDY8hARIgARIgARIgARIgASKLgGKKIqootv6eeckQAIkQAIkQAIkQAIk4IMARRRFlI9mw0NIgARIgARIgARIgARIoOgSoIiiiCq6rZ93TgIkQAIkQAIkQAIkQAI+CFBEUUT5aDY8hARIgARIgARIgARIgASKLoEiL6J6N6uofrigbdFtAbxzEiABEiABEiABEiABEiABIwKFVkRVvnGYWr3pb08YlUsXU8vv7K523MGzKAuQAAmQAAmQAAmQAAmQAAmQgCqUImrG8k2qef/R2o933FUdVfs6ZbXLsyAJkAAJkAAJkAAJkAAJkEDRJVAoRdTbY5ar41/9Q/upPnFUE3X+rrW1y7MgCZAACZAACZAACZAACZBA0SVQKEXUxe/PUo/9tkj7qTaqUkoNvbS9qlWhhPYxLEgCJEACJEACJEACJEACJFA0CRQ6EfXj9DVqv6cnqq3/mD3QFtVLq4F9G6s+baqYHcjSJEACJEACJEACJEACJEACRYpAoRBRazf/rcYu2KBeG7lUPT90sbGAcj7xplVLqUPbVlW7NamgejaqoOpXLlmkGgRvlgRIgARIgARIgARIgARIIDeBAieixolYennEErVk3Ra1dN1WNVOSSExftkn9G9GTrla2uGpdq4xqLitVcPe7Zb8GqmzJHSO6Gk9LAiRAAiRAAiRAAiRAAiSQdgIFTkS9OmKpOvXN6YlxXXr7zqp6ueKJXZ8XJgESIAESIAESIAESIAESSJYARZQhf4ooQ2AsTgIkQAIkQAIkQAIkQAKFjABFlOEDpYgyBMbiJEACJEACJEACJEACJFDICBQ4ETV75WY1ZPa6xB7D4e2qqlLFd0js+rwwCZAACZAACZAACZAACZBAsgQKnIhKFhevTgIkQAIkQAIkQAIkQAIkUNQJUEQV9RbA+ycBEiABEiABEiABEiABEjAiQBFlhIuFSYAESIAESIAESIAESIAEijoBiqii3gJ4/yRAAiRAAiRAAiRAAiRAAkYEKKKMcLEwCZAACZAACZAACZAACZBAUSdAEVXUWwDvnwRIgARIgARIgARIgARIwIgARZQRLhYmARIgARIgARIgARIgARIo6gR2mDBhwr877rijKleunCpfvryqUqWK2mGHZPdB2rRpk5o6dar1bEqXLq1atWqVmue0detWtXr1autn48aNqnLlyhazsmXLpqaOrEjyBNCG7Xbyzz//WG0EPyVLloylcrjmsmXL1Pr1660fvONoq/hhW43lERSZi2zYsEFNmzbNut+aNWuqOnXqFJl7j+JG0XesWbPG+sF/433F9znO/iPoff31119q+fLlVt+D9oHvuN3/xNUHBr0HHh+MQNJt4N9//7Xa4Lp16/K+gWka5wajm/3oNIxRV65cqdauXWux//vvv1WlSpWs979ChQqJ64uwue/Qr1+/f50nRSNr0aKF6tChg2ratGnY19M635IlS9QTTzxhla1Vq5Y6//zztY6LqhDE0rhx49SoUaPU4sWLM14GH4nGjRurPffcM9AgYtKkSeqLL77Iu8Zhhx1mPY9c9tJLL1mdRdh29NFHq0aNGuU7LQbnr732mlq6dGnev++0006qT58+vi+Pwf6rr76qcG5YsWLF1KmnnqqqVq3q+5xJHLhixQqrjYwdO9bqQDIZJiratm2rdtttN6tDCdsmT56sZGJEzZgxwxqAZTK0Vbzf3bt3V9WrVw9UBdzvDz/8kHeOY489VjVo0MD4nHjn0QZs22WXXdSuu+6qdZ433nhDLVy4MK/s4Ycfrpo1a6Z1rLMQJm4+/fTTvH865ZRTLFGQzTA4fPLJJ42vo3PAZZddZr0HTvvjjz/UJ598kvdPEMZ4T6pVq6ZzyoxlcL/2hBUKoG0eeOCBRucbMWJEHjc8/zZt2mQ9/qOPPlLTp083Oj8G3aVKlbIG4rhX9ElNmjSxhIWpoZ9EfxmmXXnllYFOh8HenDlz1OjRoxXe382bN2c8H553w4YNre9B165dLR5+Df3TM888k3d4586d1d577+33dNZxW7Zsse5hypQpavbs2dbgKZNBDKL+Xbp0CTyh88svv6hhw4blXaZbt27WN9jUIPaeeuqpvMN69+5t1THt9uCDDyq0Hx1Df1K8eHFVpkwZa1CLyWl8u0uUKKFzuFaZJNqAu2KzZs2y2iH6GfTRmSzIONf9ndACo1Gobt266oQTTtAouX2ROMeo2Sq4YMECizsm1FatWpWxGNog+q8ePXpYfXgQmzdvnnr77bc9T4FFIbR7u+1jXImJvtatWwceg40ZM0ZtJ6KcNWrZsqU66KCDrBmwOC0tIgodwtdff20NjLN9EDJxQee07777qho1ahhjwwD8gw8+yDvOa1CCgo899pi16hC2YRCZaTCKxvv888/n67yzlfWqEz4AL7zwgpo7d25eUbCDyCgohhWnjz/+2BIuuoYXGh9pfKzxUQtqGLR89dVXCh2ZiaEjgwD2K6aGDBmivvzyy7xLnnbaab46x0WLFuUbxGAgtNdee2ndyrPPPqvmz5+fVxYDhAsuuMAaeJsYxOe7776bd8i5556bc0IEA6/77rvP5BLaZW+++ebtRBQOhmCEmLINg+ozzjjD1+weBhmYELENoh7cTNvjm2++aQkxfKyuvfbanIP7t956yxpkBzUIK7QRiG2IC13D5M/jjz+uW1yr3K233qpVLlMhTJrhG5Nt0JHtxHhGu+++u+rZs6fR/dvnQ581cODAvNNjQuXggw/2fR8YPH3//fdZJ48ynRgDKkzmQLSbvqv2+b755hv122+/5Z0ebeHss89WGJCaGGbM77///rxDDjjgAKttpd2CtD3cG7i3a9fOaktYKQhiSbUBu85Yuf3888+N+xfTce7EiRPVoEGDgqDKeCwmH8866yyj8yYxRnVXEH0X3kNwMTGMOfbbbz/fnmYY87z44osml8xXFt+r5s2bWxrH74T98OHD1Q79+/e3pjGyzX5h5hydUtAXzORO0yCiMGP5zjvvZF15qlixotUB4WOEZWu34SOHwU2umexMTAqCiEK93R8vtA8MwExdNdyDcHQkZ555pq9BoUkbC6ssZl3ef/99y7XTbbYLHf4d7SSTEMfHHsLD7yAC5wZDCKhsM5IQFRiwZKsDrn3kkUf66szSKKLABLPcWMU1sYIgorCCABHgXGXE4BeDYBNDf4/zYOBh28knn2x9VEwMbfqee+6x+kCdQUBYIsquo+lzTouIAjeIp6FDh2bFjZUmfH+xwm2v0rsLYzYVHgPuVUuvZxiWiILrEFYXx48fn/GSGKhggILBHtpupj4Kg6njjz/e10SO+zuESmDyEhMgmKjStaIqomw+eAYY5/lZ3Uy6DeAeMKGFiV28K5nM/r6GMc5Ni4hKaozq5Pvnn38q9OnZvF4wDkYflm2cjHNhIhkTYqZhREFFlH0fWInFNxSr8aZmiSjp1PJEFBQlBvH4Azo929AJnnfeecYDZNMK2eWTFlFYaYFrkfOFs1cOsHoAHs4OGh0wlvV+//33fMvHaDwQBCYq14+IwkudrRHbTLGa5pytR6Px+vBi2RViMZOh43z66afzufWZzmaiw4M7lN3WIMDguhn3yqffdor35LPPPst3OGbzMYMJtyiwszsG2z8bogMzdk5BBfckDGD9uFVAPA0ePDhfHfAhxEpe/fr1LXdYe2UBdYCvMtrCyJEjtxN+mBXq1auXEY60iijchKkoMBVReAfwvnoZBnr2+wlBu8cee3gdYonAbB8Vdx+B9wYTGCYTXXALRBuwDW5QhxxyiGe93AXwEbXd47By6OVK5RZRGOx6fTzhkoOPML4LaLvugdChhx6q7XrlFlEQjRAiQczU7Qv93SuvvJJv9R3Xx4Rbx44drXcW/233vWhnuHe4rOI741y1x3Fwn4THgomFIaJwH/hOwhXRaRAx6ANr165t3Yf9rcR9wB0ewhHvmlMYYpB73HHHGYcQZBJRqAv6P3g06FphEFFoN7kmU8AbzwD3ijbkfm54F0488USjlc00tAE8Ywgo53uBOEKs0uLdwHtkT+6i7wg6zsU3dObMmTmbFkQdVmZtg0dPLjdnlMN4UTf+P8kxqn1PcD2G54ZzLIO+fOedd7Ym49Ae8b2zDUyw8o4xgzv8BG6lxxxzjOeY1AndLaLAN5PnFMY9mOTDs0e/B3Zuzy3UG5NRGLeZWD4R5TwQN4tlMueFsOSORhmHJSmiABsDe7wotkFMIM7Cywcfx77++uuWP7htEFAXXXSRdsfkR0TpPJP33nsv32zhDTfcEFgUQ5Q999xz+WYXIRrhYuRlaNgYfDlZYeUAg8eCYHA/gxuZswPB4Bg/XjOg6EDwfuFjZlunTp2sNmZi7raCYzEghHseOuRcho8f4mHcIsDUHS/NIgofzwsvvFB7lc9UROk+qwceeCDPzQkrj+ecc47uoVnLud368PGAS62OYQCAQbyzj8LkhR8R/+2336pff/3VOtX//vc/Va9evZxVcIsoicn1FFHOE2LFF+3W6ToC8XjJJZdo9bFuEWU62Nbh61UGMa/OFSh8wBH/BxHq1XdgIPzTTz+pn3/+OV+/i8Ev3JJ0LQwRhRUoTAjZhpV38ISQ9pqgw/XRFpyxjBj44n31+s467zGbiAJTrKx4tUf7XIVBROH5ox3oGvoBPAOnJ42pK30a2oC7P8Mzx3fMyysmynEuvvGPPvpo3qPA+73//vvrPpqc5ZIeo6Jy6EcRU+lcbIFo6tu3r6crLcZ+iGNEGIBzZdrEhR91cIsoExdcTCBgItEZ248+C+1GZ/xqP6CsIioTJAxILr/8cqMPnt8Wk6SI+vDDD63ZPtswM4BZPq+Pgl0eM86I8cE92HbUUUep9u3ba+EoSCIKN+QcROH/EfiNAZnXYMC9imP6AdCCGVEhdBzoQJwvoOnHByIMQsqeVccA5NJLL803c5Or+uikEQhtd2IYNMAlT7ed2efGgMyZGMI0nijNIgr3iCV6dOw6VpBEVCa3Ptynl0sCPsBI2mPH4KDdwA8fq5Z+DG0QbRmrnddcc43n9yGoiEId0eYxgeHsY3VXHZMWUc6VO9wLuGHga5qMBQNHrALZgxD0uxdffLH2IwwqopBoCW7MtmHSBs8Aq0+6hraImWxnjB9mpOHap2vZRBSON3HrK4oiCowQn4h30jaTdpSWNoA2hL4bhnEaxqlek4j2/brFQFjj3ChFVNJj1ExeSEgUAZGoO04Gf4RCIGTGHsOYxjMGEVG4PibAMRnpjGVHwglMcnp5R9jtJ6eIQiF0knhRbEPGKBOXEd2O0F0uKRGF5WAsC9sGtzKsIpk0DByLFRp85G3Dg4Hbio4VNBGV6YWCSxhcw7IZBnAYyNkzYJiBhDuSbsenwzHKMsgI9d133+VdAsHREDCmhoBoDAJsw0qvbmY097vpxxXPvi46Miyz22bi2pU2EQX3IaxWOLMj6g6wC5KIwrNyD2LgxomZ/FxZHxF47cxmhpVTv1nZnANPBKfDHcLLwhBRuIa73eEDrpPNMUkRhY82ZqedSSSw+oxVaD/mfpZYedEVw0FEFFbDHnnkkbz7wODn9NNPN5rBte8X3wDE5qE+tsGtR9etxi2i4EbkzP7o9S2yr1lURRTuH99i54QERIjTDStT20xTG4DnkJ01GasI8IYxsSjGuVGJqDSMUeFSjURatsFTCxNBusLD+WzwLUI/ZhsmPjAW1DlXUBGFa2KsgLG6M5ZOJ5mbXV9PEeUe0OsORkwacKaySYkouIkgXa9tRxxxhOWj7sewGmX7HGNZGR84nSQTBU1EgY3brQ8vAFx7smVIgiuR06cYvvBB4xL8PCO/xzizIWIAAaFtEvdmXxerlkhRa4tJzAJiEOyVbQwDjocffjgvpgAdD47zaxAcuCd7VQxuXVhV0HHvSpuIQnwZBk6YYbINs4vomL2CpguaiML92Znx7HvFynm2NLnujw4md9AvmU4S2ddy9lW6YiAsEYU0xi+//HLeM9aNx0xSRGHmFe7etmELEaSo92tuIYQ4JLi06FgQEYUkEnAPtw0xEEG2uHBzwVYhEGU65hZREAAYVNsxiLorrUVZRLld8rAy7bUymqY24HSXxoD+pJNO0mk6eWWiGOdGJaKSHqNi5RtjBTumCWMVvHN+t2vB+RAS4ozXR0I299Y6mR5oGCIK53VnqcVEFL6LOuYpojC4Q2wQVgrgduDloqVzUZ0ySYgozBIixamdZQ0zMXCv8hrQZrsfCCj4e2NmBL6iuucpiCIKDLAygxUa23DPWH1z3zd86NFp2waRCrFaUAzPFAk1bIP7HNw1/RqeNzoStBNdIeZ2wTMJrM9WT/dgBKsKWF3wsjSKKHTCbpcHnZizgiiiMPjDTL4zO2SmmXysGGPG2Z5xQ1+O99PPNgx2m3C60WCvJJ0PaVgiyj2Duc8++1hpmr0sSRHljkuFsNeZWMt1T0hsg28zVmDw8df9zgQRUYhlhVuibZhE8rtFgn0ODKQQ8A2D8Lniiiu02pO730KMHfpUvP+2oW5IjJVr/FKURRRiU9CP26YT15ymNuBsO3gXMG7zmjBzvlNRjHOjEFFpGKO6hUvQ8Q+eg1vE6HrChCWiMP7CZLbtvaKzVYfdfjxFlNcHKaq/JyGi3LNhJrEUYXIoqCIKLzjEhdMtwB3ohyxbcGdxClU/+/mEydv0XEhLjCyMtgVZrTS9tl0eM/CYibftuuuuM/poZLouBjD4GNmmG5uQVhGFmWiIC6dbH2Yoc21cXRBFFJ6Xe1YYYgbxMc7AasS9QXzbFjRRED489957r/Uum2yIHpaIQjyQ05ddN7FCUiIKfv/YU8xedcaECZJhJGV+RRT6+bvvvtvK8gZDDBQESlBDchLE15q2z0wiCoMgrPjhm26bV2B/URZR7ncJe73l2isubW3AHZeNPh6TgEG2DQnanqMQUWkYo7oncOH1oJtRMBtTtCd8S2xPGCzaXHXVVZ4TQmGJKNTL3YYQl4kxkJdRRDkIuTvxJAbHqE5BFVGoOzZ6xUDcTl2LQRwGc/YMtXN1AB86ZEKB60ZBMmxO6vS51/EfD/P+0OEMGDAgLxgTs6yYCQ5qGBTDLcLOGIgP0PXXX+952rSKKFTc/dFBO4TbY7ZZyoIqonCvbnHiHDTig45VKDuTJDZYhhuZjt95tgbgjPvUjTvJVE/T7Hw4B54T4hjsfgYrDIjX1YmpTEpEQfBhsGqbSfyj50voo4BfEQUPC7iq26Y7a+xVRWQChpuQbbncUp3nyiai3IlXvNz6iqqIwsD1oYceypvYRB+JVeVclrY2gGeNe3BmysVAHBPhiK2D23KQvs6r7Wb6exQiKg1jVHcoBsSOTr/rxdDp1YCymJjxSlITpohCchtnCIDXpIt9P5aIkqDyfzFnNNmJAAAdwklEQVSQxU8Q1w4vSCZ/T2Ilyp12VuchmtyTbtmCLKJwj263Pnu5193gTfz3ddnFUc4ZxIrBOFaB4jT3YCOM5XS7/u4OUicNfppFFO7L7daXy320IIsot1sf3LqQJRN9unPlEm0Wq7/Z9n/TbcvOGUmTtPhBVqKwmoNBszMxBuprkhnTLaLQD3ntbZWNCQZmurPdbjdmk4ytus/EpJxfEYWYYcRl2BbmthR33HFH3kBYdyuAbCIK9XN/S3O59RVVEeVOqgDRAXfgXJa2NoC6uvtuZ/2RMt8e48Y1zo1CRKVhjIrVdKSGh+kIbt0+yZ2sS8ezIEwRhQUAZFy2TTfMxBJRMhNobbYLsxsb0k1jKUv3A6ELSrdcEiLq7bffVtg8zDZdH3/de9ItV9BFVCa3PuxfA79rOyU44gCQRjKuGDtd9jrl7rnnnrxZuyRcctyzgGHOaLs/qFhFRLKLXJZ2EQW3PqzCrFmzJu82snXQBVlE4ebcbn0YMGDfNWcqamSRRDbJoGZvbonVZrj/6CancIsoJCXIZXAbwzPE80M8onODVhyHPWEQCK8bC+QWUUE4IPHKjTfeqHUKtxsMVgKRWCIp8yuisD+VcxPRMNx5bAaIS7DfU90BWi4RhfO691PLNsNc1EQUsrz9+OOP+Vxi0Z6xUu+VgTltbcBuP+j/ECNoJxXJ9m7FMc6NQkSlYYx6++235/XBYbny4jm5M/7pxHmHKaLQ76D/sU03Qcl2IsrZ6DDAxQ7AmOULOmtp+qFIQkS5d7y++eabtQcGpveXq3xBF1G4N7dbH9qS7UOPwRYy93kt1YbJNKxzQSBittQ2kywuYdVh6tSpVkY223r37q3wE4a5472QIcvL3TLtIgpcMrn1YTXG7fdf0EUU7tUtUpzvns4ss047QhwUfNjhAmq6v5u7fjrXy1TG3qAW6dl1BRzOk5SIQkpgDBRs000qgcGYM2mIfbxzk0onH7gu6UxO+RVRX331lRo8eHDeJXX6CN1njJlgfDtgeL74BnuJYy8RlcmtD4kT3NnnCoOIwjgNrrrZDO0Ig0UwsVcTnGV1V3TT1gac94B7w6oGtn/wElM4LqpxbhQiKukxKlw/EQ9pm0kWTa8+wO1OpzOuCVNEucd2mJzDONXLcooo+2DMNGIPGq8ZQ6+Lmfw9CRHlzjaj48pkck+6ZQuDiMK9YrYSM1Zu082ipcsrznIYuGAmxh7AmOz/FVY93QN93Q+fzvXdWZp0ZssLgojCvbvT+Gba26swiKhM2fpw//BbxyxzroBxnTaCMhMnTlSDBg2yiiO1tcm3IaiIwsooVnCQbREfOlNLSkS5V3nhaomEHF7mTmHvVV53L0e/IsqdYtlkbyqvutsbN9vldCYyvUQUzuX+pqINwV3fuYVDYRBRXnxz/R3vE1wzvUQrzpG2NpDpvuD6i8kzxC8jHtG5D1mm8mGPc6MQUUmPUd3vCDKCYtujMMw9OYxMqxgr5rIwRRQm+e+88868y2EyAm7qXmaJKImx+BeZvvCDtKWZZihwooMOOkhhV+I4LAkR5Q5s0/0Yhc2jsIioTG59mP1D+mmdjjpsrmGdD2nw7eQLSIOPxBJxmrvj6Nq1q8LSdxjmdn3BoNsrTrKgiKhMbn1uV6TCIKLQDjLFB4S5x59TkCLDnG5qftTNLaIQ/O00uOvhWWEQgvg/27DahGQ/Omn3c70LbhEF33fsMeXHsFqSbS889/ncg324ODdr1szzsmkTUW63RN2tEDxvVAr0798/L3shXK6uvvpqz8N0RBRO4u7b3DG5RVVE4ZsMFvA60rW0tQGdeqM/iXOcG4WISnqMisljeOLYLtWYjIDLfxiG1W2scNqmMzkXpohyv/9wg8ekgpdtl50PkJB1Cb6lcD2Amncalrf8zP55VcT99yRElHsWHjE7uh9I0/vLVb6wiCjc48iRI9Unn3ySd7sFbVPdTM/JOVtqEhMRVhvBvm3YaNe2MJfUnZsI4/zIzucVF4lOBH7otukODt083IGdcNPaY489tLBhx3F7sz5s0gehnsnc+1G4V2cKi4jCvTs3oDRJQa4D3D63n5hAk8QSGPSg/3DuJo+P28EHH6zlspbpXpLKzufe10o3+2vaRJQ7QcZee+3lOzGH8/lg8hZB67bprvLriqhMbn3oJ7A/H6wwiCjEkdn3k6nt43uF/hyr0egTML7BRKCppa0NmNY/jnFuFCIqDWNUZ9wiJsNvuummUCbF3aubOrGWYYood8IuXY+pnCnO0alJ5j6FitrWunVrhYFw1JaEiMLeP4gJsS2p7EmFSUS5O1udFyPqthX0/O4ZzbgTkLh9d3UDsL3uGx8WLGfbaWLh3gCXVi9zJzM49thjjWY17fNjFRzuCrZhoKy7QqAronBut1ufM7thYRJRzo9dmAHAzr4ZbnyYMTQxExGF8yKGA+3CKaSCZKRMSkRNmTLFWoWzDRMEmCjwMkxkOlM3u8v/9ttv+TY51/Wg8OvO507VHuRZOO8FiQ4Q82GbbqydrojCeREn40yygkkAuFVCXBQGEaXLzKvNef09bW3Aq765/h7VODcKEZWGMao7Lksn+ZTO83FnBsZG8JhIyWVhiiinizquqbvC7rlPFDYGxOyQvSIFlwooz6hz7ichopD5CZvF2gYf4cMPP1zn+Wcsgw/fBx98YK3cwb8SMz863CiifCOP5UD3srPujHK2yi1evNjavBdtBKtKXpmRcB6naMD/hxG/t2rVKmuvDdt0fYLdqzt4Z/DumJo7sNQki5yJiEJwLDbhdWbrszfWo4jyfmrOD7lOGlr3GU1FFI7HKgL6ZtuNFv/mNxYwKRGF9jZw4MC8eEq4ycJdNqi5XauiFlFwtcSYwBZ2uqnIve5zzJgx1nYEtumucJmIKJzbvbJnZzeliPJ6Qv/9PU1tAK5lGzZssMJQMPFXpUoV/Rv5/5JRjHOjEFFpGKO63zc/34BMDwhjD4xBYHiOcOV1xixmOiZMEeVe5dP1uvMUUag4Aoih0myLY3PRJEQU7s85e4tMN1dccYXxC2kf4B4U6mbGoojyjTyWAzEj/sgjj+RdK1OCApOKuDMdHXLIIQobWOayoUOHKuwZYVsY8YruzHx9+/a1Niv0snnz5lkbLNvmd3DrFqcmnbOJiEI9s7n1YYYVfue26cyGefHB352udWENOr2uG9VKlD1jiAk1pDbHB8/E/IgonN89+40JKYhfbMpqYkmJKNTRuV8X/l83Q1+u+4tbRKEuzmeI54D3JEi2VayCow+xXXJxjUsvvVRrQGwqotyJV1B/uPVhVQrxrrYdcMABVqxQ2u3WW2/Nq2JcK1FpagPIEgoRBdOd+Mv0TMMe50YholDvpMeomPTFXpm2heGd5s6eqzumCktEof9BKAOeGQx9wjXXXKOVhElLRLkDx8NavsvVOSUlotx+mSYDOff9uF9KXVc2iqi0f7aU9cLZQe8YROKdgFudqeHlxQDbnmHHwBTugdhtPZehPI6zswRiA1XUAcHYfgzCEKsz9uwy0r5iJsgrHgrXcscyYH85DG5Nzb0prknCAlMRhbq5U07DLQkDcoqo7E8OHgkDBgyw2onfAYtfEYVauTebxLuHLGsmiS2SFFHu1RbdwUKudykJETVp0iTL1d82xOEgbbhfc7vZmWwdYSqiUEe3CzLaD2I5nbGmFFG5n2Za2oDTvQyD36uuusrXdzDscW5UIioNY1SMFew9P9FKdLL4ZmtNWEnEPo7OJEK6cdVhiSi3iNPdIwr3pCWiXn/9dStVJAyBZNhc0GRfDj8da1Iiyj2rjuwjmC00vV80MLif2HsjYVULbhY6Wekoovy0mHiPQRwCPt62+XX9dA+qkHkMvrg65hbputlkMp3bPbA1jXNAJ4h3FgYBiBVcnb1q7LqgI8UssD2jaBrn5UdEwa0P9XamvsVqsXPVnStR+VuLc3Ud21706tVLp6nmKxNEREHE4ZkhuYptpmIuSREFtyHMJDv3rwkae5uEiMJ37dFHH8337pi43zobBJ4pVvbhsmmbzkabdlk/IgrHutuh+92niMr9aqelDbhFhY4nR6Y7C3ucG5WISsMY1Z1Mqnr16lZsoek4Gc/B7VVjMlYOQ0RhQhCr4HCVtM0krttTRGFzNgxu7BlqpMPE7vBRW1IiCvfl3s8DPtPoUHXimXA8PpDYNNAZCK3r343jKaKibl3Bz48PCGZj7MEc2gZePCxt6xqy0b3wwgt5QhvHwa0E2eV0LNN+QLrBkM7zjxgxwtr3wzZkbsLEgcnKmtufWGejPGcd3LuVm4o4PyIK13e7iLm5U0TlJ/L5558rZJmD6e5z5GYaREThXO4EJPg3XddTlE1SROH67hl8rPZi5TbXJqnZ+gPEWWFFCAMr26KOibKv4353kOkSafRN3PrQjyL7Ir55tmFVC/2g7vfWr4jKtp+aXQ+KKO+vUBragDtOCDFRGKOiPepaFOPcqEQU7inpMSo8YF588UU1Z86cPMRIAnXggQdqLRTYByGZDMSrc1LJZCuOoCIK94GxD7JI24ZkFsjMrdv/5BRRmBVGJjJnB20HYOs2Tr/lkhRRmBHDLBtmDW3DCgEC5r1m13EsGjhS89oGVwEEqeluckkR5bfVxHucO+YNLx0+vBDdXoaOHwHOzuQG2KcDQszEMu0HhFUxZLbzilXBhwMDGAzqnOYnDT3uBxMHtnshZqSQtQ2rY14G1xp0ZFgZgoEj3heT7QX8iihcDwycnaizvhRR+Z8eVgwwOWS6Uug8S1ARhXO5XTHhznrRRRdpDZySFlGZ6o82v+uuu1rZ+nRmc/Gu4OP966+/5huA4NxxiahM7w7qjphI9IFegxB849977z2FGAvbICghznWS69jH+BVRON7t1udspxRRXj33tr+7+88k2oA7sxtWRpDwSWc7nqjGuVGKqKTHqHju+A4gNsq5DRJ4Y2Xdy70anie//PKL+vHHH/PGDDgn9qFFfLeuBRFRmIyDe7iz/0HbxQQO3Il1LZ+IwgAI2THQuUEh4o/2wAYnxEwZfB+9Okfdi+cql6SIQr3gZoVUyPagEP+G/RSgtjEwdAsivIjgBTcvp/iCaxN2dPdqVE4WFFF6LQhCFR9h27B6gQ9fnOZOl41royPBIAKiyD0gQscD9xvnzCuOwUt7+umne4r0TPfmTkyBMpiNw+onhAhcUu13Fp0XfI8RwP3DDz/kE3E4Du0bAsyPuVejcA74FmMz4KZNm+aJOqxqo5/BRwbvi3MLBRzjZ/PgICIqk1ufff8UUf+1BOf+ZH7dV3G2MEQUZi4Rl+jM1qebuMctonCcn2ySzncEotJkBQYDD6RtdyZSwPnwXcGEHfqymjVrKohD2zDpgXdl6tSpavLkyduJJ7zj6HcgYnSEmN8U5877xrcOM8nudxjvOwZEmNWFe45tuG8MWmbOnKl+/vnnfKvwcHWHS6DpZspBRFSm9mjX1UREJfktSiqxhM0pDW0A3xLERtnu4Hbd8A3G+41Nre13Ka5xbpQiCveX5BjV5gvXd2SgtsNW8O+YvIUnCjINow9zLjzgu48JVySRcq5i4TiMVRBX6bVQ4ex/3CIKY/NM3kAY96Cd4gfjHywMQeM4DX0mFokwZjExS0Tddddd/yIgHR8k9+a69skAAzfo7NRNLmRaNmkRhfpihh6DdPceHfhY4aMJYWQPSt0vL45HY8DA2ETV4jiKKL3W4g4GRCY5uPXEaeiQ8RFH2me34aXEjCp+0D7QqToFtl0e7QhC2yuZRK77gjscVnPsncSdZZEmFAM8/A0DGGeHZ5dDXbEUj31//BruDQNDuClmMvQd9n4szskJZ1kMvrASppPQwnlcEBGF82BQh9lMt1FE/UfE6Qfvx23UPlMYIgrncu/rgX/TSQTkFlF+27vzOJNYRvs4fFe+++67jH2HXQYDEggrJG/J9N7a5dDHYOZd1xUYx4UhonAe3AfcPLOt5qJfw/YemD1HH5jp3cf39Jhjjsm5UWy25xRURGVz6zMRUUl+i5IWUWloA6gDBsbIfpltDIt3Kc5xbtQiCvec1BjV+S6CO/p054SW/XdMjGArB/RhixYt2m7ixy7ndwN1t4jy25f7FVC4niWi+vXr92+ui2Nwuv/++2u7o/m9EedxaRBRqA+W/OA64oxv0rm/5s2bWzOCJrOT9nkponQIKyvZCWZBbUtCRNnXRpzI999/n7WTyHRH6GCw6oJZG79Z9ZznRYeCwYxzeVqHJDo5DMBM3OeynRcDPaxIIc7K1MACLoA6yVfc5w4qonC+TG59FFH/kbY3mTZJ/5qpDYQlonBu98bXWPXA3ku5RHhaRJTNBun28d6afmNwPO4XyT0wCPHaU8X9LMISUfZ58c7DPSfTYCpXX4CZX7jK++0Dg4oo1C2TWzRFlGkPrqx+P4k2YNcUqwyfffZZvnAK3bsIe5wbh4hKaozqZoqwBHw/7QR0uswhrjB527FjR91D8pULKqLQZyJDKlbNsVDkx7KKKMwaI90vBjYI9Izb0iKicN+YOcOHDllE8DubYfCHVSf4tWMp069RROmRS5OIQo0xA4ZUvRBUuYQMXly8W2gnJm6eelS2CX+ka4XbT7YVH6ySYtkb73eQtpqtTlgqRzvGagHckLIZBrvoxLAvFmar/VoYIiqTWx9F1LYngtWGe+65x1pJhbsqYtb8WpgiCkIACV6cK7xeLqlpE1E2R7i3wEUH74zTjd7NGd9mrNhiKwG4Kum47mV6VmGLKLudoP74VrpdFZ11wOoUXCjR/8DdOIiFIaJw/bfffttyk7SNIsrfU0FfEXcbcNd0ypQpVh0wXsv1/YlynBuXiMK9xz1GzdYycM9499GPZfK6sY9DaBDeffRhJu577uuaiCgINqx4Y+LJdr+GeAvqXWeJKLnhf+FuhJtBRhNcAL7Mfjtnf69e/qPSJKKcNUPDwMcHyhs/+H/En+BDgN9+ZtHD4FWUzwE3EsyCmAYlRskM75PdRtBeMHuPNoIfvF9xxBVC1MEH2f7B/druhfhtOnPthxc+qJgdxOw0fuCWhM4MHRl+8M4E6UT91InHmBPAc4PLKAwrlogxoEVDAO+M3XfgN94bTDbY32ZMNhSE7wzi1hBHh/4HfSDuwe5/EF9cEO7B9Amn8Vtkeg9hlk+6DUBcYEITrqT4JqdtnBsma/tcaRijInQAfZf9/mNSCO+8/f7rJlmLgk8U59xBGlpOd74oLup1zrSKKK968+/xE/j666+tuAK4T+62227xV4BXJAESIAESKPIE+C0q8k2AAIogAYqoIvjQC8stOzffRHIG0yQehYUD74MESIAESCA5AvwWJceeVyaBJAlQRCVJn9f2TQBuLggsRzY4iCeIKBoJkAAJkAAJxEmA36I4afNaJJAuAhRR6XoerI0mASQvwN4Q2FjvtNNOs2KNaCRAAiRAAiQQJwF+i+KkzWuRQLoIUESl63mwNgYEkH0HmV6STIJiUF0WJQESIAESKIQE+C0qhA+Vt0QCGgQoojQgsQgJkAAJkAAJkAAJkAAJkAAJ2AQootgWSIAESIAESIAESIAESIAESMCAQCpF1NatWxXSnMOwn02NGjUMbolFSYAESIAESIAESIAESIAESCA6AqkUUdHdLs9MAiRAAiRAAiRAAiRAAiRAAsEIUEQF48ejSYAESIAESIAESOD/2q9DAgAAAIRh/VsTAvkFQDAUBAgQiAk4UbHB1SVAgAABAgQIECBA4BNwoj4/aQIECBAgQIAAAQIEYgJOVGxwdQkQIECAAAECBAgQ+AScqM9PmgABAgQIECBAgACBmIATFRtcXQIECBAgQIAAAQIEPgEn6vOTJkCAAAECBAgQIEAgJuBExQZXlwABAgQIECBAgACBT8CJ+vykCRAgQIAAAQIECBCICThRscHVJUCAAAECBAgQIEDgE3CiPj9pAgQIECBAgAABAgRiAk5UbHB1CRAgQIAAAQIECBD4BJyoz0+aAAECBAgQIECAAIGYgBMVG1xdAgQIECBAgAABAgQ+ASfq85MmQIAAAQIECBAgQCAm4ETFBleXAAECBAgQIECAAIFPwIn6/KQJECBAgAABAgQIEIgJOFGxwdUlQIAAAQIECBAgQOATcKI+P2kCBAgQIECAAAECBGICTlRscHUJECBAgAABAgQIEPgEnKjPT5oAAQIECBAgQIAAgZiAExUbXF0CBAgQIECAAAECBD4BJ+rzkyZAgAABAgQIECBAICbgRMUGV5cAAQIECBAgQIAAgU/Aifr8pAkQIECAAAECBAgQiAk4UbHB1SVAgAABAgQIECBA4BNwoj4/aQIECBAgQIAAAQIEYgJOVGxwdQkQIECAAAECBAgQ+AScqM9PmgABAgQIECBAgACBmIATFRtcXQIECBAgQIAAAQIEPgEn6vOTJkCAAAECBAgQIEAgJuBExQZXlwABAgQIECBAgACBT8CJ+vykCRAgQIAAAQIECBCICThRscHVJUCAAAECBAgQIEDgE3CiPj9pAgQIECBAgAABAgRiAk5UbHB1CRAgQIAAAQIECBD4BJyoz0+aAAECBAgQIECAAIGYgBMVG1xdAgQIECBAgAABAgQ+ASfq85MmQIAAAQIECBAgQCAm4ETFBleXAAECBAgQIECAAIFPwIn6/KQJECBAgAABAgQIEIgJOFGxwdUlQIAAAQIECBAgQOATcKI+P2kCBAgQIECAAAECBGICTlRscHUJECBAgAABAgQIEPgEnKjPT5oAAQIECBAgQIAAgZiAExUbXF0CBAgQIECAAAECBD4BJ+rzkyZAgAABAgQIECBAICbgRMUGV5cAAQIECBAgQIAAgU/Aifr8pAkQIECAAAECBAgQiAk4UbHB1SVAgAABAgQIECBA4BNwoj4/aQIECBAgQIAAAQIEYgJOVGxwdQkQIECAAAECBAgQ+AScqM9PmgABAgQIECBAgACBmIATFRtcXQIECBAgQIAAAQIEPgEn6vOTJkCAAAECBAgQIEAgJuBExQZXlwABAgQIECBAgACBT8CJ+vykCRAgQIAAAQIECBCICThRscHVJUCAAAECBAgQIEDgE3CiPj9pAgQIECBAgAABAgRiAk5UbHB1CRAgQIAAAQIECBD4BJyoz0+aAAECBAgQIECAAIGYgBMVG1xdAgQIECBAgAABAgQ+ASfq85MmQIAAAQIECBAgQCAm4ETFBleXAAECBAgQIECAAIFPwIn6/KQJECBAgAABAgQIEIgJOFGxwdUlQIAAAQIECBAgQOATcKI+P2kCBAgQIECAAAECBGICTlRscHUJECBAgAABAgQIEPgEnKjPT5oAAQIECBAgQIAAgZiAExUbXF0CBAgQIECAAAECBD4BJ+rzkyZAgAABAgQIECBAICbgRMUGV5cAAQIECBAgQIAAgU/Aifr8pAkQIECAAAECBAgQiAk4UbHB1SVAgAABAgQIECBA4BNwoj4/aQIECBAgQIAAAQIEYgJOVGxwdQkQIECAAAECBAgQ+AScqM9PmgABAgQIECBAgACBmIATFRtcXQIECBAgQIAAAQIEPgEn6vOTJkCAAAECBAgQIEAgJuBExQZXlwABAgQIECBAgACBT8CJ+vykCRAgQIAAAQIECBCICThRscHVJUCAAAECBAgQIEDgE3CiPj9pAgQIECBAgAABAgRiAk5UbHB1CRAgQIAAAQIECBD4BJyoz0+aAAECBAgQIECAAIGYgBMVG1xdAgQIECBAgAABAgQ+ASfq85MmQIAAAQIECBAgQCAm4ETFBleXAAECBAgQIECAAIFPwIn6/KQJECBAgAABAgQIEIgJOFGxwdUlQIAAAQIECBAgQOATcKI+P2kCBAgQIECAAAECBGICTlRscHUJECBAgAABAgQIEPgEnKjPT5oAAQIECBAgQIAAgZiAExUbXF0CBAgQIECAAAECBD4BJ+rzkyZAgAABAgQIECBAICbgRMUGV5cAAQIECBAgQIAAgU/Aifr8pAkQIECAAAECBAgQiAk4UbHB1SVAgAABAgQIECBA4BNwoj4/aQIECBAgQIAAAQIEYgJOVGxwdQkQIECAAAECBAgQ+AScqM9PmgABAgQIECBAgACBmIATFRtcXQIECBAgQIAAAQIEPgEn6vOTJkCAAAECBAgQIEAgJuBExQZXlwABAgQIECBAgACBT2B26x7cOxGXFAAAAABJRU5ErkJggg=='; //phpcs:ignore

    private const GET_ROUTE = 'new.user.signature_image.get';
    private const UPDATE_ROUTE = 'new.user.signature_image.update';
    private const DELETE_ROUTE = 'new.user.signature_image.delete';

    public function testSaveAndGetSignature()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createManager($account);
        $this->be($user);
        $response = $this->get(
            $account->route(self::GET_ROUTE)
        );
        $this->assertEquals(
            Response::HTTP_NO_CONTENT,
            $response->status()
        );

        $updateResponse = $this->post(
            $account->route(self::UPDATE_ROUTE),
            ['image' => self::SIGNATURE_STRING_PNG]
        );

        $this->assertEquals(
            Response::HTTP_OK,
            $updateResponse->status()
        );

        $getSigResponse = $this->get(
            $account->route(self::GET_ROUTE),
        );

        $this->assertEquals(self::SIGNATURE_STRING_PNG, $getSigResponse->streamedContent());
    }

    public function testDeleteSignature()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createManager($account);
        $this->be($user);

        $updateResponse = $this->post(
            $account->route(self::UPDATE_ROUTE),
            ['image' => self::SIGNATURE_STRING_PNG]
        );
        $updateResponse->assertOk();

        $deleteResponse = $this->post(
            $account->route(self::DELETE_ROUTE),
        );
        $deleteResponse->assertOk();

        $response = $this->get(
            $account->route(self::GET_ROUTE),
        );
        $response->assertStatus(Response::HTTP_NO_CONTENT);
    }
}
