<?php

namespace Tests\Feature\User;

use App\Context;
use App\Http\Responses\Response;
use App\User;
use Tests\Support\Factories\ContextFactory;
use Tests\Support\Factories\MembershipFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class ApiAddTest extends TestCase
{
    public function testCreateUser()
    {
        $mainContext = Context::findOrFail(1);
        $user = UserFactory::createManager($mainContext->account);
        $context = ContextFactory::createChild($mainContext);
        MembershipFactory::create($user, $context);
        $this->be($user);

        $userData = [
            'firstname' => 'Test',
            'lastname' => '123',
            'group_id' => $context->id,
            'email' => $this->faker->unique()->email,
            'is_external' => true,
            'external_id' => 'test_external_id',
            'mobile' => '**********'
        ];

        $response = $this->put('https://' . $user->account->hostname . '/api/v1/user/add', $userData);
        $response->assertOk();
        $createdUser = User::where('auth_id', $userData['email'])->first();
        $this->assertTrue((bool)$createdUser->is_external);
        $this->assertEquals($userData['firstname'], $createdUser->firstname);
        $this->assertEquals($userData['lastname'], $createdUser->lastname);
        $this->assertEquals($userData['email'], $createdUser->auth_id);
        $this->assertEquals($userData['external_id'], $createdUser->external_id);
        $this->assertEquals('+***********', $createdUser->mobile);
    }

    public function testIsExternalIsStringTrue()
    {
        $mainContext = Context::findOrFail(1);
        $user = UserFactory::createManager($mainContext->account);
        $context = ContextFactory::createChild($mainContext);
        MembershipFactory::create($user, $context);
        $this->be($user);
        $userData = [
            'firstname' => 'Test',
            'lastname' => '123',
            'group_id' => $context->id,
            'email' => $this->faker->unique()->email,
            'is_external' => 'true',
            'external_id' => $this->faker->uuid
        ];

        $response = $this->put('https://' . $user->account->hostname . '/api/v1/user/add', $userData);
        $response->assertOk();
        $createdUser = User::where('auth_id', $userData['email'])->first();
        $this->assertTrue((bool)$createdUser->is_external);
        $this->assertEquals($userData['firstname'], $createdUser->firstname);
        $this->assertEquals($userData['lastname'], $createdUser->lastname);
        $this->assertEquals($userData['email'], $createdUser->auth_id);
        $this->assertEquals($userData['external_id'], $createdUser->external_id);
    }

    public function testIsExternalIsStringFalse()
    {
        $mainContext = Context::findOrFail(1);
        $user = UserFactory::createManager($mainContext->account);
        $context = ContextFactory::createChild($mainContext);
        MembershipFactory::create($user, $context);
        $this->be($user);
        $userData = [
            'firstname' => 'Test',
            'lastname' => '123',
            'group_id' => $context->id,
            'email' => '<EMAIL>',
            'is_external' => 'false'
        ];
        $response = $this->put('https://' . $user->account->hostname . '/api/v1/user/add', $userData);
        $response->assertOk();
        $createdUser = User::where('auth_id', $userData['email'])->first();
        $this->assertFalse((bool)$createdUser->is_external);
        $this->assertEquals($userData['firstname'], $createdUser->firstname);
        $this->assertEquals($userData['lastname'], $createdUser->lastname);
        $this->assertEquals($userData['email'], $createdUser->auth_id);
    }
}