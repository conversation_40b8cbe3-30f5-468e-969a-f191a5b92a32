<?php

namespace Tests\Feature\User;

use App\Http\Responses\Response;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\OldTestCase;

class SearchTest extends OldTestCase
{
    public function testSearchUser()
    {
        $this->markTestSkipped('Failing for no reason during pipelines');
        $account = AccountFactory::create();
        $colleague = UserFactory::createColleague($account);
        $colleague->firstname = 'Brad';
        $colleague->save();
        $user = UserFactory::createManager($account);
        $this->be($user);
        $response = $this->get(
            'https://' . $user->account->hostname . '/member/index?page=1&search=Brad',
        );
        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );

        $searchUser = json_decode($response->getContent(), 1)['data'][0];
        $this->assertEquals($searchUser['id'], $colleague->id);
    }
}