<?php

namespace Tests\Feature\Company;

use App\Company;
use App\Http\Responses\Response;
use Illuminate\Http\Testing\FileFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\OldTestCase;

class MassCreateTest extends OldTestCase
{
    /**
     * throws an error if the company does not belong to that account
     * @throw InvalidCompanyException
     * @group ignore
     */
    public function testUpdateFromAnotherAccountThrowsError()
    {
        $this->markTestSkipped('Deprecated');

        $company = CompanyFactory::create();
        $content = file_get_contents($this->getAssetsPath('csv/company/update_from_another_account.csv'));
        $content = str_replace(':id', $company->id, $content);
        $file = (new FileFactory())->createWithContent('csv.csv', $content);
        $user = UserFactory::createManager();
        $this->be($user);
        $response = $this->post(
            'https://' . $user->account->hostname . '/company/mass',
            [
                'csv' => $file
            ]
        );

        $this->assertEquals(
            Response::HTTP_BAD_REQUEST,
            $response->status()
        );
    }

    /**
     * Check if the company was created.
     * @group ignore
     */
    public function testCreate()
    {
        $this->markTestSkipped('Deprecated');

        $name = $this->faker->name;
        $content = file_get_contents($this->getAssetsPath('csv/company/create.csv'));
        $content = str_replace(':name', $name, $content);
        $file = (new FileFactory())->createWithContent('csv.csv', $content);

        $user = UserFactory::createManager();
        $this->be($user);
        $response = $this->post(
            'https://' . $user->account->hostname . '/company/mass',
            [
                'csv' => $file
            ]
        );

        // Check if the company was created.
        $exists = Company::query()
            ->where([
                'account_id' => $user->account_id,
                'name' => $name
            ])->exists();

        // Make assertion
        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );
        $this->assertTrue($exists);
    }

    /**
     *  Checks if company can be created with only the required columns
     * @group ignore
     */
    public function testWithOnlyRequiredColumns()
    {
        $this->markTestSkipped('Deprecated');

        $name = $this->faker->name;
        $content = file_get_contents($this->getAssetsPath('csv/company/only_required_columns.csv'));
        $content = str_replace(':name', $name, $content);
        $file = (new FileFactory())->createWithContent('csv.csv', $content);

        $user = UserFactory::createManager();
        $this->be($user);
        $response = $this->post(
            'https://' . $user->account->hostname . '/company/mass',
            [
                'csv' => $file
            ]
        );

        // Check if the company was created.
        $exists = Company::query()
            ->where([
                'account_id' => $user->account_id,
                'name' => $name
            ])->exists();

        // Make assertion
        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );
        $this->assertTrue($exists);
    }

    /**
     * Checks if csv can be uploaded with columns in a different order
     * @group ignore
     */
    public function testColumnsInDifferentOrder()
    {
        $this->markTestSkipped('Deprecated');

        $name = $this->faker->name;
        $content = file_get_contents($this->getAssetsPath('csv/company/columns_in_different_order.csv'));
        $content = str_replace(':name', $name, $content);
        $file = (new FileFactory())->createWithContent('csv.csv', $content);

        $user = UserFactory::createManager();
        $this->be($user);
        $response = $this->post(
            'https://' . $user->account->hostname . '/company/mass',
            [
                'csv' => $file
            ]
        );

        // Check if one company was created even with the order of the columns changed
        $exists = Company::query()
            ->where([
                'account_id' => $user->account_id,
                'name' => $name
            ])->exists();

        // Make assertion
        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );
        $this->assertTrue($exists);
    }

    /**
     * Checks if company can be created if there are additional columns in the csv
     * @group ignore
     */
    public function testAdditionalColumns()
    {
        $this->markTestSkipped('Deprecated');

        $name = $this->faker->name;
        $content = file_get_contents($this->getAssetsPath('csv/company/additional_columns.csv'));
        $content = str_replace(':name', $name, $content);
        $file = (new FileFactory())->createWithContent('csv.csv', $content);

        $user = UserFactory::createManager();
        $this->be($user);
        $response = $this->post(
            'https://' . $user->account->hostname . '/company/mass',
            [
                'csv' => $file
            ]
        );

        // Check if one company was created even with an additional column
        $exists = Company::query()
            ->where([
                'account_id' => $user->account_id,
                'name' => $name
            ])->exists();

        // Make assertion
        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );
        $this->assertTrue($exists);
    }

    /**
     * Check if a csv file can update and insert a company
     * @group ignore
     */
    public function testUpdateAndInsert()
    {
        $this->markTestSkipped('Deprecated');

        $company = CompanyFactory::create();
        $name = $this->faker->name;
        $content = file_get_contents($this->getAssetsPath('csv/company/update_and_insert.csv'));
        $content = str_replace(':name', $name, $content);
        $content = str_replace(':id', $company->id, $content);
        $file = (new FileFactory())->createWithContent('csv.csv', $content);

        $user = UserFactory::createManager($company->account);
        $this->be($user);
        $response = $this->post(
            'https://' . $user->account->hostname . '/company/mass',
            [
                'csv' => $file
            ]
        );

        // Check if company with IDs is updated
        $updateExists = Company::query()
            ->where([
                'account_id' => $user->account_id,
                'id' => $company->id
            ])->exists();

        // Check if company with empty IDs is created
        $createExist = Company::query()
            ->where([
                'account_id' => $user->account_id,
                'name' => $name
            ])->exists();

        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );

        $this->assertTrue($updateExists && $createExist);
    }

    /**
     * Check if a company can be created with multiple fiscal & vat numbers
     * @group ignore
     */
    public function testMultipleFiscalVatNumbers()
    {

        $this->markTestSkipped('Deprecated');

        $name = $this->faker->name;
        $content = file_get_contents($this->getAssetsPath('csv/company/multiple_fiscal_vat_numbers.csv'));
        $content = str_replace(':name', $name, $content);
        $file = (new FileFactory())->createWithContent('csv.csv', $content);

        $user = UserFactory::createManager();
        $this->be($user);
        $response = $this->post(
            'https://' . $user->account->hostname . '/company/mass',
            [
                'csv' => $file
            ]
        );

        // Check if the company was created.
        $exists = Company::query()
            ->where([
                'account_id' => $user->account_id,
                'name' => $name
            ])->exists();

        // Make assertion
        $this->assertEquals(Response::HTTP_OK, $response->status());
        $this->assertTrue($exists);
    }

    /**
     * Test if the csv import can handle the csv export
     * @group ignore
     */
    public function testExportImport()
    {
        $this->markTestSkipped('Deprecated');

        $user = UserFactory::createManager();
        $company1 = CompanyFactory::createCompanyWithName($user->account, 'Company1');
        $company2 = CompanyFactory::createCompanyWithName($user->account, 'Company2');
        $company3 = CompanyFactory::createCompanyWithName($user->account, 'Company3');
        $this->be($user);
        $response = $this->get('https://' . $user->account->hostname . '/company/export/companies.csv');
        $fileContent = str_replace($company2->name, 'replacementCompany', $response->getContent());
        $file = (new FileFactory())->createWithContent('csv.csv', $fileContent);
        $response = $this->post(
            'https://' . $user->account->hostname . '/company/mass',
            [
                'csv' => $file
            ]
        );

        $newExport = $this->get('https://' . $user->account->hostname . '/company/export/companies.csv');

        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );

        $this->assertTrue(strpos($newExport->getContent(), 'replacementCompany') !== false);
    }

    /**
     * Test if company can be created with multiple KVK numbers
     * @group ignore
     */
    public function testMultipleKvKNumbers()
    {
        $this->markTestSkipped('Deprecated');
        $name = $this->faker->name;
        $content = file_get_contents($this->getAssetsPath('csv/company/multiple_kvk_numbers.csv'));
        $content = str_replace(':name', $name, $content);
        $file = (new FileFactory())->createWithContent('csv.csv', $content);

        $user = UserFactory::createManager();
        $this->be($user);
        $response = $this->post(
            'https://' . $user->account->hostname . '/company/mass',
            [
                'csv' => $file
            ]
        );

        // Check if the company was created.F
        $exists = Company::query()
            ->where([
                'account_id' => $user->account_id,
                'name' => $name
            ])->exists();

        // Make assertion
        $this->assertEquals(Response::HTTP_OK, $response->status());
        $this->assertTrue($exists);
    }
}