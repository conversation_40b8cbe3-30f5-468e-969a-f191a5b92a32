<?php

namespace Tests\Feature\Declarations;

use App;
use App\AccountService;
use App\Company;
use App\CompanyIdentifier;
use App\Exceptions\PreconditionFailedException;
use App\Exceptions\UnauthorizedException;
use App\Factories\TaskFileFactory;
use App\Http\Responses\Response;
use App\Mail\ServiceTask\VatApproval\VatApprovalMail;
use App\Models\CompanyUploadTypePermission;
use App\Models\DefaultCompanyUserPermission;
use App\Models\Mongo\TrackingPixel;
use App\Service;
use App\Services\TaskFiles\TaskFilePrepService;
use App\ServiceTask;
use App\ServiceTaskResponse;
use App\User;
use Tests\Support\Builders\AccountServiceBuilder;
use Tests\Support\Builders\ServiceTaskBuilder;
use Tests\Support\Builders\ServiceTaskGroupBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\AccountServiceUploadTypeFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyIdentifierFactory;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\CompanyUserPermissionFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class OpenNewTest extends TestCase
{
    private const SEND_SINGLE_ROUTE = 'new.task.send_single';

    public function testSendToUser(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()->setCompany($company)->setAccountService($accountService)->setVat()->build();
        $manager = UserFactory::createManager($account);
        CompanyUserFactory::create($company, $manager);

        $user = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $user);

        $companyIdentifier = CompanyIdentifier::query()->where([
            'account_id' => $account->id,
            'identifier' => $task->getIdentifier(),
        ])->firstOrFail();
        CompanyUserPermissionFactory::create($companyIdentifier, $companyUser);

        $this->be($manager);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'approve']
                        ]
                    ]
                ]
            ]
        );

        // Assert Equals.
        $this->assertEquals(Response::HTTP_OK, $response->status());

        $serviceTask = $task->refresh();

        $this->assertEquals(App\ServiceTask::STATUS_SENT, $serviceTask->status);
        $this->assertEquals($accountService->getLogiusConnection(), $task->getLogiusConnection());

        $serviceTaskResponse = ServiceTaskResponse::query()
            ->where([
                'user_id' => $user->id,
                'task_id' => $task->id,
            ])
            ->first();

        $this->assertNotNull($serviceTaskResponse);
        $trackingPixel = TrackingPixel::query()
            ->where('service_task_response_id', $serviceTaskResponse->id)
            ->first();

        $this->assertNotNull($trackingPixel);
        $this->assertEquals(VatApprovalMail::class, $trackingPixel->email_type);
    }

    public function testSendToUsers(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()->setCompany($company)->setAccountService($accountService)->setVat()->build();
        $accountServiceCompany = $task->getAccountServiceCompany();
        $manager = UserFactory::createManager($account);
        CompanyUserFactory::create($company, $manager);

        $user1 = UserFactory::createClientUser($account);
        $user2 = UserFactory::createClientUser($account);
        $companyUser1 = CompanyUserFactory::create($company, $user1);
        $companyUser2 = CompanyUserFactory::create($company, $user2);
        $companyIdentifier = CompanyIdentifier::query()->where([
            'account_id' => $account->id,
            'identifier' => $task->getIdentifier(),
        ])->firstOrFail();
        CompanyUserPermissionFactory::create($companyIdentifier, $companyUser1);
        CompanyUserPermissionFactory::create($companyIdentifier, $companyUser2);

        $this->be($manager);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser1->user_id, 'permission' => 'approve'],
                            ['user_id' => $companyUser2->user_id, 'permission' => 'approve']
                        ]
                    ]
                ]
            ]
        );

        // Assert Equals.
        $this->assertEquals(Response::HTTP_OK, $response->status());

        $serviceTask = $task->refresh();

        $this->assertEquals(App\ServiceTask::STATUS_SENT, $serviceTask->status);

        $this->assertEquals($accountService->getLogiusConnection(), $serviceTask->getLogiusConnection());

        $serviceTaskResponse1 = ServiceTaskResponse::query()
            ->where([
                'user_id' => $user1->id,
                'task_id' => $serviceTask->id,
            ])
            ->first();
        $serviceTaskResponse2 = ServiceTaskResponse::query()
            ->where([
                'user_id' => $user2->id,
                'task_id' => $serviceTask->id,
            ])
            ->first();

        $this->assertNotNull($serviceTaskResponse1);
        $this->assertNotNull($serviceTaskResponse2);
        $trackingPixel1 = TrackingPixel::query()
            ->where('service_task_response_id', $serviceTaskResponse1->id)
            ->first();
        $trackingPixel2 = TrackingPixel::query()
            ->where('service_task_response_id', $serviceTaskResponse2->id)
            ->first();

        $this->assertNotNull($trackingPixel1);
        $this->assertNotNull($trackingPixel2);
        $this->assertEquals(VatApprovalMail::class, $trackingPixel1->email_type);
        $this->assertEquals(VatApprovalMail::class, $trackingPixel2->email_type);
    }

    public function testSendToUsersWithTaskFromDifferentAccount(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()->setCompany($company)->setAccountService($accountService)->setVat()->build();
        $user = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $user);

        $companyIdentifier = CompanyIdentifier::query()->where([
            'account_id' => $account->id,
            'identifier' => $task->getIdentifier(),
        ])->firstOrFail();
        CompanyUserPermissionFactory::create($companyIdentifier, $companyUser);

        $manager = UserFactory::createManager();

        $this->be($manager);
        $response = $this->post(
            $manager->account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'approve']
                        ]
                    ]
                ]
            ]
        );

        // Assert Equals.
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->status());
        $this->assertInstanceOf(UnauthorizedException::class, $response->exception);
    }

    public function testSendToUsersWithoutUsers(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()->setCompany($company)->setAccountService($accountService)->setVat()->build();

        $manager = UserFactory::createManager($account);

        CompanyUserFactory::create($company, $manager);

        $this->be($manager);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                        ]
                    ]
                ]
            ]
        );

        // Assert Equals.
        $this->assertEquals(Response::HTTP_PRECONDITION_FAILED, $response->status());
        $this->assertInstanceOf(PreconditionFailedException::class, $response->exception);
    }

    public function testSendToUserWithInformAndNoneRequestingParty(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_TASKS_PROVIDER)
            ->setLogiusConnection(AccountService::NONE)
            ->build();

        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();
        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);

        $this->be($internal);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'inform']
                        ]
                    ]
                ]
            ]
        );

        // Assert Equals.
        $this->assertEquals(Response::HTTP_OK, $response->status());
        $serviceTask = $task->refresh();
        $this->assertEquals(ServiceTask::STATUS_COMPLETED, $serviceTask->status);
    }

    public function testSendToUserWithInformAutomaticBelastingdienstPartyDocumentApproval(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_TASKS_PROVIDER)
            ->setLogiusConnection(AccountService::BELASTINGDIENST_AUTOMATIC)
            ->build();
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setType(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();
        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);

        $this->be($internal);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'inform']
                        ]
                    ]
                ]
            ]
        );

        // Assert Equals.
        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );

        $task = $task->refresh();

        $this->assertEquals(
            ServiceTask::STATUS_COMPLETED,
            $task->status
        );

        $createdPermission = DefaultCompanyUserPermission::query()
            ->where([
                    'account_id' => $account->id,
                    'company_id' => $company->id,
                    'company_user_id' => $companyUser->id
                ])->get();
        $this->assertEquals(1, count($createdPermission));
        $this->assertEquals('inform', $createdPermission[0]->permission, 'Upload Type Permissions should be set to inform for this user.');
    }

    public function testSendToUserWithApproveForUploadType(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_TASKS_PROVIDER)
            ->setLogiusConnection(AccountService::BELASTINGDIENST_AUTOMATIC)
            ->build();
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setType(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();

        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);

        $uploadType = AccountServiceUploadTypeFactory::create('Document', $accountService);

        $task->refresh();
        $task->getFiles()[0]->account_service_upload_type_id = $uploadType->id;
        $task->push();

        $this->be($internal);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'approve']
                        ]
                    ]
                ]
            ]
        );

        // Assert Equals.
        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );

        $task = $task->refresh();

        $this->assertEquals(
            ServiceTask::STATUS_SENT,
            $task->status
        );

        $createdPermission = CompanyUploadTypePermission::query()
            ->where([
                    'account_service_upload_type_id' => $uploadType->id,
                    'account_id' => $account->id,
                    'company_id' => $company->id,
                    'company_user_id' => $companyUser->id
                ])->get();

        $this->assertEquals(1, count($createdPermission));

        $this->assertEquals('approve', $createdPermission[0]->permission, 'Upload Type Permissions should be set to approve for this user.');
    }

    public function testSendToUserWithInformAndBelastingdienstManualAsRequestingParty(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_TASKS_PROVIDER)
            ->setLogiusConnection(AccountService::BELASTINGDIENST_MANUAL)
            ->build();
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();

        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);

        $this->be($internal);

        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'inform']
                        ]
                    ]
                ]
            ]
        );
        $this->assertEquals(Response::HTTP_OK, $response->status());

        $task = $task->refresh();
        $this->assertEquals(ServiceTask::STATUS_READY_FOR_BELASTINGDIENST, $task->status);
    }

    public function testSendToUserWithInformAndBelastingdienstAutomaticAsRequestingParty(): void
    {
        $this->markTestSkipped();
        // After debugging in pipelines found that LogiusRequestSender->doRequest() results in algemene fout,
        // which is run from supplyDeclarationJob. Perhaps we need to mock this. Certificate is supplied correctly in pipelines.
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_TASKS_PROVIDER)
            ->setLogiusConnection(AccountService::BELASTINGDIENST_AUTOMATIC)
            ->build();
        $company = CompanyFactory::create($account);

        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();

        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);

        $this->be($internal);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'inform']
                        ]
                    ]
                ]
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $response->status());
        $serviceTask = $task->refresh();
        $this->assertEquals(ServiceTask::STATUS_PENDING_TO_BELASTINGDIENST, $serviceTask->status);
    }

    public function testSendToUserWithInformAndKvkManualAsRequestingParty(): void
    {
        $this->markTestSkipped(
            'Soap client is giving an error unable to connect to host.Every test connecting to Logius are not working'
        );
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_YEAR_WORK_PROVIDER)
            ->setLogiusConnection(AccountService::KVK_MANUAL)
            ->build();
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setCompanySize(Company::SMALL)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();

        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);
        $this->be($internal);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'inform']
                        ]
                    ]
                ]
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $response->status());
        $serviceTask = $task->refresh();
        $this->assertEquals(ServiceTask::STATUS_READY_FOR_KVK, $serviceTask->status);
    }

    public function testSendToUserWithInformAndKvkAutomaticAsRequestingParty(): void
    {
        $this->markTestSkipped(
            'Soap client is giving an error unable to connect to host.Every test connecting to Logius are not working'
        );
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_YEAR_WORK_PROVIDER)
            ->setLogiusConnection(AccountService::KVK_AUTOMATIC)
            ->build();
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setCompanySize(Company::SMALL)
            ->setType(ServiceTask::TYPE_YEARWORK_APPROVAL)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();

        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);

        $this->be($internal);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'inform']
                        ]
                    ]
                ]
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $response->status());

        $serviceTask = $task->refresh();

        $this->assertEquals(ServiceTask::STATUS_PENDING_TO_KVK, $serviceTask->status);
    }

    public function testSendToUserWithInformAndAfasManualAsRequestingParty(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::AFAS_TASKS_PROVIDER)
            ->setLogiusConnection(AccountService::AFAS_MANUAL)
            ->build();
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();

        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);

        $this->be($internal);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'inform']
                        ]
                    ]
                ]
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $response->status());
        $task = $task->refresh();
        $this->assertEquals(ServiceTask::STATUS_READY_FOR_AFAS, $task->status);
    }

    public function testSendToUserWithInformAndAfasAutomaticAsRequestingParty(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::AFAS_TASKS_PROVIDER)
            ->setLogiusConnection(AccountService::AFAS_AUTOMATIC)
            ->build();
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();

        $taskFile = $task->files()->first();
        $taskFile->filename = '****************.********************************.IB.pdf';
        $taskFile->save();

        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);

        $this->be($internal);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'inform']
                        ]
                    ]
                ]
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $response->status());
        $serviceTask = $task->refresh();
        $this->assertEquals(ServiceTask::STATUS_COMPLETED, $serviceTask->status);
    }

    public function testTaskGroupWithBelastingdienstAutomaticTask1ReadyForBelastingdienst(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::NEXTENS_SERVICE)
            ->setLogiusConnection(AccountService::BELASTINGDIENST_AUTOMATIC)
            ->build();
        $company = CompanyFactory::create($account);

        $taskGroup = ServiceTaskGroupBuilder::new()
            ->setAccountService($accountService)
            ->build();

        $task1 = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setType(ServiceTask::TYPE_IHZ_APPROVAL)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->setGroupUuid($taskGroup->uuid)
            ->build();

        $task2 = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->setGroupUuid($taskGroup->uuid)
            ->build();

        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);

        $this->be($internal);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task1->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'inform']
                        ]
                    ]
                ]
            ]
        );

        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );

        $task1 = $task1->refresh();

        $this->assertEquals(
            ServiceTask::STATUS_READY_FOR_BELASTINGDIENST,
            $task1->status
        );
    }

    public function testTaskGroupWithBelastingdienstAutomaticTask1AndTask2SentToBelastingdienst(): void
    {
        $this->markTestSkipped(
            'Soap client is giving an error unable to connect to host.Every test connecting to Logius are not working'
        );
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::NEXTENS_SERVICE)
            ->setLogiusConnection(AccountService::BELASTINGDIENST_AUTOMATIC)
            ->build();

        $company = CompanyFactory::create($account);

        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);
        $this->be($internal);

        $client = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $client);
        $identifier = CompanyIdentifierFactory::create($company, '001000044B37', 'obnumber');
        CompanyUserPermissionFactory::create($identifier, $companyUser, ServiceTaskResponse::PERMISSION_INFORM);

        $taskGroup = ServiceTaskGroupBuilder::new()
            ->setAccountService($accountService)
            ->build();

        $task1 = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setType(ServiceTask::TYPE_VAT_APPROVAL)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->setGroupUuid($taskGroup->uuid)
            ->build();

        $task1->status = ServiceTask::STATUS_SENT;
        $task1->save();
        $task1->status = ServiceTask::STATUS_READY_FOR_BELASTINGDIENST;
        $task1->save();

        $task2 = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->setGroupUuid($taskGroup->uuid)
            ->setFilePath('logius/xbrl/NT15/ICP-2021/VB-01_bd-rpt-icp-opgaaf-2021.xbrl')
            ->build();

        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task2->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'inform']
                        ]
                    ]
                ]
            ]
        );

        $this->assertEquals(
            Response::HTTP_OK,
            $response->status()
        );

        $task1 = $task1->refresh();
        $task2 = $task2->refresh();

        $this->assertEquals(ServiceTask::STATUS_PENDING_TO_BELASTINGDIENST, $task1->status);
        $this->assertEquals(ServiceTask::STATUS_PENDING_TO_BELASTINGDIENST, $task2->status);
    }

    public function testSendWithPlaceholdersWithoutSigners(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setService(Service::MANUAL_TASKS_PROVIDER)
            ->setLogiusConnection(AccountService::BELASTINGDIENST_AUTOMATIC)
            ->setAllUserPermission('approve')
            ->build();
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setType(ServiceTask::TYPE_DOCUMENT_APPROVAL)
            ->setStatus(ServiceTask::STATUS_OPEN)
            ->build();
        $content = $this->asset('pdf/anchor/pdf_with_anchor_type_only.pdf');
        $taskFile = TaskFileFactory::createPdf($accountService, $content, 'file.pdf');
        $taskFile->task()->associate($task);
        $taskFile->save();

        $prepService = resolve(TaskFilePrepService::class);
        $prepService->prepForTask($task);

        $manager = UserFactory::createManager($account);
        CompanyUserFactory::create($company, $manager);

        $user = UserFactory::createClientUser($account);
        $companyUser = CompanyUserFactory::create($company, $user);

        $this->be($manager);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'approve']
                        ]
                    ]
                ]
            ]
        );

        $response->assertStatus(Response::HTTP_PRECONDITION_FAILED);
        $this->assertEquals('This task has 2 placeholders without assigned user', $response->json('title'));
    }

    public function testSendToUserWithoutMobile(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()->setCompany($company)->setAccountService($accountService)->setVat()->build();
        $manager = UserFactory::createManager($account);
        CompanyUserFactory::create($company, $manager);

        $user = UserFactory::createClientUser($account);
        $user->mobile = null;
        $user->save();
        $companyUser = CompanyUserFactory::create($company, $user);

        $companyIdentifier = CompanyIdentifier::query()->where([
                                                                   'account_id' => $account->id,
                                                                   'identifier' => $task->getIdentifier(),
                                                               ])->firstOrFail();
        CompanyUserPermissionFactory::create($companyIdentifier, $companyUser);

        $this->be($manager);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'approve']
                        ]
                    ]
                ]
            ]
        );

        // Assert Equals.
        $this->assertEquals(Response::HTTP_PRECONDITION_FAILED, $response->status());
    }

    public function testSendToUserWithoutFrontAuthAndWithoutMobile(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceFactory::createManualTasks($account);
        $company = CompanyFactory::create($account);
        $task = ServiceTaskBuilder::new()->setCompany($company)->setAccountService($accountService)->setVat()->build();
        $manager = UserFactory::createManager($account);
        CompanyUserFactory::create($company, $manager);

        $user = UserFactory::createClientUser($account);
        $user->front_auth_method = User::AUTH_METHOD_NONE;
        $user->mobile = null;
        $user->save();
        $companyUser = CompanyUserFactory::create($company, $user);

        $companyIdentifier = CompanyIdentifier::query()->where([
                                                                   'account_id' => $account->id,
                                                                   'identifier' => $task->getIdentifier(),
                                                               ])->firstOrFail();
        CompanyUserPermissionFactory::create($companyIdentifier, $companyUser);

        $this->be($manager);
        $response = $this->post(
            $account->route(self::SEND_SINGLE_ROUTE),
            [
                'tasks' => [
                    [
                        'id' => $task->id,
                        'permissions' => [
                            ['user_id' => $companyUser->user_id, 'permission' => 'approve']
                        ]
                    ]
                ]
            ]
        );

        // Assert Equals.
        $this->assertEquals(Response::HTTP_OK, $response->status());

        $serviceTask = $task->refresh();

        $this->assertEquals(App\ServiceTask::STATUS_SENT, $serviceTask->status);
        $this->assertEquals($accountService->getLogiusConnection(), $task->getLogiusConnection());

        $serviceTaskResponse = ServiceTaskResponse::query()
            ->where([
                        'user_id' => $user->id,
                        'task_id' => $task->id,
                    ])
            ->first();

        $this->assertNotNull($serviceTaskResponse);
        $trackingPixel = TrackingPixel::query()
            ->where('service_task_response_id', $serviceTaskResponse->id)
            ->first();

        $this->assertNotNull($trackingPixel);
        $this->assertEquals(VatApprovalMail::class, $trackingPixel->email_type);
    }
}
