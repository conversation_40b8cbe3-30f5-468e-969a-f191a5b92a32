<?php

namespace Tests\Feature\Declarations;

use App\AccountService;
use App\Http\Responses\Response;
use App\ServiceTask;
use Tests\Support\Builders\AccountServiceBuilder;
use Tests\Support\Builders\ServiceTaskBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceCompanyFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class ReadyBelastingdienstTest extends TestCase
{
    public function testSendToBelastingdienstWithManualTasks(): void
    {
        $this->markTestSkipped();
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setManualTasks()
            ->setLogiusConnection(AccountService::BELASTINGDIENST_MANUAL)
            ->build();
        $company = CompanyFactory::create($account);
        $serviceTask = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setStatus(ServiceTask::STATUS_SENT)
            ->setType(ServiceTask::TYPE_VAT_APPROVAL)
            ->build();
        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $this->be($internal);
        $response = $this->post(
            'https://' . $account->hostname . '/task/send_to_requesting_party',
            ['task_id' => $serviceTask->id]
        );

        // Assert Equals.
        $this->assertEquals(Response::HTTP_OK, $response->status());
        $serviceTask = $serviceTask->refresh();
        $this->assertEquals(ServiceTask::STATUS_PENDING_TO_BELASTINGDIENST, $serviceTask->status);
        $this->assertEquals(1, $serviceTask->round);
    }

    public function testReopenWithManualTasks(): void
    {
        $account = AccountFactory::create();
        $accountService = AccountServiceBuilder::new()
            ->setAccount($account)
            ->setManualTasks()
            ->setLogiusConnection(AccountService::BELASTINGDIENST_MANUAL)
            ->build();
        $company = CompanyFactory::create($account);
        $serviceTask = ServiceTaskBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company)
            ->setStatus(ServiceTask::STATUS_READY_FOR_KVK)
            ->build();
        $internal = UserFactory::createColleague($account);
        CompanyUserFactory::create($company, $internal);

        $this->be($internal);
        $response = $this->post(
            'https://' . $account->hostname . '/task/refresh',
            ['task_id' => $serviceTask->id]
        );

        // Assert Equals.
        $this->assertEquals(Response::HTTP_OK, $response->status());
        $serviceTask = $serviceTask->refresh();
        $this->assertEquals(ServiceTask::STATUS_OPEN, $serviceTask->status);
        $this->assertEquals(2, $serviceTask->round);
    }
}
