<?php

namespace Tests\Feature\Api\App\Front\Questions;

use App\Auth\SessionKey;
use Symfony\Component\HttpFoundation\Response;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use Tests\Support\Builders\BookkeepingQuestionBuilder;
use Tests\Support\Builders\OpenQuestionBuilder;
use Tests\Support\Builders\OpenQuestionEmailSettingsUserBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class QuestionsCountTest extends TestCase
{
    public const ENDPOINT = 'api.app.front.questions.questions_to_answer.count';
    public const DUMMY_TOKEN = 'tokendoesntmatteraslongasitsnotempty';

    public function testUnauthorised(): void
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $this->be($user, setFrontToken: false);

        $response = $this->get($account->route(self::ENDPOINT));
        // 303 because it should redirect to the login page because of frontAuth middleware.
        $response->assertStatus(Response::HTTP_SEE_OTHER);
    }

    public function testGetQuestionsToAnswerCount(): void
    {
        $account = AccountFactory::create();
        $user = UserFactory::createClientUser($account);
        $this->be($user);
        $accountService = AccountServiceFactory::createExactOpenQuestions($account);
        $company1 = CompanyFactory::create($account);
        $company2 = CompanyFactory::create($account);

        $companyUser1 = CompanyUserFactory::create($company1, $user);
        $companyUser2 = CompanyUserFactory::create($company2, $user);

        $openQuestion1 = OpenQuestionBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company1)
            ->setStatus(OpenQuestion::STATUS_OPEN)
            ->build();
        BookkeepingQuestionBuilder::new()->setOpenQuestion($openQuestion1)->build();

        $openQuestion2 = OpenQuestionBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company2)
            ->setStatus(OpenQuestion::STATUS_PENDING)
            ->build();
        BookkeepingQuestionBuilder::new()->setOpenQuestion($openQuestion2)->build();

        $openQuestion3 = OpenQuestionBuilder::new()
            ->setAccountService($accountService)
            ->setCompany($company2)
            ->setStatus(OpenQuestion::STATUS_COMPLETED)
            ->build();
        BookkeepingQuestionBuilder::new()->setOpenQuestion($openQuestion3)->build();

        OpenQuestionEmailSettingsUserBuilder::new()->setCompanyUser($companyUser1)->build();
        OpenQuestionEmailSettingsUserBuilder::new()->setCompanyUser($companyUser2)->build();

        $this->be($user, frontAuthToken: self::DUMMY_TOKEN);
        \Session::put(SessionKey::FRONT_AUTH_TOKEN, self::DUMMY_TOKEN);
        $response = $this->get($account->route(self::ENDPOINT))->assertOk();
        $this->assertEquals(1, $response->json('data'));
    }
}
