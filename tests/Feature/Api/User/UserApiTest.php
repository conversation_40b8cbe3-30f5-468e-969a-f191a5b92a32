<?php

namespace Tests\Feature\Api\User;

use App\Context;
use App\Http\Responses\Response;
use App\Role;
use App\User;
use Tests\Feature\Api\Http\ApiTestCase;
use Tests\Support\Factories\RoleUserFactory;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\MembershipFactory;
use Tests\Support\Factories\UserFactory;

class UserApiTest extends ApiTestCase
{
    /**
     * @deprecated this test is using a deprecated endpoint
     * @return void
     * @throws \Throwable
     */
    public function testGetUserByEmail()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $email = $this->faker->email;
        $user->email = $email;
        $user->save();

        $anotherUser = UserFactory::createClientUser($account);
        $anotherUser->email = $email;
        $anotherUser->save();

        $headers = [
            'Authorization' => 'Bearer ' . $account->createToken('api-test')->plainTextToken,
            'Accept' => 'application/json'
        ];

        $response = $this->get(
            $account->route('api.user.get_by_email', ['email' => $email]),
            $headers
        );

        $response->assertOk();
        $content = json_decode($response->getContent(), true);

        $this->assertCount(2, $content['data']);
        foreach ($content['data'] as $userData) {
            $this->assertEquals($email, $userData['email']);
        }
    }

    public function testEmptyResponse()
    {
        $mainContext = Context::findOrFail(1);
        $user = UserFactory::createManager($mainContext->account);
        $this->be($user);

        $response = $this->get(
            $user->account->route('api.user.get_by_email', ['email' => '<EMAIL>'])
        );
        $response->assertNoContent();
    }

    /**
     * @deprecated this test is using a deprecated endpoint
     * @return void
     * @throws \Throwable
     */
    public function testGetUserWithManagerUserSession()
    {
        $mainContext = Context::findOrFail(1);
        $user = UserFactory::createManager($mainContext->account);
        $this->be($user);

        $colleague = UserFactory::createColleague($user->account);
        $colleague->email = '<EMAIL>';
        $colleague->save();
        $response = $this->get(
            $user->account->route('api.user.get_by_email', ['email' => '<EMAIL>'])
        );
        $response->assertOk();
        $content = json_decode($response->getContent(), true);
        $this->assertCount(1, $content['data']);
    }

    /**
     * @deprecated this test is using a deprecated endpoint
     * @return void
     * @throws \Throwable
     */
    public function testGetUserWithColleagueUserSession()
    {
        $mainContext = Context::findOrFail(1);
        $user = UserFactory::createColleague($mainContext->account);
        $this->be($user);

        $client = UserFactory::createClientUser($user->account);
        $client->email = '<EMAIL>';
        $client->save();

        $response = $this->get(
            $user->account->route('api.user.get_by_email', ['email' => '<EMAIL>'])
        );
        $response->assertNoContent(); // no user returned as client user does not have same membership as colleague yet.

        MembershipFactory::create($user, $mainContext);
        MembershipFactory::create($client, $mainContext);
        $response = $this->get(
            $user->account->route('api.user.get_by_email', ['email' => '<EMAIL>'])
        );
        $response->assertOk();
        $content = json_decode($response->getContent(), true);
        $this->assertCount(1, $content['data'], 'Users are now in same group');
    }

    public function testUpdateUser()
    {
        $mainContext = Context::findOrFail(1);
        $manager = UserFactory::createManager($mainContext->account);
        $this->be($manager);

        $user = UserFactory::createColleague($manager->account);

        $response = $this->patch($user->getAccount()->route('api.user.update', ['user_id' => $user->id]), [
            'username' => 'securelogin username',
            'firstname' => 'securelogin firstname',
            'lastname' => 'securelogin lastname',
            'email' => '<EMAIL>',
            'mobile_number' => '+***********',
            'language' => 'nl',
            'external_id' => '12',
            'internal_client_id' => '123456',
        ]);

        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('id', $responseContent);
        $this->assertEquals($user->id, $responseContent['id']);

        $user = $user->refresh();

        $this->assertEquals(Response::HTTP_OK, $response->status());
        $this->assertEquals('securelogin username', $user->auth_id);
        $this->assertEquals('securelogin firstname', $user->firstname);
        $this->assertEquals('securelogin lastname', $user->lastname);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('+***********', $user->mobile);
        $this->assertEquals('nl', $user->language);
        $this->assertEquals('12', $user->external_id);
        $this->assertEquals('123456', $user->internal_client_id);
    }

    public function testGetById()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);

        $this->be($user);

        $response = $this->get($account->route('api.user.get_by_id', ['user_id' => $user->id]));

        $response->assertOk();
    }

    public function testGetByIdFromDifferentAccount()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague();

        $this->be($user);

        $response = $this->get($account->route('api.user.get_by_id', ['user_id' => $user->id]));

        $response->assertUnauthorized();
    }

    public function testUpdateEmailAsUsername()
    {
        $mainContext = Context::findOrFail(1);
        $manager = UserFactory::createManager($mainContext->account);
        $this->be($manager);

        $user = UserFactory::createColleague($manager->account);
        $user->status = User::STATUS_VERIFIED;
        $user->auth_id = $user->email;
        $user->firstname = 'Jane';
        $user->lastname = 'Doe';
        $user->mobile = '+***********';
        $user->language = 'nl';
        $user->external_id = '12';
        $user->internal_client_id = '123456';
        $user->save();

        $response = $this->patch($user->getAccount()->route('api.user.update', ['user_id' => $user->id]), [
            'email' => '<EMAIL>',
        ]);

        $responseContent = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('id', $responseContent);
        $this->assertEquals($user->id, $responseContent['id']);

        $user = $user->refresh();

        $this->assertEquals(Response::HTTP_OK, $response->status());
        $this->assertEquals('<EMAIL>', $user->auth_id);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('Jane', $user->firstname);
        $this->assertEquals('Doe', $user->lastname);
        $this->assertEquals('+***********', $user->mobile);
        $this->assertEquals('nl', $user->language);
        $this->assertEquals('12', $user->external_id);
        $this->assertEquals('123456', $user->internal_client_id);
    }

    public function testUpdateUnauthorized()
    {
        $mainContext = Context::findOrFail(1);
        $normalUser = UserFactory::createColleague($mainContext->account);
        $this->be($normalUser);

        $user = UserFactory::createColleague($normalUser->account);

        $response = $this->patch($user->getAccount()->route('api.user.update', ['user_id' => $user->id]), [
            'email' => '<EMAIL>',
        ]);
        $response->assertStatus(403);
    }

    public function testUpdateWrongUsername()
    {
        $mainContext = Context::findOrFail(1);
        $normalUser = UserFactory::createManager($mainContext->account);
        $this->be($normalUser);

        $user = UserFactory::createColleague($normalUser->account);

        $response = $this->patch($user->getAccount()->route('api.user.update', ['user_id' => $user->id]), [
            'username' => 'Janey%',
        ]);

        $response->assertStatus(422);
    }

    /**
     * @deprecated this test is using a deprecated api endpoint
     * @return void
     */
    public function testGetAllUserRoles()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createManager($account);
        $user2 = UserFactory::createClientUser($account);

        /** @var Role $role */
        /** @var Role $role2 */
        $role = Role::query()->where(Role::NAME, Role::ONE_TIME_SIGNER)->first();
        $role2 = Role::query()->where(Role::NAME, Role::CLIENT_USER)->first();
        RoleUserFactory::create($role, $user2);
        RoleUserFactory::create($role2, $user2);

        $this->be($user);

        $response = $this->get($account->route('api.user.get_by_id', ['user_id' => $user2->id]));

        $this->assertEquals(trans('role.name.' . Role::CLIENT_USER), $response->json('data.roles.0.name'));
        $this->assertEquals(trans('role.name.' . Role::ONE_TIME_SIGNER), $response->json('data.roles.1.name'));
        $this->assertCount(2, $response->json('data.roles'));
    }
}
