<?php

namespace Tests\Feature\Api\V2;

use App\Models\OpenQuestions\OpenQuestionCategory;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\Template\TemplateFactory;
use Tests\Support\TestCase;
use Tests\Support\Factories\UserFactory;

class TemplateTest extends TestCase
{
    private const INDEX_ENDPOINT = 'api.v2.manage.templates.index';

    public function testIndexForbidden(): void
    {
        $account = AccountFactory::create();
        $this->getJson($account->route(self::INDEX_ENDPOINT))->assertUnauthorized();
    }

    public function testIndex(): void
    {
        $account = AccountFactory::create();
        AccountServiceFactory::createManualQuestions();
        $user = UserFactory::createManager($account);
        $this->be($user);

        $url = $account->route(self::INDEX_ENDPOINT);

        $template1 = TemplateFactory::create($account);
        $template2 = TemplateFactory::create($account, category: OpenQuestionCategory::FISCAL);
        $template3 = TemplateFactory::create($account, fieldCount: 3, category: OpenQuestionCategory::OTHER);

        $response = $this->getJson($url)->assertOk();
        $response->assertJsonStructure([
            'current_page',
            'data' => [
                [
                    'id',
                    'name',
                    'category',
                    'created_at',
                    'updated_at',
                    'fields_to_answer_count'
                ],
                [
                    'id',
                    'name',
                    'category',
                    'created_at',
                    'updated_at',
                    'fields_to_answer_count'
                ],
                [
                    'id',
                    'name',
                    'category',
                    'created_at',
                    'updated_at',
                    'fields_to_answer_count'
                ]
            ],
            'first_page_url',
            'from',
            'last_page',
            'last_page_url',
            'links' => [
                [
                    'url',
                    'label',
                    'active'
                ],
                [
                    'url',
                    'label',
                    'active'
                ],
                [
                    'url',
                    'label',
                    'active'
                ]
            ],
            'next_page_url',
            'path',
            'per_page',
            'prev_page_url',
            'to',
            'total'
        ]);

        $json = $response->json();
        $this->assertEquals(3, $json['to']);
        $this->assertEquals(3, $json['total']);
        $this->assertCount(3, $json['data']);

        $this->assertEquals($template3->id, $json['data'][0]['id']);
        $this->assertEquals($template3->name, $json['data'][0]['name']);
        $this->assertEquals(OpenQuestionCategory::OTHER, $json['data'][0]['category']);
        $this->assertEquals($template3->fieldsToAnswer()->count(), $json['data'][0]['fields_to_answer_count']);

        $this->assertEquals($template2->id, $json['data'][1]['id']);
        $this->assertEquals($template2->name, $json['data'][1]['name']);
        $this->assertEquals(OpenQuestionCategory::FISCAL, $json['data'][1]['category']);
        $this->assertEquals($template2->fieldsToAnswer()->count(), $json['data'][1]['fields_to_answer_count']);

        $this->assertEquals($template1->id, $json['data'][2]['id']);
        $this->assertEquals($template1->name, $json['data'][2]['name']);
        $this->assertEquals(OpenQuestionCategory::BOOKKEEPING, $json['data'][2]['category']);
        $this->assertEquals($template1->fieldsToAnswer()->count(), $json['data'][2]['fields_to_answer_count']);

        // test count request
        $url .= '?count=1';
        $response = $this->getJson($url)->assertOk();
        $count = $response->json('count');
        $this->assertEquals(3, $count);
    }
}