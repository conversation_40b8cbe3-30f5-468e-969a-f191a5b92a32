<?php

namespace Tests\Feature\Api\Private\Caseware;

use App\Repositories\Services\SbrRecipientRepository;
use Illuminate\Database\Eloquent\Collection;
use Mockery;
use Symfony\Component\HttpFoundation\Response;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\TestCase;

class SbrRecipientsTest extends TestCase
{
    private const ROUTE_NAME = 'api.private.caseware.sbr_recipients';

    public function testInvalidApiKey()
    {
        $account = AccountFactory::create();
        $apiKey = random_string(20);

        $response = $this->get(
            $account->route(self::ROUTE_NAME, ['sbr_request_id' => 123]),
            ['x-api-key' => $apiKey . 'X']
        );
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    public function testGetSbrRecipients()
    {
        $account = AccountFactory::create();
        $apiKey = random_string(20);
        AccountServiceFactory::createCaseware($account, $apiKey);

        $recipients = new Collection();
        $recipients->push(['id' => 1, 'name' => 'Bank 1']);
        $recipients->push(['id' => 2, 'name' => 'Bank 2']);

        $sbrRecipientRepositoryMock = Mockery::mock(SbrRecipientRepository::class);
        $sbrRecipientRepositoryMock->shouldReceive('indexMinimal')->andReturn($recipients);

        $this->app->instance(SbrRecipientRepository::class, $sbrRecipientRepositoryMock);
        $response = $this->get($account->route(self::ROUTE_NAME), ['x-api-key' => $apiKey])->assertOk();

        $response->assertJsonStructure(
            [
                '*' => [
                    'id',
                    'name'
                ]
            ]
        );
        $json = $response->json();
        $this->assertCount(2, $json);
        $this->assertEquals(1, $json[0]['id']);
        $this->assertEquals('Bank 1', $json[0]['name']);
        $this->assertEquals(2, $json[1]['id']);
        $this->assertEquals('Bank 2', $json[1]['name']);
    }
}
