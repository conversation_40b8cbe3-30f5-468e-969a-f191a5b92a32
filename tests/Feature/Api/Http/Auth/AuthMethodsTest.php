<?php

namespace Tests\Feature\Api\Http\Auth;

use Symfony\Component\HttpFoundation\Response;
use Tests\Feature\Api\Http\ApiTestCase;

class AuthMethodsTest extends ApiTestCase
{
    private static $endpoint = 'api.auth.methods';

    /**
     * @return void
     */
    public function testAuthMethodEndpoint()
    {
        $this->dataCreatorParameters->setUserUsageTypes(['api']);
        $this->dataCreatorParameters->setAccountUsageTypes(['api']);
        $this->dataCreatorParameters->setUserIsManager(true);
        $this->setupTestCase($this->dataCreatorParameters);
        $response = $this->assertHttpGet(self::$endpoint, [], Response::HTTP_OK);

        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'name',
                    'url',
                    'issuer',
                    'certificate',
                    'allowed_origins',
                    'protocol',
                    'metadata_url',
                ]
            ]
        ]);
    }
}
