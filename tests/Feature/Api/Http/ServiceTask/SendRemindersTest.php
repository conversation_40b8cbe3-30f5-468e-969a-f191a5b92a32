<?php

namespace Tests\Feature\Api\Http\ServiceTask;

use App\ServiceTask;
use App\ServiceTaskResponse;
use Symfony\Component\HttpFoundation\Response;
use Tests\Feature\Api\Http\ApiTestCase;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\ServiceTaskResponseFactory;
use Tests\Support\Factories\UserFactory;

class SendRemindersTest extends ApiTestCase
{
    protected static string $sendReminderEndpoint = 'new.task.status_overview.send_reminders';

    public function testSuccessfulSendReminders()
    {
        $serviceTaskResponse = ServiceTaskResponseFactory::create();
        $serviceTask = $serviceTaskResponse->task;
        CompanyUserFactory::create($serviceTask->company, $serviceTaskResponse->user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_APPROVE;
        $serviceTaskResponse->save();
        $serviceTask->status = ServiceTask::STATUS_SENT;
        $serviceTask->save();
        $this->be($serviceTaskResponse->user);
        $requestBodyParams = ['tasks' => [$serviceTaskResponse->task->id]];
        $this->assertHttpPost(
            $this->getRoute($serviceTaskResponse->account, self::$sendReminderEndpoint),
            [],
            $requestBodyParams,
            Response::HTTP_OK
        );
    }

    public function testWrongUserSendReminders()
    {
        $serviceTaskResponse = ServiceTaskResponseFactory::create();
        $serviceTask = $serviceTaskResponse->task;
        CompanyUserFactory::create($serviceTask->company, $serviceTaskResponse->user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_APPROVE;
        $serviceTaskResponse->save();
        $serviceTask->status = ServiceTask::STATUS_SENT;
        $serviceTask->save();
        $this->be(UserFactory::createColleague($serviceTask->account));
        $requestBodyParams = ['tasks' => [$serviceTaskResponse->task->id]];
        $this->assertHttpPost(
            $this->getRoute($serviceTaskResponse->account, self::$sendReminderEndpoint),
            [],
            $requestBodyParams,
            Response::HTTP_FORBIDDEN
        );
    }

    public function testExternalUserSendReminders()
    {
        $serviceTaskResponse = ServiceTaskResponseFactory::create();
        $serviceTask = $serviceTaskResponse->task;
        CompanyUserFactory::create($serviceTask->company, $serviceTaskResponse->user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_APPROVE;
        $serviceTaskResponse->save();
        $serviceTask->status = ServiceTask::STATUS_SENT;
        $serviceTask->save();
        $user = UserFactory::createClientUser($serviceTask->account);
        $this->be($user);
        $requestBodyParams = ['tasks' => [$serviceTaskResponse->task->id]];
        $this->assertHttpPost(
            $this->getRoute($serviceTaskResponse->account, self::$sendReminderEndpoint),
            [],
            $requestBodyParams,
            Response::HTTP_FORBIDDEN
        );
    }

    public function testWrongAccountSendReminders()
    {
        $serviceTaskResponse = ServiceTaskResponseFactory::create();
        $serviceTask = $serviceTaskResponse->task;
        CompanyUserFactory::create($serviceTask->company, $serviceTaskResponse->user);
        $serviceTaskResponse->permission = ServiceTaskResponse::PERMISSION_APPROVE;
        $serviceTaskResponse->save();
        $serviceTask->status = ServiceTask::STATUS_SENT;
        $serviceTask->save();
        $user = UserFactory::createColleague();
        $this->be($user);
        $requestBodyParams = ['tasks' => [$serviceTaskResponse->task->id]];
        $this->assertHttpPost(
            $this->getRoute($serviceTaskResponse->account, self::$sendReminderEndpoint),
            [],
            $requestBodyParams,
            Response::HTTP_FOUND
        );
    }
}