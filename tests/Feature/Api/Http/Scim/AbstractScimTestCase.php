<?php

namespace Tests\Feature\Api\Http\Scim;

use App\Account;
use App\Consumer;
use App\Service;
use App\Token;
use Illuminate\Testing\TestResponse;
use Symfony\Component\HttpFoundation\Response;
use Tests\Feature\Api\Http\ApiTestCase;
use Tests\Support\Builders\AccountServiceBuilder;

abstract class AbstractScimTestCase extends ApiTestCase
{
    /**
     * @var string ENDPOINT - Endpoint used for SCIM 2.0
     */
    protected const ENDPOINT = 'https://scim.securelogin.nu/api/scim/2.0/';

    /**
     * @inheritDoc
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->dataCreatorParameters
            ->setConsumerAccountId(Account::getAdminAccount()->id)
            ->setConsumerAuthId("8adf8e6e-67b2-4cf2-a259-e3dc5476c621")
            ->setConsumerProvisioningId("8adf8e6e-67b2-4cf2-a259-e3dc5476c621") // Audience in the Bearer Token.
            ->setConsumerProtocol('oauth2')
            ->setConsumerVerifyKeys([])
            ->setConsumerConfiguration(["notifyOnCreateUser" => true, Consumer::SCOPES => ['scim'], 'multi-accounts' => true])
            ->setConsumerMfa(0)
            ->setConsumerIdentityLinking(1)
            ->setConsumerAllowedOrigins(['https://sts.windows.net']);
        $this->dataCreatorParameters->setUserUsageTypes(['api']);

        $this->dataCreatorParameters->setAccountHostname('scim.securelogin.nu');
        $this->dataCreatorParameters->setAccountUsageTypes(['api']);

        $this->setupTestCase($this->dataCreatorParameters, false);

        AccountServiceBuilder::new()
            ->setAccount($this->dataCreator->getAccount())
            ->setService(Service::AZURE_CRM_SERVICE)
            ->setEnabled(true)
            ->setProperties(["auth_id_reference" => "external_id", "notify_on_create" => true])
            ->build();

        // Create access token.
        $token = Token::make('oauth2_access_token', time() + 9000)
            ->setRandomToken()
            ->setUserId($this->dataCreator->getUser()->id)
            ->setAccountId($this->dataCreator->getAccount()->id)
            ->setConsumerId($this->dataCreator->getConsumer()->id)
            ->setData(['scopes' => ['scim']])
            ->persist()
            ->token;

        $this->withHeaders(['Authorization' => 'Bearer ' . $token]);
    }

    /**
     * Create a user with dummy data given an ExternalId.
     *
     * @param $externalId - The external ID of the user that is going to be deactivated.
     *
     * @return TestResponse
     */
    protected function createUser(string $externalId): TestResponse
    {
        $response = $this->post(
            self::ENDPOINT . 'Users',
            self::createUserResource($externalId)
        );

        if ($response->status() === Response::HTTP_CREATED) {
            $data = \json_decode($response->baseResponse->content(), true);

            $this->assertStringStartsWith('User-', $data['id']);
            $this->assertEquals($externalId, $data['externalId']);
            // @TODO: Check if this will bring consequences
//            $this->assertEquals('username' . $externalId, $data['userName']);
        }

        return $response;
    }

    public static function createUserResource(string $externalId)
    {
        return [
            "schemas" => [
                "urn:ietf:params:scim:schemas:core:2.0:User",
                "urn:ietf:params:scim:schemas:extension:enterprise:2.0:User"
            ],
            "externalId" => $externalId,
            "userName" => 'username' . $externalId,
            "active" => true,
            "emails" => [[
                "primary" => true,
                "type" => "work",
                "value" => "<EMAIL>"
            ]],
            "meta" => [
                "resourceType" => "User"
            ],
            "name" => [
                "familyName" => "Family Name",
                "givenName" => "Given Name",
            ],
            "preferredLanguage" => "en-US",
        ];
    }

    /**
     * Deletes a user
     *
     * @param string $id
     *
     * @return void
     */
    protected function deleteUser(string $id): void
    {
        $response = $this->delete(self::ENDPOINT . 'Users/' . $id);

        $response->assertStatus(204);
    }
}
