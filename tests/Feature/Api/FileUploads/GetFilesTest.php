<?php

namespace Tests\Feature\Api\FileUploads;

use App\Models\OpenQuestions\OpenQuestionCategory;
use Tests\Support\Builders\ClientQuestionBuilder;
use Tests\Support\Builders\OpenQuestionBuilder;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class GetFilesTest extends TestCase
{
    private const ENDPOINT = 'api.v1.upload.files';

    public function testWithoutValidToken()
    {
        $account = AccountFactory::create();
        $headers = ['Authorization' => 'Bearer 123|2323'];
        $this->get($account->route(self::ENDPOINT, 123), $headers)->assertUnauthorized();
    }

    public function testWithoutToken()
    {
        $account = AccountFactory::create();
        $this->get($account->route(self::ENDPOINT, 123))->assertUnauthorized();
    }

    public function testWithUserFromDifferentAccount()
    {
        $account = AccountFactory::create();
        $company = CompanyFactory::create($account);
        $currentUser = UserFactory::createManager($account);
        $this->be($currentUser);
        $account2 = AccountFactory::create();
        $openQuestion = OpenQuestionBuilder::new()
            ->setCompany($company)
            ->setCategory(OpenQuestionCategory::CLIENT)
            ->build();

        $question = ClientQuestionBuilder::new()
            ->setOpenQuestion($openQuestion)
            ->build();

        $this->get($account2->route(self::ENDPOINT, $question->id))->assertForbidden();
    }

    public function testNotFound()
    {
        $account = AccountFactory::create();
        $currentUser = UserFactory::createManager($account);
        $this->be($currentUser);
        $this->get($account->route(self::ENDPOINT, 123))->assertNotFound();
    }
}
