<?php

namespace Tests\Feature\Placeholders;

use App\Http\Responses\Response;
use App\Models\TaskFile\Placeholder;
use App\ServiceTask;
use App\TaskAuditLog;
use App\ValueObject\Placeholder\Signing\ImagePlaceholder;
use App\ValueObject\Placeholder\Signing\InitialsPlaceholder;
use App\ValueObject\Placeholder\Signing\StampPlaceholder;
use App\ValueObject\Placeholder\Stamp\InputDatePlaceholder;
use App\ValueObject\Placeholder\Stamp\InputTextPlaceholder;
use Tests\Support\Builders\Services\Signing\PlaceholderBuilder;
use Tests\Support\Factories\CompanyUserFactory;
use Tests\Support\Factories\ServiceTaskFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class PlaceholderTest extends TestCase
{
    private const STORE_ENDPOINT = 'new.task_file_placeholders.store';

    public function testAddInputPlaceholder(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputTextPlaceholder::DEFAULT_WIDTH,
                    'h' => InputTextPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_TEXT,
                    Placeholder::FILLED_BY => $clientUser->id
                ]
            ]
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);

        $response->assertOk();
        $placeholder = Placeholder::where(Placeholder::TASK_FILE_ID, $taskFile->id)->first();

        $this->assertNotNull($placeholder);
        $this->assertEquals(Placeholder::TYPE_INPUT_TEXT, $placeholder->type);
        $this->assertEquals($clientUser->id, $placeholder->filled_by);
    }

    public function testAddDatePlaceholder(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InputDatePlaceholder::DEFAULT_WIDTH,
                    'h' => InputDatePlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INPUT_DATE,
                    Placeholder::FILLED_BY => $clientUser->id
                ]
            ]
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);

        $response->assertOk();
        $placeholder = Placeholder::where(Placeholder::TASK_FILE_ID, $taskFile->id)->first();

        $this->assertNotNull($placeholder);
        $this->assertEquals(Placeholder::TYPE_INPUT_DATE, $placeholder->type);
        $this->assertEquals($clientUser->id, $placeholder->filled_by);
    }

    public function testAddInitialsSignature(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => InitialsPlaceholder::DEFAULT_WIDTH,
                    'h' => InitialsPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_INITIALS_SIGNATURE,
                    Placeholder::FILLED_BY => $clientUser->id
                ]
            ]
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);

        $response->assertOk();
        $placeholder = Placeholder::where(Placeholder::TASK_FILE_ID, $taskFile->id)->first();

        $this->assertNotNull($placeholder);
        $this->assertEquals(Placeholder::TYPE_INITIALS_SIGNATURE, $placeholder->type);
        $this->assertEquals($clientUser->id, $placeholder->filled_by);
    }

    public function testAddStampSignature(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => StampPlaceholder::DEFAULT_WIDTH,
                    'h' => StampPlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_STAMP_SIGNATURE,
                    Placeholder::FILLED_BY => $clientUser->id
                ]
            ]
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);

        $response->assertOk();
        $placeholder = Placeholder::where(Placeholder::TASK_FILE_ID, $taskFile->id)->first();

        $this->assertNotNull($placeholder);
        $this->assertEquals(Placeholder::TYPE_STAMP_SIGNATURE, $placeholder->type);
        $this->assertEquals($clientUser->id, $placeholder->filled_by);
    }

    public function testAddImageSignature(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => ImagePlaceholder::DEFAULT_WIDTH,
                    'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_IMAGE_SIGNATURE,
                    Placeholder::FILLED_BY => $clientUser->id
                ]
            ]
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);

        $response->assertOk();
        $placeholder = Placeholder::where(Placeholder::TASK_FILE_ID, $taskFile->id)->first();

        $this->assertNotNull($placeholder);
        $this->assertEquals(Placeholder::TYPE_IMAGE_SIGNATURE, $placeholder->type);
        $this->assertEquals($clientUser->id, $placeholder->filled_by);
    }

    public function testAddPlaceholdersWithOrder(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => true,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::SIGNING_ORDER => 1,
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => ImagePlaceholder::DEFAULT_WIDTH,
                    'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_IMAGE_SIGNATURE,
                    Placeholder::FILLED_BY => $clientUser->id
                ],
                [
                    Placeholder::SIGNING_ORDER => 2,
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 21,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => ImagePlaceholder::DEFAULT_WIDTH,
                    'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_IMAGE_SIGNATURE,
                    Placeholder::FILLED_BY => $colleagueUser->id
                ]
            ]
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);

        $response->assertOk();
        $placeholders = Placeholder::where(Placeholder::TASK_FILE_ID, $taskFile->id)->get();

        $this->assertNotNull($placeholders);
        $this->assertEquals(Placeholder::TYPE_IMAGE_SIGNATURE, $placeholders[0]->type);
        $this->assertEquals($clientUser->id, $placeholders[0]->filled_by);
        $this->assertEquals(1, $placeholders[0]->signing_order);
        $this->assertEquals(Placeholder::TYPE_IMAGE_SIGNATURE, $placeholders[1]->type);
        $this->assertEquals($colleagueUser->id, $placeholders[1]->filled_by);
        $this->assertEquals(2, $placeholders[1]->signing_order);
        $this->assertTrue(
            $task->auditLogs()
                ->where(TaskAuditLog::ACTION, TaskAuditLog::SIGNING_ORDER_ENABLED)
                ->exists()
        );
        $this->assertCount(
            2,
            $task->auditLogs()
                ->where(TaskAuditLog::ACTION, TaskAuditLog::PLACEHOLDER_SIGNING_ORDER_UPDATED)
                ->get()
        );
    }

    public function testAddPlaceholdersWithoutRequiredOrder(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => true,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => ImagePlaceholder::DEFAULT_WIDTH,
                    'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Stamp',
                    Placeholder::TYPE => Placeholder::TYPE_IMAGE_SIGNATURE,
                    Placeholder::FILLED_BY => $clientUser->id
                ]
            ]
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);
        $response->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    public function testUpdatePlaceholdersOrderWhenOrderIsDisabled(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $task->custom_signing_order_enabled = true;
        $task->save();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        $placeholderColleague = PlaceholderBuilder::new()
            ->setTaskFile($taskFile)
            ->setFilledBy($colleagueUser)
            ->setSigningOrder(4)
            ->build();
        $placeholderClientUser = PlaceholderBuilder::new()
            ->setTaskFile($taskFile)
            ->setFilledBy($clientUser)
            ->setSigningOrder(3)
            ->build();

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    'id' => $placeholderColleague->id,
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => $placeholderColleague->top,
                    Placeholder::BOTTOM => $placeholderColleague->bottom,
                    Placeholder::LEFT => $placeholderColleague->left,
                    Placeholder::RIGHT => $placeholderColleague->right,
                    'w' => ImagePlaceholder::DEFAULT_WIDTH,
                    'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Placeholder',
                    Placeholder::TYPE => Placeholder::TYPE_STAMP_SIGNATURE,
                    Placeholder::FILLED_BY => $colleagueUser->id
                ],
                [
                    'id' => $placeholderClientUser->id,
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => $placeholderClientUser->top,
                    Placeholder::BOTTOM => $placeholderClientUser->bottom,
                    Placeholder::LEFT => $placeholderClientUser->left,
                    Placeholder::RIGHT => $placeholderClientUser->right,
                    'w' => ImagePlaceholder::DEFAULT_WIDTH,
                    'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Placeholder',
                    Placeholder::TYPE => Placeholder::TYPE_STAMP_SIGNATURE,
                    Placeholder::FILLED_BY => $clientUser->id
                ]
            ]
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);

        $response->assertOk();
        $task->refresh();
        $placeholderColleague->refresh();
        $placeholderClientUser->refresh();
        $this->assertEquals(1, $placeholderColleague->signing_order);
        $this->assertEquals(2, $placeholderClientUser->signing_order);

        $this->assertTrue(
            $task->auditLogs()
                ->where(TaskAuditLog::ACTION, TaskAuditLog::SIGNING_ORDER_DISABLED)
                ->exists()
        );
    }

    public function testUpdatePlaceholderGroup()
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        $placeholderColleague = PlaceholderBuilder::new()
            ->setTaskFile($taskFile)
            ->setFilledBy($colleagueUser)
            ->setSigningOrder(4)
            ->build();
        $placeholderClientUser = PlaceholderBuilder::new()
            ->setTaskFile($taskFile)
            ->setFilledBy($clientUser)
            ->setSigningOrder(3)
            ->build();

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    'id' => $placeholderColleague->id,
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => $placeholderColleague->top,
                    Placeholder::BOTTOM => $placeholderColleague->bottom,
                    Placeholder::LEFT => $placeholderColleague->left,
                    Placeholder::RIGHT => $placeholderColleague->right,
                    'w' => ImagePlaceholder::DEFAULT_WIDTH,
                    'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Placeholder',
                    Placeholder::TYPE => Placeholder::TYPE_STAMP_SIGNATURE,
                    Placeholder::FILLED_BY => $colleagueUser->id,
                    'pagesSelectedToCopyPlaceholder' => [2, 3, 4]
                ],
                [
                    'id' => $placeholderClientUser->id,
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => $placeholderClientUser->top,
                    Placeholder::BOTTOM => $placeholderClientUser->bottom,
                    Placeholder::LEFT => $placeholderClientUser->left,
                    Placeholder::RIGHT => $placeholderClientUser->right,
                    'w' => ImagePlaceholder::DEFAULT_WIDTH,
                    'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Placeholder',
                    Placeholder::TYPE => Placeholder::TYPE_STAMP_SIGNATURE,
                    Placeholder::FILLED_BY => $clientUser->id
                ]
            ]
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);

        $response->assertOk();
        $this->assertCount(4, $taskFile->signingPlaceholders);
        $this->assertCount(3, $taskFile->signingPlaceholders()->whereNotNull(Placeholder::GROUP)->get());
    }

    public function testCreatePlaceholderGroup()
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => [
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 11,
                    Placeholder::BOTTOM => 85,
                    Placeholder::LEFT => 9,
                    Placeholder::RIGHT => 34,
                    'w' => ImagePlaceholder::DEFAULT_WIDTH,
                    'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Placeholder',
                    Placeholder::TYPE => Placeholder::TYPE_STAMP_SIGNATURE,
                    Placeholder::FILLED_BY => $colleagueUser->id,
                ],
                [
                    Placeholder::PAGE => 1,
                    Placeholder::TOP => 21,
                    Placeholder::BOTTOM => 65,
                    Placeholder::LEFT => 19,
                    Placeholder::RIGHT => 34,
                    'w' => ImagePlaceholder::DEFAULT_WIDTH,
                    'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                    Placeholder::TITLE => 'Placeholder',
                    Placeholder::TYPE => Placeholder::TYPE_STAMP_SIGNATURE,
                    Placeholder::FILLED_BY => $clientUser->id,
                    'pagesSelectedToCopyPlaceholder' => [2, 3, 4]

                ]
            ]
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);

        $response->assertOk();
        $this->assertCount(4, $taskFile->signingPlaceholders);
        $this->assertCount(3, $taskFile->signingPlaceholders()->whereNotNull(Placeholder::GROUP)->get());
    }

    public function testAllowedPlaceholders()
    {
        chdir(base_path());
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        // create 40 placeholders with copy (this means it will below the limit of allowed placeholders)
        $placeholders = [];
        for ($i = 0; $i < 20; $i++) {
            $placeholders[] = [
                Placeholder::PAGE => 1,
                Placeholder::TOP => 21,
                Placeholder::BOTTOM => 65,
                Placeholder::LEFT => 19,
                Placeholder::RIGHT => 34,
                'w' => ImagePlaceholder::DEFAULT_WIDTH,
                'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                Placeholder::TITLE => 'Placeholder',
                Placeholder::TYPE => Placeholder::TYPE_STAMP_SIGNATURE,
                Placeholder::FILLED_BY => $clientUser->id,
                'pagesSelectedToCopyPlaceholder' => [1, 2]
            ];
        }
        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => $placeholders
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);
        $response->assertStatus(Response::HTTP_OK);
    }

    public function testMaximumAllowedPlaceholders()
    {
        chdir(base_path());
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        // create 202 placeholders with copy (this means it will be more placeholders than the maximum allowed)
        $placeholders = [];
        for ($i = 0; $i < 101; $i++) {
            $placeholders[] = [
                Placeholder::PAGE => 1,
                Placeholder::TOP => 21,
                Placeholder::BOTTOM => 65,
                Placeholder::LEFT => 19,
                Placeholder::RIGHT => 34,
                'w' => ImagePlaceholder::DEFAULT_WIDTH,
                'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                Placeholder::TITLE => 'Placeholder',
                Placeholder::TYPE => Placeholder::TYPE_STAMP_SIGNATURE,
                Placeholder::FILLED_BY => $clientUser->id,
                'pagesSelectedToCopyPlaceholder' => [1, 2]
            ];
        }
        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => $placeholders
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);
        $response->assertStatus(Response::HTTP_PRECONDITION_FAILED);
    }

    public function testExactAllowedPlaceholders()
    {
        chdir(base_path());
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $task = ServiceTaskFactory::createDocumentApproval();
        $taskFile = $task->getFiles('pdf')[0];
        $account = $task->account;

        $colleagueUser = UserFactory::createColleague($account);
        $clientUser = UserFactory::createClientUser($account);

        CompanyUserFactory::create($task->company, $colleagueUser);
        CompanyUserFactory::create($task->company, $clientUser);

        $this->be($colleagueUser);

        // create 200 placeholders with copy (this means it will be the exact amount of allowed placeholders)
        $placeholders = [];
        for ($i = 0; $i < 100; $i++) {
            $placeholders[] = [
                Placeholder::PAGE => 1,
                Placeholder::TOP => 21,
                Placeholder::BOTTOM => 65,
                Placeholder::LEFT => 19,
                Placeholder::RIGHT => 34,
                'w' => ImagePlaceholder::DEFAULT_WIDTH,
                'h' => ImagePlaceholder::DEFAULT_HEIGHT,
                Placeholder::TITLE => 'Placeholder',
                Placeholder::TYPE => Placeholder::TYPE_STAMP_SIGNATURE,
                Placeholder::FILLED_BY => $clientUser->id,
                'pagesSelectedToCopyPlaceholder' => [1, 2]
            ];
        }
        $payload = [
            ServiceTask::CUSTOM_SIGNING_ORDER_ENABLED => false,
            'pages' => [
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ],
                [
                    'height' => 842,
                    'width' => 595,
                ]
            ],
            'placeholders' => $placeholders
        ];

        $response = $this->post($account->route(self::STORE_ENDPOINT, [$task->id, $taskFile->id]), $payload);
        $response->assertStatus(Response::HTTP_OK);
    }
}
