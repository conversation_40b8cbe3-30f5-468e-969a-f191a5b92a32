<?php

namespace Tests\Feature\DashboardCategory;

use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\DashboardCategoryFactory;
use Tests\Support\Factories\DashboardCategoryUserWidgetFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\Factories\WidgetFactory;
use Tests\Support\TestCase;

class DashboardCategoryTest extends TestCase
{
    public function testIndex()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $this->be($user);

        $userWidget = WidgetFactory::createUserWidget($user);
        $category = DashboardCategoryFactory::create($user);
        DashboardCategoryUserWidgetFactory::create($category, $userWidget);

        $response = $this->get($account->route('new.dashboard_category.index'));

        $response->assertOk();
        $data = json_decode($response->content());
        $this->assertCount(1, $data);
        $this->assertCount(1, $data[0]->user_widgets);
        $this->assertEquals($userWidget->id, $data[0]->user_widgets[0]->id);
    }

    public function testIndexWithSoftDeletedUserWidget()
    {
        $account = AccountFactory::create();
        $user = UserFactory::createColleague($account);
        $this->be($user);

        $userWidget = WidgetFactory::createUserWidget($user);
        $userWidget->delete();
        $category = DashboardCategoryFactory::create($user);
        DashboardCategoryUserWidgetFactory::create($category, $userWidget);

        $response = $this->get($account->route('new.dashboard_category.index'));

        $response->assertOk();
        $data = json_decode($response->content());
        $this->assertCount(1, $data);
        $this->assertCount(0, $data[0]->user_widgets);
    }
}
