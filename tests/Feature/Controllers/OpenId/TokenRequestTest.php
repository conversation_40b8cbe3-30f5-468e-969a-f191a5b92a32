<?php

namespace Tests\Feature\Controllers\OpenId;

use App\Auth\Providers\Jwt\Constants\JwtClaimTypes;
use App\Auth\Providers\OpenId\Constants\StandardScopes;
use App\Auth\Providers\OpenId\Constants\TokenResponse;
use App\Auth\Providers\OpenId\Helpers\Scopes;
use App\Auth\Helpers\JwtHelper as Jwt;
use Tests\Helper\DataCreator;

class TokenRequestTest extends OpenIdTestCase
{

    public function testSuccessfulTokenRequest()
    {
        $this->dataCreator = new DataCreator($this->dataCreatorParameters);
        $user = $this->dataCreator->getUser();
        $this->be($user);

        $this->assertSuccessfulTokenAcquisition($this->client_id, $this->redirectUrl);
    }

    public function testTokenIncludesRequestedScopes()
    {
        $this->dataCreator = new DataCreator($this->dataCreatorParameters);
        $user = $this->dataCreator->getUser();
        $this->be($user);

        $claims = $this->getIdTokenClaims([StandardScopes::OPENID]);
        $this->assertNotNull($claims[JwtClaimTypes::USER_ID]);
        $this->assertNotNull($claims[JwtClaimTypes::SUBJECT]);
        $this->assertFalse(isset($claims[JwtClaimTypes::EMAIL]));

        $claims = $this->getIdTokenClaims([StandardScopes::OPENID, StandardScopes::EMAIL]);
        $this->assertNotNull($claims[JwtClaimTypes::USER_ID]);
        $this->assertNotNull($claims[JwtClaimTypes::SUBJECT]);
        $this->assertTrue(isset($claims[JwtClaimTypes::EMAIL]));

        $claims = $this->getIdTokenClaims([StandardScopes::OPENID, StandardScopes::EMAIL, StandardScopes::PROFILE]);
        $this->assertNotNull($claims[JwtClaimTypes::USER_ID]);
        $this->assertNotNull($claims[JwtClaimTypes::SUBJECT]);
        $this->assertNotNull($claims[JwtClaimTypes::EMAIL]);
    }

    /**
     * @param  array  $scopes
     * @return array
     */
    protected function getIdTokenClaims(array $scopes): array
    {
        $payload = $this->assertSuccessfulTokenAcquisition(
            $this->client_id,
            $this->redirectUrl,
            Scopes::serialize($scopes)
        );
        return Jwt::parse($payload[TokenResponse::IDENTITY_TOKEN])->claims()->all();
    }
}