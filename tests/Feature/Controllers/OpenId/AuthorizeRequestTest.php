<?php

namespace Tests\Feature\Controllers\OpenId;

use Symfony\Component\HttpFoundation\Response;
use Tests\Helper\DataCreator;

class AuthorizeRequestTest extends OpenIdTestCase
{
    public function testAfasAuthenticationParameterValidation()
    {
        $this->dataCreator = new DataCreator($this->dataCreatorParameters);
        $user = $this->dataCreator->getUser();
        $this->be($user);

        // Bad return uri
        $this->assertHttpGet(
            self::$openIdAuthorizationEndpoint,
            $this->getAuthorizationRequestParameters($this->client_id, "banana"),
            Response::HTTP_BAD_REQUEST
        );
        // Insecure
        $this->assertHttpGet(
            self::$openIdAuthorizationEndpoint,
            $this->getAuthorizationRequestParameters($this->client_id, "HTTP://example.com"),
            Response::HTTP_BAD_REQUEST
        );
        // Not Authorized
        $this->assertHttpGet(
            self::$openIdAuthorizationEndpoint,
            $this->getAuthorizationRequestParameters($this->client_id, "https://example.com"),
            Response::HTTP_BAD_REQUEST
        );
        // Good
        $this->assertHttpGet(
            self::$openIdAuthorizationEndpoint,
            $this->getAuthorizationRequestParameters($this->client_id, $this->redirectUrl),
            Response::HTTP_FOUND
        );
    }

    public function testAfasAuthenticationWithoutLoggedInUser()
    {
        $this->dataCreator = new DataCreator($this->dataCreatorParameters);
        $user = $this->dataCreator->getUser();

        // Initial call with a logged out user shows the login page
        $authResponse = $this->assertHttpGet(
            self::$openIdAuthorizationEndpoint,
            $this->getAuthorizationRequestParameters(
                $this->client_id,
                $this->redirectUrl
            ),
            Response::HTTP_FOUND
        );
        $authResponse->assertLocationPrefix(route(self::$getLoginEndpoint));
        $authResponse->assertReturnUrlPrefix(route(self::$openIdAuthorizationEndpoint));

        // After login, user is redirected to the original authentication endpoint
        $loginResponse = $this->postLoginCredentials($user);
        $loginResponse->assertLocationPrefix(route(self::$openIdAuthorizationEndpoint));

        // Call to Authentication endpoint now succeeds
        $this->assertSuccessfulTokenAcquisition($this->client_id, $this->redirectUrl);
    }
}
