<?php

namespace Tests\Feature\Postbode;

use App\Account;
use App\Enums\LetterRequests\LetterRequestStatus;
use App\Models\LetterRequest\LetterRequest;
use App\Models\LetterRequest\TaskFileLetterRequest;
use App\Repositories\Http\Postbode\PostbodeApiRepository;
use App\Services\Postbode\LetterService;
use App\TaskAuditLog;
use App\ValueObject\Postbode\Document;
use App\ValueObject\Postbode\Letter;
use App\ValueObject\Postbode\LetterResponse;
use Mockery;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\ServiceTaskFactory;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class LetterServiceTest extends TestCase
{
    use LetterTrait;

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    public function testSendTaskFileLetter(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $account = Account::getAdminAccount();

        // handle task and task file
        $serviceTask = ServiceTaskFactory::createDocumentApproval(
            AccountServiceFactory::createManualTasks($account),
            CompanyFactory::create($account)
        );
        $serviceTask->save();
        $taskFile = $serviceTask->files()->first();

        // create letter
        $content = $this->asset('Postbode/test_letter.pdf');
        $document = new Document('test_letter.pdf', $content);
        $user = UserFactory::createClientUser($account);
        $letter = new Letter($user, [$document->toArray()], 'NL', [Letter::METADATA_TASK_FILE_ID => $taskFile->id]);

        // mock letter response
        $postbodeApiRepositoryMock = Mockery::mock(PostbodeApiRepository::class);
        $postbodeApiRepositoryMock
            ->shouldReceive('sendLetter')
            ->andReturn(new LetterResponse($this->getLetterWithStatus(LetterRequestStatus::SENT, $taskFile->id)));
        $this->app->instance(PostbodeApiRepository::class, $postbodeApiRepositoryMock);

        // send letter and check if saved
        $letterService = resolve(LetterService::class);
        $letterService->sendTaskFileLetter($taskFile, $letter);

        $letterRequest = LetterRequest::query()->where(LetterRequest::EXTERNAL_ID, 2113461)->first();
        $this->assertEquals(LetterRequestStatus::SENT->value, $letterRequest->status->value);
        $this->assertCount(1, $letterRequest->taskFileLetterRequests);
    }

    /**
     * @runInSeparateProcess
     * @preserveGlobalState disabled
     */
    public function testUpdateTaskDocumentWithSavedLetter(): void
    {
        require('vendor-local/setasign/SetaPDF/Autoload.php');

        $account = Account::getAdminAccount();

        // handle task and task file
        $serviceTask = ServiceTaskFactory::createDocumentApproval(
            AccountServiceFactory::createManualTasks($account),
            CompanyFactory::create($account)
        );
        $serviceTask->save();
        $taskFile = $serviceTask->files()->first();

        // create letter and new file content
        $content = $this->asset('Postbode/test_letter.pdf');
        $document = new Document('test_letter.pdf', $content);
        $user = UserFactory::createClientUser($account);
        $letter = new Letter($user, [$document->toArray()], 'NL', [Letter::METADATA_TASK_FILE_ID => $taskFile->id]);

        // mock send letter response and then mock get letter response with new content
        $postbodeApiRepositoryMock = Mockery::mock(PostbodeApiRepository::class);
        $postbodeApiRepositoryMock
            ->shouldReceive('sendLetter')
            ->andReturn(new LetterResponse($this->getLetter($taskFile->id)));
        $postbodeApiRepositoryMock
            ->shouldReceive('getLetter')
            ->andReturn(new LetterResponse($this->getLetterWithPdf($content, $taskFile->id)));
        $this->app->instance(PostbodeApiRepository::class, $postbodeApiRepositoryMock);

        // send letter and check if saved
        $letterService = resolve(LetterService::class);
        $letterService->sendTaskFileLetter($taskFile, $letter);

        $letterRequest = LetterRequest::query()->where(LetterRequest::EXTERNAL_ID, 2113461)->first();
        $this->assertTrue(LetterRequest::query()->where(LetterRequest::EXTERNAL_ID, 2113461)->exists());

        // update letter with new content from mocked response
        $letterService->updateLetterDocument(2113461, LetterRequestStatus::SENT);

        // get task file from letter request
        $taskFileLetterRequest = TaskFileLetterRequest::query()->where(TaskFileLetterRequest::LETTER_REQUEST_ID, $letterRequest->id)->first();

        $this->assertNotEquals($taskFile->checksum, $taskFileLetterRequest->taskFile->checksum);
        $this->assertCount(
            2,
            $serviceTask->auditLogs()->where(TaskAuditLog::ACTION, TaskAuditLog::FILE_ADDED)->get()
        );
    }

    public function testUpdateTaskDocumentWithoutSavedLetter(): void
    {
        $letterService = resolve(LetterService::class);

        $this->assertFalse($letterService->updateLetterDocument(123, LetterRequestStatus::SENT));
    }
}