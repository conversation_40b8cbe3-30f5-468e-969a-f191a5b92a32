<?php

namespace Tests\Feature\EmailTemplate\Preview;

use App\Models\EmailTemplate\EmailTemplate;
use App\Services\EmailTemplate\EmailTemplateService;
use Tests\Support\Factories\UserFactory;
use Tests\Support\TestCase;

class DividendTaskInformPayTest extends TestCase
{
    private ?string $templateUserInvitationJson = '{
            "id": null,
            "language": "nl",
            "name": "Variant 222",
            "type": "dividend_task_customer_inform_pay",
            "fields": [
                {
                    "content": "Betalen voor :company",
                    "key": "subject"
                },
                {
                    "content": "Hoi :firstname!",
                    "key": "greetings"
                },
                {
                    "content": "Kun je tååk &quot;:title&quot; voor :company ff voor me \'\'checken\'\'?!",
                    "key": "intro"
                },
                {
                    "content": "Je bent per saldo een bedrag van :currency :amount aan dividendbelasting verschuldigd. <PERSON>ak het over naar de Belastingdienst op rekeningnummer NL26INGB0000441290. Dit bedrag moet voor het eind van de maand (uiterlijk op :final_pay_date) op de rekening van de Belastingdienst staan.",
                    "key": "money_pay"
                },
                {
                    "content": "Ontvangen niet zichtbaar",
                    "key": "money_receive"
                },
                {
                    "content": "Nul niet zichtbaar",
                    "key": "money_zero"
                },
                {
                    "content": "Klikky klaklklak",
                    "key": "click_button_below"
                },
                {
                    "content": "Check \":title\" -->",
                    "key": "button"
                },
                {
                    "content": "Doei!!!<br><br>:account",
                    "key": "regards"
                }
            ],
            "conditions": [
                {
                    "available": true,
                    "event": "task_dividend_tax_inform_pay",
                    "label": "Dividendbelasting",
                    "selected": true
                }
            ]
        }';

    public function testPreviewDividendTaskInformPay()
    {
        $user = UserFactory::createAdminUser();
        $this->be($user);
        $user->firstname = 'Gørdå';
        $user->language = 'nl';
        $user->save();

        $url = $user->account->route('settings.email.template.store');
        $response = $this->postJson($url, json_decode($this->templateUserInvitationJson, true));
        $response->assertOk();
        $responseArray = json_decode($response->getContent(), true, flags: JSON_THROW_ON_ERROR);
        $responseTemplate = $responseArray['data'];
        $this->assertGreaterThanOrEqual(1, $responseTemplate['id']);

        /** @var EmailTemplateService $service */
        $service = resolve(EmailTemplateService::class);
        $template = EmailTemplate::findOrFail($responseTemplate['id']);
        $mail = $service->testMail($user, $template, false);

        $mail->assertSeeInHtml('Hoi ' . $user->firstname . '!');
        $mail->assertSeeInHtml('Kun je tååk &quot;Testaangifte ', false);
        $mail->assertSeeInHtml(' voor Testbedrijf BV ff ');
        $mail->assertSeeInHtml('Je bent per saldo een bedrag van € 10.000 aan dividendbelasting verschuldigd.');
        $mail->assertSeeInHtml('>Check &quot;Testaangifte ', false);
        $mail->assertDontSeeInHtml('niet zichtbaar');
        $mail->assertDontSeeInHtml(':final_pay_date');
        $mail->assertDontSeeInHtml(':reference');
    }
}
