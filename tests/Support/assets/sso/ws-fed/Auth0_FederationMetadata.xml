<EntityDescriptor entityID="urn:auth0securelogin-dev" xmlns="urn:oasis:names:tc:SAML:2.0:metadata">
    <RoleDescriptor xsi:type="fed:SecurityTokenServiceType"
                    protocolSupportEnumeration="http://docs.oasis-open.org/wsfed/federation/200706"
                    ServiceDisplayName="urn:auth0securelogin-dev" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xmlns:fed="http://docs.oasis-open.org/wsfed/federation/200706">
        <KeyDescriptor use="signing">
            <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                <X509Data>
                    <X509Certificate>
                        MIIDEzCCAfugAwIBAgIJAdDHw5xQ2DkpMA0GCSqGSIb3DQEBCwUAMCcxJTAjBgNVBAMTHHNlY3VyZWxvZ2luLWRldi5ldS5hdXRoMC5jb20wHhcNMTkwNzMwMTUyMDIwWhcNMzMwNDA3MTUyMDIwWjAnMSUwIwYDVQQDExxzZWN1cmVsb2dpbi1kZXYuZXUuYXV0aDAuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzj+oOXg8sr/Apk+QRcUvStlYtKbC9NGoVketFSBT0JTUBy5/8CPo8FF+458IqSETkKY5/PUTSo2N7hyskd+HL6AhpnOCZ9IinNmHsPuX+KwSEmOprsnRy07QwHCI1pF13/iledExGP7clREr6bc6oxYX6r067aTnzzBSBWhkjdw1M1jJpbRym3VQilHJf8e9AmTYc0eJRnvNvB/z/1uxJpi+PYd8pX4PuULOaTRJzBvDJ333BQKdkGRR3gPh+Z40wj0Bk3isr/AaChPpBCq5Rdy5FbEh6lC9lrRIqAFkNY4mu2icBwllMLd46y6Uc+OMtSn5pHjJUyGA0aJT/dueNwIDAQABo0IwQDAPBgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBSWyWMc3hFFZ3iBuidIgMTgDf9KpjAOBgNVHQ8BAf8EBAMCAoQwDQYJKoZIhvcNAQELBQADggEBAJR2yAHE3CfhxmehTuT1xCwcA+QJRq0wimQCSA8yVbXTOhX0sw2Im7nnMqwZApvxAr/O25ko85oB4Ol7azVDLaEzNzd93igNVHp4Nh4vvHV3rZpbPORLx7xiK9Cakl1k/y9tiDqRgTp0S/K2bw04STT0ujI5/m+eACQl+F6T+fAst4FN15/Q4v8BpfIah587yWp84r5C6hiNYR6C46ZZqU6Z+WscJuBHHndHQBqD96IAX133mnILuA3T/X7LBxPZVLmhFFvHjgGXTeiDc01ZAimZmif2LwedpNLybUZLOnO7MFxe4ZmhQGVWYVTn5lyUZGwK2htWjJIb3VZq+dICeSc=
                    </X509Certificate>
                </X509Data>
            </KeyInfo>
        </KeyDescriptor>
        <fed:TokenTypesOffered>
            <fed:TokenType Uri="urn:oasis:names:tc:SAML:2.0:assertion"/>
            <fed:TokenType Uri="urn:oasis:names:tc:SAML:1.0:assertion"/>
        </fed:TokenTypesOffered>
        <fed:ClaimTypesOffered>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress" Optional="true"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>E-Mail Address</auth:DisplayName>
                <auth:Description>The e-mail address of the user</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname" Optional="true"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Given Name</auth:DisplayName>
                <auth:Description>The given name of the user</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name" Optional="true"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Name</auth:DisplayName>
                <auth:Description>The unique name of the user</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname" Optional="true"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Surname</auth:DisplayName>
                <auth:Description>The surname of the user</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier" Optional="true"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Name ID</auth:DisplayName>
                <auth:Description>The SAML name identifier of the user</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/upn" Optional="true"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>UPN</auth:DisplayName>
                <auth:Description>The user principal name (UPN) of the user</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/claims/Group" Optional="true"
                            xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Group</auth:DisplayName>
                <auth:Description>A group that the user is a member of</auth:Description>
            </auth:ClaimType>
        </fed:ClaimTypesOffered>
        <fed:PassiveRequestorEndpoint>
            <EndpointReference xmlns="http://www.w3.org/2005/08/addressing">
                <Address>https://securelogin-dev.eu.auth0.com/wsfed</Address>
            </EndpointReference>
        </fed:PassiveRequestorEndpoint>
    </RoleDescriptor>
</EntityDescriptor>