<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt19/bd/********/entrypoints/bd-rpt-ob-suppletie-2025.xsd -->
<!-- Intellectual Property State of the Netherlands -->
<!-- Created on: 22-10-2024 16:00:00 -->
<xbrli:xbrl xml:lang="nl" xmlns:bd-i="http://www.nltaxonomie.nl/nt19/bd/********/dictionary/bd-data" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:xbrli="http://www.xbrl.org/2003/instance">
  <link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt19/bd/********/entrypoints/bd-rpt-ob-suppletie-2025.xsd"/>
  <xbrli:context id="Msg">
    <xbrli:entity>
      <xbrli:identifier scheme="www.belastingdienst.nl/omzetbelastingnummer">001001735B02</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:startDate>2025-07-01</xbrli:startDate>
      <xbrli:endDate>2025-09-30</xbrli:endDate>
    </xbrli:period>
  </xbrli:context>
  <xbrli:unit id="EUR">
    <xbrli:measure>iso4217:EUR</xbrli:measure>
  </xbrli:unit>
  <bd-i:SoftwareVendorAccountNumber contextRef="Msg">swo99999</bd-i:SoftwareVendorAccountNumber>
  <bd-i:SoftwarePackageName contextRef="Msg">ApplicatieNaam</bd-i:SoftwarePackageName>
  <bd-i:SoftwarePackageVersion contextRef="Msg">ApplicatieVersie</bd-i:SoftwarePackageVersion>
  <bd-i:MessageReferenceSupplierVAT contextRef="Msg">OB-14-00G11</bd-i:MessageReferenceSupplierVAT>
  <bd-i:DateTimeCreation contextRef="Msg">************</bd-i:DateTimeCreation>
  <bd-i:TaxConsultantNumber contextRef="Msg">234564</bd-i:TaxConsultantNumber>
  <bd-i:ContactType contextRef="Msg">INT</bd-i:ContactType>
  <bd-i:ContactInitials contextRef="Msg">Initials</bd-i:ContactInitials>
  <bd-i:ContactPrefix contextRef="Msg">Prefix</bd-i:ContactPrefix>
  <bd-i:ContactSurname contextRef="Msg">Surname</bd-i:ContactSurname>
  <bd-i:TaxedTurnoverSuppliesServicesGeneralTariff decimals="INF" contextRef="Msg" unitRef="EUR">2000</bd-i:TaxedTurnoverSuppliesServicesGeneralTariff>
  <bd-i:TaxedTurnoverSuppliesServicesReducedTariff decimals="INF" contextRef="Msg" unitRef="EUR">8000</bd-i:TaxedTurnoverSuppliesServicesReducedTariff>
  <bd-i:ValueAddedTaxAmountTotalNew decimals="INF" contextRef="Msg" unitRef="EUR">900</bd-i:ValueAddedTaxAmountTotalNew>
  <bd-i:ValueAddedTaxAmountTotalOld decimals="INF" contextRef="Msg" unitRef="EUR">200</bd-i:ValueAddedTaxAmountTotalOld>
  <bd-i:ValueAddedTaxOwed decimals="INF" contextRef="Msg" unitRef="EUR">900</bd-i:ValueAddedTaxOwed>
  <bd-i:ValueAddedTaxToBePaidAdditionalToBePaidBack decimals="INF" contextRef="Msg" unitRef="EUR">700</bd-i:ValueAddedTaxToBePaidAdditionalToBePaidBack>
  <bd-i:ValueAddedTaxSuppliesServicesGeneralTariff decimals="INF" contextRef="Msg" unitRef="EUR">420</bd-i:ValueAddedTaxSuppliesServicesGeneralTariff>
  <bd-i:ValueAddedTaxSuppliesServicesReducedTariff decimals="INF" contextRef="Msg" unitRef="EUR">480</bd-i:ValueAddedTaxSuppliesServicesReducedTariff>
</xbrli:xbrl>
