<?xml version="1.0" encoding="UTF-8"?>
<!-- Testcase CASE_BNL_2, stroom MEDIH2017-XBRL, based on Taxonomy NT12 created by B/CA-CCT (DIT IS EEN TESTBERICHT) -->
<!-- Situatie: Alleenstaande belpl met eigen woning en in loondienst.

Persoonlijke situatie bevat 
AHK afbouw o.b.v. inkomen > 40.000

Inkomen BNL 43.278,-
EW woz 175.000
Betaalde rente 9000
Restant lening 143.000
AHKberekend: 1507
AK berekend: 2833
Te ontvangen: -/- 3.478 excl bel rente

geen aanslag ZVW

 -->
<!-- Created: 201709131910 -->
<!-- Verwacht resultaat: Goed Geval -->
<xbrli:xbrl xmlns:bd-i="http://www.nltaxonomie.nl/nt12/bd/20171213/dictionary/bd-data" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:xlink="http://www.w3.org/1999/xlink" xml:lang="nl">
	<link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt12/bd/20171213/entrypoints/bd-rpt-ih-sba-2017.xsd"/>
	<xbrli:context id="c1d1">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2017-01-01</xbrli:startDate>
			<xbrli:endDate>2017-12-31</xbrli:endDate>
		</xbrli:period>
	</xbrli:context>
	<xbrli:unit id="EUR">
		<xbrli:measure>iso4217:EUR</xbrli:measure>
	</xbrli:unit>
	<xbrli:unit id="PURE">
		<xbrli:measure>xbrli:pure</xbrli:measure>
	</xbrli:unit>
	<bd-i:ContributionBaseIncome contextRef="c1d1" decimals="INF" unitRef="EUR">33791</bd-i:ContributionBaseIncome>
	<bd-i:DateOfBirth contextRef="c1d1">1976-07-10</bd-i:DateOfBirth>
	<bd-i:EmployedPersonsTaxCreditCalculated contextRef="c1d1" decimals="INF" unitRef="EUR">2833</bd-i:EmployedPersonsTaxCreditCalculated>
	<bd-i:FiscalYear contextRef="c1d1">2017</bd-i:FiscalYear>
	<bd-i:GeneralTaxCreditCalculated contextRef="c1d1" decimals="INF" unitRef="EUR">1507</bd-i:GeneralTaxCreditCalculated>
	<bd-i:IncomeTaxBracket1 contextRef="c1d1" decimals="INF" unitRef="EUR">1778</bd-i:IncomeTaxBracket1>
	<bd-i:IncomeTaxBracket2 contextRef="c1d1" decimals="INF" unitRef="EUR">1815</bd-i:IncomeTaxBracket2>
	<bd-i:IncomeTaxBracket3 contextRef="c1d1" decimals="INF" unitRef="EUR">733</bd-i:IncomeTaxBracket3>
	<bd-i:NoticeOfAssessmentDate contextRef="c1d1">2018-06-01</bd-i:NoticeOfAssessmentDate>
	<bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly contextRef="c1d1">2018-09-13</bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly>
	<bd-i:NoticeOfAssessmentNumber contextRef="c1d1">1111.11.134.H.76.01</bd-i:NoticeOfAssessmentNumber>
	<bd-i:ObjectionDeadlineDate contextRef="c1d1">2018-07-13</bd-i:ObjectionDeadlineDate>
	<bd-i:OverallIncome contextRef="c1d1" decimals="INF" unitRef="EUR">35590</bd-i:OverallIncome>
	<bd-i:SocialInsuranceContributionBalance contextRef="c1d1" decimals="INF" unitRef="EUR">9343</bd-i:SocialInsuranceContributionBalance>
	<bd-i:SocialInsuranceRatePercentage contextRef="c1d1" decimals="INF" unitRef="PURE">27.650</bd-i:SocialInsuranceRatePercentage>
	<bd-i:TaxAppropriation contextRef="c1d1">H</bd-i:TaxAppropriation>
	<bd-i:TaxAssesmentAmountExclusiveOfTaxInterestCompensate contextRef="c1d1" decimals="INF" unitRef="EUR">-3478</bd-i:TaxAssesmentAmountExclusiveOfTaxInterestCompensate>
	<bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance contextRef="c1d1" decimals="INF" unitRef="EUR">-3536</bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance>
	<bd-i:TaxBox1 contextRef="c1d1" decimals="INF" unitRef="EUR">13669</bd-i:TaxBox1>
	<bd-i:TaxConsultantNameAddress1 contextRef="c1d1">B. Eswaar</bd-i:TaxConsultantNameAddress1>
	<bd-i:TaxConsultantNameAddress2 contextRef="c1d1">Consu lentes 123</bd-i:TaxConsultantNameAddress2>
	<bd-i:TaxConsultantNameAddress3 contextRef="c1d1">Zuthpen</bd-i:TaxConsultantNameAddress3>
	<bd-i:TaxConsultantNameAddress4 contextRef="c1d1">7203BE</bd-i:TaxConsultantNameAddress4>
	<bd-i:TaxCreditsCombined contextRef="c1d1" decimals="INF" unitRef="EUR">4340</bd-i:TaxCreditsCombined>
	<bd-i:TaxReturnMessageType contextRef="c1d1">62</bd-i:TaxReturnMessageType>
	<bd-i:TaxableIncomeEmploymentOwnerOccupiedHouse contextRef="c1d1" decimals="INF" unitRef="EUR">35590</bd-i:TaxableIncomeEmploymentOwnerOccupiedHouse>
	<bd-i:TaxpayerNameAddress1 contextRef="c1d1">*********</bd-i:TaxpayerNameAddress1>
	<bd-i:TaxpayerNameAddress2 contextRef="c1d1">Kerkweg 23</bd-i:TaxpayerNameAddress2>
	<bd-i:TaxpayerNameAddress3 contextRef="c1d1">Vleuten</bd-i:TaxpayerNameAddress3>
	<bd-i:TaxpayerNameAddress4 contextRef="c1d1">3425JZ</bd-i:TaxpayerNameAddress4>
</xbrli:xbrl>
