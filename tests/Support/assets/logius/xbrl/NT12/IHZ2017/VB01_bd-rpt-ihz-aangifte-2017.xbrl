<?xml version="1.0" encoding="UTF-8"?>
<!--XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt12/bd/20171213/entrypoints/bd-rpt-ihz-aangifte-2017.xsd -->
<!--entrypoint http://www.nltaxonomie.nl/nt12/bd/20171213/entrypoints/bd-rpt-ihz-aangifte-2017.xsd -->
<!--Intellectual Property State of the Netherlands -->
<!--Created on: Created on: 14-11-2017 12:00:00 -->
<xbrli:xbrl xml:lang="nl" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://xbrl.org/2006/xbrldi http://www.xbrl.org/2006/xbrldi-2006.xsd" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:bd-i="http://www.nltaxonomie.nl/nt12/bd/20171213/dictionary/bd-data" xmlns:bd-t="http://www.nltaxonomie.nl/nt12/bd/20171213/dictionary/bd-tuples" xmlns:bd-dim-mem="http://www.nltaxonomie.nl/nt12/bd/20171213/dictionary/bd-domain-members" xmlns:bd-dim-dim="http://www.nltaxonomie.nl/nt12/bd/20171213/validation/bd-axes" xmlns:xbrldi="http://xbrl.org/2006/xbrldi" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:xlink="http://www.w3.org/1999/xlink">
	<link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt12/bd/20171213/entrypoints/bd-rpt-ihz-aangifte-2017.xsd"/>
	<xbrli:context id="CD_Declarant">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2017-01-01</xbrli:startDate>
			<xbrli:endDate>2017-12-31</xbrli:endDate>
		</xbrli:period>
		<xbrli:scenario>
			<xbrldi:explicitMember dimension="bd-dim-dim:PartyDimension">bd-dim-mem:Declarant</xbrldi:explicitMember>
		</xbrli:scenario>
	</xbrli:context>
	<xbrli:unit id="EUR">
		<xbrli:measure>iso4217:EUR</xbrli:measure>
	</xbrli:unit>
	<xbrli:unit id="PURE">
		<xbrli:measure>xbrli:pure</xbrli:measure>
	</xbrli:unit>
	<bd-i:DateOfBirth contextRef="CD_Declarant">1954-08-24</bd-i:DateOfBirth>
	<bd-i:Initials contextRef="CD_Declarant">P</bd-i:Initials>
	<bd-i:LivingTogetherSpousesExists contextRef="CD_Declarant">false</bd-i:LivingTogetherSpousesExists>
	<bd-i:OwnHouseBenefitsPartyFilingTaxReturnPart decimals="INF" contextRef="CD_Declarant" unitRef="EUR">1092</bd-i:OwnHouseBenefitsPartyFilingTaxReturnPart>
	<bd-i:OwnHouseDeductibleNonOrMinorAcquisitionDebtPartyFilingTaxReturnPartAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">1092</bd-i:OwnHouseDeductibleNonOrMinorAcquisitionDebtPartyFilingTaxReturnPartAmount>
	<bd-i:OwnHouseIncomeSharePartyFilingTaxReturn decimals="INF" contextRef="CD_Declarant" unitRef="EUR">1092</bd-i:OwnHouseIncomeSharePartyFilingTaxReturn>
	<bd-i:OwnHousePrincipalResidenceRentalValueTotal decimals="INF" contextRef="CD_Declarant" unitRef="EUR">1092</bd-i:OwnHousePrincipalResidenceRentalValueTotal>
	<bd-i:PrefilledTaxReturnIncomeUseWatermark contextRef="CD_Declarant">64191864191864191864191864191832</bd-i:PrefilledTaxReturnIncomeUseWatermark>
	<bd-i:SoftwarePackageName contextRef="CD_Declarant">NaamApp12</bd-i:SoftwarePackageName>
	<bd-i:SoftwarePackageVersion contextRef="CD_Declarant">Versie12</bd-i:SoftwarePackageVersion>
	<bd-i:SoftwareSupplierCode contextRef="CD_Declarant">AAAA</bd-i:SoftwareSupplierCode>
	<bd-i:SoftwareVendorAccountNumber contextRef="CD_Declarant">SWO12356</bd-i:SoftwareVendorAccountNumber>
	<bd-i:Surname contextRef="CD_Declarant">Enden</bd-i:Surname>
	<bd-i:TaxConsultantInitials contextRef="CD_Declarant">J.</bd-i:TaxConsultantInitials>
	<bd-i:TaxConsultantNumber contextRef="CD_Declarant">971923</bd-i:TaxConsultantNumber>
	<bd-i:TaxConsultantPrefix contextRef="CD_Declarant">de</bd-i:TaxConsultantPrefix>
	<bd-i:TaxConsultantSurname contextRef="CD_Declarant">Vries</bd-i:TaxConsultantSurname>
	<bd-i:TaxConsultantTelephoneNumber contextRef="CD_Declarant">(*************</bd-i:TaxConsultantTelephoneNumber>
	<bd-i:TaxReturnMessageType contextRef="CD_Declarant">62</bd-i:TaxReturnMessageType>
	<bd-i:TelephoneNumber contextRef="CD_Declarant">06-********</bd-i:TelephoneNumber>
	<bd-i:WagesHealthInsuranceLawMaximumContribution contextRef="CD_Declarant">false</bd-i:WagesHealthInsuranceLawMaximumContribution>
	<bd-t:OwnHousePrincipalResidenceObjectSpecification>
		<bd-t:AddressPresentation>
			<bd-i:HouseNumberAddition contextRef="CD_Declarant">6101</bd-i:HouseNumberAddition>
			<bd-i:HouseNumberNL decimals="INF" contextRef="CD_Declarant" unitRef="PURE">46101</bd-i:HouseNumberNL>
			<bd-i:PlaceOfResidenceNL contextRef="CD_Declarant">11746301</bd-i:PlaceOfResidenceNL>
			<bd-i:PostalCodeNL contextRef="CD_Declarant">1174FA</bd-i:PostalCodeNL>
			<bd-i:StreetNameNL contextRef="CD_Declarant">11746101</bd-i:StreetNameNL>
		</bd-t:AddressPresentation>
		<bd-i:OwnHousePrincipalResidenceImmovablePropertyLawValue decimals="INF" contextRef="CD_Declarant" unitRef="EUR">145700</bd-i:OwnHousePrincipalResidenceImmovablePropertyLawValue>
		<bd-i:OwnHousePrincipalResidencePeriodEnd contextRef="CD_Declarant">2017-12-12</bd-i:OwnHousePrincipalResidencePeriodEnd>
		<bd-i:OwnHousePrincipalResidencePeriodStart contextRef="CD_Declarant">2017-01-12</bd-i:OwnHousePrincipalResidencePeriodStart>
	</bd-t:OwnHousePrincipalResidenceObjectSpecification>
</xbrli:xbrl>
