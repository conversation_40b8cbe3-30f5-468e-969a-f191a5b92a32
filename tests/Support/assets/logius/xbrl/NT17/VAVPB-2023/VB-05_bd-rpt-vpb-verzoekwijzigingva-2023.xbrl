<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report entrypoint http://www.nltaxonomie.nl/nt17/bd/20221207/entrypoints/bd-rpt-vpb-verzoekwijzigingva-2023.xsd -->
<!-- Intellectual Property State of the Netherlands -->
<!-- Created on: 03-10-2022 16:00:00 -->
<xbrli:xbrl xml:lang="nl" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:bd-i="http://www.nltaxonomie.nl/nt17/bd/20221207/dictionary/bd-data" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:xlink="http://www.w3.org/1999/xlink">
	<link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt17/bd/20221207/entrypoints/bd-rpt-vpb-verzoekwijzigingva-2023.xsd"/>
	<xbrli:context id="Declarant">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2023-01-01</xbrli:startDate>
			<xbrli:endDate>2023-12-31</xbrli:endDate>
		</xbrli:period>
	</xbrli:context>
	<xbrli:unit id="uEUR">
		<xbrli:measure>iso4217:EUR</xbrli:measure>
	</xbrli:unit>
	<bd-i:AdvanceLeviesSettled decimals="INF" contextRef="Declarant" unitRef="uEUR">27000</bd-i:AdvanceLeviesSettled>
	<bd-i:CorporationTaxTaxableAmount decimals="INF" contextRef="Declarant" unitRef="uEUR">180000</bd-i:CorporationTaxTaxableAmount>
	<bd-i:FunctionalCurrencySchemeExists contextRef="Declarant">false</bd-i:FunctionalCurrencySchemeExists>
	<bd-i:LossesToBeSetOffThisYearTotal decimals="INF" contextRef="Declarant" unitRef="uEUR">0</bd-i:LossesToBeSetOffThisYearTotal>
	<bd-i:SoftwarePackageName contextRef="Declarant">SWPN</bd-i:SoftwarePackageName>
	<bd-i:SoftwarePackageVersion contextRef="Declarant">a1</bd-i:SoftwarePackageVersion>
	<bd-i:SoftwareSupplierCode contextRef="Declarant">VAVP</bd-i:SoftwareSupplierCode>
	<bd-i:SoftwareVendorAccountNumber contextRef="Declarant">SWO99999</bd-i:SoftwareVendorAccountNumber>
	<bd-i:TaxConsultantInitials contextRef="Declarant">l</bd-i:TaxConsultantInitials>
	<bd-i:TaxConsultantNumber contextRef="Declarant">184561</bd-i:TaxConsultantNumber>
	<bd-i:TaxConsultantPrefix contextRef="Declarant">het</bd-i:TaxConsultantPrefix>
	<bd-i:TaxConsultantSurname contextRef="Declarant">Uitstel1</bd-i:TaxConsultantSurname>
	<bd-i:TaxConsultantTelephoneNumber contextRef="Declarant">**********</bd-i:TaxConsultantTelephoneNumber>
	<bd-i:TaxableProfit decimals="INF" contextRef="Declarant" unitRef="uEUR">180000</bd-i:TaxableProfit>
</xbrli:xbrl>
