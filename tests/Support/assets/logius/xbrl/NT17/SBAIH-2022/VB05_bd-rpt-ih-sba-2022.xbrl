<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt17/bd/20230215.a/bd-rpt-ih-sba-2022.xsd -->
<!-- Intellectual Property State of the Netherlands -->
<!-- Created on: 03-11-2022 16:02:00 -->
<xbrli:xbrl xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:bd-i="http://www.nltaxonomie.nl/nt17/bd/20221207/dictionary/bd-data" xmlns:bd-i-ext1="http://www.nltaxonomie.nl/nt17/bd/20230215.a/dictionary/bd-data-ext1" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xlink="http://www.w3.org/1999/xlink" xml:lang="nl">
    <link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt17/bd/20230215.a/entrypoints/bd-rpt-ih-sba-2022.xsd"/>
    <xbrli:context id="c1d1">
        <xbrli:entity>
            <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
        </xbrli:entity>
        <xbrli:period>
            <xbrli:startDate>2022-01-01</xbrli:startDate>
            <xbrli:endDate>2022-12-31</xbrli:endDate>
        </xbrli:period>
    </xbrli:context>
    <xbrli:unit id="EUR">
        <xbrli:measure>iso4217:EUR</xbrli:measure>
    </xbrli:unit>
    <xbrli:unit id="PURE">
        <xbrli:measure>xbrli:pure</xbrli:measure>
    </xbrli:unit>
    <bd-i-ext1:ContributionBaseIncome contextRef="c1d1" decimals="INF" unitRef="EUR">16134</bd-i-ext1:ContributionBaseIncome>
    <bd-i-ext1:EmployedPersonsTaxCreditCalculated contextRef="c1d1" decimals="INF" unitRef="EUR">784</bd-i-ext1:EmployedPersonsTaxCreditCalculated>
    <bd-i-ext1:FiscalYear contextRef="c1d1">2022</bd-i-ext1:FiscalYear>
    <bd-i-ext1:GeneralTaxCreditCalculated contextRef="c1d1" decimals="INF" unitRef="EUR">1703</bd-i-ext1:GeneralTaxCreditCalculated>
    <bd-i-ext1:IncomeTaxBracket1 contextRef="c1d1" decimals="INF" unitRef="EUR">1435</bd-i-ext1:IncomeTaxBracket1>
    <bd-i-ext1:ObjectionDeadlineDate contextRef="c1d1">2022-07-13</bd-i-ext1:ObjectionDeadlineDate>
    <bd-i-ext1:ParentsTaxCreditCalculated contextRef="c1d1" decimals="INF" unitRef="EUR">1292</bd-i-ext1:ParentsTaxCreditCalculated>
    <bd-i-ext1:SocialInsuranceContributionBalance contextRef="c1d1" decimals="INF" unitRef="EUR">3017</bd-i-ext1:SocialInsuranceContributionBalance>
    <bd-i-ext1:SocialInsuranceRatePercentage contextRef="c1d1" decimals="INF" unitRef="PURE">18.700</bd-i-ext1:SocialInsuranceRatePercentage>
    <bd-i-ext1:TaxAppropriation contextRef="c1d1">H</bd-i-ext1:TaxAppropriation>
    <bd-i-ext1:TaxAssesmentAmountExclusiveOfTaxInterestCompensate contextRef="c1d1" decimals="INF" unitRef="EUR">-447</bd-i-ext1:TaxAssesmentAmountExclusiveOfTaxInterestCompensate>
    <bd-i-ext1:TaxBox1 contextRef="c1d1" decimals="INF" unitRef="EUR">1322</bd-i-ext1:TaxBox1>
    <bd-i-ext1:TaxBox3 contextRef="c1d1" decimals="INF" unitRef="EUR">258</bd-i-ext1:TaxBox3>
    <bd-i-ext1:TaxConsultantNameAddress1 contextRef="c1d1">E. te Ernstig</bd-i-ext1:TaxConsultantNameAddress1>
    <bd-i-ext1:TaxConsultantNameAddress2 contextRef="c1d1">Beconstraat 54321</bd-i-ext1:TaxConsultantNameAddress2>
    <bd-i-ext1:TaxConsultantNameAddress3 contextRef="c1d1">Deventer</bd-i-ext1:TaxConsultantNameAddress3>
    <bd-i-ext1:TaxConsultantNameAddress4 contextRef="c1d1">7425EE</bd-i-ext1:TaxConsultantNameAddress4>
    <bd-i-ext1:TaxCreditsCombined contextRef="c1d1" decimals="INF" unitRef="EUR">3779</bd-i-ext1:TaxCreditsCombined>
    <bd-i-ext1:TaxCreditsNonPayablePart contextRef="c1d1" decimals="INF" unitRef="EUR">77</bd-i-ext1:TaxCreditsNonPayablePart>
    <bd-i-ext1:TaxPartnerApplied contextRef="c1d1" decimals="INF" unitRef="EUR">*********</bd-i-ext1:TaxPartnerApplied>
    <bd-i-ext1:TaxableIncomeBox3 contextRef="c1d1" decimals="INF" unitRef="EUR">860</bd-i-ext1:TaxableIncomeBox3>
    <bd-i-ext1:TaxableIncomeEmploymentOwnerOccupiedHouse contextRef="c1d1" decimals="INF" unitRef="EUR">15741</bd-i-ext1:TaxableIncomeEmploymentOwnerOccupiedHouse>
    <bd-i-ext1:TaxationElsewhereBox1Deduction contextRef="c1d1" decimals="INF" unitRef="EUR">1068</bd-i-ext1:TaxationElsewhereBox1Deduction>
    <bd-i:DateOfBirth contextRef="c1d1">1953-10-00</bd-i:DateOfBirth>
    <bd-i:NoticeOfAssessmentDate contextRef="c1d1">2022-06-01</bd-i:NoticeOfAssessmentDate>
    <bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly contextRef="c1d1">2022-09-30</bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly>
    <bd-i:NoticeOfAssessmentNumber contextRef="c1d1">1111.11.274.H.22.01</bd-i:NoticeOfAssessmentNumber>
    <bd-i:OverallIncome contextRef="c1d1" decimals="INF" unitRef="EUR">16994</bd-i:OverallIncome>
    <bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance contextRef="c1d1" decimals="INF" unitRef="EUR">-466</bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance>
    <bd-i-ext1:TaxOnGamesOfChanceDividendTotal contextRef="c1d1" decimals="INF" unitRef="EUR">1626</bd-i-ext1:TaxOnGamesOfChanceDividendTotal>
    <bd-i:TaxReturnMessageType contextRef="c1d1">20</bd-i:TaxReturnMessageType>
    <bd-i:TaxpayerNameAddress1 contextRef="c1d1">*********</bd-i:TaxpayerNameAddress1>
    <bd-i:TaxpayerNameAddress2 contextRef="c1d1">Acacialaan 3520</bd-i:TaxpayerNameAddress2>
    <bd-i:TaxpayerNameAddress3 contextRef="c1d1">Apeldoorn</bd-i:TaxpayerNameAddress3>
    <bd-i:TaxpayerNameAddress4 contextRef="c1d1">9823DF</bd-i:TaxpayerNameAddress4>
</xbrli:xbrl>
