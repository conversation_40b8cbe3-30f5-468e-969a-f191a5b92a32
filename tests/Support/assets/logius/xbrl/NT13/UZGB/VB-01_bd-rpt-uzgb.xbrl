<?xml version="1.0" encoding="UTF-8"?>
<!--XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt13/bd/********/entrypoints/bd-rpt-uzgb.xsd -->
<!--Intellectual Property State of the Netherlands -->
<!--Created on: Created on: 29-10-2018 11:00:00 -->
<xbrli:xbrl xml:lang="nl" xmlns:bd-i="http://www.nltaxonomie.nl/nt13/bd/********/dictionary/bd-data" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xbrli="http://www.xbrl.org/2003/instance">
	<link:schemaRef xlink:type="simple"  xlink:href="http://www.nltaxonomie.nl/nt13/bd/********/entrypoints/bd-rpt-uzgb.xsd"/>
	<xbrli:context id="Msg">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">001212126L10</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2019-01-01</xbrli:startDate>
			<xbrli:endDate>2019-12-31</xbrli:endDate>
		</xbrli:period>
	</xbrli:context>
	<bd-i:SoftwareVendorAccountNumber contextRef="Msg">swo99990</bd-i:SoftwareVendorAccountNumber>
	<bd-i:SoftwarePackageName contextRef="Msg">SoftwarePackage 1</bd-i:SoftwarePackageName>
	<bd-i:SoftwarePackageVersion contextRef="Msg">SofwareVersion 1</bd-i:SoftwarePackageVersion>
	<bd-i:DeliveryVanUser contextRef="Msg">1</bd-i:DeliveryVanUser>
	<bd-i:StatementType contextRef="Msg">1</bd-i:StatementType>
	<bd-i:WithholdingAgentName contextRef="Msg">aaaaaaaaaaaaaaaa</bd-i:WithholdingAgentName>
	<bd-i:WithholdingAgentTelephoneNumber contextRef="Msg">012-**********</bd-i:WithholdingAgentTelephoneNumber>
	<bd-i:EmployeeSurname contextRef="Msg">aaaaaaaaaaaaaaaaaaaaa</bd-i:EmployeeSurname>
	<bd-i:EmployeeInitials contextRef="Msg">E.I.N.I.T</bd-i:EmployeeInitials>
	<bd-i:EmployeePrefix contextRef="Msg">EmPrefix</bd-i:EmployeePrefix>
	<bd-i:EmployeeIdentificationNumber contextRef="Msg">*********</bd-i:EmployeeIdentificationNumber>
	<bd-i:EmployeeDateOfBirth contextRef="Msg">1970-10-30</bd-i:EmployeeDateOfBirth>
	<bd-i:StatementCommencingDate contextRef="Msg">2018-01-01</bd-i:StatementCommencingDate>
	<bd-i:StatementDeliveryVanLicensePlate contextRef="Msg">9-VZZ-99</bd-i:StatementDeliveryVanLicensePlate>
	<bd-i:StatementDeliveryVanRegistrationCountry contextRef="Msg">NL</bd-i:StatementDeliveryVanRegistrationCountry>
	<bd-i:SignatorySurname contextRef="Msg">aaaaaaaaaaaaaaaaaa</bd-i:SignatorySurname>
	<bd-i:SignatoryInitials contextRef="Msg">S.I.N.I.T.</bd-i:SignatoryInitials>
	<bd-i:SignatoryPrefix contextRef="Msg">SiPrefix</bd-i:SignatoryPrefix>
	<bd-i:SignatoryFunction contextRef="Msg">Signatory Function</bd-i:SignatoryFunction>
	<bd-i:SignatureDate contextRef="Msg">2018-01-09</bd-i:SignatureDate>
</xbrli:xbrl>
