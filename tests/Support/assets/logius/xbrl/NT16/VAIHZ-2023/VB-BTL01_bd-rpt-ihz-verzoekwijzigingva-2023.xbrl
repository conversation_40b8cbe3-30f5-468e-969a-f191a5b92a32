<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt16/bd/20220921/entrypoints/bd-rpt-ihz-verzoekwijzigingva-2023.xsd -->
<!-- Intellectual Property State of the Netherlands -->
<!-- Created on: 26-07-2022 16:02:00 -->
<xbrli:xbrl xml:lang="nl" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:bd-t="http://www.nltaxonomie.nl/nt16/bd/20211208/dictionary/bd-tuples" xmlns:bd-i="http://www.nltaxonomie.nl/nt16/bd/20211208/dictionary/bd-data" xmlns:bd-dim-dim="http://www.nltaxonomie.nl/nt16/bd/20211208/validation/bd-axes" xmlns:bd-i-ext1="http://www.nltaxonomie.nl/nt16/bd/20220216/dictionary/bd-data-ext1" xmlns:nl-cd="http://www.nltaxonomie.nl/nt16/sbr/20210301/dictionary/nl-common-data" xmlns:bd-t-ext2="http://www.nltaxonomie.nl/nt16/bd/20220921/dictionary/bd-tuples-ext2" xmlns:xbrldi="http://xbrl.org/2006/xbrldi" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:bd-dim-mem="http://www.nltaxonomie.nl/nt16/bd/20211208/dictionary/bd-domain-members">
  <link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt16/bd/20220921/entrypoints/bd-rpt-ihz-verzoekwijzigingva-2023.xsd"/>
  <xbrli:context id="CD_Declarant">
    <xbrli:entity>
      <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:startDate>2023-01-01</xbrli:startDate>
      <xbrli:endDate>2023-12-31</xbrli:endDate>
    </xbrli:period>
    <xbrli:scenario>
      <xbrldi:explicitMember dimension="bd-dim-dim:PartyDimension">bd-dim-mem:Declarant</xbrldi:explicitMember>
    </xbrli:scenario>
  </xbrli:context>
  <xbrli:context id="CD_Declarant_Abroad">
    <xbrli:entity>
      <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:startDate>2023-01-01</xbrli:startDate>
      <xbrli:endDate>2023-12-31</xbrli:endDate>
    </xbrli:period>
    <xbrli:scenario>
      <xbrldi:explicitMember dimension="bd-dim-dim:PartyDimension">bd-dim-mem:Declarant</xbrldi:explicitMember>
      <xbrldi:explicitMember dimension="bd-dim-dim:ResidenceDimension">bd-dim-mem:Abroad</xbrldi:explicitMember>
    </xbrli:scenario>
  </xbrli:context>
  <xbrli:context id="CD_Declarant_Foreign">
    <xbrli:entity>
      <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:startDate>2023-01-01</xbrli:startDate>
      <xbrli:endDate>2023-12-31</xbrli:endDate>
    </xbrli:period>
    <xbrli:scenario>
      <xbrldi:explicitMember dimension="bd-dim-dim:PartyDimension">bd-dim-mem:Declarant</xbrldi:explicitMember>
      <xbrldi:explicitMember dimension="bd-dim-dim:TaxpayerDimension">bd-dim-mem:Foreign</xbrldi:explicitMember>
    </xbrli:scenario>
  </xbrli:context>
  <xbrli:context id="CD_Declarant_Netherlands">
    <xbrli:entity>
      <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:startDate>2023-01-01</xbrli:startDate>
      <xbrli:endDate>2023-12-31</xbrli:endDate>
    </xbrli:period>
    <xbrli:scenario>
      <xbrldi:explicitMember dimension="bd-dim-dim:PartyDimension">bd-dim-mem:Declarant</xbrldi:explicitMember>
      <xbrldi:explicitMember dimension="bd-dim-dim:ResidenceDimension">bd-dim-mem:Netherlands</xbrldi:explicitMember>
    </xbrli:scenario>
  </xbrli:context>
  <xbrli:context id="CD_Declarant_World">
    <xbrli:entity>
      <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:startDate>2023-01-01</xbrli:startDate>
      <xbrli:endDate>2023-12-31</xbrli:endDate>
    </xbrli:period>
    <xbrli:scenario>
      <xbrldi:explicitMember dimension="bd-dim-dim:EarningsSetupDimension">bd-dim-mem:World</xbrldi:explicitMember>
      <xbrldi:explicitMember dimension="bd-dim-dim:PartyDimension">bd-dim-mem:Declarant</xbrldi:explicitMember>
    </xbrli:scenario>
  </xbrli:context>
  <xbrli:unit id="EUR">
    <xbrli:measure>iso4217:EUR</xbrli:measure>
  </xbrli:unit>
  <xbrli:unit id="PURE">
    <xbrli:measure>xbrli:pure</xbrli:measure>
  </xbrli:unit>
  <bd-i:ActivitiesWorkCostsTotalAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">201</bd-i:ActivitiesWorkCostsTotalAmount>
  <bd-i:ActivitiesWorkResultsGrossTotalAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">1201</bd-i:ActivitiesWorkResultsGrossTotalAmount>
  <bd-i:AssetsMadeAvailable decimals="INF" contextRef="CD_Declarant_World" unitRef="EUR">5000</bd-i:AssetsMadeAvailable>
  <bd-i:CommutingExpensesAccordingToTableTotalAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">700</bd-i:CommutingExpensesAccordingToTableTotalAmount>
  <bd-i:ContributionBaseIncomeFictitiousActivities decimals="INF" contextRef="CD_Declarant" unitRef="EUR">5000</bd-i:ContributionBaseIncomeFictitiousActivities>
  <bd-i:ContributionBaseIncomeOtherActivities decimals="INF" contextRef="CD_Declarant" unitRef="EUR">1000</bd-i:ContributionBaseIncomeOtherActivities>
  <bd-i:ContributionBaseIncomePeriodicPayments decimals="INF" contextRef="CD_Declarant" unitRef="EUR">600</bd-i:ContributionBaseIncomePeriodicPayments>
  <bd-i:ContributionBaseIncomePresentEmployment decimals="INF" contextRef="CD_Declarant_Netherlands" unitRef="EUR">30100</bd-i:ContributionBaseIncomePresentEmployment>
  <bd-i:ContributionBaseIncomePreviousEmployment decimals="INF" contextRef="CD_Declarant_Netherlands" unitRef="EUR">400</bd-i:ContributionBaseIncomePreviousEmployment>
  <bd-i:ContributionBasePersonDependentDeduction decimals="INF" contextRef="CD_Declarant" unitRef="EUR">2000</bd-i:ContributionBasePersonDependentDeduction>
  <bd-i:ContributionBaseProfit decimals="INF" contextRef="CD_Declarant" unitRef="EUR">900</bd-i:ContributionBaseProfit>
  <bd-i:DateOfBirth contextRef="CD_Declarant">1991-03-21</bd-i:DateOfBirth>
  <bd-i:FictitiousActivitiesCostsTotalAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">6318</bd-i:FictitiousActivitiesCostsTotalAmount>
  <bd-i:FictitiousActivitiesExemptionAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">682</bd-i:FictitiousActivitiesExemptionAmount>
  <bd-i:FictitiousActivitiesResultGrossTotalAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">12000</bd-i:FictitiousActivitiesResultGrossTotalAmount>
  <bd-i:ForeignerPartTaxYearSocialInsuranceContributionsPayableExists contextRef="CD_Declarant">true</bd-i:ForeignerPartTaxYearSocialInsuranceContributionsPayableExists>
  <bd-i:ForeignerPartTaxYearTaxpayerExists contextRef="CD_Declarant">true</bd-i:ForeignerPartTaxYearTaxpayerExists>
  <bd-i:IncomeDependentCombinationTaxCreditExists contextRef="CD_Declarant">true</bd-i:IncomeDependentCombinationTaxCreditExists>
  <bd-i:IncomeEmploymentWithoutWageTax decimals="INF" contextRef="CD_Declarant" unitRef="EUR">100</bd-i:IncomeEmploymentWithoutWageTax>
  <bd-i:IncomeEmploymentWithoutWageTax decimals="INF" contextRef="CD_Declarant_World" unitRef="EUR">100</bd-i:IncomeEmploymentWithoutWageTax>
  <bd-i:IncomeOtherActivities decimals="INF" contextRef="CD_Declarant_World" unitRef="EUR">1000</bd-i:IncomeOtherActivities>
  <bd-i:IncomeRatioNetherlandsWorld decimals="INF" contextRef="CD_Declarant" unitRef="PURE">97</bd-i:IncomeRatioNetherlandsWorld>
  <bd-i:Initials contextRef="CD_Declarant">J</bd-i:Initials>
  <bd-i:LivingTogetherSpousesExists contextRef="CD_Declarant">false</bd-i:LivingTogetherSpousesExists>
  <bd-i:MaintenanceObligationsPeriodicPaymentsPartyFilingTaxReturnPart decimals="INF" contextRef="CD_Declarant" unitRef="EUR">1400</bd-i:MaintenanceObligationsPeriodicPaymentsPartyFilingTaxReturnPart>
  <bd-i:NationalityCode contextRef="CD_Declarant">BEL</bd-i:NationalityCode>
  <bd-i:OwnHouseBenefitsPartyFilingTaxReturnPart decimals="INF" contextRef="CD_Declarant" unitRef="EUR">2925</bd-i:OwnHouseBenefitsPartyFilingTaxReturnPart>
  <bd-i:OwnHouseDeductibleCostsPartyFilingTaxReturnPart decimals="INF" contextRef="CD_Declarant" unitRef="EUR">0</bd-i:OwnHouseDeductibleCostsPartyFilingTaxReturnPart>
  <bd-i:OwnHouseDeductibleNonOrMinorAcquisitionDebtPartyFilingTaxReturnPartAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">2925</bd-i:OwnHouseDeductibleNonOrMinorAcquisitionDebtPartyFilingTaxReturnPartAmount>
  <bd-i:OwnHouseIncomeSharePartyFilingTaxReturn decimals="INF" contextRef="CD_Declarant" unitRef="EUR">2925</bd-i:OwnHouseIncomeSharePartyFilingTaxReturn>
  <bd-i:OwnHousePrincipalResidenceRentalValueTotal decimals="INF" contextRef="CD_Declarant" unitRef="EUR">2925</bd-i:OwnHousePrincipalResidenceRentalValueTotal>
  <bd-i:QualifyingForeignTaxpayer contextRef="CD_Declarant">true</bd-i:QualifyingForeignTaxpayer>
  <bd-i:QualifyingForeignTaxpayerResidentCountry contextRef="CD_Declarant">true</bd-i:QualifyingForeignTaxpayerResidentCountry>
  <bd-i:RedemptionsAnnuities decimals="INF" contextRef="CD_Declarant_World" unitRef="EUR">600</bd-i:RedemptionsAnnuities>
  <bd-i:RevisionInterestCalculatedAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">120</bd-i:RevisionInterestCalculatedAmount>
  <bd-i:SocialInsuranceAowAnwDeviatingPeriodEndDate contextRef="CD_Declarant">2023-11-30</bd-i:SocialInsuranceAowAnwDeviatingPeriodEndDate>
  <bd-i:SocialInsuranceAowAnwDeviatingPeriodStartDate contextRef="CD_Declarant">2023-01-01</bd-i:SocialInsuranceAowAnwDeviatingPeriodStartDate>
  <bd-i:SocialInsuranceObligationBaseAmount decimals="INF" contextRef="CD_Declarant_Abroad" unitRef="EUR">900</bd-i:SocialInsuranceObligationBaseAmount>
  <bd-i:SocialInsuranceObligationLongTermCareLawHealthInsuranceLawDeviatingPeriodEndDate contextRef="CD_Declarant">2023-11-30</bd-i:SocialInsuranceObligationLongTermCareLawHealthInsuranceLawDeviatingPeriodEndDate>
  <bd-i:SocialInsuranceObligationLongTermCareLawHealthInsuranceLawDeviatingPeriodStartDate contextRef="CD_Declarant">2023-01-01</bd-i:SocialInsuranceObligationLongTermCareLawHealthInsuranceLawDeviatingPeriodStartDate>
  <bd-i:SoftwarePackageName contextRef="CD_Declarant">OLAV-VA</bd-i:SoftwarePackageName>
  <bd-i:SoftwarePackageVersion contextRef="CD_Declarant">2023.1.0.11</bd-i:SoftwarePackageVersion>
  <bd-i:SoftwareSupplierCode contextRef="CD_Declarant">BOAV</bd-i:SoftwareSupplierCode>
  <bd-i:SoftwareVendorAccountNumber contextRef="CD_Declarant">SW099999</bd-i:SoftwareVendorAccountNumber>
  <bd-i:Surname contextRef="CD_Declarant">Jakkoda</bd-i:Surname>
  <bd-i:TaxableProfit decimals="INF" contextRef="CD_Declarant_World" unitRef="EUR">900</bd-i:TaxableProfit>
  <bd-i:TaxConsultantInitials contextRef="CD_Declarant">H</bd-i:TaxConsultantInitials>
  <bd-i:TaxConsultantNumber contextRef="CD_Declarant">125866</bd-i:TaxConsultantNumber>
  <bd-i:TaxConsultantPrefix contextRef="CD_Declarant">de</bd-i:TaxConsultantPrefix>
  <bd-i:TaxConsultantSurname contextRef="CD_Declarant">Hakkelaar</bd-i:TaxConsultantSurname>
  <bd-i:TaxCreditsTaxpayerPeriodEndDate contextRef="CD_Declarant">2023-11-30</bd-i:TaxCreditsTaxpayerPeriodEndDate>
  <bd-i:TaxCreditsTaxpayerPeriodStartDate contextRef="CD_Declarant">2023-01-01</bd-i:TaxCreditsTaxpayerPeriodStartDate>
  <bd-i:TaxReturnMessageType contextRef="CD_Declarant">16</bd-i:TaxReturnMessageType>
  <bd-i:TravelAllowanceReceivedTotalAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">800</bd-i:TravelAllowanceReceivedTotalAmount>
  <bd-i:TravelDeductionPublicTransport decimals="INF" contextRef="CD_Declarant_World" unitRef="EUR">0</bd-i:TravelDeductionPublicTransport>
  <bd-i:WagesHealthInsuranceLawMaximumContribution contextRef="CD_Declarant">false</bd-i:WagesHealthInsuranceLawMaximumContribution>
  <bd-i:WageTaxArtistsAndSportsProfessionals decimals="INF" contextRef="CD_Declarant" unitRef="EUR">300</bd-i:WageTaxArtistsAndSportsProfessionals>
  <bd-i:YoungestChildDateOfBirth contextRef="CD_Declarant">2011-08-08</bd-i:YoungestChildDateOfBirth>
  <bd-i-ext1:DoublePosition contextRef="CD_Declarant">true</bd-i-ext1:DoublePosition>
  <bd-t:MaintenanceObligationPaidPersonSpecification>
    <bd-t:AddressAbroadPresentation>
      <nl-cd:CountryCodeISO contextRef="CD_Declarant">BEL</nl-cd:CountryCodeISO>
      <bd-i:HouseNumberAbroad contextRef="CD_Declarant">1-2A</bd-i:HouseNumberAbroad>
      <bd-i:PlaceOfResidenceAbroad contextRef="CD_Declarant">Gent</bd-i:PlaceOfResidenceAbroad>
      <bd-i:PostalCodeAbroad contextRef="CD_Declarant">1245OO</bd-i:PostalCodeAbroad>
      <bd-i:StreetNameAbroad contextRef="CD_Declarant">Veld</bd-i:StreetNameAbroad>
    </bd-t:AddressAbroadPresentation>
    <bd-i:MaintenanceObligationPaidPersonDateOfBirth contextRef="CD_Declarant">1991-05-24</bd-i:MaintenanceObligationPaidPersonDateOfBirth>
    <bd-i:MaintenanceObligationPaidPersonIdentificationNumber contextRef="CD_Declarant">333333330</bd-i:MaintenanceObligationPaidPersonIdentificationNumber>
    <bd-i:MaintenanceObligationPaidPersonInitials contextRef="CD_Declarant">P</bd-i:MaintenanceObligationPaidPersonInitials>
    <bd-i:MaintenanceObligationPaidPersonPrefix contextRef="CD_Declarant">de</bd-i:MaintenanceObligationPaidPersonPrefix>
    <bd-i:MaintenanceObligationPaidPersonSurname contextRef="CD_Declarant">Peen</bd-i:MaintenanceObligationPaidPersonSurname>
  </bd-t:MaintenanceObligationPaidPersonSpecification>
  <bd-t-ext2:OwnHousePrincipalResidenceObjectSpecification>
    <bd-i:OwnHousePrincipalResidenceImmovablePropertyLawValue decimals="INF" contextRef="CD_Declarant" unitRef="EUR">450000</bd-i:OwnHousePrincipalResidenceImmovablePropertyLawValue>
    <bd-i:OwnHousePrincipalResidencePercentageEntitlement decimals="INF" contextRef="CD_Declarant" unitRef="PURE">100</bd-i:OwnHousePrincipalResidencePercentageEntitlement>
    <bd-i:OwnHousePrincipalResidencePeriodEnd contextRef="CD_Declarant">2023-12-31</bd-i:OwnHousePrincipalResidencePeriodEnd>
    <bd-i:OwnHousePrincipalResidencePeriodStart contextRef="CD_Declarant">2023-01-01</bd-i:OwnHousePrincipalResidencePeriodStart>
  </bd-t-ext2:OwnHousePrincipalResidenceObjectSpecification>
  <bd-t:PresentWorkSpecification>
    <bd-i:PresentWorkEmployerOrBenefitsAgencyName contextRef="CD_Declarant">IH19046VBB01</bd-i:PresentWorkEmployerOrBenefitsAgencyName>
    <bd-i:PresentWorkWages decimals="INF" contextRef="CD_Declarant" unitRef="EUR">1010</bd-i:PresentWorkWages>
  </bd-t:PresentWorkSpecification>
  <bd-t:PresentWorkSpecification>
    <bd-i:PresentWorkEmployerOrBenefitsAgencyName contextRef="CD_Declarant">Boer 2</bd-i:PresentWorkEmployerOrBenefitsAgencyName>
    <bd-i:PresentWorkWages decimals="INF" contextRef="CD_Declarant" unitRef="EUR">1515</bd-i:PresentWorkWages>
  </bd-t:PresentWorkSpecification>
  <bd-t:PresentWorkSpecification>
    <bd-i:PresentWorkEmployerOrBenefitsAgencyName contextRef="CD_Declarant">Boer 3</bd-i:PresentWorkEmployerOrBenefitsAgencyName>
    <bd-i:PresentWorkWages decimals="INF" contextRef="CD_Declarant" unitRef="EUR">2525</bd-i:PresentWorkWages>
  </bd-t:PresentWorkSpecification>
  <bd-t:PresentWorkSpecification>
    <bd-i:PresentWorkEmployerOrBenefitsAgencyName contextRef="CD_Declarant">Boer 4</bd-i:PresentWorkEmployerOrBenefitsAgencyName>
    <bd-i:PresentWorkWages decimals="INF" contextRef="CD_Declarant" unitRef="EUR">3030</bd-i:PresentWorkWages>
  </bd-t:PresentWorkSpecification>
  <bd-t:PresentWorkSpecification>
    <bd-i:PresentWorkEmployerOrBenefitsAgencyName contextRef="CD_Declarant">Boer 5</bd-i:PresentWorkEmployerOrBenefitsAgencyName>
    <bd-i:PresentWorkWages decimals="INF" contextRef="CD_Declarant" unitRef="EUR">21920</bd-i:PresentWorkWages>
  </bd-t:PresentWorkSpecification>
  <bd-t:PreviousWorkSpecification>
    <bd-i:PreviousWorkBenefitsAgencyName contextRef="CD_Declarant">Puur 1</bd-i:PreviousWorkBenefitsAgencyName>
    <bd-i:PreviousWorkIncomeAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">27</bd-i:PreviousWorkIncomeAmount>
    <bd-i:PreviousWorkWithheldWageLevy decimals="INF" contextRef="CD_Declarant" unitRef="EUR">3</bd-i:PreviousWorkWithheldWageLevy>
  </bd-t:PreviousWorkSpecification>
  <bd-t:PreviousWorkSpecification>
    <bd-i:PreviousWorkBenefitsAgencyName contextRef="CD_Declarant">Puur 2</bd-i:PreviousWorkBenefitsAgencyName>
    <bd-i:PreviousWorkIncomeAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">37</bd-i:PreviousWorkIncomeAmount>
    <bd-i:PreviousWorkWithheldWageLevy decimals="INF" contextRef="CD_Declarant" unitRef="EUR">8</bd-i:PreviousWorkWithheldWageLevy>
  </bd-t:PreviousWorkSpecification>
  <bd-t:PreviousWorkSpecification>
    <bd-i:PreviousWorkBenefitsAgencyName contextRef="CD_Declarant">Puur 3</bd-i:PreviousWorkBenefitsAgencyName>
    <bd-i:PreviousWorkIncomeAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">41</bd-i:PreviousWorkIncomeAmount>
    <bd-i:PreviousWorkWithheldWageLevy decimals="INF" contextRef="CD_Declarant" unitRef="EUR">14</bd-i:PreviousWorkWithheldWageLevy>
  </bd-t:PreviousWorkSpecification>
  <bd-t:PreviousWorkSpecification>
    <bd-i:PreviousWorkBenefitsAgencyName contextRef="CD_Declarant">Puur 4</bd-i:PreviousWorkBenefitsAgencyName>
    <bd-i:PreviousWorkIncomeAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">89</bd-i:PreviousWorkIncomeAmount>
    <bd-i:PreviousWorkWithheldWageLevy decimals="INF" contextRef="CD_Declarant" unitRef="EUR">48</bd-i:PreviousWorkWithheldWageLevy>
  </bd-t:PreviousWorkSpecification>
  <bd-t:PreviousWorkSpecification>
    <bd-i:PreviousWorkBenefitsAgencyName contextRef="CD_Declarant">Puur 5</bd-i:PreviousWorkBenefitsAgencyName>
    <bd-i:PreviousWorkIncomeAmount decimals="INF" contextRef="CD_Declarant" unitRef="EUR">206</bd-i:PreviousWorkIncomeAmount>
    <bd-i:PreviousWorkWithheldWageLevy decimals="INF" contextRef="CD_Declarant" unitRef="EUR">127</bd-i:PreviousWorkWithheldWageLevy>
  </bd-t:PreviousWorkSpecification>
  <bd-t:RedemptionAnnuitiesCoveredByWagetaxSpecification>
    <bd-i:RedemptionAnnuitiesCoveredByWagetaxAnnuityRedemption decimals="INF" contextRef="CD_Declarant" unitRef="EUR">56</bd-i:RedemptionAnnuitiesCoveredByWagetaxAnnuityRedemption>
    <bd-i:RedemptionAnnuitiesCoveredByWagetaxPayingAgencyName contextRef="CD_Declarant">Soufle 1</bd-i:RedemptionAnnuitiesCoveredByWagetaxPayingAgencyName>
  </bd-t:RedemptionAnnuitiesCoveredByWagetaxSpecification>
  <bd-t:RedemptionAnnuitiesCoveredByWagetaxSpecification>
    <bd-i:RedemptionAnnuitiesCoveredByWagetaxAnnuityRedemption decimals="INF" contextRef="CD_Declarant" unitRef="EUR">168</bd-i:RedemptionAnnuitiesCoveredByWagetaxAnnuityRedemption>
    <bd-i:RedemptionAnnuitiesCoveredByWagetaxPayingAgencyName contextRef="CD_Declarant">Soufle 2</bd-i:RedemptionAnnuitiesCoveredByWagetaxPayingAgencyName>
  </bd-t:RedemptionAnnuitiesCoveredByWagetaxSpecification>
  <bd-t:RedemptionAnnuitiesCoveredByWagetaxSpecification>
    <bd-i:RedemptionAnnuitiesCoveredByWagetaxAnnuityRedemption decimals="INF" contextRef="CD_Declarant" unitRef="EUR">376</bd-i:RedemptionAnnuitiesCoveredByWagetaxAnnuityRedemption>
    <bd-i:RedemptionAnnuitiesCoveredByWagetaxPayingAgencyName contextRef="CD_Declarant">Soufle 3</bd-i:RedemptionAnnuitiesCoveredByWagetaxPayingAgencyName>
  </bd-t:RedemptionAnnuitiesCoveredByWagetaxSpecification>
  <bd-t:ResidenceForeignCountrySpecification>
    <bd-i:CountryResidenceEndDate contextRef="CD_Declarant">2023-11-30</bd-i:CountryResidenceEndDate>
    <bd-i:CountryResidenceStartDate contextRef="CD_Declarant">2023-01-01</bd-i:CountryResidenceStartDate>
    <bd-i:ResidenceForeignCountryCountryCodeISO contextRef="CD_Declarant">BEL</bd-i:ResidenceForeignCountryCountryCodeISO>
  </bd-t:ResidenceForeignCountrySpecification>
  <bd-t:ResidenceForeignCountrySpecification>
    <bd-i:CountryResidenceEndDate contextRef="CD_Declarant">2023-12-31</bd-i:CountryResidenceEndDate>
    <bd-i:CountryResidenceStartDate contextRef="CD_Declarant">2023-12-01</bd-i:CountryResidenceStartDate>
    <bd-i:ResidenceForeignCountryCountryCodeISO contextRef="CD_Declarant">USA</bd-i:ResidenceForeignCountryCountryCodeISO>
  </bd-t:ResidenceForeignCountrySpecification>
  <bd-t:TaxationElsewhereBox1IncomeForeignTaxpayerIncomeSpecification>
    <bd-i:TaxationElsewhereBox1IncomeAmount decimals="INF" contextRef="CD_Declarant_Foreign" unitRef="EUR">97</bd-i:TaxationElsewhereBox1IncomeAmount>
    <bd-i:TaxationElsewhereBox1IncomeDescription contextRef="CD_Declarant_Foreign">Belgische Eigen Woning</bd-i:TaxationElsewhereBox1IncomeDescription>
  </bd-t:TaxationElsewhereBox1IncomeForeignTaxpayerIncomeSpecification>
  <bd-i:TaxConsultantTelephoneNumber contextRef="CD_Declarant">011-1172870000</bd-i:TaxConsultantTelephoneNumber>
</xbrli:xbrl>
