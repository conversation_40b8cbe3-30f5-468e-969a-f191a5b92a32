<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt11/bd/20170920/entrypoints/bd-rpt-ihz-verzoekwijzigingva-2018.xsd
Intellectual Property State of the Netherlands
Created on: 14-06-2017 11:20:00
-->
<xbrli:xbrl xml:lang="nl" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:bd-burg-tuple="http://www.nltaxonomie.nl/nt11/bd/20161207/dictionary/bd-burg-tuples" xmlns:bd-tuple-ext2="http://www.nltaxonomie.nl/nt11/bd/20170920/dictionary/bd-tuples-ext2" xmlns:bd-alg="http://www.nltaxonomie.nl/nt11/bd/20161207/dictionary/bd-algemeen" xmlns:bd-ext2="http://www.nltaxonomie.nl/nt11/bd/20170920/dictionary/bd-ext2" xmlns:bd-burg="http://www.nltaxonomie.nl/nt11/bd/20161207/dictionary/bd-burgers" xmlns:bd-dim-dim="http://www.nltaxonomie.nl/nt11/bd/20161207/validation/bd-axes" xmlns:bd-dim-mem="http://www.nltaxonomie.nl/nt11/bd/20161207/dictionary/bd-domain-members" xmlns:xbrldi="http://xbrl.org/2006/xbrldi" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:xlink="http://www.w3.org/1999/xlink">
	<link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt11/bd/20170920/entrypoints/bd-rpt-ihz-verzoekwijzigingva-2018.xsd"/>
	<xbrli:context id="Context_Duration_Declarant">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2018-01-01</xbrli:startDate>
			<xbrli:endDate>2018-12-31</xbrli:endDate>
		</xbrli:period>
		<xbrli:scenario>
			<xbrldi:explicitMember dimension="bd-dim-dim:PartyDimension">bd-dim-mem:Declarant</xbrldi:explicitMember>
		</xbrli:scenario>
	</xbrli:context>
	<xbrli:context id="Context_Duration_Partner">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2018-01-01</xbrli:startDate>
			<xbrli:endDate>2018-12-31</xbrli:endDate>
		</xbrli:period>
		<xbrli:scenario>
			<xbrldi:explicitMember dimension="bd-dim-dim:PartyDimension">bd-dim-mem:Partner</xbrldi:explicitMember>
		</xbrli:scenario>
	</xbrli:context>
	<xbrli:unit id="EUR">
		<xbrli:measure>iso4217:EUR</xbrli:measure>
	</xbrli:unit>
	<xbrli:unit id="PURE">
		<xbrli:measure>xbrli:pure</xbrli:measure>
	</xbrli:unit>
	<bd-alg:DateOfBirth contextRef="Context_Duration_Declarant">1990-04-02</bd-alg:DateOfBirth>
	<bd-alg:DateOfBirth contextRef="Context_Duration_Partner">1992-02-29</bd-alg:DateOfBirth>
	<bd-alg:HouseNumberNL decimals="INF" contextRef="Context_Duration_Declarant" unitRef="PURE">168</bd-alg:HouseNumberNL>
	<bd-alg:Initials contextRef="Context_Duration_Declarant">D.E.</bd-alg:Initials>
	<bd-alg:Initials contextRef="Context_Duration_Partner">P.</bd-alg:Initials>
	<bd-alg:PostalCodeNL contextRef="Context_Duration_Declarant">8854XK</bd-alg:PostalCodeNL>
	<bd-alg:SoftwarePackageName contextRef="Context_Duration_Declarant">Digitalis</bd-alg:SoftwarePackageName>
	<bd-alg:SoftwarePackageVersion contextRef="Context_Duration_Declarant">1.0.0.000</bd-alg:SoftwarePackageVersion>
	<bd-alg:SoftwareSupplierCode contextRef="Context_Duration_Declarant">JJJJ</bd-alg:SoftwareSupplierCode>
	<bd-alg:SoftwareVendorAccountNumber contextRef="Context_Duration_Declarant">********</bd-alg:SoftwareVendorAccountNumber>
	<bd-alg:Surname contextRef="Context_Duration_Declarant">Belastingbetaler</bd-alg:Surname>
	<bd-alg:Surname contextRef="Context_Duration_Partner">Artner</bd-alg:Surname>
	<bd-alg:TaxReturnMessageType contextRef="Context_Duration_Declarant">15</bd-alg:TaxReturnMessageType>
	<bd-burg:IncomeOverallAmount decimals="INF" contextRef="Context_Duration_Partner" unitRef="EUR">18999</bd-burg:IncomeOverallAmount>
	<bd-burg:IncomeTaxOwed decimals="INF" contextRef="Context_Duration_Partner" unitRef="EUR">7065</bd-burg:IncomeTaxOwed>
	<bd-burg:LivingTogetherSpousesExists contextRef="Context_Duration_Declarant">true</bd-burg:LivingTogetherSpousesExists>
	<bd-burg:MarriedEntireTaxYear contextRef="Context_Duration_Declarant">false</bd-burg:MarriedEntireTaxYear>
	<bd-burg:MarriedPartOfTaxYear contextRef="Context_Duration_Declarant">false</bd-burg:MarriedPartOfTaxYear>
	<bd-burg:OwnHouseBenefitsPartyFilingTaxReturnPart decimals="INF" contextRef="Context_Duration_Declarant" unitRef="EUR">1492</bd-burg:OwnHouseBenefitsPartyFilingTaxReturnPart>
	<bd-burg:OwnHouseDeductibleCoststPartyFilingTaxReturnPart decimals="INF" contextRef="Context_Duration_Declarant" unitRef="EUR">4500</bd-burg:OwnHouseDeductibleCoststPartyFilingTaxReturnPart>
	<bd-burg:OwnHouseIncomeSharePartyFilingTaxReturn decimals="INF" contextRef="Context_Duration_Declarant" unitRef="EUR">-3008</bd-burg:OwnHouseIncomeSharePartyFilingTaxReturn>
	<bd-burg:OwnHouseInterestLoan decimals="INF" contextRef="Context_Duration_Declarant" unitRef="EUR">4500</bd-burg:OwnHouseInterestLoan>
	<bd-burg:OwnHousePrincipalResidenceRentalValueTotal decimals="INF" contextRef="Context_Duration_Declarant" unitRef="EUR">1492</bd-burg:OwnHousePrincipalResidenceRentalValueTotal>
	<bd-burg:RequestEntireYearPartnerTaxPurposes contextRef="Context_Duration_Declarant">false</bd-burg:RequestEntireYearPartnerTaxPurposes>
	<bd-burg:UnmarriedConstraintEntireYearIdenticalAddress contextRef="Context_Duration_Declarant">false</bd-burg:UnmarriedConstraintEntireYearIdenticalAddress>
	<bd-burg:UnmarriedConstraintPartOfYearIdenticalAddress contextRef="Context_Duration_Declarant">true</bd-burg:UnmarriedConstraintPartOfYearIdenticalAddress>
	<bd-burg:UnmarriedConstraintSatisfactionPartnerTaxPurposes contextRef="Context_Duration_Declarant">true</bd-burg:UnmarriedConstraintSatisfactionPartnerTaxPurposes>
	<bd-ext2:PlaceOfResidence contextRef="Context_Duration_Declarant">Den Helder</bd-ext2:PlaceOfResidence>
	<bd-ext2:StreetName contextRef="Context_Duration_Declarant">Gravenallee</bd-ext2:StreetName>
	<bd-tuple-ext2:OwnHousePrincipalResidenceIncomeSpecification>
		<bd-burg:OwnHousePrincipalResidenceImmovablePropertyLawValue decimals="INF" contextRef="Context_Duration_Declarant" unitRef="EUR">199000</bd-burg:OwnHousePrincipalResidenceImmovablePropertyLawValue>
		<bd-ext2:OwnHousePrincipalResidencePercentageEntitlement decimals="INF" contextRef="Context_Duration_Declarant" unitRef="PURE">100</bd-ext2:OwnHousePrincipalResidencePercentageEntitlement>
		<bd-burg:OwnHousePrincipalResidencePeriodEnd contextRef="Context_Duration_Declarant">2018-12-31</bd-burg:OwnHousePrincipalResidencePeriodEnd>
		<bd-burg:OwnHousePrincipalResidencePeriodStart contextRef="Context_Duration_Declarant">2018-01-01</bd-burg:OwnHousePrincipalResidencePeriodStart>
	</bd-tuple-ext2:OwnHousePrincipalResidenceIncomeSpecification>
	<bd-burg-tuple:PresentWorkSpecification>
		<bd-burg:PresentWorkEmployerOrBenefitsAgencyName contextRef="Context_Duration_Declarant">Gemeente Amsterdam</bd-burg:PresentWorkEmployerOrBenefitsAgencyName>
		<bd-burg:PresentWorkWages decimals="INF" contextRef="Context_Duration_Declarant" unitRef="EUR">44900</bd-burg:PresentWorkWages>
	</bd-burg-tuple:PresentWorkSpecification>
</xbrli:xbrl>
