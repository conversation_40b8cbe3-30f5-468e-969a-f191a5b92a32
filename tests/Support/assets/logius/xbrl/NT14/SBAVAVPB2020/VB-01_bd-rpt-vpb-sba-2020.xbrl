<?xml version="1.0" encoding="UTF-8"?>
<!--XBRL instance based on taxonomy entrypoint http://www.nltaxonomie.nl/nt14/bd/20191211/entrypoints/bd-rpt-vpb-sba-2019.xsd-->
<!--Intellectual Property State of the Netherlands-->
<!--Created on: 01-10-2019 11:25:00-->
<xbrli:xbrl xmlns:bd-i="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-data" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:xlink="http://www.w3.org/1999/xlink" xml:lang="nl">
	<link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt14/bd/20191211/entrypoints/bd-rpt-vpb-sba-2020.xsd"/>
	<xbrli:context id="c1d1">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2020-06-01</xbrli:startDate>
			<xbrli:endDate>2021-05-31</xbrli:endDate>
		</xbrli:period>
	</xbrli:context>
	<xbrli:unit id="EUR">
		<xbrli:measure>iso4217:EUR</xbrli:measure>
	</xbrli:unit>
	<xbrli:unit id="JPY">
		<xbrli:measure>iso4217:JPY</xbrli:measure>
	</xbrli:unit>
	<xbrli:unit id="PURE">
		<xbrli:measure>xbrli:pure</xbrli:measure>
	</xbrli:unit>
	<bd-i:CompetentUnitNumber contextRef="c1d1">123</bd-i:CompetentUnitNumber>
	<bd-i:CorporationTax contextRef="c1d1" decimals="INF" unitRef="EUR">9999999999999</bd-i:CorporationTax>
	<bd-i:CorporationTaxRateType contextRef="c1d1">N</bd-i:CorporationTaxRateType>
	<bd-i:CorporationTaxTaxableAmount contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:CorporationTaxTaxableAmount>
	<bd-i:CorporationTaxTaxableAmountFunctionalCurrency decimals="INF" contextRef="c1d1" unitRef="JPY">9999999999999</bd-i:CorporationTaxTaxableAmountFunctionalCurrency>
	<bd-i:DiscountPaymentAtOnce contextRef="c1d1" decimals="INF" unitRef="EUR">9999999999999</bd-i:DiscountPaymentAtOnce>
	<bd-i:ExchangeRateFinancialYearFunctionalCurrencySchemeAverage contextRef="c1d1" decimals="INF" unitRef="EUR">00.00628153</bd-i:ExchangeRateFinancialYearFunctionalCurrencySchemeAverage>
	<bd-i:FinancialYearBackwardShiftingLossEndDate contextRef="c1d1">2021-05-31</bd-i:FinancialYearBackwardShiftingLossEndDate>
	<bd-i:FinancialYearBackwardShiftingLossStartDate contextRef="c1d1">2020-06-01</bd-i:FinancialYearBackwardShiftingLossStartDate>
	<bd-i:FiscalLossesAfterSettledThisFinancialYearBalance contextRef="c1d1" decimals="INF" unitRef="JPY">-999999999999</bd-i:FiscalLossesAfterSettledThisFinancialYearBalance>
	<bd-i:ForeignBusinessProfitDockedIndication contextRef="c1d1" decimals="INF" unitRef="PURE">0</bd-i:ForeignBusinessProfitDockedIndication>
	<bd-i:ForeignBusinessProfitSettledToTransfer contextRef="c1d1" decimals="INF" unitRef="EUR">9999999999999</bd-i:ForeignBusinessProfitSettledToTransfer>
	<bd-i:FunctionalCurrencyOrEURApplied contextRef="c1d1">JPY</bd-i:FunctionalCurrencyOrEURApplied>
	<bd-i:InnovationBoxBalanceThresholdEndFinancialYear contextRef="c1d1" decimals="INF" unitRef="JPY">9999999999999</bd-i:InnovationBoxBalanceThresholdEndFinancialYear>
	<bd-i:InnovationBoxDisposalndication contextRef="c1d1">true</bd-i:InnovationBoxDisposalndication>
	<bd-i:LossRecovery contextRef="c1d1">true</bd-i:LossRecovery>
	<bd-i:LossRevisedExplanationAmount contextRef="c1d1" decimals="INF" unitRef="JPY">-999999999999</bd-i:LossRevisedExplanationAmount>
	<bd-i:LossSettledDisposalIssued contextRef="c1d1" decimals="INF" unitRef="JPY">-999999999999</bd-i:LossSettledDisposalIssued>
	<bd-i:LossesToBeSetOffThisYearTotal contextRef="c1d1" decimals="INF" unitRef="JPY">9999999999999</bd-i:LossesToBeSetOffThisYearTotal>
	<bd-i:MisdemeanorFineAppliedNotificationDate contextRef="c1d1">2020-02-01</bd-i:MisdemeanorFineAppliedNotificationDate>
	<bd-i:MisdemeanorPenaltyDeferredIndication contextRef="c1d1">true</bd-i:MisdemeanorPenaltyDeferredIndication>
	<bd-i:MisdemeanourPenalty contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:MisdemeanourPenalty>
	<bd-i:NewEstablishedBalanceToBePaidToBeReceived contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:NewEstablishedBalanceToBePaidToBeReceived>
	<bd-i:NoticeOfAssessmentDate contextRef="c1d1">2020-11-30</bd-i:NoticeOfAssessmentDate>
	<bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly contextRef="c1d1">2020-03-03</bd-i:NoticeOfAssessmentExpiringDateFirstAndOnly>
	<bd-i:NoticeOfAssessmentExpiringDateLastPeriod contextRef="c1d1">2020-04-04</bd-i:NoticeOfAssessmentExpiringDateLastPeriod>
	<bd-i:NoticeOfAssessmentMultipleTermsExpiringDate contextRef="c1d1">2020-02-02</bd-i:NoticeOfAssessmentMultipleTermsExpiringDate>
	<bd-i:NoticeOfAssessmentNumber contextRef="c1d1">99.77.233.V.86.0612</bd-i:NoticeOfAssessmentNumber>
	<bd-i:ParticipatingInterestSetoffToTransferToNextYear contextRef="c1d1" decimals="INF" unitRef="EUR">9999999999999</bd-i:ParticipatingInterestSetoffToTransferToNextYear>
	<bd-i:ParticipatingInterestSettled contextRef="c1d1" decimals="INF" unitRef="EUR">9999999999999</bd-i:ParticipatingInterestSettled>
	<bd-i:ParticipatingInterestSettledDockedDisposal contextRef="c1d1" decimals="INF" unitRef="PURE">0</bd-i:ParticipatingInterestSettledDockedDisposal>
	<bd-i:ProvisionalRefundInConnectionWithLossSettled contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:ProvisionalRefundInConnectionWithLossSettled>
	<bd-i:ProvisionalRefundSettled contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:ProvisionalRefundSettled>
	<bd-i:ProvisionalTaxAssessmentSettled contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:ProvisionalTaxAssessmentSettled>
	<bd-i:QualificationTaxableProfitClaimsAndDebtsBalanceEntitiesAffiliatedWithTaxpayer contextRef="c1d1" decimals="INF" unitRef="JPY">-999999999999</bd-i:QualificationTaxableProfitClaimsAndDebtsBalanceEntitiesAffiliatedWithTaxpayer>
	<bd-i:SurchargeNotAdequateNoticeTaxReturn contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:SurchargeNotAdequateNoticeTaxReturn>
	<bd-i:TaxAssessmentAmount contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxAssessmentAmount>
	<bd-i:TaxAssessmentAmountAfterUseDiscountBalance contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxAssessmentAmountAfterUseDiscountBalance>
	<bd-i:TaxAssessmentAmountToBePaidToBeReceivedPreviouslyEstablished contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxAssessmentAmountToBePaidToBeReceivedPreviouslyEstablished>
	<bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxAssessmentAmountToBeReceivedToBePaidBalance>
	<bd-i:TaxAssessmentAmountToBeSettledToBeReceived contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxAssessmentAmountToBeSettledToBeReceived>
	<bd-i:TaxAssessmentToBeBroughtTo contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxAssessmentToBeBroughtTo>
	<bd-i:TaxAssessmentsOtherToBeBroughtTo contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxAssessmentsOtherToBeBroughtTo>
	<bd-i:TaxBackDuty contextRef="c1d1" decimals="INF" unitRef="EUR">9999999999999</bd-i:TaxBackDuty>
	<bd-i:TaxBalanceAfterDeduction contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxBalanceAfterDeduction>
	<bd-i:TaxBalanceBeforeDeduction contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxBalanceBeforeDeduction>
	<bd-i:TaxForeignBusinessProfitSettled contextRef="c1d1" decimals="INF" unitRef="EUR">9999999999999</bd-i:TaxForeignBusinessProfitSettled>
	<bd-i:TaxInterest contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxInterest>
	<bd-i:TaxInterestCalculatedPeriodEnd contextRef="c1d1">2020-01-11</bd-i:TaxInterestCalculatedPeriodEnd>
	<bd-i:TaxInterestCalculatedPeriodStart contextRef="c1d1">2016-07-01</bd-i:TaxInterestCalculatedPeriodStart>
	<bd-i:TaxInterestCompensate contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxInterestCompensate>
	<bd-i:TaxInterestCompensateCalculatedPeriodEnd contextRef="c1d1">2020-02-11</bd-i:TaxInterestCompensateCalculatedPeriodEnd>
	<bd-i:TaxInterestCompensateCalculatedPeriodStart contextRef="c1d1">2016-08-01</bd-i:TaxInterestCompensateCalculatedPeriodStart>
	<bd-i:TaxOnGamesOfChanceDividendTotal contextRef="c1d1" decimals="INF" unitRef="EUR">9999999999999</bd-i:TaxOnGamesOfChanceDividendTotal>
	<bd-i:TaxRefundLossSetOffTotal contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxRefundLossSetOffTotal>
	<bd-i:TaxReturnMessageType contextRef="c1d1">VPB12</bd-i:TaxReturnMessageType>
	<bd-i:TaxableAmountSettledNew contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxableAmountSettledNew>
	<bd-i:TaxableAmountSettledPreviously contextRef="c1d1" decimals="INF" unitRef="EUR">-999999999999</bd-i:TaxableAmountSettledPreviously>
	<bd-i:TaxableProfit contextRef="c1d1" decimals="INF" unitRef="JPY">-999999999999</bd-i:TaxableProfit>
	<bd-i:TaxationElsewhereDeduction contextRef="c1d1" decimals="INF" unitRef="EUR">9999999999999</bd-i:TaxationElsewhereDeduction>
	<bd-i:TaxpayerNameAddress1 contextRef="c1d1">TaxPayerNameAddressFCU</bd-i:TaxpayerNameAddress1>
	<bd-i:TaxpayerNameAddress3 contextRef="c1d1">1750661</bd-i:TaxpayerNameAddress3>
	<bd-i:TaxpayerNameAddress5 contextRef="c1d1">1751898</bd-i:TaxpayerNameAddress5>
</xbrli:xbrl>
