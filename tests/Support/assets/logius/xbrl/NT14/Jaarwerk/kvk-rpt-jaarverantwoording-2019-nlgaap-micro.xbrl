<?xml version="1.0" encoding="UTF-8"?>
<xbrli:xbrl xmlns:xbrli="http://www.xbrl.org/2003/instance" xml:lang="nl" xmlns:kvk-i="http://www.nltaxonomie.nl/nt14/kvk/20191211/dictionary/kvk-data" xmlns:jenv-bw2-dm="http://www.nltaxonomie.nl/nt14/jenv/20191211/dictionary/jenv-bw2-domains" 
xmlns:jenv-bw2-i="http://www.nltaxonomie.nl/nt14/jenv/20191211/dictionary/jenv-bw2-data" xmlns:rj-i="http://www.nltaxonomie.nl/nt14/rj/20191211/dictionary/rj-data" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:nl-cd="http://www.nltaxonomie.nl/nt14/sbr/20190320/dictionary/nl-common-data" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xbrldi="http://xbrl.org/2006/xbrldi" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:jenv-bw2-dim="http://www.nltaxonomie.nl/nt14/jenv/20191211/dictionary/jenv-bw2-axes">
	<link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt14/kvk/20191211/entrypoints/kvk-rpt-jaarverantwoording-2019-nlgaap-micro.xsd"/>
	<xbrli:context id="FY18d_ManagingOrSupervisoryDirector_Typed_1">
		<xbrli:entity>
			<xbrli:identifier scheme="http://www.kvk.nl/kvk-id">30267979</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2018-01-01</xbrli:startDate>
			<xbrli:endDate>2018-12-31</xbrli:endDate>
		</xbrli:period>
		<xbrli:scenario>
			<xbrldi:typedMember dimension="jenv-bw2-dim:ManagingOrSupervisoryDirectorOrRepresentativeNamesAxis">
				<jenv-bw2-dm:ManagingOrSupervisoryDirectorOrRepresentativeTypedMember>Frank Jensen</jenv-bw2-dm:ManagingOrSupervisoryDirectorOrRepresentativeTypedMember>
			</xbrldi:typedMember>
		</xbrli:scenario>
	</xbrli:context>
	<xbrli:context id="FY18d">
		<xbrli:entity>
			<xbrli:identifier scheme="http://www.kvk.nl/kvk-id">30267975</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2018-01-01</xbrli:startDate>
			<xbrli:endDate>2018-12-31</xbrli:endDate>
		</xbrli:period>
	</xbrli:context>
	<xbrli:context id="FY18d_Commercial_Separate">
		<xbrli:entity>
			<xbrli:identifier scheme="http://www.kvk.nl/kvk-id">30267975</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2018-01-01</xbrli:startDate>
			<xbrli:endDate>2018-12-31</xbrli:endDate>
		</xbrli:period>
		<xbrli:scenario>
			<xbrldi:explicitMember dimension="jenv-bw2-dim:BasisOfPreparationAxis">jenv-bw2-dm:CommercialMember</xbrldi:explicitMember>
			<xbrldi:explicitMember dimension="jenv-bw2-dim:FinancialStatementsTypeAxis">jenv-bw2-dm:SeparateMember</xbrldi:explicitMember>
		</xbrli:scenario>
	</xbrli:context>
	<xbrli:context id="FY18i_Commercial_Separate">
		<xbrli:entity>
			<xbrli:identifier scheme="http://www.kvk.nl/kvk-id">30267975</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:instant>2018-12-31</xbrli:instant>
		</xbrli:period>
		<xbrli:scenario>
			<xbrldi:explicitMember dimension="jenv-bw2-dim:BasisOfPreparationAxis">jenv-bw2-dm:CommercialMember</xbrldi:explicitMember>
			<xbrldi:explicitMember dimension="jenv-bw2-dim:FinancialStatementsTypeAxis">jenv-bw2-dm:SeparateMember</xbrldi:explicitMember>
		</xbrli:scenario>
	</xbrli:context>
	<xbrli:context id="FY17i_Commercial_Separate">
		<xbrli:entity>
			<xbrli:identifier scheme="http://www.kvk.nl/kvk-id">30267975</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:instant>2017-12-31</xbrli:instant>
		</xbrli:period>
		<xbrli:scenario>
			<xbrldi:explicitMember dimension="jenv-bw2-dim:BasisOfPreparationAxis">jenv-bw2-dm:CommercialMember</xbrldi:explicitMember>
			<xbrldi:explicitMember dimension="jenv-bw2-dim:FinancialStatementsTypeAxis">jenv-bw2-dm:SeparateMember</xbrldi:explicitMember>
		</xbrli:scenario>
	</xbrli:context>
	<xbrli:context id="FY17d_Commercial_Separate">
		<xbrli:entity>
			<xbrli:identifier scheme="http://www.kvk.nl/kvk-id">30267975</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2017-01-01</xbrli:startDate>
			<xbrli:endDate>2017-12-31</xbrli:endDate>
		</xbrli:period>
		<xbrli:scenario>
			<xbrldi:explicitMember dimension="jenv-bw2-dim:BasisOfPreparationAxis">jenv-bw2-dm:CommercialMember</xbrldi:explicitMember>
			<xbrldi:explicitMember dimension="jenv-bw2-dim:FinancialStatementsTypeAxis">jenv-bw2-dm:SeparateMember</xbrldi:explicitMember>
		</xbrli:scenario>
	</xbrli:context>
	<xbrli:unit id="EUR">
		<xbrli:measure>iso4217:EUR</xbrli:measure>
	</xbrli:unit>
	<kvk-i:EmailAddressContact contextRef="FY18d"><EMAIL></kvk-i:EmailAddressContact>
	<nl-cd:StreetNameNL contextRef="FY18d">Noordeinde</nl-cd:StreetNameNL>
	<nl-cd:HouseNumberNL contextRef="FY18d">68</nl-cd:HouseNumberNL>
	<nl-cd:PostalCodeNL contextRef="FY18d">2514GL</nl-cd:PostalCodeNL>
	<nl-cd:PlaceOfResidenceNL contextRef="FY18d">Den Haag</nl-cd:PlaceOfResidenceNL>
	<kvk-i:LegalSizeCriteriaClassification contextRef="FY18d">Micro</kvk-i:LegalSizeCriteriaClassification>
	<jenv-bw2-i:LegalEntityName contextRef="FY18d">BAA BV</jenv-bw2-i:LegalEntityName>
	<jenv-bw2-i:LegalEntityLegalForm contextRef="FY18d">Besloten vennootschap met beperkte aansprakelijkheid</jenv-bw2-i:LegalEntityLegalForm>
	<jenv-bw2-i:LegalEntityRegisteredOffice contextRef="FY18d">Den Haag</jenv-bw2-i:LegalEntityRegisteredOffice>
	<jenv-bw2-i:ChamberOfCommerceRegistrationNumber contextRef="FY18d">30267975</jenv-bw2-i:ChamberOfCommerceRegistrationNumber>
	<jenv-bw2-i:FinancialReportingPeriodCurrentStartDate contextRef="FY18d">2018-01-01</jenv-bw2-i:FinancialReportingPeriodCurrentStartDate>
	<jenv-bw2-i:FinancialReportingPeriodCurrentEndDate contextRef="FY18d">2018-12-31</jenv-bw2-i:FinancialReportingPeriodCurrentEndDate>
	<jenv-bw2-i:DocumentAdoptionStatus contextRef="FY18d">Nee</jenv-bw2-i:DocumentAdoptionStatus>
	<jenv-bw2-i:FinancialReportingPeriodPreviousStartDate contextRef="FY18d">2017-01-01</jenv-bw2-i:FinancialReportingPeriodPreviousStartDate>
	<jenv-bw2-i:FinancialReportingPeriodPreviousEndDate contextRef="FY18d">2017-12-31</jenv-bw2-i:FinancialReportingPeriodPreviousEndDate>
	<rj-i:FinancialReportingPeriodDifferentThanAnnualStatus contextRef="FY18d">Nee</rj-i:FinancialReportingPeriodDifferentThanAnnualStatus>
	<rj-i:DocumentPresentationCurrency contextRef="FY18d">EUR</rj-i:DocumentPresentationCurrency>
	<jenv-bw2-i:BasisOfPreparation contextRef="FY18d">Commercieel</jenv-bw2-i:BasisOfPreparation>
	<jenv-bw2-i:BalanceSheetBeforeAfterAppropriationResults contextRef="FY18d_Commercial_Separate">Na</jenv-bw2-i:BalanceSheetBeforeAfterAppropriationResults>
	<jenv-bw2-i:CostsIncorporationShareIssue contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">150000</jenv-bw2-i:CostsIncorporationShareIssue>
	<jenv-bw2-i:AssetsNoncurrent contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">150000</jenv-bw2-i:AssetsNoncurrent>	
	<jenv-bw2-i:CalledUpShareCapital contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">50000</jenv-bw2-i:CalledUpShareCapital>
	<jenv-bw2-i:AssetsCurrent contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">50000</jenv-bw2-i:AssetsCurrent>
	<jenv-bw2-i:Assets contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">200000</jenv-bw2-i:Assets>
	<jenv-bw2-i:Equity contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">100000</jenv-bw2-i:Equity>
	<jenv-bw2-i:Provisions contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">25000</jenv-bw2-i:Provisions>
	<jenv-bw2-i:Liabilities contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">75000</jenv-bw2-i:Liabilities>
	<jenv-bw2-i:EquityAndLiabilities contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">200000</jenv-bw2-i:EquityAndLiabilities>	
	<jenv-bw2-i:CostsIncorporationShareIssue contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">140000</jenv-bw2-i:CostsIncorporationShareIssue>
	<jenv-bw2-i:AssetsNoncurrent contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">140000</jenv-bw2-i:AssetsNoncurrent>
	<jenv-bw2-i:CalledUpShareCapital contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">60000</jenv-bw2-i:CalledUpShareCapital>
	<jenv-bw2-i:AssetsCurrent contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">60000</jenv-bw2-i:AssetsCurrent>
	<jenv-bw2-i:Assets contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">200000</jenv-bw2-i:Assets>
	<jenv-bw2-i:Equity contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">100000</jenv-bw2-i:Equity>
	<jenv-bw2-i:Provisions contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">35000</jenv-bw2-i:Provisions>
	<jenv-bw2-i:Liabilities contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">65000</jenv-bw2-i:Liabilities>	
	<jenv-bw2-i:EquityAndLiabilities contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">200000</jenv-bw2-i:EquityAndLiabilities>
	<jenv-bw2-i:NetRevenue contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">200000</jenv-bw2-i:NetRevenue>
	<jenv-bw2-i:OperatingIncomeOther contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">80000</jenv-bw2-i:OperatingIncomeOther>
	<jenv-bw2-i:IncomeOther contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">30000</jenv-bw2-i:IncomeOther>
	<jenv-bw2-i:Income contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">310000</jenv-bw2-i:Income>
	<jenv-bw2-i:WagesSalaries contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">80000</jenv-bw2-i:WagesSalaries>
	<jenv-bw2-i:DepreciationAmortisationAndDecreaseInValueAssets contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">10000</jenv-bw2-i:DepreciationAmortisationAndDecreaseInValueAssets>
	<jenv-bw2-i:OperatingExpensesOther contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">10000</jenv-bw2-i:OperatingExpensesOther>
	<jenv-bw2-i:ExpensesOther contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">5000</jenv-bw2-i:ExpensesOther>
	<jenv-bw2-i:CostsRawMaterialsConsumables contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">5000</jenv-bw2-i:CostsRawMaterialsConsumables>
	<jenv-bw2-i:Expenses contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">110000</jenv-bw2-i:Expenses>
	<jenv-bw2-i:ResultBeforeTax contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">200000</jenv-bw2-i:ResultBeforeTax>
	<jenv-bw2-i:IncomeTaxExpense contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">100000</jenv-bw2-i:IncomeTaxExpense>
	<jenv-bw2-i:ResultAfterTax contextRef="FY18d_Commercial_Separate" unitRef="EUR" decimals="INF">100000</jenv-bw2-i:ResultAfterTax>
	<jenv-bw2-i:NetRevenue contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">180000</jenv-bw2-i:NetRevenue>
	<jenv-bw2-i:OperatingIncomeOther contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">100000</jenv-bw2-i:OperatingIncomeOther>
	<jenv-bw2-i:IncomeOther contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">20000</jenv-bw2-i:IncomeOther>
	<jenv-bw2-i:Income contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">300000</jenv-bw2-i:Income>
	<jenv-bw2-i:WagesSalaries contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">80000</jenv-bw2-i:WagesSalaries>
	<jenv-bw2-i:DepreciationAmortisationAndDecreaseInValueAssets contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">5000</jenv-bw2-i:DepreciationAmortisationAndDecreaseInValueAssets>
	<jenv-bw2-i:OperatingExpensesOther contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">15000</jenv-bw2-i:OperatingExpensesOther>
	<jenv-bw2-i:ExpensesOther contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">5500</jenv-bw2-i:ExpensesOther>
	<jenv-bw2-i:CostsRawMaterialsConsumables contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">5500</jenv-bw2-i:CostsRawMaterialsConsumables>
	<jenv-bw2-i:Expenses contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">111000</jenv-bw2-i:Expenses>
	<jenv-bw2-i:ResultBeforeTax contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">189000</jenv-bw2-i:ResultBeforeTax>
	<jenv-bw2-i:IncomeTaxExpense contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">100000</jenv-bw2-i:IncomeTaxExpense>
	<jenv-bw2-i:ResultAfterTax contextRef="FY17d_Commercial_Separate" unitRef="EUR" decimals="INF">89000</jenv-bw2-i:ResultAfterTax>
	<jenv-bw2-i:DirectorType contextRef="FY18d_ManagingOrSupervisoryDirector_Typed_1">Bestuurder (huidig)</jenv-bw2-i:DirectorType>
	<jenv-bw2-i:DirectorSignedStatus contextRef="FY18d_ManagingOrSupervisoryDirector_Typed_1">Nee</jenv-bw2-i:DirectorSignedStatus>
	<jenv-bw2-i:DirectorReasonNoSignature contextRef="FY18d_ManagingOrSupervisoryDirector_Typed_1">De jaarrekening is nog niet vastgesteld</jenv-bw2-i:DirectorReasonNoSignature>
</xbrli:xbrl>
