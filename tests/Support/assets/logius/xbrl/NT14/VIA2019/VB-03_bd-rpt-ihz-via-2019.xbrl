<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt14/bd/20191211/entrypoints/bd-rpt-ihz-via-2019.xsd -->
<!-- Intellectual Property State of the Netherlands -->
<!-- Situatie: Alleenstaande, Bel pl weduwenaar, ink tgw arbeid, arb krt en levensloopverl krt, Z<PERSON> uitkering, Ink vrg arbeid met inh LH, Ink vrg arbeid BTL, OZ en aanhorigheid, hoofdverblijf, effectenrek BIN, hypotheek.  -->
<!-- Created: 2019-10-10T14:00:00 -->
<xbrli:xbrl xmlns:bd-i="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-data" xmlns:bd-dim-mem="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-domain-members" xmlns:bd-t="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-tuples" xmlns:bd-dim-dim="http://www.nltaxonomie.nl/nt14/bd/20191211/validation/bd-axes" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:xbrldi="http://xbrl.org/2006/xbrldi" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:xlink="http://www.w3.org/1999/xlink" xml:lang="nl">
	<link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt14/bd/20191211/entrypoints/bd-rpt-ihz-via-2019.xsd"/>
	<xbrli:context id="Context_Duration">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:startDate>2019-01-01</xbrli:startDate>
			<xbrli:endDate>2019-12-31</xbrli:endDate>
		</xbrli:period>
	</xbrli:context>
	<xbrli:context id="Context_Instant_Begin">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:instant>2019-01-01</xbrli:instant>
		</xbrli:period>
		<xbrli:scenario>
			<xbrldi:explicitMember dimension="bd-dim-dim:TimeDimension">bd-dim-mem:Begin</xbrldi:explicitMember>
		</xbrli:scenario>
	</xbrli:context>
	<xbrli:context id="Context_Instant_End">
		<xbrli:entity>
			<xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
		</xbrli:entity>
		<xbrli:period>
			<xbrli:instant>2019-12-31</xbrli:instant>
		</xbrli:period>
		<xbrli:scenario>
			<xbrldi:explicitMember dimension="bd-dim-dim:TimeDimension">bd-dim-mem:End</xbrldi:explicitMember>
		</xbrli:scenario>
	</xbrli:context>
	<xbrli:unit id="EUR">
		<xbrli:measure>iso4217:EUR</xbrli:measure>
	</xbrli:unit>
    <xbrli:unit id="PURE">
        <xbrli:measure>xbrli:pure</xbrli:measure>
    </xbrli:unit>
	<bd-i:BankAccountNumber contextRef="Context_Duration">31NL26RABO98765432</bd-i:BankAccountNumber>
	<bd-i:DateOfBirth contextRef="Context_Duration">1974-04-04</bd-i:DateOfBirth>
	<bd-i:PrefilledTaxReturnIncomeUseWatermark contextRef="Context_Duration">OLOgwFYN2OLOgwFYN2OLOgwFYN2OLOgw</bd-i:PrefilledTaxReturnIncomeUseWatermark>
	<bd-i:TaxpayerName contextRef="Context_Duration">A.L. Leen</bd-i:TaxpayerName>
	<bd-i:WagesPresentWorkTotal contextRef="Context_Duration" decimals="INF" unitRef="EUR">6000</bd-i:WagesPresentWorkTotal>
	<bd-i:WagesPreviousWorkTotal contextRef="Context_Duration" decimals="INF" unitRef="EUR">23000</bd-i:WagesPreviousWorkTotal>
	<bd-t:AssetsOnStockAccountsSpecification>
		<bd-i:AssetsOnStockAccountsAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:AssetsOnStockAccountsAccountHolderIdentificationNumber>
		<bd-i:AssetsOnStockAccountsBalance contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">500</bd-i:AssetsOnStockAccountsBalance>
		<bd-i:AssetsOnStockAccountsBalanceType contextRef="Context_Duration">31</bd-i:AssetsOnStockAccountsBalanceType>
		<bd-i:AssetsOnStockAccountsBankAccountNumber contextRef="Context_Duration">******************</bd-i:AssetsOnStockAccountsBankAccountNumber>
		<bd-i:AssetsOnStockAccountsBankProductLabel contextRef="Context_Duration">Abcd</bd-i:AssetsOnStockAccountsBankProductLabel>
		<bd-i:AssetsOnStockAccountsCommonInterestIndication contextRef="Context_Duration">03</bd-i:AssetsOnStockAccountsCommonInterestIndication>
		<bd-i:AssetsOnStockAccountsProductID contextRef="Context_Duration">Rabo effect</bd-i:AssetsOnStockAccountsProductID>
		<bd-i:AssetsOnStockAccountsRecordType contextRef="Context_Duration">07</bd-i:AssetsOnStockAccountsRecordType>
		<bd-i:AssetsOnStockAccountsSourceBankTaxReferenceNumber contextRef="Context_Duration">*********</bd-i:AssetsOnStockAccountsSourceBankTaxReferenceNumber>
	</bd-t:AssetsOnStockAccountsSpecification>
	<bd-t:ConstructionDepotSpecification>
		<bd-i:ConstructionDepotAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:ConstructionDepotAccountHolderIdentificationNumber>
		<bd-i:ConstructionDepotBalance contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">15000</bd-i:ConstructionDepotBalance>
		<bd-i:ConstructionDepotBalance contextRef="Context_Instant_End" decimals="INF" unitRef="EUR">10000</bd-i:ConstructionDepotBalance>
		<bd-i:ConstructionDepotBankAccountNumber contextRef="Context_Duration">*********</bd-i:ConstructionDepotBankAccountNumber>
        <bd-i:ConstructionDepotCommonInterestIndication contextRef="Context_Duration">01</bd-i:ConstructionDepotCommonInterestIndication>
        <bd-i:ConstructionDepotInterest contextRef="Context_Duration" decimals="INF" unitRef="EUR">5000</bd-i:ConstructionDepotInterest>
        <bd-i:ConstructionDepotLabel contextRef="Context_Duration">Depot</bd-i:ConstructionDepotLabel>
        <bd-i:ConstructionDepotProductID contextRef="Context_Duration">BDEFGHIJK</bd-i:ConstructionDepotProductID>
        <bd-i:ConstructionDepotSourceBankTaxReferenceNumber contextRef="Context_Duration">*********</bd-i:ConstructionDepotSourceBankTaxReferenceNumber>
	</bd-t:ConstructionDepotSpecification>
	<bd-t:DebtAccountsSpecification>
		<bd-i:DebtAccountsAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:DebtAccountsAccountHolderIdentificationNumber>
		<bd-i:DebtAccountsBalanceType contextRef="Context_Duration">29</bd-i:DebtAccountsBalanceType>
		<bd-i:DebtAccountsBankAccountNumber contextRef="Context_Duration">NL98ABNA*********</bd-i:DebtAccountsBankAccountNumber>
		<bd-i:DebtAccountsBankProductLabel contextRef="Context_Duration">Schuld 1</bd-i:DebtAccountsBankProductLabel>
		<bd-i:DebtAccountsCommonInterestIndication contextRef="Context_Duration">01</bd-i:DebtAccountsCommonInterestIndication>
        <bd-i:DebtAccountsInstalmentIndication contextRef="Context_Duration">true</bd-i:DebtAccountsInstalmentIndication>
		<bd-i:DebtAccountsLoanStartAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">150000</bd-i:DebtAccountsLoanStartAmount>
        <bd-i:DebtAccountsMortgageIndication contextRef="Context_Duration">true</bd-i:DebtAccountsMortgageIndication>
		<bd-i:DebtAccountsRecordType contextRef="Context_Duration">06</bd-i:DebtAccountsRecordType>
		<bd-i:DebtAccountsSourceBankTaxReferenceNumber contextRef="Context_Duration">*********</bd-i:DebtAccountsSourceBankTaxReferenceNumber>
		<bd-i:DebtAccountsSourceProductID contextRef="Context_Duration">1234</bd-i:DebtAccountsSourceProductID>
		<bd-t:DebtInterestSpecification>
			<bd-i:DebtInterestAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">10000</bd-i:DebtInterestAmount>
			<bd-i:DebtInterestAmountOwnHouseDebt contextRef="Context_Duration" decimals="INF" unitRef="EUR">10000</bd-i:DebtInterestAmountOwnHouseDebt>
			<bd-i:DebtInterestBalanceType contextRef="Context_Duration">24</bd-i:DebtInterestBalanceType>
		</bd-t:DebtInterestSpecification>
		<bd-t:DebtPositionSpecification>
			<bd-i:DebtPositionBankAccountBalance contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">305000</bd-i:DebtPositionBankAccountBalance>
			<bd-i:DebtPositionBankAccountBalance contextRef="Context_Instant_End" decimals="INF" unitRef="EUR">305020</bd-i:DebtPositionBankAccountBalance>
			<bd-i:DebtPositionBankAccountBalanceOriginalCurrency contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">100000</bd-i:DebtPositionBankAccountBalanceOriginalCurrency>
            <bd-i:DebtPositionBankAccountBalanceOriginalCurrency contextRef="Context_Instant_End" decimals="INF" unitRef="EUR">175000</bd-i:DebtPositionBankAccountBalanceOriginalCurrency>
			<bd-i:DebtPositionBankAccountBenefitTaxpayer contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">125000</bd-i:DebtPositionBankAccountBenefitTaxpayer>
			<bd-i:DebtPositionBankAccountBenefitTaxpayer contextRef="Context_Instant_End" decimals="INF" unitRef="EUR">125010</bd-i:DebtPositionBankAccountBenefitTaxpayer>
			<bd-i:DebtPositionBankAccountBox3Balance contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">180000</bd-i:DebtPositionBankAccountBox3Balance>
			<bd-i:DebtPositionBankAccountOwnHouseDebtBalance contextRef="Context_Instant_End" decimals="INF" unitRef="EUR">305020</bd-i:DebtPositionBankAccountOwnHouseDebtBalance>
			<bd-i:DebtPositionBankAccountRestDebt contextRef="Context_Instant_End" decimals="INF" unitRef="EUR">180010</bd-i:DebtPositionBankAccountRestDebt>
		</bd-t:DebtPositionSpecification>
	</bd-t:DebtAccountsSpecification>
	<bd-t:DebtAccountsSpecification>
		<bd-i:DebtAccountsAccountHolderIdentificationNumber contextRef="Context_Duration">*********</bd-i:DebtAccountsAccountHolderIdentificationNumber>
		<bd-i:DebtAccountsBalanceType contextRef="Context_Duration">29</bd-i:DebtAccountsBalanceType>
		<bd-i:DebtAccountsBankAccountNumber contextRef="Context_Duration">**********</bd-i:DebtAccountsBankAccountNumber>
		<bd-i:DebtAccountsBankProductLabel contextRef="Context_Duration">Schuld 2</bd-i:DebtAccountsBankProductLabel>
		<bd-i:DebtAccountsCommonInterestIndication contextRef="Context_Duration">01</bd-i:DebtAccountsCommonInterestIndication>
		<bd-i:DebtAccountsRecordType contextRef="Context_Duration">06</bd-i:DebtAccountsRecordType>
		<bd-i:DebtAccountsSourceBankTaxReferenceNumber contextRef="Context_Duration">*********</bd-i:DebtAccountsSourceBankTaxReferenceNumber>
		<bd-i:DebtAccountsSourceProductID contextRef="Context_Duration">Doorlopend krediet</bd-i:DebtAccountsSourceProductID>
		<bd-t:DebtPositionSpecification>
			<bd-i:DebtPositionBankAccountBalance contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">25000</bd-i:DebtPositionBankAccountBalance>
			<bd-i:DebtPositionBankAccountBenefitTaxpayer contextRef="Context_Instant_Begin" decimals="INF" unitRef="EUR">25000</bd-i:DebtPositionBankAccountBenefitTaxpayer>
		</bd-t:DebtPositionSpecification>
	</bd-t:DebtAccountsSpecification>
	<bd-t:IncomePreviousWorkAbroadWithoutDutchWageTaxSpecification>
		<bd-i:ForeignBenefitsAgencyNameAndAddress contextRef="Context_Duration">OTAN</bd-i:ForeignBenefitsAgencyNameAndAddress>
		<bd-i:IncomePreviousWorkAbroadWithoutDutchWageTaxAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">11000</bd-i:IncomePreviousWorkAbroadWithoutDutchWageTaxAmount>
		<bd-i:IncomePreviousWorkAbroadWithoutDutchWageTaxCountryCode contextRef="Context_Duration">BEL</bd-i:IncomePreviousWorkAbroadWithoutDutchWageTaxCountryCode>
	</bd-t:IncomePreviousWorkAbroadWithoutDutchWageTaxSpecification>
	<bd-t:InvestmentPaymentSpecification>
		<bd-i:InvestmentPaymentAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">6200</bd-i:InvestmentPaymentAmount>
		<bd-i:InvestmentPaymentCode contextRef="Context_Duration">INLG</bd-i:InvestmentPaymentCode>
		<bd-i:InvestmentPaymentFinancialAgencyName contextRef="Context_Duration">Fin Inst BV</bd-i:InvestmentPaymentFinancialAgencyName>
		<bd-i:InvestmentPaymentPolicyNumber contextRef="Context_Duration">321654tg06a</bd-i:InvestmentPaymentPolicyNumber>
	</bd-t:InvestmentPaymentSpecification>
	<bd-t:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationSpecification>
		<bd-t:AddressPresentation>
			<bd-i:HouseNumberAddition contextRef="Context_Duration">A</bd-i:HouseNumberAddition>
			<bd-i:HouseNumberNL contextRef="Context_Duration" decimals="INF" unitRef="PURE">1</bd-i:HouseNumberNL>
			<bd-i:PlaceOfResidenceNL contextRef="Context_Duration">Boerenbuiten</bd-i:PlaceOfResidenceNL>
			<bd-i:PostalCodeNL contextRef="Context_Duration">1234AD</bd-i:PostalCodeNL>
			<bd-i:StreetNameNL contextRef="Context_Duration">Hooiweg</bd-i:StreetNameNL>
		</bd-t:AddressPresentation>
		<bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanDescription contextRef="Context_Duration">lening ouders</bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanDescription>
		<bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanNumber contextRef="Context_Duration">1</bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanNumber>
		<bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanPeriodEndDate contextRef="Context_Duration">2019-07-01</bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanPeriodEndDate>
		<bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanPeriodStartDate contextRef="Context_Duration">2015-01-01</bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanPeriodStartDate>
		<bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanProviderIdentificationNumber contextRef="Context_Duration">*********</bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanProviderIdentificationNumber>
		<bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanProviderInitials contextRef="Context_Duration">M.N.</bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanProviderInitials>
		<bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanProviderSurname contextRef="Context_Duration">Vader</bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanProviderSurname>
		<bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanRedemptionLinear contextRef="Context_Duration">true</bd-i:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationLoanRedemptionLinear>
	</bd-t:OwnHouseDebtDeductibleInterestOtherThanTaxAdministrationSpecification>
	<bd-t:OwnHouseSecondHomeImmovablePropertyOtherSpecification>
		<bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawAddress contextRef="Context_Duration">1234AB Nieuwstraat 1</bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawAddress>
		<bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawAppurtenanceObjectNumber contextRef="Context_Duration" decimals="INF" unitRef="PURE">123456</bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawAppurtenanceObjectNumber>
		<bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawMunicipalityName contextRef="Context_Duration">Apeldoorn</bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawMunicipalityName>
		<bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawValue contextRef="Context_Duration" decimals="INF" unitRef="EUR">250000</bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawValue>
		<bd-t:ResidenceSituationPeriodQualificationSpecification>
			<bd-i:ResidenceSituation contextRef="Context_Duration">01</bd-i:ResidenceSituation>
			<bd-i:ResidenceSituationEndDate contextRef="Context_Duration">2019-10-01</bd-i:ResidenceSituationEndDate>
			<bd-i:ResidenceSituationPreQualification contextRef="Context_Duration">01</bd-i:ResidenceSituationPreQualification>
			<bd-i:ResidenceSituationPrincipalResidenceObjectNumber contextRef="Context_Duration" decimals="INF" unitRef="PURE">*********0</bd-i:ResidenceSituationPrincipalResidenceObjectNumber>
			<bd-i:ResidenceSituationStartDate contextRef="Context_Duration">2011-03-01</bd-i:ResidenceSituationStartDate>
		</bd-t:ResidenceSituationPeriodQualificationSpecification>
		<bd-t:ResidenceSituationPeriodQualificationSpecification>
			<bd-i:ResidenceSituation contextRef="Context_Duration">04</bd-i:ResidenceSituation>
			<bd-i:ResidenceSituationEndDate contextRef="Context_Duration">2019-12-31</bd-i:ResidenceSituationEndDate>
			<bd-i:ResidenceSituationPreQualification contextRef="Context_Duration">02</bd-i:ResidenceSituationPreQualification>
			<bd-i:ResidenceSituationPrincipalResidenceObjectNumber contextRef="Context_Duration" decimals="INF" unitRef="PURE">*********0</bd-i:ResidenceSituationPrincipalResidenceObjectNumber>
			<bd-i:ResidenceSituationStartDate contextRef="Context_Duration">2019-10-02</bd-i:ResidenceSituationStartDate>
		</bd-t:ResidenceSituationPeriodQualificationSpecification>
	</bd-t:OwnHouseSecondHomeImmovablePropertyOtherSpecification>
	<bd-t:OwnHouseSecondHomeImmovablePropertyOtherSpecification>
		<bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawAddress contextRef="Context_Duration">1234AV 1</bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawAddress>
		<bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawAppurtenanceObjectNumber contextRef="Context_Duration" decimals="INF" unitRef="PURE">*********1</bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawAppurtenanceObjectNumber>
		<bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawMunicipalityName contextRef="Context_Duration">Apeldoorn</bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawMunicipalityName>
		<bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawValue contextRef="Context_Duration" decimals="INF" unitRef="EUR">19000</bd-i:OwnHouseSecondHomeImmovablePropertyOtherImmovablePropertyLawValue>
	</bd-t:OwnHouseSecondHomeImmovablePropertyOtherSpecification>
	<bd-t:PeriodicPaymentsDisabilityPremiumsSpecification>
		<bd-i:PeriodicPaymentsDisabilityCode contextRef="Context_Duration">PRAV</bd-i:PeriodicPaymentsDisabilityCode>
		<bd-i:PeriodicPaymentsDisabilityLabel contextRef="Context_Duration">PU</bd-i:PeriodicPaymentsDisabilityLabel>
		<bd-i:PeriodicPaymentsDisabilityPaidAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">360</bd-i:PeriodicPaymentsDisabilityPaidAmount>
		<bd-i:PeriodicPaymentsDisabilityPolicyNumber contextRef="Context_Duration">357951</bd-i:PeriodicPaymentsDisabilityPolicyNumber>
		<bd-i:PeriodicPaymentsDisabilityProductID contextRef="Context_Duration">VERZ-PU</bd-i:PeriodicPaymentsDisabilityProductID>
	</bd-t:PeriodicPaymentsDisabilityPremiumsSpecification>
	<bd-t:PresentWorkSpecification>
		<bd-i:PresentWorkEmployedPersonsTaxCreditSettled contextRef="Context_Duration" decimals="INF" unitRef="EUR">960</bd-i:PresentWorkEmployedPersonsTaxCreditSettled>
		<bd-i:PresentWorkEmployerOrBenefitsAgencyName contextRef="Context_Duration">Boss</bd-i:PresentWorkEmployerOrBenefitsAgencyName>
		<bd-i:PresentWorkIncomeRatioCode contextRef="Context_Duration">11</bd-i:PresentWorkIncomeRatioCode>
		<bd-i:PresentWorkLifecycleLeaveReductionApplied contextRef="Context_Duration" decimals="INF" unitRef="EUR">209</bd-i:PresentWorkLifecycleLeaveReductionApplied>
		<bd-i:PresentWorkWages contextRef="Context_Duration" decimals="INF" unitRef="EUR">44000</bd-i:PresentWorkWages>
		<bd-i:PresentWorkWageTaxChartCode contextRef="Context_Duration">222</bd-i:PresentWorkWageTaxChartCode>																									  
		<bd-i:PresentWorkWithheldWageLevy contextRef="Context_Duration" decimals="INF" unitRef="EUR">1240</bd-i:PresentWorkWithheldWageLevy>
	</bd-t:PresentWorkSpecification>
	<bd-t:PresentWorkSpecification>
		<bd-i:PresentWorkEmployedPersonsTaxCreditSettled contextRef="Context_Duration" decimals="INF" unitRef="EUR">0</bd-i:PresentWorkEmployedPersonsTaxCreditSettled>
		<bd-i:PresentWorkEmployerOrBenefitsAgencyName contextRef="Context_Duration">UWV</bd-i:PresentWorkEmployerOrBenefitsAgencyName>
		<bd-i:PresentWorkHealthInsuranceLawWithheld contextRef="Context_Duration" decimals="INF" unitRef="EUR">30</bd-i:PresentWorkHealthInsuranceLawWithheld>
		<bd-i:PresentWorkIncomeRatioCode contextRef="Context_Duration">09</bd-i:PresentWorkIncomeRatioCode>
		<bd-i:PresentWorkLifecycleLeaveReductionApplied contextRef="Context_Duration" decimals="INF" unitRef="EUR">0</bd-i:PresentWorkLifecycleLeaveReductionApplied>
		<bd-i:PresentWorkWages contextRef="Context_Duration" decimals="INF" unitRef="EUR">6000</bd-i:PresentWorkWages>
		<bd-i:PresentWorkWageTaxChartCode contextRef="Context_Duration">111</bd-i:PresentWorkWageTaxChartCode>
		<bd-i:PresentWorkWithheldWageLevy contextRef="Context_Duration" decimals="INF" unitRef="EUR">600</bd-i:PresentWorkWithheldWageLevy>
	</bd-t:PresentWorkSpecification>
	<bd-t:PreviousWorkSpecification>
		<bd-i:PreviousWorkBenefitsAgencyName contextRef="Context_Duration">Defensie</bd-i:PreviousWorkBenefitsAgencyName>
		<bd-i:PreviousWorkIncomeAmount contextRef="Context_Duration" decimals="INF" unitRef="EUR">12000</bd-i:PreviousWorkIncomeAmount>
		<bd-i:PreviousWorkIncomeRatioCode contextRef="Context_Duration">18</bd-i:PreviousWorkIncomeRatioCode>
		<bd-i:PreviousWorkLifecycleLeaveReductionApplied contextRef="Context_Duration" decimals="INF" unitRef="EUR">0</bd-i:PreviousWorkLifecycleLeaveReductionApplied>																																							   
		<bd-i:PreviousWorkWageTaxChartCode contextRef="Context_Duration">132</bd-i:PreviousWorkWageTaxChartCode>
		<bd-i:PreviousWorkWithheldWageLevy contextRef="Context_Duration" decimals="INF" unitRef="EUR">1212</bd-i:PreviousWorkWithheldWageLevy>
	</bd-t:PreviousWorkSpecification>
	<bd-t:RelationshipTaxpayerPersonalDataPartnerChildrenSpecification>
        <bd-i:RelationshipTaxpayerAddressRegistrationPeriodEndDate contextRef="Context_Duration">2019-10-01</bd-i:RelationshipTaxpayerAddressRegistrationPeriodEndDate>
        <bd-i:RelationshipTaxpayerAddressRegistrationPeriodStartDate contextRef="Context_Duration">2019-01-01</bd-i:RelationshipTaxpayerAddressRegistrationPeriodStartDate>
		<bd-i:RelationshipTaxpayerDateOfBirth contextRef="Context_Duration">1980-01-01</bd-i:RelationshipTaxpayerDateOfBirth>
		<bd-i:RelationshipTaxpayerDeceasedDate contextRef="Context_Duration">2019-10-01</bd-i:RelationshipTaxpayerDeceasedDate>
        <bd-i:RelationshipTaxpayerIdentificationNumber contextRef="Context_Duration">*********</bd-i:RelationshipTaxpayerIdentificationNumber>
        <bd-i:RelationshipTaxpayerRelationCode contextRef="Context_Duration">02</bd-i:RelationshipTaxpayerRelationCode>
        <bd-i:RelationshipTaxpayerSurname contextRef="Context_Duration">P. ArtnerOVL</bd-i:RelationshipTaxpayerSurname>
		<bd-i:RelationshipTaxpayerWeddingDate contextRef="Context_Duration">2011-11-11</bd-i:RelationshipTaxpayerWeddingDate>
	</bd-t:RelationshipTaxpayerPersonalDataPartnerChildrenSpecification>
	<bd-t:RetirementNetSpecification>
		<bd-i:RetirementNetCode contextRef="Context_Duration">PNPV</bd-i:RetirementNetCode>
		<bd-i:RetirementNetLabel contextRef="Context_Duration">Winterthur</bd-i:RetirementNetLabel>
		<bd-i:RetirementNetPolicyNumber contextRef="Context_Duration">ID-NETPEN</bd-i:RetirementNetPolicyNumber>
		<bd-i:RetirementNetPremiumsPaid contextRef="Context_Duration" decimals="INF" unitRef="EUR">2580</bd-i:RetirementNetPremiumsPaid>
		<bd-i:RetirementNetProductID contextRef="Context_Duration">551133</bd-i:RetirementNetProductID>
	</bd-t:RetirementNetSpecification>
</xbrli:xbrl>
