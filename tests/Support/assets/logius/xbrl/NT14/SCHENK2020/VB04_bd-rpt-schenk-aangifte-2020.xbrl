<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt14/bd/20200219/entrypoints/bd-rpt-schenk-aangifte-2020.xsd
Intellectual Property State of the Netherlands
Created on: 20-12-2019 14:01:00
-->
<xbrli:xbrl xml:lang="nl" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:bd-t="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-tuples" xmlns:bd-i-ext1="http://www.nltaxonomie.nl/nt14/bd/20200219/dictionary/bd-data-ext1" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:bd-t-ext1="http://www.nltaxonomie.nl/nt14/bd/20200219/dictionary/bd-tuples-ext1" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:bd-i="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-data" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:nl-cd="http://www.nltaxonomie.nl/nt14/sbr/20190320/dictionary/nl-common-data">
  <link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt14/bd/20200219/entrypoints/bd-rpt-schenk-aangifte-2020.xsd"/>
  <xbrli:context id="ContextDuration">
    <xbrli:entity>
      <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:startDate>2020-01-01</xbrli:startDate>
      <xbrli:endDate>2020-12-31</xbrli:endDate>
    </xbrli:period>
  </xbrli:context>
  <xbrli:unit id="EUR">
    <xbrli:measure>iso4217:EUR</xbrli:measure>
  </xbrli:unit>
  <xbrli:unit id="PURE">
    <xbrli:measure>xbrli:pure</xbrli:measure>
  </xbrli:unit>
  <bd-i:ContactInitials contextRef="ContextDuration">V</bd-i:ContactInitials>
  <bd-i:ContactPrefix contextRef="ContextDuration">s26c</bd-i:ContactPrefix>
  <bd-i:ContactSurname contextRef="ContextDuration">Smit [C]</bd-i:ContactSurname>
  <bd-i:ContactTelephoneNumber contextRef="ContextDuration">012-3456789</bd-i:ContactTelephoneNumber>
  <bd-i-ext1:SignatureDate contextRef="ContextDuration">2020-02-04</bd-i-ext1:SignatureDate>
  <bd-i:SoftwarePackageName contextRef="ContextDuration">BD-KetenTest</bd-i:SoftwarePackageName>
  <bd-i:SoftwarePackageVersion contextRef="ContextDuration">1754604-S26</bd-i:SoftwarePackageVersion>
  <bd-i:SoftwareVendorAccountNumber contextRef="ContextDuration">********</bd-i:SoftwareVendorAccountNumber>
  <bd-i-ext1:ContactIdentificationNumber contextRef="ContextDuration">*********</bd-i-ext1:ContactIdentificationNumber>
  <bd-i-ext1:DonorDateOfBirth contextRef="ContextDuration">1931-01-03</bd-i-ext1:DonorDateOfBirth>
  <bd-i-ext1:DonorIdentificationNumber contextRef="ContextDuration">*********</bd-i-ext1:DonorIdentificationNumber>
  <bd-i-ext1:DonorInitials contextRef="ContextDuration">OPA</bd-i-ext1:DonorInitials>
  <bd-i-ext1:DonorIsCorrespondentIndication contextRef="ContextDuration">false</bd-i-ext1:DonorIsCorrespondentIndication>
  <bd-i-ext1:DonorIsDeclarantIndication contextRef="ContextDuration">false</bd-i-ext1:DonorIsDeclarantIndication>
  <bd-i-ext1:DonorPrefix contextRef="ContextDuration">s26s1</bd-i-ext1:DonorPrefix>
  <bd-i-ext1:DonorSurname contextRef="ContextDuration">Kok</bd-i-ext1:DonorSurname>
  <bd-i-ext1:RecipientDateOfBirth contextRef="ContextDuration">1962-05-13</bd-i-ext1:RecipientDateOfBirth>
  <bd-i-ext1:RecipientInitials contextRef="ContextDuration">M</bd-i-ext1:RecipientInitials>
  <bd-i-ext1:RecipientIsContactPersonIndication contextRef="ContextDuration">false</bd-i-ext1:RecipientIsContactPersonIndication>
  <bd-i-ext1:RecipientIsDeclarantIndication contextRef="ContextDuration">true</bd-i-ext1:RecipientIsDeclarantIndication>
  <bd-i-ext1:RecipientPrefix contextRef="ContextDuration">s26o</bd-i-ext1:RecipientPrefix>
  <bd-i-ext1:RecipientSurname contextRef="ContextDuration">Smit</bd-i-ext1:RecipientSurname>
  <bd-t-ext1:CorrespondenceAddressPresentation>
    <bd-t:AddressPresentation>
      <bd-i:HouseNumberNL decimals="INF" contextRef="ContextDuration" unitRef="PURE">9</bd-i:HouseNumberNL>
      <bd-i:PlaceOfResidenceNL contextRef="ContextDuration">Maassluis</bd-i:PlaceOfResidenceNL>
      <bd-i:PostalCodeNL contextRef="ContextDuration">3146CC</bd-i:PostalCodeNL>
      <bd-i:StreetNameNL contextRef="ContextDuration">Stadsmolen</bd-i:StreetNameNL>
    </bd-t:AddressPresentation>
  </bd-t-ext1:CorrespondenceAddressPresentation>
  <bd-t-ext1:GiftSpecification>
    <bd-i-ext1:GiftBenefitAmount decimals="INF" contextRef="ContextDuration" unitRef="EUR">1600</bd-i-ext1:GiftBenefitAmount>
    <bd-i-ext1:GiftBenefitPeriod contextRef="ContextDuration">maand</bd-i-ext1:GiftBenefitPeriod>
    <bd-i-ext1:GiftDate contextRef="ContextDuration">2020-04-01</bd-i-ext1:GiftDate>
    <bd-i-ext1:GiftExemptionANBIOrSBBI contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionANBIOrSBBI>
    <bd-i-ext1:GiftExemptionDebtor contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionDebtor>
    <bd-i-ext1:GiftExemptionExpensiveStudyIncreased contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionExpensiveStudyIncreased>
    <bd-i-ext1:GiftExemptionNaturalObligation contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionNaturalObligation>
    <bd-i-ext1:GiftExemptionOneOffIncreased contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionOneOffIncreased>
    <bd-i-ext1:GiftExemptionOther contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionOther>
    <bd-i-ext1:GiftExemptionOwnHouseOneOffIncreased contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionOwnHouseOneOffIncreased>
    <bd-i-ext1:GiftExtensionPartnerConceptIndication contextRef="ContextDuration">false</bd-i-ext1:GiftExtensionPartnerConceptIndication>
    <bd-i-ext1:GiftPeriodicFirstDate contextRef="ContextDuration">2020-04-01</bd-i-ext1:GiftPeriodicFirstDate>
    <bd-i-ext1:GiftPeriodicLastDate contextRef="ContextDuration">2029-03-31</bd-i-ext1:GiftPeriodicLastDate>
    <bd-i-ext1:GiftRecipientDonorRelationCode contextRef="ContextDuration">120</bd-i-ext1:GiftRecipientDonorRelationCode>
    <bd-i-ext1:GiftRevocableGiftIndication contextRef="ContextDuration">false</bd-i-ext1:GiftRevocableGiftIndication>
    <bd-i-ext1:GiftTaxPayerIndication contextRef="ContextDuration">false</bd-i-ext1:GiftTaxPayerIndication>
  </bd-t-ext1:GiftSpecification>
</xbrli:xbrl>
