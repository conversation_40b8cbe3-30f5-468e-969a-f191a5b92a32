<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt14/bd/20200219/entrypoints/bd-rpt-schenk-aangifte-2020.xsd
Intellectual Property State of the Netherlands
Created on: 20-12-2019 14:01:00
-->
<xbrli:xbrl xml:lang="nl" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:bd-t="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-tuples" xmlns:bd-i-ext1="http://www.nltaxonomie.nl/nt14/bd/20200219/dictionary/bd-data-ext1" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:bd-t-ext1="http://www.nltaxonomie.nl/nt14/bd/20200219/dictionary/bd-tuples-ext1" xmlns:bd-i="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-data" xmlns:nl-cd="http://www.nltaxonomie.nl/nt14/sbr/20190320/dictionary/nl-common-data" xmlns:xlink="http://www.w3.org/1999/xlink">
  <link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt14/bd/20200219/entrypoints/bd-rpt-schenk-aangifte-2020.xsd"/>
  <xbrli:context id="ContextDuration">
    <xbrli:entity>
      <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:startDate>2020-01-01</xbrli:startDate>
      <xbrli:endDate>2020-12-31</xbrli:endDate>
    </xbrli:period>
  </xbrli:context>
  <xbrli:unit id="EUR">
    <xbrli:measure>iso4217:EUR</xbrli:measure>
  </xbrli:unit>
  <xbrli:unit id="PURE">
    <xbrli:measure>xbrli:pure</xbrli:measure>
  </xbrli:unit>
  <bd-i-ext1:SignatureDate contextRef="ContextDuration">2020-02-03</bd-i-ext1:SignatureDate>
  <bd-i:SoftwarePackageName contextRef="ContextDuration">BD-KetenTest</bd-i:SoftwarePackageName>
  <bd-i:SoftwarePackageVersion contextRef="ContextDuration">1754604-S23</bd-i:SoftwarePackageVersion>
  <bd-i:SoftwareVendorAccountNumber contextRef="ContextDuration">********</bd-i:SoftwareVendorAccountNumber>
  <bd-i-ext1:ContactEmailAddressFull contextRef="ContextDuration"><EMAIL></bd-i-ext1:ContactEmailAddressFull>
  <bd-i-ext1:DonorDateOfBirth contextRef="ContextDuration">1955-06-01</bd-i-ext1:DonorDateOfBirth>
  <bd-i-ext1:DonorIdentificationNumber contextRef="ContextDuration">*********</bd-i-ext1:DonorIdentificationNumber>
  <bd-i-ext1:DonorInitials contextRef="ContextDuration">NL</bd-i-ext1:DonorInitials>
  <bd-i-ext1:DonorIsCorrespondentIndication contextRef="ContextDuration">true</bd-i-ext1:DonorIsCorrespondentIndication>
  <bd-i-ext1:DonorIsDeclarantIndication contextRef="ContextDuration">false</bd-i-ext1:DonorIsDeclarantIndication>
  <bd-i-ext1:DonorPrefix contextRef="ContextDuration">s23s</bd-i-ext1:DonorPrefix>
  <bd-i-ext1:DonorSurname contextRef="ContextDuration">Kaaskop</bd-i-ext1:DonorSurname>
  <bd-i-ext1:RecipientDateOfBirth contextRef="ContextDuration">1985-06-01</bd-i-ext1:RecipientDateOfBirth>
  <bd-i-ext1:RecipientInitials contextRef="ContextDuration">D</bd-i-ext1:RecipientInitials>
  <bd-i-ext1:RecipientIsContactPersonIndication contextRef="ContextDuration">false</bd-i-ext1:RecipientIsContactPersonIndication>
  <bd-i-ext1:RecipientIsDeclarantIndication contextRef="ContextDuration">true</bd-i-ext1:RecipientIsDeclarantIndication>
  <bd-i-ext1:RecipientPrefix contextRef="ContextDuration">s23o</bd-i-ext1:RecipientPrefix>
  <bd-i-ext1:RecipientSurname contextRef="ContextDuration">Kaaskop</bd-i-ext1:RecipientSurname>
  <bd-t-ext1:GiftSpecification>
    <bd-i-ext1:GiftDate contextRef="ContextDuration">2020-03-01</bd-i-ext1:GiftDate>
    <bd-i-ext1:GiftDebtRecognitionOutOfGenerosity decimals="INF" contextRef="ContextDuration" unitRef="EUR">200000</bd-i-ext1:GiftDebtRecognitionOutOfGenerosity>
    <bd-i-ext1:GiftExemptionANBIOrSBBI contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionANBIOrSBBI>
    <bd-i-ext1:GiftExemptionDebtor contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionDebtor>
    <bd-i-ext1:GiftExemptionExpensiveStudyIncreased contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionExpensiveStudyIncreased>
    <bd-i-ext1:GiftExemptionNaturalObligation contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionNaturalObligation>
    <bd-i-ext1:GiftExemptionOneOffIncreased contextRef="ContextDuration">true</bd-i-ext1:GiftExemptionOneOffIncreased>
    <bd-i-ext1:GiftExemptionOwnHouseOneOffIncreased contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionOwnHouseOneOffIncreased>
    <bd-i-ext1:GiftExtensionPartnerConceptIndication contextRef="ContextDuration">false</bd-i-ext1:GiftExtensionPartnerConceptIndication>
    <bd-i-ext1:GiftRecipientDonorRelationCode contextRef="ContextDuration">120</bd-i-ext1:GiftRecipientDonorRelationCode>
    <bd-i-ext1:GiftRevocableGiftIndication contextRef="ContextDuration">false</bd-i-ext1:GiftRevocableGiftIndication>
    <bd-i-ext1:GiftTaxPayerIndication contextRef="ContextDuration">false</bd-i-ext1:GiftTaxPayerIndication>
    <bd-i-ext1:NotaryConvenantCreationDate contextRef="ContextDuration">2017-03-01</bd-i-ext1:NotaryConvenantCreationDate>
    <bd-t-ext1:NotaryDomesticAddressPresentation>
      <bd-t:AddressPresentation>
        <bd-i:HouseNumberNL decimals="INF" contextRef="ContextDuration" unitRef="PURE">1</bd-i:HouseNumberNL>
        <bd-i:PlaceOfResidenceNL contextRef="ContextDuration">Volendam</bd-i:PlaceOfResidenceNL>
        <bd-i:PostalCodeNL contextRef="ContextDuration">7777DD</bd-i:PostalCodeNL>
        <bd-i:StreetNameNL contextRef="ContextDuration">Gouden Berg</bd-i:StreetNameNL>
      </bd-t:AddressPresentation>
    </bd-t-ext1:NotaryDomesticAddressPresentation>
    <bd-i-ext1:NotaryInitials contextRef="ContextDuration">PC</bd-i-ext1:NotaryInitials>
    <bd-i-ext1:NotaryNumber contextRef="ContextDuration">011119</bd-i-ext1:NotaryNumber>
    <bd-i-ext1:NotaryPrefix contextRef="ContextDuration">s23</bd-i-ext1:NotaryPrefix>
    <bd-i-ext1:NotarySurname contextRef="ContextDuration">Achterom [N]</bd-i-ext1:NotarySurname>
  </bd-t-ext1:GiftSpecification>
</xbrli:xbrl>
