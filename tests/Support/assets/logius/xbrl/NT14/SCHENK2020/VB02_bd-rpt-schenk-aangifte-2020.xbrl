<?xml version="1.0" encoding="UTF-8"?>
<!-- XBRL instance based on taxonomy report namespace http://www.nltaxonomie.nl/nt14/bd/20200219/entrypoints/bd-rpt-schenk-aangifte-2020.xsd
Intellectual Property State of the Netherlands
Created on: 20-12-2019 14:01:00
-->
<xbrli:xbrl xml:lang="nl" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:bd-t="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-tuples" xmlns:bd-i-ext1="http://www.nltaxonomie.nl/nt14/bd/20200219/dictionary/bd-data-ext1" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:bd-t-ext1="http://www.nltaxonomie.nl/nt14/bd/20200219/dictionary/bd-tuples-ext1" xmlns:bd-i="http://www.nltaxonomie.nl/nt14/bd/20191211/dictionary/bd-data" xmlns:nl-cd="http://www.nltaxonomie.nl/nt14/sbr/20190320/dictionary/nl-common-data" xmlns:xlink="http://www.w3.org/1999/xlink">
  <link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt14/bd/20200219/entrypoints/bd-rpt-schenk-aangifte-2020.xsd"/>
  <xbrli:context id="ContextDuration">
    <xbrli:entity>
      <xbrli:identifier scheme="www.belastingdienst.nl/identificatie">*********</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:startDate>2020-01-01</xbrli:startDate>
      <xbrli:endDate>2020-12-31</xbrli:endDate>
    </xbrli:period>
  </xbrli:context>
  <xbrli:unit id="EUR">
    <xbrli:measure>iso4217:EUR</xbrli:measure>
  </xbrli:unit>
  <bd-i-ext1:SignatureDate contextRef="ContextDuration">2020-02-02</bd-i-ext1:SignatureDate>
  <bd-i:SoftwarePackageName contextRef="ContextDuration">BD-KetenTest</bd-i:SoftwarePackageName>
  <bd-i:SoftwarePackageVersion contextRef="ContextDuration">1754604-S14</bd-i:SoftwarePackageVersion>
  <bd-i:SoftwareVendorAccountNumber contextRef="ContextDuration">********</bd-i:SoftwareVendorAccountNumber>
  <bd-i-ext1:ContactEmailAddressFull contextRef="ContextDuration"><EMAIL></bd-i-ext1:ContactEmailAddressFull>
  <bd-i-ext1:DonorDateOfBirth contextRef="ContextDuration">1950-09-12</bd-i-ext1:DonorDateOfBirth>
  <bd-i-ext1:DonorIdentificationNumber contextRef="ContextDuration">*********</bd-i-ext1:DonorIdentificationNumber>
  <bd-i-ext1:DonorInitials contextRef="ContextDuration">H</bd-i-ext1:DonorInitials>
  <bd-i-ext1:DonorIsCorrespondentIndication contextRef="ContextDuration">false</bd-i-ext1:DonorIsCorrespondentIndication>
  <bd-i-ext1:DonorIsDeclarantIndication contextRef="ContextDuration">false</bd-i-ext1:DonorIsDeclarantIndication>
  <bd-i-ext1:DonorPrefix contextRef="ContextDuration">s14s</bd-i-ext1:DonorPrefix>
  <bd-i-ext1:DonorSurname contextRef="ContextDuration">Jacobs</bd-i-ext1:DonorSurname>
  <bd-i-ext1:RecipientDateOfBirth contextRef="ContextDuration">1985-05-07</bd-i-ext1:RecipientDateOfBirth>
  <bd-i-ext1:RecipientInitials contextRef="ContextDuration">BE</bd-i-ext1:RecipientInitials>
  <bd-i-ext1:RecipientIsContactPersonIndication contextRef="ContextDuration">true</bd-i-ext1:RecipientIsContactPersonIndication>
  <bd-i-ext1:RecipientIsDeclarantIndication contextRef="ContextDuration">true</bd-i-ext1:RecipientIsDeclarantIndication>
  <bd-i-ext1:RecipientPrefix contextRef="ContextDuration">s14o</bd-i-ext1:RecipientPrefix>
  <bd-i-ext1:RecipientSurname contextRef="ContextDuration">Jacobs</bd-i-ext1:RecipientSurname>
  <bd-t-ext1:GiftSpecification>
    <bd-i-ext1:GiftCashMoneyBalances decimals="INF" contextRef="ContextDuration" unitRef="EUR">50000</bd-i-ext1:GiftCashMoneyBalances>
    <bd-i-ext1:GiftDate contextRef="ContextDuration">2020-03-24</bd-i-ext1:GiftDate>
    <bd-i-ext1:GiftExemptionANBIOrSBBI contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionANBIOrSBBI>
    <bd-i-ext1:GiftExemptionDebtor contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionDebtor>
    <bd-i-ext1:GiftExemptionExpensiveStudyIncreased contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionExpensiveStudyIncreased>
    <bd-i-ext1:GiftExemptionNaturalObligation contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionNaturalObligation>
    <bd-i-ext1:GiftExemptionOneOffIncreased contextRef="ContextDuration">true</bd-i-ext1:GiftExemptionOneOffIncreased>
    <bd-i-ext1:GiftExemptionOwnHouseOneOffIncreased contextRef="ContextDuration">false</bd-i-ext1:GiftExemptionOwnHouseOneOffIncreased>
    <bd-i-ext1:GiftExtensionPartnerConceptIndication contextRef="ContextDuration">false</bd-i-ext1:GiftExtensionPartnerConceptIndication>
    <bd-i-ext1:GiftRecipientDonorRelationCode contextRef="ContextDuration">120</bd-i-ext1:GiftRecipientDonorRelationCode>
    <bd-i-ext1:GiftRevocableGiftIndication contextRef="ContextDuration">false</bd-i-ext1:GiftRevocableGiftIndication>
    <bd-i-ext1:GiftTaxPayerIndication contextRef="ContextDuration">false</bd-i-ext1:GiftTaxPayerIndication>
  </bd-t-ext1:GiftSpecification>
</xbrli:xbrl>
