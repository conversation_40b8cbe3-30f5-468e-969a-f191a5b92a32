<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<xbrli:xbrl xml:lang="nl" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:xbrldi="http://xbrl.org/2006/xbrldi" xmlns:iso4217="http://www.xbrl.org/2003/iso4217" xmlns:nl-cd="http://www.nltaxonomie.nl/nt13/sbr/20180301/dictionary/nl-common-data" xmlns:rj-t="http://www.nltaxonomie.nl/nt13/rj/20181212/dictionary/rj-tuples" xmlns:jenv-bw2-i="http://www.nltaxonomie.nl/nt13/jenv/20181212/dictionary/jenv-bw2-data" xmlns:jenv-bw2-dim="http://www.nltaxonomie.nl/nt13/jenv/20181212/dictionary/jenv-bw2-axes" xmlns:jenv-bw2-dm="http://www.nltaxonomie.nl/nt13/jenv/20181212/dictionary/jenv-bw2-domains" xmlns:kvk-i="http://www.nltaxonomie.nl/nt13/kvk/20181212/dictionary/kvk-data" xmlns:rj-i="http://www.nltaxonomie.nl/nt13/rj/20181212/dictionary/rj-data">
  <link:schemaRef xlink:type="simple" xlink:href="http://www.nltaxonomie.nl/nt13/kvk/20181212/entrypoints/kvk-rpt-jaarverantwoording-2018-nlgaap-micro-publicatiestukken.xsd" />
  <xbrli:context id="FY18d">
    <xbrli:entity>
      <xbrli:identifier scheme="http://www.kvk.nl/kvk-id">1234567</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:startDate>2018-01-01</xbrli:startDate>
      <xbrli:endDate>2018-12-31</xbrli:endDate>
    </xbrli:period>
  </xbrli:context>
  <xbrli:context id="FY18d_Commercial_Separate">
    <xbrli:entity>
      <xbrli:identifier scheme="http://www.kvk.nl/kvk-id">1234567</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:startDate>2018-01-01</xbrli:startDate>
      <xbrli:endDate>2018-12-31</xbrli:endDate>
    </xbrli:period>
    <xbrli:scenario>
      <xbrldi:explicitMember dimension="jenv-bw2-dim:BasisOfPreparationAxis">jenv-bw2-dm:CommercialMember</xbrldi:explicitMember>
      <xbrldi:explicitMember dimension="jenv-bw2-dim:FinancialStatementsTypeAxis">jenv-bw2-dm:SeparateMember</xbrldi:explicitMember>
    </xbrli:scenario>
  </xbrli:context>
  <xbrli:context id="FY18i_Commercial_Separate">
    <xbrli:entity>
      <xbrli:identifier scheme="http://www.kvk.nl/kvk-id">1234567</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:instant>2018-12-31</xbrli:instant>
    </xbrli:period>
    <xbrli:scenario>
      <xbrldi:explicitMember dimension="jenv-bw2-dim:BasisOfPreparationAxis">jenv-bw2-dm:CommercialMember</xbrldi:explicitMember>
      <xbrldi:explicitMember dimension="jenv-bw2-dim:FinancialStatementsTypeAxis">jenv-bw2-dm:SeparateMember</xbrldi:explicitMember>
    </xbrli:scenario>
  </xbrli:context>
  <xbrli:context id="FY17i_Commercial_Separate">
    <xbrli:entity>
      <xbrli:identifier scheme="http://www.kvk.nl/kvk-id">1234567</xbrli:identifier>
    </xbrli:entity>
    <xbrli:period>
      <xbrli:instant>2017-12-31</xbrli:instant>
    </xbrli:period>
    <xbrli:scenario>
      <xbrldi:explicitMember dimension="jenv-bw2-dim:BasisOfPreparationAxis">jenv-bw2-dm:CommercialMember</xbrldi:explicitMember>
      <xbrldi:explicitMember dimension="jenv-bw2-dim:FinancialStatementsTypeAxis">jenv-bw2-dm:SeparateMember</xbrldi:explicitMember>
    </xbrli:scenario>
  </xbrli:context>
  <xbrli:unit id="EUR">
    <xbrli:measure>iso4217:EUR</xbrli:measure>
  </xbrli:unit>
  <jenv-bw2-i:LegalEntityName contextRef="FY18d">Example Company</jenv-bw2-i:LegalEntityName>
  <kvk-i:TitleOfTheDocument contextRef="FY18d">Jaarwerk 2018</kvk-i:TitleOfTheDocument>
  <jenv-bw2-i:Equity contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">-1234</jenv-bw2-i:Equity>
  <jenv-bw2-i:Equity contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">-3456</jenv-bw2-i:Equity>
  <jenv-bw2-i:BalanceSheetBeforeAfterAppropriationResults contextRef="FY18d_Commercial_Separate">Na</jenv-bw2-i:BalanceSheetBeforeAfterAppropriationResults>
  <jenv-bw2-i:LegalEntityLegalForm contextRef="FY18d">Besloten vennootschap met beperkte aansprakelijkheid</jenv-bw2-i:LegalEntityLegalForm>
  <jenv-bw2-i:FinancialReportingPeriodCurrentStartDate contextRef="FY18d">2018-01-01</jenv-bw2-i:FinancialReportingPeriodCurrentStartDate>
  <jenv-bw2-i:AssetsNoncurrentOther contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">23456</jenv-bw2-i:AssetsNoncurrentOther>
  <jenv-bw2-i:AssetsNoncurrentOther contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">12345</jenv-bw2-i:AssetsNoncurrentOther>
  <jenv-bw2-i:AssetsCurrentOther contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">1234</jenv-bw2-i:AssetsCurrentOther>
  <jenv-bw2-i:AssetsCurrentOther contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">12345</jenv-bw2-i:AssetsCurrentOther>
  <jenv-bw2-i:LiabilitiesDisclosure contextRef="FY18d_Commercial_Separate">De schulden zijn onderverdeeld in langlopende schulden ten bedrage van € ... en kortlopende schulden ten bedrage van € ...</jenv-bw2-i:LiabilitiesDisclosure>
  <jenv-bw2-i:LegalEntityRegisteredOffice contextRef="FY18d">Sweet Lake City</jenv-bw2-i:LegalEntityRegisteredOffice>
  <jenv-bw2-i:FinancialReportingPeriodCurrentEndDate contextRef="FY18d">2018-12-31</jenv-bw2-i:FinancialReportingPeriodCurrentEndDate>
  <jenv-bw2-i:Liabilities contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">98765</jenv-bw2-i:Liabilities>
  <jenv-bw2-i:Liabilities contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">45678</jenv-bw2-i:Liabilities>
  <jenv-bw2-i:AssetsNoncurrent contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">34122</jenv-bw2-i:AssetsNoncurrent>
  <jenv-bw2-i:AssetsNoncurrent contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">1231</jenv-bw2-i:AssetsNoncurrent>
  <jenv-bw2-i:AssetsCurrent contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">1231</jenv-bw2-i:AssetsCurrent>
  <jenv-bw2-i:AssetsCurrent contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">3211</jenv-bw2-i:AssetsCurrent>
  <jenv-bw2-i:ChamberOfCommerceRegistrationNumber contextRef="FY18d">34522</jenv-bw2-i:ChamberOfCommerceRegistrationNumber>
  <jenv-bw2-i:FinancialReportingPeriodPreviousStartDate contextRef="FY18d">2017-01-01</jenv-bw2-i:FinancialReportingPeriodPreviousStartDate>
  <jenv-bw2-i:EquityAndLiabilities contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">42322</jenv-bw2-i:EquityAndLiabilities>
  <jenv-bw2-i:EquityAndLiabilities contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">12212</jenv-bw2-i:EquityAndLiabilities>
  <jenv-bw2-i:Assets contextRef="FY18i_Commercial_Separate" unitRef="EUR" decimals="INF">12212</jenv-bw2-i:Assets>
  <jenv-bw2-i:Assets contextRef="FY17i_Commercial_Separate" unitRef="EUR" decimals="INF">33333</jenv-bw2-i:Assets>
  <kvk-i:BusinessNames contextRef="FY18d">Example Company</kvk-i:BusinessNames>
  <jenv-bw2-i:FinancialReportingPeriodPreviousEndDate contextRef="FY18d">2017-12-31</jenv-bw2-i:FinancialReportingPeriodPreviousEndDate>
  <kvk-i:LegalSizeCriteriaClassification contextRef="FY18d">Micro</kvk-i:LegalSizeCriteriaClassification>
  <rj-i:FinancialReportingPeriodDifferentThanAnnualStatus contextRef="FY18d">Nee</rj-i:FinancialReportingPeriodDifferentThanAnnualStatus>
  <kvk-i:SbiBusinessCode contextRef="FY18d">71111</kvk-i:SbiBusinessCode>
  <rj-t:EntityAddressPresentation>
    <nl-cd:StreetNameNL contextRef="FY18d">Main street</nl-cd:StreetNameNL>
    <nl-cd:HouseNumberNL contextRef="FY18d">1</nl-cd:HouseNumberNL>
    <nl-cd:PostalCodeNL contextRef="FY18d">1000 AB</nl-cd:PostalCodeNL>
    <nl-cd:PlaceOfResidenceNL contextRef="FY18d">Sweet Lake City</nl-cd:PlaceOfResidenceNL>
  </rj-t:EntityAddressPresentation>
  <jenv-bw2-i:BasisOfPreparation contextRef="FY18d">Commercieel</jenv-bw2-i:BasisOfPreparation>
  <rj-i:DocumentPresentationCurrency contextRef="FY18d">EUR</rj-i:DocumentPresentationCurrency>
  <jenv-bw2-i:DocumentAdoptionStatus contextRef="FY18d">Nee</jenv-bw2-i:DocumentAdoptionStatus>
  <kvk-i:DocumentResubmissionDueToUnsurmountableInaccuracies contextRef="FY18d">Nee</kvk-i:DocumentResubmissionDueToUnsurmountableInaccuracies>
  <kvk-i:EmailAddressContact contextRef="FY18d"><EMAIL></kvk-i:EmailAddressContact>
</xbrli:xbrl>