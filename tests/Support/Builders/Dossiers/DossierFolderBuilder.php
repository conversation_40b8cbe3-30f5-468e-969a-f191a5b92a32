<?php

namespace Tests\Support\Builders\Dossiers;

use App\Account;
use App\Company;
use App\Models\Dossiers\DossierFolder;

class DossierFolderBuilder
{
    private Company $company;
    private Account $account;
    private ?DossierFolder $parent = null;
    private string $name = 'Folder';
    private int $sortIndex = 0;
    private bool $locked = false;

    public static function new(): self
    {
        return new self();
    }

    public function setAccount(Account $account): self
    {
        $this->account = $account;
        return $this;
    }

    public function setCompany(Company $company): self
    {
        $this->company = $company;
        return $this;
    }

    public function setParent(DossierFolder $parent): self
    {
        $this->parent = $parent;
        return $this;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function setSortIndex(int $sortIndex): self
    {
        $this->sortIndex = $sortIndex;
        return $this;
    }

    public function build(): DossierFolder
    {
        return DossierFolder::factory()->create(
            [
                DossierFolder::ACCOUNT_ID => $this->company->account_id,
                DossierFolder::COMPANY_ID => $this->company->id,
                DossierFolder::PARENT_ID => $this->parent?->id,
                DossierFolder::NAME => $this->name,
                DossierFolder::SORT_INDEX => $this->sortIndex,
                DossierFolder::LOCKED => $this->locked,
            ]
        );
    }

    public function setLocked(bool $value): self
    {
        $this->locked = $value;
        return $this;
    }
}