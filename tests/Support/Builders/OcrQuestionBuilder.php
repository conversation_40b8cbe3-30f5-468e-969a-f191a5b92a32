<?php

namespace Tests\Support\Builders;

use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Models\OpenQuestions\Questions\OpticalCharacterRecognition;

class OcrQuestionBuilder
{
    private ?OpenQuestion $openQuestion = null;
    private string $public_url = 'https://www.securelogin.nu/';
    private string $created_on_date = '2021-06-16';
    private string $transactionDate = '2024-01-17';

    public static function new(): self
    {
        return new self();
    }

    public function setOpenQuestion(OpenQuestion $openQuestion): self
    {
        $this->openQuestion = $openQuestion;
        return $this;
    }

    public function setPublicUrl(string $url): self
    {
        $this->public_url = $url;
        return $this;
    }

    public function setCreatedOnDate(string $date): self
    {
        $this->created_on_date = $date;
        return $this;
    }

    public function setAttachmentNeeded(bool $attachmentNeeded): self
    {
        $this->attachment_needed = $attachmentNeeded;
        return $this;
    }

    public function build(): OpticalCharacterRecognition
    {
        return OpticalCharacterRecognition::factory()->create([
           'open_question_id' => $this->openQuestion->id,
           'public_url' => $this->public_url,
           'created_on_date' => $this->created_on_date,
           'transaction_date' => $this->transactionDate,
           'invoice_number' => 'NL831283123182',
           'description' => 'hey there',
        ]);
    }
}
