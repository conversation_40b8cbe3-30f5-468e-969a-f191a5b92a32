<?php

namespace Tests\Support\Builders\SharedFiles;

use App\Account;
use App\Models\SharedFiles\SharedFileBundle;
use App\User;
use App\Support\Carbon;

class SharedFileBundleBuilder
{
    private Account $account;
    private User $createdBy;
    private ?Carbon $expireAt = null;
    private ?Carbon $createdAt = null;

    public static function new(): self
    {
        return new self();
    }

    public function setAccount(Account $account): self
    {
        $this->account = $account;
        return $this;
    }

    public function setCreatedBy(User $createdBy): self
    {
        $this->createdBy = $createdBy;
        return $this;
    }

    public function setExpireAt(Carbon $expireAt): self
    {
        $this->expireAt = $expireAt;
        return $this;
    }

    public function setCreatedAt(Carbon $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function build(): SharedFileBundle
    {
        $attributes = [
            SharedFileBundle::ACCOUNT_ID => $this->account->id,
            SharedFileBundle::CREATED_BY => $this->createdBy->id,
        ];
        if ($this->expireAt !== null) {
            $attributes[SharedFileBundle::EXPIRE_AT] = $this->expireAt;
        }
        if ($this->createdAt !== null) {
            $attributes[SharedFileBundle::CREATED_AT] = $this->createdAt;
        }
        return SharedFileBundle::factory()->create($attributes);
    }
}
