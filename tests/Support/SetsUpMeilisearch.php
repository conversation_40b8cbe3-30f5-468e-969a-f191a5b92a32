<?php

namespace Tests\Support;

use App\Repositories\Meilisearch\MeilisearchRepository;
use Illuminate\Support\Facades\Artisan;

trait SetsUpMeilisearch
{
    protected function setUpMeilisearch(): void
    {
        \Config::set('scout.driver', 'meilisearch');
        if (!config('scout.meilisearch.admin_key') || !config('scout.meilisearch.admin_key_uid')) {
            Artisan::call('scout:sync-index-settings');

            $keys = resolve(MeiliSearchRepository::class)->getAdminKeys();

            \Config::set('scout.meilisearch.admin_key', $keys['key']);
            \Config::set('scout.meilisearch.admin_key_uid', $keys['uid']);

            $this->waitForMeilisearchTasksToFinish();
        }
    }

    protected function tearDownMeilisearch(): void
    {
        \Config::set('scout.driver');
    }

    /**
     * Waits for all pending Meilisearch tasks to complete before continuing.
     * This ensures indexing and other async operations are finished,
     * so search results are up to date.
     */

    protected function waitForMeilisearchTasksToFinish(): void
    {
        $timeout = now()->addSeconds(15);

        do {
            sleep(1);

            $tasks = resolve(MeiliSearchRepository::class)->getTasks();

            $hasPending = collect($tasks)->contains(function ($task) {
                return in_array($task['status'], ['enqueued', 'processing']);
            });

            if (!$hasPending) {
                return; // All done
            }
        } while (now()->lt($timeout));
    }
}
