<?php

namespace Tests\Support\Factories\OpenQuestions;

use App\AccountService;
use App\Company;
use App\Models\OpenQuestions\OpenQuestionCategory;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Support\Carbon;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;

class OpenQuestionFactory
{
    public static function create(
        Company $company = null,
        AccountService $accountService = null,
        ?string $type = OpenQuestion::TYPE_PRIVATE_OR_BUSINESS,
        string $category = OpenQuestionCategory::BOOKKEEPING,
        Carbon $lastSent = null,
        Carbon $createdAt = null,
        ?string $relationType = null
    ): OpenQuestion {
        if ($company === null) {
            $company = CompanyFactory::create();
        }

        if ($accountService === null) {
            $accountService = AccountServiceFactory::createTwinfieldOpenQuestions($company->account);
        }

        $preferences = [];

        // Force some fields depending on the type of question.
        switch ($type) {
            case OpenQuestion::TYPE_MISSING_INVOICE:
                $preferences['file_upload_required'] = true;
                break;
            case OpenQuestion::TYPE_PRIVATE_OR_BUSINESS:
                $preferences['file_upload_required'] = false;
                break;
            default:
                $preferences['file_upload_required'] = true;
        }

        $openQuestionType = null;
        if ($type) {
            $openQuestionType = OpenQuestionTypeFactory::create($accountService, $type, $type, $preferences);
        }

        $question = \App\Factories\Models\OpenQuestionFactory::create(
            $accountService,
            $company,
            'Title',
            'Subtitle',
            $openQuestionType?->id,
            $category,
            $preferences['file_upload_required']
        );

        $question->last_sent = $lastSent;
        if (isset($createdAt)) {
            $question->created_at = $createdAt;
        }
        $question->save();

        return $question;
    }
}
