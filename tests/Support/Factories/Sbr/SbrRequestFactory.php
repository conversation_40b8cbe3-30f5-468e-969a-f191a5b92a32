<?php

namespace Tests\Support\Factories\Sbr;

use App\Account;
use App\Models\Sbr\SbrRequest;
use App\ServiceTask;

class SbrRequestFactory
{
    public static function create(
        Account $account,
        ServiceTask $serviceTask = null,
        string $status = 'new',
        ?string $responseCode = null
    ): SbrRequest {
        return SbrRequest::factory()->create(
            [
                SbrRequest::ACCOUNT_ID => $account->id,
                SbrRequest::SERVICE_TASK_ID => $serviceTask?->id,
                SbrRequest::STATUS => $status,
                SbrRequest::RESPONSE_CODE => $responseCode,
                SbrRequest::ROUND => 0,
            ]
        );
    }
}
