<?php

namespace Tests\Support\Factories;

use App\Account;
use App\Consumer;
use App\Models\ConsumerIssuer;
use App\Repositories\ConsumerIssuerRepository;
use App\Repositories\ConsumerRepository;

class ConsumersIssuersFactory
{
    public static function createForAzureSso(
        string $issuer,
        Account $account = null
    ): ConsumerIssuer {
        if ($account === null) {
            $account = AccountFactory::create();
        }

        $consumer = resolve(ConsumerRepository::class)->getByAccountAndAuthId(
            Account::getAdminAccount(),
            config('services.azure.multi_tenant_sso.auth_id')
        );

        return ConsumerIssuer::factory()->create(
            [
                'consumer_id' => $consumer->id,
                'account_id' => $account->id,
                'issuer' => $issuer,
            ]
        );
    }
}
