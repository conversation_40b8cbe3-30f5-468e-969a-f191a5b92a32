<?php

namespace Tests\Support\Factories\Template;

use App\Account;
use App\AccountService;
use App\Company;
use App\Models\OpenQuestions\OpenQuestionCategory;
use App\Models\OpenQuestions\Questions\OpenQuestion;
use App\Models\OpenQuestions\Questions\Templates\Template;
use App\Models\OpenQuestions\Questions\Templates\TemplateEntry;
use Tests\Support\Factories\AccountFactory;
use Tests\Support\Factories\AccountServiceFactory;
use Tests\Support\Factories\CompanyFactory;
use Tests\Support\Factories\OpenQuestions\OpenQuestionFactory;

class TemplateEntryFactory
{
    public static function create(
        Account $account = null,
        Template $template = null,
        Company $company = null,
        AccountService $accountService = null,
        string $category = OpenQuestionCategory::BOOKKEEPING
    ): TemplateEntry {
        if ($account === null) {
            $account = AccountFactory::create();
        }

        if ($template === null) {
            $template = TemplateFactory::create($account);
        }

        if ($company === null) {
            $company = CompanyFactory::create($account);
        }

        if ($accountService === null) {
            $accountService = AccountServiceFactory::createManualQuestions($account);
        }

        $openQuestion = OpenQuestionFactory::create(
            $company,
            $accountService,
            OpenQuestion::TYPE_TEMPLATE,
            $category,
            relationType: 'template'
        );

        $templateEntry = TemplateEntry::factory()->create([
            'template_id' => $template->id,
            'open_question_id' => $openQuestion->id
        ]);

        TemplateAnswerFactory::create($templateEntry);

        return $templateEntry;
    }
}
