<?php

namespace Tests\Support\Factories;

use App\Company;
use App\Models\ServiceTask\YearworkApprovalTask;
use App\Models\TaskFile;
use App\ServiceTask;
use App\ServiceTaskSigningUser;
use App\User;

class YearWorkTaskFactory
{
    public static function create()
    {
        /** @var ServiceTask $serviceTask */
        $serviceTask = YearworkApprovalTask::factory()->create([
            'properties' => json_encode(['company_size' => Company::MEDIUM]),
        ]);

        return $serviceTask;
    }

    public static function createForPkiSigning()
    {
        $serviceTask = self::create();

        // Add Pki consumer for the service task account.
        ConsumerFactory::createPkiSigningConsumerAndToken($serviceTask->account);

        return $serviceTask;
    }
}
