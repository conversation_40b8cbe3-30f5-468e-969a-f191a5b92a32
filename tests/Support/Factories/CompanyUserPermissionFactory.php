<?php

namespace Tests\Support\Factories;

use App\CompanyIdentifier;
use App\CompanyUser;
use App\Models\CompanyUserPermission;
use App\ServiceTaskResponse;

class CompanyUserPermissionFactory
{
    public static function create(
        CompanyIdentifier $companyIdentifier,
        CompanyUser $companyUser,
        string $permission = ServiceTaskResponse::PERMISSION_APPROVE
    ): CompanyUserPermission {
        return CompanyUserPermission::factory()->create([
            'account_id' => $companyIdentifier->account_id,
            'company_id' => $companyIdentifier->company_id,
            'identifier_id' => $companyIdentifier->id,
            'company_user_id' => $companyUser->id,
            'permission' => $permission,
        ]);
    }
}
