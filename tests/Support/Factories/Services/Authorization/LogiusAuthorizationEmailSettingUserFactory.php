<?php

namespace Tests\Support\Factories\Services\Authorization;

use App\CompanyIdentifier;
use App\CompanyUser;
use App\Models\Authorizations\LogiusAuthorizationEmailSettingUser;

class LogiusAuthorizationEmailSettingUserFactory
{
    public static function create(
        CompanyUser $companyUser,
        CompanyIdentifier $companyIdentifier
    ): LogiusAuthorizationEmailSettingUser {
        return LogiusAuthorizationEmailSettingUser::factory()->create(
            [
                LogiusAuthorizationEmailSettingUser::COMPANY_USER_ID => $companyUser->id,
                LogiusAuthorizationEmailSettingUser::IDENTIFIER_ID => $companyIdentifier->id,
                LogiusAuthorizationEmailSettingUser::CREATED_BY => $companyUser->user_id,
            ]
        );
    }
}