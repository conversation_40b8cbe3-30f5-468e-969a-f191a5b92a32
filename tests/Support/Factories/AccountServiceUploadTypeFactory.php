<?php

namespace Tests\Support\Factories;

use App\AccountService;
use App\AccountServiceUploadType;

class AccountServiceUploadTypeFactory
{
    public static function create(string $title, AccountService $accountService = null): AccountServiceUploadType
    {
        if ($accountService === null) {
            $accountService = AccountServiceFactory::create();
        }

        return AccountServiceUploadType::factory()->create([
            'account_id' => $accountService->account->id,
            'account_service_id' => $accountService->id,
            'title' => $title
        ]);
    }
}
