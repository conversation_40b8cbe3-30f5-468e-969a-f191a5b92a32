body {
  font-family: "Proxima Nova", sans-serif;
  font-weight: 400;
  font-style: normal;
  padding: 0;
  margin: 0;
  background-color: white;
}

header {
  padding: 20px;
}

hr {
  margin: 0;
  border: 0;
  height: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

input:focus,
select:focus,
textarea:focus,
button:focus {
  outline: none;
}

.container {
  display: none;
  width: 330px;
}

.login-bar {
  display: none;
  width: 330px;
  text-align: center;
}

.login-bar img {
  width: 128px;
  display: block;
  margin: auto;
}


.login-bar b {
  line-height: 20px;
  color: gray;
}

.search-bar {
  padding: 20px;
  position: relative;
  display: flex;
  min-width: 100px;
  background-color: #f8f8f8;
}

.search {
  border: 1px solid darkgray;
  border-radius: 8px;
  height: 40px;
  width: 100%;
  display: inline-block;
  padding: 2px 23px 2px 30px;
  outline: 0;
  background-color: white;
}

.search-icon {
  position: absolute;
  top: 33px;
  left: 30px;
  width: 14px;
}

.result {
  max-height: 400px;
  overflow-y: scroll;
  display: grid;
  grid-template-columns: repeat(1, 1fr);
}

.widget {
  cursor: pointer;
  display: grid;
  padding: 15px;
  grid-template-columns: 1fr 4fr;
  border-bottom: solid #d3d3d3;
  border-width: thin;
}

.widget-pressed {
  background-color: #0a7dc4;
}

.widget:hover {
  background-color: rgba(15, 155, 243, 0.05);
}

.widget img {
  object-fit: scale-down;
  border-radius: 4px;
  width: 30px;
  height: 30px;
  background-color: white;
}

.title {
  font-size: 15px;
  margin: 0;
  padding: 0;
}

.description {
  margin-top: 4px;
  font-size: 11px;
  color: #999999;
}

#result-empty {
  margin: 13px;
}

.title,
.description {
  white-space: nowrap;
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recording-container {
    padding: 20px;
    position: relative;
    display: flex;
    min-width: 100px;
    background-color: #f8f8f8;
}

.record-button {
    min-width: 120px;
    padding: 1em;
    border-radius: 4px;
    cursor: pointer;
    background-color: #0AAAEE;
    color: white;
    border: none;
}

button#stop-recording {
    display: none;
}