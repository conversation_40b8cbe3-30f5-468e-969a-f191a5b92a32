import browser from 'webextension-polyfill';
import './styles/style.css'
import {BrowserExtensionApi} from "../scripts/hix/BrowserExtensionApi";
import {IconHelper} from "../scripts/helpers/IconHelper";

class IndexWidget {
    async init() {
        this.searchList = [];
        this.foundNoResult = false;
        this.fetchAuthenticatedData();
    }

    fetchAuthenticatedData() {
        browser.storage.local.get('host/hostname/domain').then(async (result) => {
            if (result && result['host/hostname/domain']) {
                let domain = result['host/hostname/domain'];
                let userInfoPromise = (new BrowserExtensionApi()).getUserInfo();
                userInfoPromise.then((userInfo) => {
                    console.debug(userInfo);
                    browser.storage.local.set({'user_info': userInfo});
                    if (userInfo && userInfo.hasOwnProperty('auth_id')) {
                        this.showWidgets(domain);
                    } else {
                        this.showLoginbar();
                    }
                });
            } else {
                this.showLoginbar();
            }
        });
    }

    showLoginbar() {
        document.querySelector('.loading-container').style.display = "none";
        document.querySelector('.login-bar').style.display = "block"
        IconHelper.setInactive();
    }

    async showWidgets(domain) {
        await this.getWidgets(domain);
        document.querySelector('.container').style.display = "block";
        document.querySelector('.loading-container').style.display = "none"
        this.display();
        this.eventListeners();
        document.querySelector('.search').focus();
    }

    eventListeners() {
        document.querySelector('.search').addEventListener('keyup', () => {
            this.search(document.querySelector('.search').value);
        });

        document.querySelector('.search').addEventListener('search', () => {
            this.search(document.querySelector('.search').value);
        });

        window.addEventListener('keyup', (e) => {
            if (e.key === "Enter") {
                const widgetElements = document.querySelectorAll('.widget');
                if (widgetElements.length > 0) {
                    widgetElements[0].click();
                }
            }
        });
    }

    async getWidgets(domain) {
        const response = await fetch(`https://${domain}/api/v1/user/widget?per_page=2000`);
        this.widgets = (await response.json()).data
    }

    search(text) {
        this.foundNoResult = false;

        this.searchList = this.widgets.filter(widget => {
            const label = widget.label.toLowerCase();
            return label.includes(text.toLowerCase());
        })

        if (this.searchList.length === 0) this.foundNoResult = true;

        this.display()
    }

    display() {
        let widgetContainer = document.querySelector('.result');

        if (!this.foundNoResult) {
            widgetContainer.innerHTML = "";
            let widgetList = this.searchList.length > 0 ? this.searchList : this.widgets;

            for (let i = 0; i < widgetList.length; i++) {
                let widget = widgetList[i];
                let widgetComponent = `
        <div data-href="${widget.start_url}" class="widget">
          <img src="${widget.image}" alt="${widget.label}">
          <div class="text">
            <h2 class="title" title="${widget.label}">${widget.label}</h2>
            <div class="description" title="${widget.description}">${widget.description}</div>
          </div>
        </div>
      `;

                widgetContainer.insertAdjacentHTML('beforeend', widgetComponent);
            }

            document.querySelectorAll('.widget').forEach(widget => {
                widget.addEventListener('click', () => {
                    widget.classList.add('widget-pressed');
                    this.redirectTo(widget.dataset.href);
                });
            });
        } else widgetContainer.innerHTML = `<p id="result-empty">Geen resultaten gevonden</p>`;

    }

    redirectTo(url) {
        browser.tabs.create({url: url})
    }
}

export {
    IndexWidget
}