import {sendToBExtension} from "../scripts/custom_sso/inject/CommunicationService";
import {RecordButton} from "../scripts/custom_sso/inject/RecordButton/RecordButton";

class CustomSSO {
    constructor() {
        sendToBExtension('is_recording').then((isRecording) => {
            document.querySelector('.recording-container').appendChild(new RecordButton(isRecording).injectableElement);
        });
    }
}

export default new CustomSSO();