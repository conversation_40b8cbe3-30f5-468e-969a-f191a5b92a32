import browser from 'webextension-polyfill';
import hixExtension from './scripts/invisiblehand/HixExtension';
import taxModule from './scripts/tax/background/TaxModule';
// This import needs to stay in order for custom widget creation to work.
import CustomSsoModule from "./scripts/custom_sso/background/CustomSsoModule";
import {LogHelper} from "./scripts/helpers/LogHelper";

browser.HixExtension = hixExtension;
browser.TaxExtension = taxModule;

LogHelper.initListener();

if (!navigator.userAgent.includes("Firefox")) {
    browser.alarms.create("wakeUp", { periodInMinutes: 0.5 });

    browser.alarms.onAlarm.addListener((alarm) => {
        if (alarm.name === "wakeUp") {
            console.log("Service worker is active again");
        }
    });
}

/**
 * This file is always loaded when the extension is active
 */