import browser from 'webextension-polyfill';
import helper from '../helper';
import KnownHosts from '../knownHosts';
import SSOInvisibleHand from './sso_invisiblehand';
import SSOWebRequestHandler from '../sso_webrequest_handler';
import OpenQuestionsModule from "../open_questions/background/OpenQuestionsModule";
import TranslationModule from "../open_questions/background/TranslationModule";
import {OpenQuestionsApi} from "../open_questions/api/OpenQuestionsApi";
import {IconHelper} from "../helpers/IconHelper";
import {LogHelper} from "../helpers/LogHelper";
import BelastingtoolModule from "../belastingtool/background/BelastingtoolModule";
import {BrowserExtensionApi} from "../hix/BrowserExtensionApi";

const HEADER_EXTENSION_VERSION = 'X-SecureLogin-Ext';
const HEADER_EXTENSION_INVISIBLE_HAND = 'X-SL-Invisiblehand';
const HEADER_EXTENSION_USER_AUTHENTICATED = 'New-X-SL-User-Authenticated'; // To ensure retro compatibility
const HEADER_SETTINGS_TIMESTAMP = 'x-sl-settings-last-modified';
const HEADER_EXTENSION_PRIVATE_WINDOW = 'X-SL-Private-Window';
const HEADER_EXTENSION_REQUEST = 'X-SecureLogin-Ext-Request';

class HixExtension {

    constructor() {
        console.log('HixExtension loading...');
        this.version = browser.runtime.getManifest().version;

        // for valid referer matching, see https://developer.mozilla.org/en-US/Add-ons/WebExtensions/Match_patterns
        this.targetUrls = ['https://*/*']; // listen to all HTTPS requests
        this.SSOWebRequestHandlersByTabId = new Map(); // Map with all handlers by tabId
        this.knownHosts = new KnownHosts();

        this.addHeadersReceivedListener(); // handle incoming Invisible Hand and Extension Request headers
        this.addInitialSSORequestListener(); //initial request is SSO request like /access/123
        if (navigator.userAgent.includes("Firefox")) {
            this.addOutboundRequestsListener(); // add Extension version if needed
        }

        console.log('HixExtension ' + this.version + ' loaded.');
        this.checkUserAndReloadModules();
    }

    /**
     * Deactivate modules if user is not logged in
     */
    deactivateModules() {
        browser.openQuestionsApi = null;
        browser.OpenQuestionsModule = null;
        browser.belastingtoolModule = null;
    }

    checkUserAndReloadModules() {
        //Start service when user quit the browser or extension is reloaded.
        browser.storage.local.get('host/hostname/domain').then((result) => {
            if (result && result['host/hostname/domain']) {
                console.debug('HixExtension found domain in storage', result);
                let userInfoPromise = (new BrowserExtensionApi()).getUserInfo();
                userInfoPromise.then((userInfo) => {
                    console.debug(userInfo);
                    browser.storage.local.set({'user_info': userInfo});
                    if (userInfo && userInfo.hasOwnProperty('auth_id')) {
                        this.initModules(userInfo);
                        IconHelper.setActive();
                        LogHelper.info('HixExtension activated extension icon');
                    } else {
                        // Was not able to get the current user info, meaning the user is logged out.
                        // This means that the listeners for the current session should be removed too.
                        this.removePreviousUserInfo();
                    }
                });
            }
        });
    }

    /**
     * Listen for incoming headers
     */
    addHeadersReceivedListener() {
        browser.webRequest.onHeadersReceived.addListener((details) => {
                return helper.tryCatchPromise(async () => {
                    LogHelper.debug('HixExtension onHeadersReceived event');
                    LogHelper.debug(JSON.stringify(details));

                    return this.handleHeadersReceivedEvent(details);
                });
            },
            {
                urls: this.targetUrls,
                types: ['main_frame', 'xmlhttprequest', 'sub_frame']
            },
            ['responseHeaders']
        );
    }

    async handleHeadersReceivedEvent(details) {
        let promiseChain = [];

        LogHelper.debug('HixExtension received headers ', details);

        let userInfo = helper.headerGet(details.responseHeaders, HEADER_EXTENSION_USER_AUTHENTICATED);
        if (userInfo) {
            this.handleUserInfoReceived(JSON.parse(userInfo));
            return;
        }

        let timestamp = helper.headerGet(details.responseHeaders, HEADER_SETTINGS_TIMESTAMP);
        if (timestamp) {
            await this.handleSettingsTimestampReceived(timestamp);
            return;
        }

        let instructionData = helper.headerGet(details.responseHeaders, HEADER_EXTENSION_INVISIBLE_HAND);
        if (instructionData) {
            LogHelper.debug("HixExtension onHeadersReceived::invisibleHandHeaderReceived: " + instructionData);
            if (!this.hand) {
                this.hand = new SSOInvisibleHand(details.tabId);
            }
            promiseChain.push(this.hand.setData(instructionData));
        }

        let privateWindowUrl = helper.headerGet(details.responseHeaders, HEADER_EXTENSION_PRIVATE_WINDOW);
        if (privateWindowUrl) {
            this.handlePrivateWindowHeaderReceived(privateWindowUrl);
        }

        return Promise.all(promiseChain).then(() => {
            if (helper.headerPresent(details.responseHeaders, HEADER_EXTENSION_REQUEST)) {
                LogHelper.debug("HixExtension onHeadersReceived::headerExtensionRequested");
                this.knownHosts.add(details.url);
            }
        });
    }

    handlePrivateWindowHeaderReceived(url)
    {
        LogHelper.debug('HixExtension called handlePrivateWindowHeaderReceived()', url);

        let tabs = browser.tabs.query({active: true, lastFocusedWindow: true})

        tabs.then(function (activeTabs) {
            let url = activeTabs[0].url;
            if (url === '') {
                browser.tabs.remove(activeTabs[0].id);
            }
        }, function (onerror) {
            console.error(onerror)
        });

        browser.windows.create({url: url, incognito: true});
    }

    async handleSettingsTimestampReceived(timestamp) {
        console.debug('HixExtension called handleSettingsTimestampReceived()', timestamp);
        let lastTimestamp = (await browser.storage.local.get('settings_timestamp')).settings_timestamp;
        if (lastTimestamp && timestamp > lastTimestamp) {
            let userInfo = await browser.storage.local.get('user_info');
            await (new BrowserExtensionApi()).updateSettings(timestamp);
            await this.initModules(userInfo);
        } else {
            LogHelper.debug('HixExtension ignoring timestamp ' + timestamp + '  that is older than ' + lastTimestamp);
        }
    }

    /**
     * UserInfo is received in header when logging in or when browser extension (re)activates and checks the user_info
     * endpoint to see if the session is still active.
     * @param userInfo
     * @return boolean
     */
    async handleUserInfoReceived(userInfo) {
        console.debug('HixExtension called handleUserInfoReceived()', userInfo);

        if (!userInfo.hasOwnProperty('auth_id')) {
            console.warn('HixExtension ignored received user info without auth_id');
            this.deactivateModules();
            IconHelper.setInactive();
            return false;
        }

        console.log('HixExtension detected login at ' + userInfo.hostname);
        let subdomain = userInfo.hostname.split('.')[0];

        // To prevent update session when starting Twinfield OpenID Widget
        if (subdomain === 'auth') {
            console.warn('HixExtension ignored user authentication from auth subdomain');
            return false;
        }

        console.debug("HixExtension onHeadersReceived::User-Authenticated: " + JSON.stringify(userInfo));

        // Before adding any new listeners and information of the current user, remove everything from the previous user
        await this.removePreviousUserInfo();

        await browser.storage.local.clear();
        await browser.storage.local.set({'host/hostname/domain': userInfo.hostname});
        await browser.storage.local.set({'user_info': userInfo});

        // If user is internal initialize the modules, if external we deactivate the modules.
        if (!userInfo.is_external) {
            if (userInfo.can.use_open_questions || userInfo.can.use_belastingtool) {
                await (new BrowserExtensionApi()).updateSettings(Math.round(Date.now() / 1000));
            }
            await this.initModules(userInfo);
        } else {
            // Deactivate all modules if not already done so.
            this.deactivateModules();
        }

        // Set the icon to colorful.
        IconHelper.setActive();
        LogHelper.info('HixExtension activated extension icon');
        return true;
    }

    // Handles the log out of the previous logged-in users, including event listener removals.
    async removePreviousUserInfo() {
        LogHelper.info('HixExtension removes previous user info');
        IconHelper.setInactive();
        await browser.storage.local.clear();
        if (browser.OpenQuestionsModule) {
            browser.OpenQuestionsModule.onUserLoginChange(false);
        }
        if (browser.belastingtoolModule) {
            browser.belastingtoolModule.onUserLoginChange(false);
        }
    }

    async initModules(userInfo) {

        if (userInfo.hasOwnProperty('log_level')) {
            LogHelper.setLevel(userInfo.log_level);
            LogHelper.info('HixExtension set log level to ' + userInfo.log_level, LogHelper.currentLevel);
        }

        if (!browser.TranslationModule) {
            browser.TranslationModule = new TranslationModule();
            LogHelper.debug('HixExtension loaded TranslationModule');
        }

        browser.TranslationModule.setLanguage(userInfo.language);

        await browser.TranslationModule.loadTranslations();

        LogHelper.debug('HixExtension set language: ' + userInfo.language);
        if (userInfo.can.use_belastingtool) {
            LogHelper.debug('HixExtension detected that authenticated user can use belastingtool');
            if (!browser.belastingtoolModule) {
                browser.belastingtoolModule = new BelastingtoolModule();
                LogHelper.debug('HixExtension loaded BelastingtoolModule');
            }
        } else {
            browser.belastingtoolModule = null;
        }

        if (userInfo.can.use_open_questions) {
            LogHelper.debug('HixExtension detected that authenticated user can use open questions');
            await browser.storage.local.set({'can-use-open-questions': '1'}); // deprecated, use user.can.use_open_questions
            browser.OpenQuestionsModule = new OpenQuestionsModule();
            LogHelper.debug('HixExtension loaded OpenQuestionsModule');
            browser.openQuestionsApi = new OpenQuestionsApi();
            LogHelper.debug('HixExtension loaded OpenQuestionsApi');
            await browser.OpenQuestionsModule.setServiceModulesFromLocalStorage();
        } else {
            // Set this variable to null to prevent requests being made by external users
            browser.OpenQuestionsModule = null;
            LogHelper.debug('HixExtension unloaded OpenQuestionsModule');
            browser.openQuestionsApi = null;
            LogHelper.debug('HixExtension unloaded openQuestionsApi');
        }
    }

    addInitialSSORequestListener() {
        browser.webRequest.onBeforeSendHeaders.addListener((details) => {
                return helper.tryCatchPromise(() => {
                    LogHelper.debug('HixExtension InitialSSORequestListener', details);
                    if (helper.isSSOUrl(details.url.toString())) {
                        LogHelper.debug('HixExtension isSSOUrl');
                        return this.getSSOWebRequestByTabId(details.tabId, true).onInitialRequest(details);
                    }
                })
            },
            {
                urls: this.targetUrls, // array
                types: ['main_frame', 'sub_frame']
            },
            []
        );
    }

    /**
     * Add listener to outbound requests only for Firefox. Chrome does not allow webRequestBlocking
     */
    addOutboundRequestsListener() {
        browser.webRequest.onBeforeSendHeaders.addListener((details) => {
                return helper.tryCatchPromise(() => {
                    LogHelper.debug('HixExtension OutboundRequestsListener', details);
                    if (this.knownHosts.contains(details.url)) {
                        helper.headerChangeOrAdd(details.requestHeaders, HEADER_EXTENSION_VERSION, this.version);
                    }
                    return {requestHeaders: details.requestHeaders};
                })
            },
            {
                urls: this.targetUrls,
                types: ['xmlhttprequest', 'main_frame']
            },
            ['blocking', 'requestHeaders']
        );
    }

    /**
     * Get the WebRequest from the stack, by TabId. Return false if not exists
     * @param {number} tabId
     * @param deleteIfExists
     * @returns {SSOWebRequestHandler}
     */
    getSSOWebRequestByTabId(tabId, deleteIfExists) {
        LogHelper.debug('HixExtension getSSOWebRequestByTabId. TabId:' + tabId + ", DeleteIfExists:" + deleteIfExists);

        if (this.SSOWebRequestHandlersByTabId.has(tabId) && deleteIfExists) {
            LogHelper.debug('HixExtension SSOWebRequestHandler already existed, deleting...' + tabId);
            this.SSOWebRequestHandlersByTabId.get(tabId).removeAllWebRequestListeners();
            this.SSOWebRequestHandlersByTabId.delete(tabId);
        }

        if (!this.SSOWebRequestHandlersByTabId.has(tabId)) {
            LogHelper.debug('HixExtension Could not find WebRequestHandler by TabId' + tabId);
            this.SSOWebRequestHandlersByTabId.set(tabId, new SSOWebRequestHandler(this, tabId));
        }
        return this.SSOWebRequestHandlersByTabId.get(tabId);
    }

    /**
     * Move the WebRequest from the stack and push into the recycle bin which should
     * delete the request
     * @param {number} tabId
     * @returns {boolean}
     */
    moveSSOWebRequestToBinByTabId(tabId) {
        if (this.SSOWebRequestHandlersByTabId.has(tabId)) {
            LogHelper.debug('HixExtension Deleting SSOWebRequest for tab' + tabId);
            // this timeout has to be set as deleting the object results in a failed removing of
            // some listeners (webNavigation.onCompleted) This is probably caused by lazy delete for Javascript
            setTimeout(() => {
                this.SSOWebRequestHandlersByTabId.delete(tabId);
            }, 5000);
        }

        return false;
    }
}

export default new HixExtension(); // singleton