import browser from 'webextension-polyfill';
import {Log<PERSON>elper} from '../helpers/LogHelper';

/**
 * Invisible Hand class simulates user interaction with a page.
 * It adds requests listeners which wait for a matching page and then injects the DOMFondler script
 * with instructions on what to do on a page.
 */
class SSOInvisibleHand {

    constructor(tabId) {
        this.tabId = tabId;
        this.pageListeners = {};
        this.messagesListener = null;
        this.repeat = 0;
        this.instructionLifetime = 30000;
    }

    /**
     * Decode base64 encoded instructions and execute them.
     */
    setData(data) {

        this.config = JSON.parse(atob(data));
        //this.config.meta.debug = false;

        let promiseChain = [];

        if (this.config.settings.clear_cookies) {
            LogHelper.info('InvisibleHand Removing cookies');
            promiseChain.push(this.clearCookies(this.config.settings.clear_cookies));
        }

        if (this.config.settings.repeat) {
            this.repeat = this.config.settings.repeat;
        }

        if (this.config.settings.instruction_lifetime) {
            this.instructionLifetime = this.config.settings.instruction_lifetime;
        }

        return Promise.all(promiseChain).then(() => {
            LogHelper.info('InvisibleHand data set.', this.config);
            this.initMessagesListener();
            this.setNavigationListeners();
        });
    }

    /**
     * Removes cookies of specific url
     * Optional: remove only specific cookies if names are specified
     * @param urls
     */
    async clearCookies(urls) {
        if (typeof urls === 'string' || urls instanceof String) {
            urls = [urls];
        }

        for (const [key, value] of Object.entries(urls)) {
            // We build the array key => value, but when there are no cookieNames JS creates a numbered index.
            if (Array.isArray(value)) {
                await this.clearUrlCookies(key, value);
            } else {
                await this.clearUrlCookies(value);
            }
        }
    }

    /**
     * Removes cookies of one specific url
     * @param url
     * @param cookieNames
     */
    clearUrlCookies(url, cookieNames = []) {
        return browser.cookies.getAll({url: this._formatCookieUrl(url)})
            .then((cookies) => {
                LogHelper.info("InvisibleHand::clearUrlCookies: Removing: ", cookies);
                let cookiePromises = [];
                if (cookies) {
                    // If cookie names are specified we remove only those, if not we remove all.
                    if (cookieNames && cookieNames.length) {
                        for (const cookieName of cookieNames.values()) {
                            cookiePromises.push(this.removeCookie(url, cookieName));
                        }
                    } else {
                        for (let i = 0; i < cookies.length; i++) {
                            cookiePromises.push(this.removeCookie(url, cookies[i].name));
                        }
                    }
                }
                return Promise.all(cookiePromises);
            })
            .then((data) => {
                LogHelper.info("InvisibleHand::clearUrlCookies: Success.");
                return data
            })
            .catch((e) => {
                LogHelper.info("InvisibleHand::clearUrlCookies:", e);
                return e
            });

    }

    removeCookie(url, cookieName) {
        return browser.cookies.remove({url: url, name: cookieName}).then((c) => {
            LogHelper.info("InvisibleHand::clearUrlCookies: Removed Cookie: " + JSON.stringify(c));
            return c;
        }).catch((c) => {
            LogHelper.info("InvisibleHand::clearUrlCookies: Error Removing Cookie: " + JSON.stringify(c));
            return c;
        });
    }

    /**
     * This will format the param url to a standard format for clearing cookies for example (https://domain.nl)
     * @param url
     * @returns {string}
     * @private
     */
    _formatCookieUrl(url) {
        const regex = /(?<protocol>[a-zA-Z0-9-]*:\/\/)?(?<domain>[a-zA-Z-.0-9]+)/
        const result = regex.exec(url)
        if (result && result.groups) {
            let protocol = 'https://'
            if (result.groups.protocol) {
                protocol = result.groups.protocol
            }
            if (result.groups.domain) {
                url = protocol + result.groups.domain
            }
        }
        return url
    }

    /**
     * Open the URL that a user would enter in the browser to go to the login page.
     * The user may be redirected away from this page to another login page.
     */
    navigateStartUrl(url) {
        browser.tabs.update(null, {
            'active': true,
            'loadReplace': true,
            'url': url
        });
    }

    /**
     * Add event listener for every page
     */
    setNavigationListeners() {

        //remove any previous listeners
        for (let i in this.pageListeners) {
            this.removeNavigationListener(i);
        }

        //remove any listeners if they still exist after 30 seconds or longer if set in widget (instruction_lifetime).
        setTimeout(() => {
            for (let i in this.pageListeners) {
                this.removeNavigationListener(i);
            }
        }, this.instructionLifetime);

        for (let i in this.config.pages) {

            let step = this.config.pages[i];

            //if no URL patterns are found, use the start URL.
            let urlFilter = {url: [{urlEquals: this.config.meta.startUrl}]}; //exact match
            if (step.pattern) {
                urlFilter = {url: [{urlMatches: step.pattern}]}; //regex pattern match
            }

            LogHelper.info('InvisibleHand setting listener for ' + i + ' ' + step.pattern);
            //use bind method to pass additional information to the handler.
            let navigationListener = this.navigationHandler.bind(this, i);
            browser.webNavigation.onDOMContentLoaded.addListener(navigationListener, urlFilter);
            this.pageListeners[i] = navigationListener; //keep track of listeners so they can be removed when no longer needed
        }
    }

    /**
     * Remove navigation listener. Is called in the following scenarios:
     * 1. New login instructions are sent to the invsible hand; all previous listeners will be cleared.
     * 2. All steps on a page have been exectued. The listener is removed to prevent them being executed again if another matching URL is loaded.
     * @param i String key of page. Used to identify page in instruction set.
     */
    removeNavigationListener(i) {
        LogHelper.info('InvisibleHand Removing listener ' + i);
        let listener = this.pageListeners[i];
        if (browser.webNavigation.onDOMContentLoaded.hasListener(listener)) {
            browser.webNavigation.onDOMContentLoaded.removeListener(listener);
            delete this.pageListeners[i];
        }
    }

    /**
     * When the browser navigates to a destination page, insert script and send instructions.
     * @param i string key of matching page.
     * @param details Object with event/request details, supplied by the browser.
     */
    async navigationHandler(i, details) {

        let page = this.config.pages[i];

        LogHelper.info('InvisibleHand Injecting invisible hand instructions for ' + page.pattern);

        let scriptPromise = Promise.resolve();

        if (page.method === 'autologin') {
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/form-detector.js']
                });
        }

        await browser.scripting.executeScript(
            {
                target: {tabId: details.tabId},
                files: ['/dist/dom-fondler.js'],
            }
        );

        return scriptPromise.then((response) => {
            //send instructions to inserted script.
            LogHelper.debug('InvisibleHand sending instructions to tab ' + details.tabId);
            browser.tabs.sendMessage(details.tabId, {
                action: 'init',
                settings: this.config.settings,
                meta: this.config.meta,
                method: page.method,
                instructions: page.instructions,
                page: i
            });
            if (this.repeat <= 0) {
                this.removeNavigationListener(i);
            }
            this.repeat -= 1;
        }).catch((e) => {
            LogHelper.error('InvisibleHand unable to insert SSODOMFondler script', e);
        });
    }

    /**
     * Add event listener for the fondler to report when it is done.
     */
    initMessagesListener() {

        if (this.messagesListener && browser.runtime.onMessage.hasListener(this.messagesListener)) {
            browser.runtime.onMessage.removeListener(this.messagesListener);
        }

        this.messagesListener = (message, sender) => {
            if (message.action === 'work_done') {
                LogHelper.info('InvisibleHand Received work done message');
            }
        };

        browser.runtime.onMessage.addListener(this.messagesListener);
    }

    /**
     * Run the automatic form detector after SSOInvisiblehand.runFormDetector()
     * has been called in the console.
     */
    runFormDetector() {

        console.log('Running form detector');

        browser.scripting.executeScript(
            {
                target: {tabId: this.tabId},
                files: ['/dist/form-detector.js']
            },
            () => {
                browser.scripting.executeScript(
                    {
                        target: {tabId: this.tabId},
                        func: global.detector.extractInformation()
                    }
                    , {code: "detector.extractInformation();"})
            });

        browser.runtime.onMessage.addListener((message) => {
            LogHelper.info('InvisibleHandReceived form detector message');
            LogHelper.info(message.results);
            LogHelper.info(message.results.template);
        });
    }
}

export default SSOInvisibleHand;
