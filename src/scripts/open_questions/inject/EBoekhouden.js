import {BasePage} from "./eboekhouden/BasePage";
import {Importeren} from "./eboekhouden/Importeren";
import {OpenPosten} from "./eboekhouden/OpenPosten";
import {Mutatie} from "./eboekhouden/Mutatie";
import {Grootboek} from "./eboekhouden/Grootboek";

export class EBoekhouden {
    constructor() {
        this.page = null;
        this.selectedPage = null;
        this.availablePages = {
            'import': Importeren,
            'openpost': OpenPosten,
            'mutatie': Mutatie,
            'overzicht': Grootboek
        };
    }

    async start(demo, selectors) {
        if (!demo) {
            await (new BasePage(demo, selectors)).start();
        }
    }

    async startRequestedPage(demo, selectors, page, apiData) {
        if (!demo) {
            console.log(page, 'request')

            if (this.page === null) {
                this.selectedPage = page.name;
                this.page = new this.availablePages[page.name](demo, selectors, page, apiData);
                this.page.start();
            } else {
                if (this.selectedPage === 'overzicht' && page.name === 'overzicht') {
                    this.page.removeButtons();
                    this.page.appendApiData(apiData); // this page can have more than 1 table, we append api data again and reload
                    this.page.appendButtons();
                } else { // reload
                    this.selectedPage = page.name;
                    this.page = new this.availablePages[page.name](demo, selectors, page, apiData);
                    this.page.removeButtons(demo);
                    this.page.start();
                }
            }
        }
    }

    async removeButtons(demo) {
        if (!demo && this.page) {
            this.page.removeButtons();
        }
    }
}

let eboekhouden = new EBoekhouden();

document.addEventListener('startEBoekhouden', async function (e) {
    await eboekhouden.start(e.detail.demo, e.detail.selectors)
});

document.addEventListener('removeEBoekhoudenButtons', async function (e) {
    await eboekhouden.removeButtons(e.detail.demo)
});

document.addEventListener('startEBoekhoudenRequestedPage', async function (e) {
    await eboekhouden.startRequestedPage(
        e.detail.demo,
        e.detail.selectors,
        e.detail.page,
        e.detail.apiData
    )
});

