import {BasePage} from "./BasePage";

export class <PERSON>ssierPage extends BasePage {

    /**
     * Add new question button and dropdown
     */
    async appendComponents() {
        let buttonContainer = this.openQuestionsHelper.getButtonsContainer(`
            .sl-buttons-container {
                margin-right: 10px;
                display: inline-block;
                position: relative;
                top: 3px;
                vertical-align: top;
            }
            .sl-buttons-container > div {
                display: inline-block;
                margin: 0 5px;
                vertical-align: top;
            }
        `);
        await this.createNewQuestionButton(buttonContainer);
        await this.createQuestionsDropdownList(buttonContainer);
        await this.appendCornerStone();

        let pageButtonContainer = document.querySelector(this.selectors.buttonContainer);
        pageButtonContainer.style.display = 'flex';
        pageButtonContainer.style.justifyContent = 'space-between';
        pageButtonContainer.append(buttonContainer);
    }
}
