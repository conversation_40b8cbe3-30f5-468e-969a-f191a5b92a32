import {ServicePage} from "../ServicePage";
import {OpenQuestionsHelper} from "../helpers/OpenQuestionsHelper";
import {NewQuestion} from "../buttons/NewQuestion";
import browser from "webextension-polyfill";

class BasePage extends ServicePage {
    constructor(selectors, demo) {
        super();
        this.selectors = selectors;
        this.demo = demo;
        this.category = 'fiscal';
    }

    start() {
        this.openQuestionsHelper = new OpenQuestionsHelper(this.demo);
        this.listen();
    }

    listen() {
        let self = this;
        // had to implement this because of the await taking to long sometimes.
        let selectorFound = false;
        let selectors = this.selectors;
        const observer = new MutationObserver(async function () {
            if (self.getSelectedCompanyName() && !selectorFound) {
                if (document.querySelector(selectors.selectedCompanyIdContainer)) {
                    selectorFound = true;
                    await self.validateComponents()
                    observer.disconnect();
                }
            }
        });

        const config = {
            subtree: true,
            childList: true,
            attributes: true,
            attributeFilter: ['class']
        };
        observer.observe(document.body, config);
    }

    /**
     * Service name
     * @returns {string}
     */
    getServiceName() {
        return 'nextens_open_questions';
    }

    getSubtitle() {
        return this.getSelectedCompanyName();
    }

    getSelectedCompanyName() {
        return document.querySelector(this.selectors.selectedCompanyNameContainer).textContent.trim();
    }

    /**
     * The company ID is in a meta tag
     */
    handleSelectedId() {
        return document.querySelector(this.selectors.selectedCompanyIdContainer).content;
    }

    /**
     * Validate if we can add components to the page
     * @returns {Promise<void>}
     */
    async validateComponents() {
        let companyIsBlocked = await this.isCompanyBlocked(
            this.getServiceName(),
            this.handleSelectedId(),
            this.getSelectedCompanyName()
        ) && this.demo === false;

        if (await this.isCompanyManager()) {
            await this.appendCornerStone();
            if (companyIsBlocked) {
                return;
            }
            return this.appendComponents();

        }

        if (companyIsBlocked) {
            return;
        }

        return this.appendComponents();
    }

    /**
     * Remove button and dropdown
     */
    async removeButtonContainer() {
        let element = document.querySelector('.sl-buttons-container');
        if (element) {
            element.parentNode.removeChild(element);
        }
    }

    async reloadComponents() {
        await this.removeButtonContainer();
        await this.appendComponents()
    }

    /**
     * Add new question button
     */
    async createNewQuestionButton(container) {
        let newQuestionButton = new NewQuestion({
            'service_name': this.getServiceName(),
            'external_company_id': this.handleSelectedId(),
            'external_company_name': this.getSubtitle(),
            'title': this.getSubtitle(),
            'subtitle': this.getSubtitle(),
            'page_title': this.getSubtitle(),
            'page_url': window.location.href,
        }, this.category, this.demo);

        await newQuestionButton.init();
        newQuestionButton.handleListeners();
        newQuestionButton._injectableElement.addEventListener('question-updated', async () => {
            await this.onQuestionUpdated();
            await this.reloadComponents();
        });

        container.prepend(newQuestionButton._injectableElement);
    }

    /**
     * Create question dropdown list
     */
    async createQuestionsDropdownList(container) {
        let externalId = this.handleSelectedId();
        if (externalId) {
            let questionsDropdown = this.openQuestionsHelper.getQuestionsDropdown({
                'category': this.category,
                'service_name': this.getServiceName(),
                'external_id': externalId
            });
            if (questionsDropdown) {
                questionsDropdown.addEventListener('question-updated', async () => {
                    await this.onQuestionUpdated();
                    await this.reloadComponents();
                });
                container.prepend(questionsDropdown);
            }
        }
    }

    /**
     * Gathers the Hix company
     * Append secure login corner button
     * @returns {Promise<*>}
     */
    async appendCornerStone() {
        if (this.demo) {
            console.log('Cornerstone: Demo mode on');
            return;
        }

        await browser.storage.local.set({[this.getServiceName() + '_external_company_id']: this.handleSelectedId()});
        await browser.storage.local.set({[this.getServiceName() + '_external_company_name']: this.getSubtitle()});
        await browser.storage.local.set({[this.getServiceName() + '_external_company_code']: (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id']});

        await this.onQuestionUpdated();
    }

    async onQuestionUpdated() {
        await this.openQuestionsCommunicationService.sendToBExtension(
            'questions_updated',
            {
                'companyId': (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'],
                'companyCode': (await browser.storage.local.get(this.getServiceName() + '_external_company_code'))[this.getServiceName() + '_external_company_code'],
                'companyName': (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name'],
                'serviceName': this.getServiceName()
            }
        );
    }
}

export {BasePage}