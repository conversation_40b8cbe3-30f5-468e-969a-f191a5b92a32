import {BasePage} from "./BasePage";

export class ChangingDataPage extends BasePage {

    getSelectedCompanyName() {
        return document.querySelector(this.selectors.selectedCompanyNameContainer).textContent.trim().split(/\r?\n/)[0];
    }
    
    /**
     * Add new question button and dropdown
     */
    async appendComponents() {
        let buttonContainer = this.openQuestionsHelper.getButtonsContainer(`
            .sl-buttons-container {
                float: right;
                margin-right: 10px;
                display: inline-block;
                position: relative;
                top: 3px;
                vertical-align: top;
            }
            .sl-buttons-container > div {
                display: inline-block;
                margin: 0 5px;
                vertical-align: top;
            }
        `);
        await this.createNewQuestionButton(buttonContainer);
        await this.createQuestionsDropdownList(buttonContainer);
        await this.appendCornerStone();

        let pageButtonContainer = document.querySelector(this.selectors.buttonContainer);
        pageButtonContainer.prepend(buttonContainer);
    }
}
