import {BasePage} from "./moneybird/BasePage";
import {SelectorHelper} from "./helpers/SelectorHelper";
import {Invoices} from "./moneybird/Invoices";
import {Incoming} from "./moneybird/Incoming";
import {Quotations} from "./moneybird/Quotations";
import {DebtorOverview} from "./moneybird/DebtorOverview";
import {CreditorOverview} from "./moneybird/CreditorOverview";
import {GeneralLedger} from "./moneybird/GeneralLedger";

export class Moneybird {
    constructor() {
        this.page = null;
        this.availablePages = {
            'invoices': Invoices,
            'incoming': Incoming,
            'quotations': Quotations,
            'debtor_overview': DebtorOverview,
            'creditor_overview': CreditorOverview,
            'general_ledger': GeneralLedger,
        };
        this.selectorHelper = new SelectorHelper();
    }

    async start(demo, selectors) {
        if (!demo) {
            await (new BasePage(demo, selectors)).start();
        }
    }

    async startRequestedPage(demo, selectors) {
        if (!demo) {
            this.handlePageStart(demo, selectors, selectors.pageTitle);
        }
    }

    handlePageStart(demo, selectors, pageTitleSelector) {
        let pages = selectors.allowedPages;
        for (let language of Object.keys(pages)) {
            for (let page of Object.keys(pages[language])) {
                let pageTitle = this.getPageTitle(pageTitleSelector);
                if (this.availablePages[page]) {
                    for (let i = 0; i < pages[language][page].length; i++) {
                        let availablePage = pages[language][page][i];
                        if (pageTitle === availablePage) {
                            let currentPage = this.page;
                            if (currentPage === null) {
                                this.page = new this.availablePages[page](demo, selectors);
                                this.page.start();
                                break;
                            } else if (currentPage.constructor.name !== pageTitle) {
                                this.page = new this.availablePages[page](demo, selectors);
                                this.page.start();
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Get page title
     * @returns {*}
     */
    getPageTitle(selector) {
        let title = document.querySelector(selector);
        if (title) {
            return title.textContent.trim();
        }
        return null;
    }
}

let moneybird = new Moneybird()

document.addEventListener('start', async function (e) {
    await moneybird.start(
        e.detail.demo,
        e.detail.selectors
    )
}, {once : true})

document.addEventListener('startRequestedPage', async function (e) {
    await moneybird.startRequestedPage(
        e.detail.demo,
        e.detail.selectors
    )
}, {once: true})

