import {ClientPage} from "./ClientPage";

class DeclarationEditPage extends ClientPage {
    constructor(selectors, demo) {
        super(selectors, demo);
        this.category = 'declaration';
    }
    getQuestionTitle() {
        let titleElem = document.querySelector(this.selectors.title);
        return titleElem ? titleElem.textContent.trim() : null;
    }

    /**
     * Add new question button and dropdown
     */
    async appendComponents() {
        let buttonContainer = this.openQuestionsHelper.getButtonsContainer(`
            .sl-buttons-container {
                display: inline-block;
                position: relative;
                height: 36px;
                top: 20px;
                right: 20px;
                float: right;
            }
            .sl-question-dropdown-list {
                right: 140px;
            }
            .sl-buttons-container > div {
                display: inline-block;
                margin: 0 5px;
                height: 100%;
            }
            .sl-new-question-button {
                padding: 11px;
            }
        `);
        await this.createNewQuestionButton(buttonContainer);
        await this.createQuestionsDropdownList(buttonContainer);
        await this.appendCornerStone();

        let pageButtonContainer = document.querySelector(this.selectors.buttonContainer);
        pageButtonContainer.prepend(buttonContainer);
    }

    getDescription() {
        const path = window.location.pathname;
        const element = document.querySelector('a[href="' + path + '"]');
        return element ? element.textContent.trim() : '';
    }

    getSelectedCompanyId() {
        return window.localStorage.CS_CLIENT_ID;
    }

    getDeclarationId() {
        return window.location.pathname.split('/')[this.selectors.selectedCompanyIdUrlParam];
    }
}

export {DeclarationEditPage}