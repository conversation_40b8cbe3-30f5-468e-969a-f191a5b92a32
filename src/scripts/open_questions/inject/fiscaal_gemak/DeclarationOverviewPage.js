import {AppPage} from "./AppPage";

class DeclarationOverviewPage extends AppPage {
    constructor(selectors, demo) {
        super(selectors, demo);
        this.category = 'declaration';
    }
    getQuestionTitle() {
        let titleElem = document.querySelector(this.selectors.title);
        if (titleElem) {
            const text = titleElem.textContent;
            const textArray = text.split(" - ");
            return textArray[0].trim();
        }
        return null;
    }

    getSelectedCompanyId() {
        let elem = document.querySelector(this.selectors.subtitle);
        if (elem) {
            let match = elem.href.match(/(\d+)~/);
            return match ? match[1] : null;
        }
    }

    getDeclarationId() {
        return window.location.pathname.split('/')[this.selectors.selectedCompanyIdUrlParam];
    }
}

export {DeclarationOverviewPage}