import {ClientPage} from "./ClientPage";

class AppPage extends ClientPage {
    /**
     * Add new question button and dropdown
     */
    async appendComponents() {
        let buttonContainer = this.openQuestionsHelper.getButtonsContainer(`
            .sl-buttons-container {
                margin-right: 10px;
                display: inline-block;
                position: relative;
                height: 36px;
                top: 1px;
            }
            .sl-buttons-container > div {
                display: inline-block;
                margin: 0 5px;
                height: 100%;
            }
            .sl-question-dropdown-container {
            
            }
            .sl-new-question-button {
                padding: 11px;
            }
        `);
        await this.createNewQuestionButton(buttonContainer);
        await this.createQuestionsDropdownList(buttonContainer);
        await this.appendCornerStone();

        let pageButtonContainer = document.querySelector(this.selectors.buttonContainer);
        pageButtonContainer.prepend(buttonContainer);
    }
}

export {AppPage}