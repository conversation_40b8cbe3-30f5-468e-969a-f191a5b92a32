import {ServicePage} from "../ServicePage";
import {OpenQuestionsHelper} from "../helpers/OpenQuestionsHelper";
import {NewQuestion} from "../buttons/NewQuestion";
import browser from "webextension-polyfill";

const declarationTypesMap = {
    declaration_vpb: /^(VPB|Vennootschapsbelasting)\s\d{4}/,
    declaration_va_vpb: /^(VA VPB|Voorlopige aanslag vennootschapsbelasting)\s\d{4}/,
    declaration_vat: /^(OB|Omzetbelasting)\s\d{4}/,
    declaration_dividend: /^(Div. Bel.|Dividendbelasting)\s\d{4}/,
    declaration_ib: /^(IB|Inkomstenbelasting)\s\d{4}/,
    declaration_va_ib: /^(VA IB|Voorlopige aanslag)\s\d{4}/,
    declaration_supplementation: /^Suppletie\s/,
    declaration_ict: /^ICP\s\d{4}/
};

class ClientPage extends ServicePage {
    constructor(selectors, demo) {
        super();
        this.selectors = selectors;
        this.demo = demo;
        this.category = 'fiscal';
    }

    start() {
        this.openQuestionsHelper = new OpenQuestionsHelper(this.demo);
        this.listen();
    }

    listen() {
        let self = this;
        // had to implement this because of the await taking to long sometimes.
        let idFound = false;
        const observer = new MutationObserver(async function () {
            if (self.getSelectedCompanyName() && !idFound) {
                if (self.getSelectedCompanyId()) {
                    idFound = true;
                    await self.validateComponents()
                    observer.disconnect();
                }
            }
        });

        const config = {
            subtree: true,
            childList: true,
            attributes: true,
        }
        observer.observe(document.body, config);
        // Required for declarations page edit. Observer only works sometimes without this line
        document.querySelector('body').classList.add('observe');
    }

    /**
     * Service name
     * @returns {string}
     */
    getServiceName() {
        return 'fiscaal_gemak_open_questions';
    }

    getSubtitle() {
        return this.getSelectedCompanyName();
    }

    getSelectedCompanyName() {
        return document.querySelector(this.selectors.selectedCompanyNameContainer).textContent.trim();
    }

    /**
     * The company ID is in a meta tag
     */
    getSelectedCompanyId() {
        return window.location.pathname.split('/')[this.selectors.selectedCompanyIdUrlParam];
    }

    /**
     * Get page title
     * @returns {string|null}
     */
    getPageTitle() {
        let title = '';
        let breadcrumbs = document.querySelectorAll(this.selectors.breadcrumbs);
        if (breadcrumbs.length) {
            breadcrumbs.forEach(breadcrumb => {
                title = title + breadcrumb.textContent.trim();
                if (breadcrumbs[breadcrumbs.length-1] !== breadcrumb) {
                    title = title + " > ";
                }
            });

            return title;
        }

        return null;
    }

    /**
     * Get question title
     * @returns {string|null}
     */
    getQuestionTitle() {
        let breadcrumbs = document.querySelectorAll(this.selectors.breadcrumbs);
        if (breadcrumbs.length) {
            return breadcrumbs[breadcrumbs.length-1].textContent.trim();
        }
        return null;
    }

    /**
     * Validate if we can add components to the page
     * @returns {Promise<void>}
     */
    async validateComponents() {
        if (await this.buttonPresent()) {
            return;
        }

        let companyIsBlocked = await this.isCompanyBlocked(
            this.getServiceName(),
            this.getSelectedCompanyId(),
            this.getSubtitle()
        );

        if (await this.isCompanyManager() && companyIsBlocked) {
            return this.appendCornerStone();
        }

        if (companyIsBlocked) {
            return;
        }

        return this.appendComponents();
    }

    async buttonPresent() {
        return Boolean(document.querySelector(this.selectors.buttonContainer + ' > ' + '.sl-buttons-container'));
    }

    /**
     * Remove button and dropdown
     */
    async removeButtonContainer() {
        let element = document.querySelector('.sl-buttons-container');
        if (element) {
            element.parentNode.removeChild(element);
        }
    }

    async reloadComponents() {
        await this.removeButtonContainer();
        await this.appendComponents()
    }

    /**
     * Add new question button
     */
    async createNewQuestionButton(container) {
        let newQuestionButton = new NewQuestion({
            'service_name': this.getServiceName(),
            'external_company_id': this.getSelectedCompanyId(),
            'external_company_name': this.getSubtitle(),
            'subtitle': this.getSubtitle(),
            'page_title': this.getPageTitle(),
            'page_url': window.location.href,
            'title': this.getQuestionTitle(),
            'description': this.getDescription(),
            'selected_type': this.getSelectedType(),
            'declaration_id': this.getDeclarationId()
        }, this.category, this.demo);

        await newQuestionButton.init();
        newQuestionButton.handleListeners();
        newQuestionButton._injectableElement.addEventListener('question-updated', () => {
            this.onQuestionUpdated();
            this.reloadComponents();
        });

        container.prepend(newQuestionButton._injectableElement);
    }

    /**
     * Create question dropdown list
     */
    async createQuestionsDropdownList(container) {
        let externalId = this.getSelectedCompanyId();
        if (externalId) {
            let questionsDropdown = this.openQuestionsHelper.getQuestionsDropdown({
                'category': this.category,
                'service_name': this.getServiceName(),
                'external_id': externalId
            });
            if (questionsDropdown) {
                questionsDropdown.addEventListener('question-updated', () => {
                    this.onQuestionUpdated();
                    this.reloadComponents();
                });
                container.prepend(questionsDropdown);
            }
        }
    }

    /**
     * Gathers the Hix company
     * Append secure login corner button
     * @returns {Promise<*>}
     */
    async appendCornerStone() {
        if (this.demo) {
            console.log('Cornerstone: Demo mode on');
            return;
        }
        await browser.storage.local.set({[this.getServiceName() + '_external_company_id']: this.getSelectedCompanyId()});
        await browser.storage.local.set({[this.getServiceName() + '_external_company_name']: this.getSubtitle()});
        await browser.storage.local.set(
            {
                [this.getServiceName() + '_external_company_code']: (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id']
            });

        await this.onQuestionUpdated();
    }

    async onQuestionUpdated() {
        await this.openQuestionsCommunicationService.sendToBExtension(
            'questions_updated',
            {
                'companyId': (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'],
                'companyCode': (await browser.storage.local.get(this.getServiceName() + '_external_company_code'))[this.getServiceName() + '_external_company_code'],
                'companyName': (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name'],
                'serviceName': this.getServiceName()
            }
        );
    }

    getDescription() {
        return '';
    }

    getSelectedType() {
        for (const [type, regex] of Object.entries(declarationTypesMap)) {
            if (regex.test(this.getQuestionTitle())) {
                return type
            }
        }
        return 'other';
    }

    getDeclarationId() {
        return '';
    }
}

export {ClientPage}