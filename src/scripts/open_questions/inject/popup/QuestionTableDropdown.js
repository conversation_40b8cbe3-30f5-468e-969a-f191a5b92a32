import style from '!css-loader!./question_table_dropdown_style.css';
import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {HistoryPopup} from "../history_popup/HistoryPopup";
import {NewStatusQuestion} from "../buttons/NewStatusQuestion";

class QuestionTableDropdown {
    constructor(
        serviceData,
        possibleQuestions, // list of possible questions to create the table
        ignoreAttributes = [], // ignore attribute when matching the question
        questions = [],
        types = []
    ) {
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
        this.serviceData = serviceData;
        this.possibleQuestions = possibleQuestions ? possibleQuestions : []; // questions found for table
        this.ignoreAttributes = ignoreAttributes;
        this.questions = questions; // created questions from portal
        this.types = types;
    }

    async init() {
        this._injectableElement = await this.handleDropdown();
        return this._injectableElement;
    }

    async handleDropdown() {
        let buttonContainer = this.getButtonsContainer();
        let dropdown = this.getDropdownElement(buttonContainer); // dropdown that will have questions
        let questionsCount = this.getQuestionsCountElement(dropdown); // questions count

        dropdown.appendChild(this.getSearchElement()); // search input
        dropdown.appendChild(this.getLoadingElement()); // loading

        let questionList = this.handleQuestionElements(
            dropdown,
            this.possibleQuestions,
            this.questions,
            this.types
        );

        dropdown.appendChild(questionList);

        buttonContainer.appendChild(questionsCount);
        buttonContainer.appendChild(dropdown);

        return buttonContainer;
    }

    /**
     * Main container with every elements
     */
    getButtonsContainer() {
        let buttonContainer = document.createElement('div');
        buttonContainer.setAttribute('class', 'hix-question-table-dropdown-container');
        let styleElement = document.createElement('style');
        styleElement.innerText = style;
        buttonContainer.appendChild(styleElement);

        return buttonContainer;
    }

    /**
     * Handle question list elements
     * @param dropdown
     * @param possibleQuestions
     * @param questions
     * @param types
     * @returns {HTMLDivElement}
     */
    handleQuestionElements(dropdown, possibleQuestions, questions, types) {
        let questionsList = document.createElement('div');
        questionsList.classList.add('hix-question-table-questions-list', 'hix-block');

        for (let index in possibleQuestions) {
            let question = possibleQuestions[index];

            // row wrapper
            let rowElement = document.createElement('div');

            // left part for dropdown element
            let rowElementLeft = document.createElement('div');
            rowElementLeft.setAttribute('class', 'hix-question-table-dropdown-list-left');
            let titleElement = document.createElement('div');
            titleElement.setAttribute('class', 'hix-question-table-dropdown-list-title');
            titleElement.innerText = question.name;

            let dateElement = document.createElement('div');
            dateElement.setAttribute('class', 'hix-question-table-dropdown-list-date');
            dateElement.innerText = question.transaction_date;

            rowElementLeft.appendChild(titleElement);
            rowElementLeft.appendChild(dateElement);

            // right part for dropdown element
            let rowElementRight = document.createElement('div');
            rowElementRight.setAttribute('class', 'hix-question-table-dropdown-list-right');

            // new question button
            let statusQuestionButton = null;
            let matchingQuestion = this.handleMatchingQuestion(questions, question);
            if (matchingQuestion && matchingQuestion.status !== 'deleted') {
                statusQuestionButton = new NewStatusQuestion(
                    rowElementRight,
                    matchingQuestion,
                    this.serviceData.category,
                    false,
                    types,
                    matchingQuestion.status,
                    matchingQuestion.status_text
                );

                // clicking row will open history
                rowElement.addEventListener('click', () => {
                    this.openQuestionHistory({
                        service_name: this.serviceData.service_name,
                        id: matchingQuestion.id,
                        name: matchingQuestion.name,
                        status: matchingQuestion.status,
                        type_text: matchingQuestion.type_text
                    });
                    this.closeDropdown();
                });
            } else {
                statusQuestionButton = new NewStatusQuestion(
                    rowElementRight,
                    {
                        ...this.serviceData,
                        ...question
                    },
                    this.serviceData.category,
                    false,
                    types
                );

                // clicking row will open new question popup
                rowElement.addEventListener('click', () => {
                    statusQuestionButton.injectableElement.click();
                });
            }
            statusQuestionButton.injectableElement.style.backgroundColor = 'transparent';
            statusQuestionButton.injectableElement.style.fontSize = '12px';
            statusQuestionButton.injectableElement.style.display = 'inline';
            statusQuestionButton.injectableElement.addEventListener('new-question-click', async () => {
                this.setLoading(true);
            });
            statusQuestionButton.injectableElement.addEventListener('new-question-open', async () => {
                this.setLoading(false);
            });
            statusQuestionButton.injectableElement.addEventListener('question-error', async () => {
                this.setLoading(false);
            });
            statusQuestionButton.injectableElement.addEventListener('new-quick-question-clicked', async () => {
                this.setLoading(true);
            });
            rowElementRight.appendChild(statusQuestionButton.injectableElement);

            // put everything together on the page
            rowElement.appendChild(rowElementLeft);
            rowElement.appendChild(rowElementRight);
            questionsList.appendChild(rowElement);
            dropdown.appendChild(questionsList);
        }

        return questionsList;
    }

    /**
     * Count matching questions
     */
    handleQuestionsCount() {
        let matchingQuestionsCount = 0;
        for (let index in this.possibleQuestions) {
            let question = this.possibleQuestions[index];
            let matchingQuestion = this.handleMatchingQuestion(this.questions, question);
            if (matchingQuestion && matchingQuestion.status !== 'deleted') {
                matchingQuestionsCount++;
            }
        }
        return matchingQuestionsCount;
    }

    /**
     * Questions Count element
     */
    getQuestionsCountElement(dropdown) {
        let questionsCount = document.createElement('a');
        questionsCount.setAttribute('class', 'hix-question-table-count');
        questionsCount.innerText = this.handleQuestionsCount();
        questionsCount.addEventListener('click', () => {
            if (dropdown.style.display === 'none') {
                dropdown.style.display = 'block';
            } else {
                dropdown.style.display = 'none';
            }
        });

        return questionsCount;
    }

    /**
     * Dropdown element
     */
    getDropdownElement(buttonContainer) {
        let dropdown = document.createElement('div');
        dropdown.setAttribute('class', 'hix-question-table-dropdown-list');
        dropdown.setAttribute('style', 'display:none;');

        document.addEventListener('click', function (event) {
            let isClickInsideElement = buttonContainer.contains(event.target);
            if (!isClickInsideElement && this.questions && this.questions.length > 0) {
                if (dropdown.style.display === 'block') {
                    dropdown.style.display = 'none';
                }
            }
        });

        return dropdown;
    }

    /**
     * Debounce for search
     * @param func
     * @param timeout
     * @returns {(function(...[*]): void)|*}
     */
    debounce(func, timeout = 300) {
        let timer;
        return (...args) => {
            clearTimeout(timer);
            timer = setTimeout(() => {
                func.apply(this, args);
            }, timeout);
        };
    }

    /**
     * Search input element
     * @returns {HTMLDivElement}
     */
    getSearchElement() {
        let searchInputWrapper = document.createElement('div');
        searchInputWrapper.classList.add('hix-question-table-dropdown-search-wrapper', 'hix-block');
        let searchInput = document.createElement('input');
        searchInput.setAttribute('type', 'text');
        searchInput.setAttribute('class', 'hix-question-table-dropdown-search-input');
        searchInput.setAttribute('placeholder', 'Search');

        searchInput.addEventListener('keyup', async (event) => {
            this.setLoading(true, false);
            const debounce = this.debounce(() => this.handleSearch(event.target.value.toLowerCase()));
            debounce();
        });

        searchInputWrapper.appendChild(searchInput);

        return searchInputWrapper;
    }

    handleSearch(search) {
        // search possible question list for results
        let searchList = this.possibleQuestions.filter(question => {
            return question.name.toLowerCase().indexOf(search) !== -1
                || (question.description && question.description.toLowerCase().indexOf(search) !== -1)
                || question.amount.indexOf(search.replace('.', ',')) !== -1
                || question.transaction_date.indexOf(search) !== -1
                || (question.bookkeeping_number && question.bookkeeping_number.indexOf(search) !== -1)
                || (question.invoice_number && question.invoice_number.indexOf(search) !== -1);
        });

        // handle question list elements
        let questionsCount = this._injectableElement.querySelector('.hix-question-table-count');
        let dropdown = this._injectableElement.querySelector('.hix-question-table-dropdown-list');
        dropdown.querySelector('.hix-question-table-questions-list').remove(); // remove list
        let questionList = this.handleQuestionElements(
            dropdown,
            searchList,
            this.questions,
            this.types
        );

        // add list again to dropdown
        dropdown.appendChild(questionList);

        this.setLoading(false, false);
    }

    /**
     * Loading element
     */
    getLoadingElement() {
        let loading = document.createElement('div');
        loading.classList.add('hix-question-table-loading-wrapper', 'hix-none');
        let loadingElement = document.createElement('div');
        loadingElement.setAttribute('class', 'hix-question-table-loading');
        loading.appendChild(loadingElement);

        return loading;
    }

    /**
     * Find matching question in created questions
     * @param questions
     * @param possibleQuestion
     * @returns {null}
     */
    handleMatchingQuestion(questions, possibleQuestion) {
        let matchingQuestion = null;
        if (questions) {
            // check if possible question has a match in created questions
            matchingQuestion = questions.find((question) => {
                let differentAttribute = false;
                for (let attribute of Object.keys(possibleQuestion)) {
                    // ignore validation for this attribute
                    if (this.ignoreAttributes.includes(attribute)) {
                        continue;
                    }

                    // question has no match
                    if (possibleQuestion[attribute] !== question[attribute]) {
                        differentAttribute = true;
                        break;
                    }
                }

                // no different attribute found, it's a match
                return differentAttribute === false && question.status !== 'deleted';
            });
        }

        return matchingQuestion;
    }

    /**
     * Toggling click listener on counter will open dropdown
     */
    toggleDropdown() {
        let questionCount = this._injectableElement.querySelector('.hix-question-table-count');
        questionCount.click();
    }

    /**
     * Close table dropdown
     */
    closeDropdown() {
        let dropdown = this._injectableElement.querySelector('.hix-question-table-dropdown-list');
        dropdown.style.display = 'none';
    }

    /**
     * Enable/disable loading including search element or not
     * @param loading
     * @param includeSearch
     */
    setLoading(loading, includeSearch = true) {
        let loadingWrapper = this._injectableElement.querySelector('.hix-question-table-loading-wrapper');
        let searchWrapper = this._injectableElement.querySelector('.hix-question-table-dropdown-search-wrapper');
        let questionsList = this._injectableElement.querySelector('.hix-question-table-questions-list');
        if (loading) {
            loadingWrapper.classList.remove('hix-none');
            loadingWrapper.classList.add('hix-block');
            questionsList.classList.remove('hix-block');
            questionsList.classList.add('hix-none');
            if (includeSearch) {
                searchWrapper.classList.remove('hix-block');
                searchWrapper.classList.add('hix-none');
            }
        } else {
            loadingWrapper.classList.remove('hix-block');
            loadingWrapper.classList.add('hix-none');
            questionsList.classList.remove('hix-none');
            questionsList.classList.add('hix-block');
            if (includeSearch) {
                searchWrapper.classList.remove('hix-none');
                searchWrapper.classList.add('hix-block');
            }
        }
    }

    /**
     * Open question history
     *
     * @param data
     * @returns {Promise<void>}
     */
    async openQuestionHistory(data) {
        let historyData = await this.openQuestionsCommunicationService.sendToBExtension('get_question_history', data);
        data['messages'] = historyData.messages;
        data['hostname'] = historyData.hostname;
        const historyPopup = new HistoryPopup(data);
        await historyPopup.init();
        historyPopup.injectableElement().addEventListener('question-updated', async () => {
            this._injectableElement.dispatchEvent(new Event('question-updated', {bubbles: true}));
        });

        const html = document.querySelector('html');
        html.appendChild(historyPopup.injectableElement());
    }

    get injectableElement() {
        return this._injectableElement;
    }
}

export {QuestionTableDropdown};