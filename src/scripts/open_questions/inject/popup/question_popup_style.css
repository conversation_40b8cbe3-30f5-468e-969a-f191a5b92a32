* {
  all: unset;
}

.full-screen {
  --border-color: rgba(0, 0, 0, 0.15);
  --sl-color: #0AAAEE;

  position: fixed;
  background-color: rgba(0,0,0,0.3);
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2147483647;

  font-family: Proxima Nova,sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #353535;
}

.full-screen .popup-container {
  width: 20em;
  background-color: white;
  padding: 1.5em;

  border: var(--border-color) 2px solid;
}

.header {
  display: block;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
}

.name-date-amount-container {
  display: flex;
  justify-content: space-between;
}

.name-date-amount-container .name-date-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.name-date-container .question-name {
  font-size: 14px;
  font-weight: bold;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.subtitle {
  font-size: 10px;
  opacity: 0.5;
}

.amount-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.amount-container .amount-text {
  white-space: nowrap;
  font-size: 14px;
  font-weight: bold;
}

.type-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.margin-top-1-5 {
  margin-top: 1.5em;
}

.input-label {
  font-weight: bold;
}

.dropdown {
  border: var(--border-color) solid 1px;
  border-radius: 0.5em;
  padding: 0.6em 1.5em 0.6em 1em;
  appearance: revert;
  cursor: pointer;
}

.note-client-container {
  display: flex;
  gap: 10px;
  flex-direction: column;
}

.note-client-container textarea {
  all: revert;
  border: var(--border-color) solid 2px;
  border-radius: 0.5em;
  resize: none;
  min-height: 6em;
  background: white;
}

.attachments-container {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
}
.attachments-container div {
  flex: 0 0 100%;
}
.attachments-container input {
  all: revert
}

.bottom-buttons-container {
  display: flex;
  justify-content: space-between;
}

.bottom-buttons-container button {
  padding: 1em;
  border-radius: 0.3em;
  cursor: pointer;
}

.save-button {
  background-color: var(--sl-color);
  color: white;
}

.cancel-button {
  color: var(--sl-color);
}

.cancel-button:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.attachment-container {
  margin-top: 0.5em;
  display: flex;
}

.attachment-container input {
  all: revert
}

.company-select-container {
  display: flex;
  padding-bottom: 1.2em;
  margin-bottom: 0.8em;
  border-bottom: var(--border-color) solid 1px;
}

.company-dropdown {
  width: 100%;
}

.company-dropdown:disabled {
  background: lightgrey;
}