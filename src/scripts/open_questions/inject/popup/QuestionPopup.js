import style from '!css-loader!./question_popup_style.css';
import {NotificationHelper} from "../../../tax/inject/helpers/NotificationHelper";
import {CurrencyHelper} from "../helpers/CurrencyHelper";
import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {trans} from "../../../helpers/TranslationHelper";
import browser from "webextension-polyfill";

class QuestionPopup {

    constructor() {
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
    }

    async init(data, category, types, companies) {
        const rootContainer = document.createElement('sl-vraagposten-question');
        const shadowRoot = rootContainer.attachShadow({mode: 'open'});

        const styleElement = document.createElement('style');
        styleElement.innerText = style;
        styleElement.style.display = "none";

        shadowRoot.appendChild(styleElement);

        // region Main container
        const rootDiv = document.createElement('div');
        rootDiv.classList.add('full-screen');
        shadowRoot.appendChild(rootDiv);
        //endregion

        // region Popup
        const popupContainer = document.createElement('div');
        popupContainer.classList.add('popup-container');
        rootDiv.appendChild(popupContainer);
        // endregion

        const header = document.createElement('div');
        header.classList.add('header');
        header.innerText = await trans('open_questions.new_question');
        popupContainer.appendChild(header);

        const formElement = document.createElement('form');

        popupContainer.appendChild(formElement);

        // region Company select
        const companySelectContainer = document.createElement('div');
        companySelectContainer.classList.add('company-select-container');
        formElement.appendChild(companySelectContainer);

        const companiesDropdown = document.createElement('select');
        companiesDropdown.classList.add('company-dropdown');
        companiesDropdown.classList.add('dropdown');
        companiesDropdown.setAttribute('name', 'company_id');
        companySelectContainer.appendChild(companiesDropdown);

        const selectOption = document.createElement('option');
        selectOption.textContent = await trans('open_questions.new_question_popup.select_company');
        selectOption.hidden = true;
        selectOption.selected = true;
        selectOption.disabled = true;
        companiesDropdown.appendChild(selectOption);

        let companyId = (await browser.storage.local.get(data.service_name + '_company_id'))[data.service_name + '_company_id'];
        if (companyId) {
            companyId = parseInt(companyId);
        }

        for (let company of companies) {
            const companyInputOption = document.createElement('option');
            companyInputOption.setAttribute('value', company.id);
            if (company.id === companyId) {
                selectOption.selected = false;
                companyInputOption.selected = true;
                companiesDropdown.disabled = true;

                const hiddenCompanyInput = document.createElement('input');
                hiddenCompanyInput.type = 'hidden';
                hiddenCompanyInput.setAttribute('name', 'company_id');
                hiddenCompanyInput.value = company.id;
                companySelectContainer.appendChild(hiddenCompanyInput);
            }
            companyInputOption.textContent = company.name;
            companiesDropdown.appendChild(companyInputOption);
        }

        // region Name With Amount
        const nameAmountContainer = document.createElement('div');
        nameAmountContainer.classList.add('name-date-amount-container');
        formElement.appendChild(nameAmountContainer);

        const nameDateContainer = document.createElement('div');
        nameDateContainer.classList.add('name-date-container');
        nameAmountContainer.appendChild(nameDateContainer);

        const nameElement = document.createElement('span');
        nameElement.classList.add('question-name');
        nameElement.textContent = data.title ? data.title : data.name;
        nameDateContainer.appendChild(nameElement);

        const dateElement = document.createElement('span');
        dateElement.classList.add('subtitle');
        dateElement.textContent = data.transaction_date;
        nameDateContainer.appendChild(dateElement);

        if (data.amount && data.currency) {
            let currencySymbol = CurrencyHelper.convertToSymbol(data.currency);
            let amountText = currencySymbol + ' ' + data.amount;

            const amountContainer = document.createElement('div');
            amountContainer.classList.add('amount-container');
            nameAmountContainer.appendChild(amountContainer);

            const amountTextElement = document.createElement('span');
            amountTextElement.classList.add('amount-text');
            amountTextElement.textContent = amountText;
            amountContainer.appendChild(amountTextElement);

            if (data.invoice_number) {
                const invoiceNumberTextElement = document.createElement('span');
                invoiceNumberTextElement.classList.add('subtitle');
                invoiceNumberTextElement.textContent = data.invoice_number;
                amountContainer.appendChild(invoiceNumberTextElement);
            }
        }

        // region Type dropdown
        const typeContainer = document.createElement('div');
        typeContainer.classList.add('type-container');
        typeContainer.classList.add('margin-top-1-5');
        formElement.appendChild(typeContainer);

        const typeLabel = document.createElement('label');
        typeLabel.classList.add('input-label');
        typeLabel.textContent = await trans('common.type');
        typeLabel.setAttribute('for', 'type');
        typeContainer.appendChild(typeLabel);

        const typeDropdown = document.createElement('select');
        typeDropdown.classList.add('type-dropdown');
        typeDropdown.classList.add('dropdown');
        typeDropdown.setAttribute('name', 'type_id');
        typeContainer.appendChild(typeDropdown);

        for (let typeInput of types) {
            const typeInputOption = document.createElement('option');
            typeInputOption.setAttribute('value', typeInput.id);
            typeInputOption.setAttribute('data-key', typeInput.key);
            typeInputOption.setAttribute('data-fileupload', false);
            let preferences = typeInput.preferences;
            if (preferences && preferences.hasOwnProperty('file_upload_required')) {
                typeInputOption.setAttribute('data-fileupload', preferences.file_upload_required);
            }
            typeInputOption.textContent = typeInput.name;
            if (data.selected_type && data.selected_type === typeInput.key) {
                typeInputOption.selected = true;
            }
            typeDropdown.appendChild(typeInputOption);
        }

        // endregion

        // region Missing invoice type dropdown
        const missingInvoiceTypeContainer = document.createElement('div');
        missingInvoiceTypeContainer.classList.add('type-container');
        missingInvoiceTypeContainer.classList.add('margin-top-1-5');
        if (typeDropdown.options[0].dataset.key !== 'missing_invoice') {
            missingInvoiceTypeContainer.style.display = 'none';
        }
        formElement.appendChild(missingInvoiceTypeContainer);

        const missingInvoiceTypeLabel = document.createElement('label');
        missingInvoiceTypeLabel.classList.add('input-label');
        missingInvoiceTypeLabel.textContent = await trans('open_questions.new_question_popup.missing_invoice_type');
        missingInvoiceTypeLabel.setAttribute('for', 'type');
        missingInvoiceTypeContainer.appendChild(missingInvoiceTypeLabel);

        const missingInvoiceTypeDropdown = document.createElement('select');
        missingInvoiceTypeDropdown.classList.add('type-dropdown');
        missingInvoiceTypeDropdown.classList.add('dropdown');
        missingInvoiceTypeDropdown.setAttribute('name', 'missing_invoice_type');
        missingInvoiceTypeContainer.appendChild(missingInvoiceTypeDropdown);

        let missingInvoiceTypes = await trans('open_questions.missing_invoice_types');
        for (let missingInvoiceType of Object.keys(missingInvoiceTypes)) {
            const missingInvoiceTypeInputOption = document.createElement('option');
            missingInvoiceTypeInputOption.setAttribute('value', missingInvoiceType);
            missingInvoiceTypeInputOption.setAttribute('data-key', missingInvoiceType);
            missingInvoiceTypeInputOption.textContent = missingInvoiceTypes[missingInvoiceType];
            missingInvoiceTypeDropdown.appendChild(missingInvoiceTypeInputOption);
        }

        // endregion

        //region Note to client
        const noteToClientContainer = document.createElement('div')
        noteToClientContainer.classList.add('note-client-container')
        noteToClientContainer.classList.add('margin-top-1-5');
        formElement.appendChild(noteToClientContainer)

        const clientNoteLabel = document.createElement('label');
        clientNoteLabel.classList.add('input-label');
        clientNoteLabel.textContent = await trans('open_questions.new_question_popup.note');
        clientNoteLabel.setAttribute('for', 'internal_note');
        noteToClientContainer.appendChild(clientNoteLabel);

        const clientNoteTextField = document.createElement('textarea');
        clientNoteTextField.classList.add('note-textarea');
        clientNoteTextField.setAttribute('name', 'internal_note');
        clientNoteTextField.setAttribute('maxlength', '1000');
        noteToClientContainer.appendChild(clientNoteTextField);

        // endregion

        // region Checkbox
        const attachmentChoiceContainer = document.createElement('div');
        attachmentChoiceContainer.classList.add('attachment-container');
        formElement.appendChild(attachmentChoiceContainer);

        const checkbox = document.createElement('input');
        checkbox.setAttribute('type', 'checkbox');
        checkbox.setAttribute('name', 'attachment_needed');
        if (types && types.length) {
            if (types[0].preferences
                && types[0].preferences.hasOwnProperty('file_upload_required')
                && types[0].preferences.file_upload_required === true) {
                checkbox.setAttribute('checked', true);
            }
        }
        if (['missing_invoice', 'private_or_business'].includes(types[0].key)) {
            checkbox.disabled = true;
        }
        attachmentChoiceContainer.appendChild(checkbox);

        const attachmentLabel = document.createElement('label');
        attachmentLabel.setAttribute('for', 'attachment_needed');
        attachmentLabel.textContent = await trans('open_questions.new_question_popup.attachment_requested');
        attachmentChoiceContainer.appendChild(attachmentLabel);

        // endregion

        // region Bottom buttons
        const bottomButtonsContainer = document.createElement('div');
        bottomButtonsContainer.classList.add('bottom-buttons-container');
        bottomButtonsContainer.classList.add('margin-top-1-5');
        formElement.appendChild(bottomButtonsContainer);

        const saveButton = document.createElement('button');
        saveButton.setAttribute('role', 'submit');
        saveButton.classList.add('save-button');
        saveButton.textContent = await trans('common.save');
        // saveButton.onclick = function () {
        //     rootContainer.style.display = "none";
        // }
        bottomButtonsContainer.appendChild(saveButton);

        const cancelButton = document.createElement('button');
        cancelButton.classList.add('cancel-button');
        cancelButton.textContent = await trans('common.cancel');
        cancelButton.type = 'button';
        cancelButton.onclick = function () {
            rootContainer.remove()
        }
        bottomButtonsContainer.appendChild(cancelButton);

        // endregion

        formElement.addEventListener('submit', async ev => {
            ev.preventDefault();
            saveButton.disabled = true;
            let checkBoxDisabled = checkbox.disabled
            checkbox.disabled = false;
            const formData = new FormData(formElement);
            checkbox.disabled = checkBoxDisabled;
            let userFormData = await this.handleFormDataByCategory(formData, data, category, types);
            if (userFormData['company_id']) {
                this.openQuestionsCommunicationService.sendToBExtension('create_open_question', {
                    data: userFormData,
                    category: category
                }).then(async () => {
                    NotificationHelper.sendToast('success', await trans('open_questions.question_created'));
                    this._injectableElement.dispatchEvent(new Event('question-updated', {bubbles: true}));
                }).catch(e => {
                    console.log(e)
                    NotificationHelper.sendGenericError();
                    this._injectableElement.dispatchEvent(new Event('question-error', {bubbles: true}));
                });
                rootContainer.remove();
            } else {
                saveButton.disabled = false;
                this._injectableElement.dispatchEvent(new Event('question-error', {bubbles: true}));
                NotificationHelper.sendToast('error', await trans('open_questions.new_question_popup.company_required'));
            }
        });

        typeDropdown.addEventListener("keyup", function (event) {
            // keyCode is deprecated, so we use event.code
            if (event.code === 'Enter') {
                // Cancel the default action, if needed
                event.preventDefault();
                saveButton.click();
            }
        });

        let _this = this;
        typeDropdown.onchange = function () {
            _this.setFieldValuesForType(
                this.options[this.selectedIndex].dataset.key,
                this.options[this.selectedIndex].dataset.fileupload,
                checkbox,
                missingInvoiceTypeContainer
            );
        };

        this._injectableElement = rootContainer;
    }

    setFieldValuesForType(key, preference, checkbox, missingInvoiceTypeContainer) {
        checkbox.checked = preference === 'true' || preference === true;
        if (key === 'private_or_business') {
            checkbox.checked = false;
        }

        if (key === 'missing_invoice') {
            checkbox.checked = true;
            missingInvoiceTypeContainer.style.display = 'flex';
        } else {
            missingInvoiceTypeContainer.style.display = 'none';
        }
        checkbox.disabled = ['missing_invoice', 'private_or_business'].includes(key);
    }

    /**
     * Download each url in attachments
     * @param data
     * @returns {Promise<*[]>}
     */
    async handleAttachmentsDownload(data) {
        if (data.attachments === undefined) {
            return [];
        }
        let attachments = [];
        for (let attachment of data.attachments) {
            let response = await this.openQuestionsCommunicationService.sendToBExtension('download_file', {
                url: attachment
            });
            attachments.push(response);
        }
        return attachments;
    }

    /**
     * TODO: this should be handled by each service (nmbrs, basecone, etc)
     *
     * Handle shown data in popup by category
     * @param formData
     * @param data
     * @param category
     * @param types
     * @returns Object
     */
    async handleFormDataByCategory(formData, data, category, types) {

        let attachments = await this.handleAttachmentsDownload(data);

        // get form values
        formData = Object.fromEntries(formData);

        if (!(await browser.storage.local.get(data.service_name + '_company_id'))[data.service_name + '_company_id'] && formData['company_id']) {
            await browser.storage.local.set({[data.service_name + '_company_id']: formData['company_id']});
        }

        let type = types.find(t => t.id === parseInt(formData['type_id']));

        if (type.key === 'missing_invoice') {
            data.public_url = null;
        }

        switch (category) {
            case 'bookkeeping':
                let payload = {
                    service_name: data.service_name,
                    type_id: formData['type_id'],
                    internal_note: formData['internal_note'],
                    name: data.name,
                    company_id: formData['company_id'],
                    amount: data.amount,
                    external_company_id: (await browser.storage.local.get(data.service_name + '_external_company_id'))[data.service_name + '_external_company_id'],
                    external_company_name: (await browser.storage.local.get(data.service_name + '_external_company_name'))[data.service_name + '_external_company_name'],
                    currency: data.currency,
                    transaction_date: data.transaction_date,
                    title: data.name,
                    invoice_number: data.invoice_number,
                    bookkeeping_number: data.bookkeeping_number,
                    description: data.description,
                    attachment_needed: formData['attachment_needed'] === 'on',
                    public_url: types.find((type) => parseInt(type.id) === parseInt(formData['type_id'])).key !== 'missing_invoice' ? data.public_url : null,
                    page_title: data.page_title,
                    page_url: data.page_url,
                    attachments: attachments
                };
                if (type.key === 'missing_invoice') {
                    payload.missing_invoice_type = formData['missing_invoice_type'];
                }
                return payload;
            case 'ocr':
                let title = data.title;
                if (!title) {
                    title = data.name;
                }

                return {
                    service_name: data.service_name,
                    type_id: formData['type_id'],
                    company_id: formData['company_id'],
                    internal_note: formData['internal_note'],
                    attachment_needed: formData['attachment_needed'] === 'on',
                    title: title,
                    invoice_number: data.invoice_number,
                    currency: data.currency,
                    transaction_date: data.transaction_date,
                    description: data.description,
                    amount: data.amount,
                    external_company_name: data.external_company_name,
                    external_company_id: data.external_company_id,
                    created_on_date: data.created_on_date,
                    public_url: data.public_url,
                    page_title: data.page_title,
                    page_url: data.page_url,
                    attachments: attachments
                };
            case 'fiscal':
                return {
                    service_name: data.service_name,
                    type_id: formData['type_id'],
                    company_id: formData['company_id'],
                    internal_note: formData['internal_note'],
                    attachment_needed: formData['attachment_needed'] === 'on',
                    title: data.title === null ? types.find((type) => parseInt(type.id) === parseInt(formData['type_id'])).name : data.title,
                    subtitle: data.subtitle,
                    external_company_name: data.external_company_name,
                    external_company_id: data.external_company_id,
                    page_title: data.page_title,
                    page_url: data.page_url
                };
            case 'wage':
                return {
                    service_name: data.service_name,
                    type_id: formData['type_id'],
                    company_id: formData['company_id'],
                    internal_note: formData['internal_note'],
                    attachment_needed: formData['attachment_needed'] === 'on',
                    title: types.find((type) => parseInt(type.id) === parseInt(formData['type_id'])).name,
                    subtitle: data.subtitle,
                    external_company_name: data.external_company_name,
                    external_company_id: data.external_company_id,
                    page_title: data.page_title,
                    page_url: data.page_url
                };
            case 'declaration':
                return {
                    service_name: data.service_name,
                    type_id: formData['type_id'],
                    company_id: formData['company_id'],
                    internal_note: formData['internal_note'],
                    attachment_needed: formData['attachment_needed'] === 'on',
                    title: data.title === null ? types.find((type) => parseInt(type.id) === parseInt(formData['type_id'])).name : data.title,
                    subtitle: data.subtitle,
                    external_company_name: data.external_company_name,
                    external_company_id: data.external_company_id,
                    page_title: data.page_title,
                    page_url: data.page_url,
                    description: data.description,
                    external_id: data.declaration_id,
                };
        }
    }

    get injectableElement() {
        return this._injectableElement;
    }
}

export {QuestionPopup};