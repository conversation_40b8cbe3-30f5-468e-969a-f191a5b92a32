import style from '!css-loader!./question_dropdown_style.css';
import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {HistoryPopup} from "../history_popup/HistoryPopup";

class QuestionDropdown {
    constructor(serviceData) {
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
        this.serviceData = serviceData;
        this._injectableElement = this.handleDropdown();
    }

    handleDropdown() {
        // button container
        let buttonContainer = document.createElement('div');
        buttonContainer.setAttribute('class', 'sl-question-dropdown-container');
        const styleElement = document.createElement('style');
        styleElement.innerText = style;
        buttonContainer.appendChild(styleElement);

        // questions count
        let questionsCount = document.createElement('a');
        questionsCount.innerText = 0;

        // dropdown will have questions
        let dropdown = document.createElement('div');
        dropdown.setAttribute('class', 'sl-question-dropdown-list');
        dropdown.setAttribute('style', 'display:none;');
        questionsCount.addEventListener('click', function () {
            if (dropdown.style.display === 'none') {
                dropdown.style.display = 'block';
            } else {
                dropdown.style.display = 'none';
            }
        });
        document.addEventListener('click', function (event) {
            var isClickInsideElement = buttonContainer.contains(event.target);
            if (!isClickInsideElement) {
                if (dropdown.style.display === 'block') {
                    dropdown.style.display = 'none';
                }
            }
        });

        // handle questions
        let self = this;
        this.getQuestions().then(function (questions) {
            if (questions.length < 100) {
                if (questions.length === 0) {
                    return;
                }
                questionsCount.innerText = questions.length;
            } else {
                questionsCount.innerText = '99+';
            }

            questions.forEach(function (question) {
                // each element will open question history by id
                let dropdownElement = document.createElement('div');
                dropdownElement.addEventListener('click', function (event) {
                    self.openQuestionHistory({
                        service_name: self.serviceData.service_name,
                        id: question.id,
                        name: question.name,
                        status: question.status,
                        type_text: question.type_text
                    });

                    // close dropdown
                    dropdown.style.display = 'none';
                });

                // left part for dropdown element
                let dropdownElementLeft = document.createElement('div');
                dropdownElementLeft.setAttribute('class', 'sl-question-dropdown-list-left');
                let titleElement = document.createElement('div');
                titleElement.setAttribute('class', 'sl-question-dropdown-list-title');
                titleElement.innerText = question.name;

                let dateElement = document.createElement('div');
                dateElement.setAttribute('class', 'sl-question-dropdown-list-date');
                dateElement.innerText = question.subtitle;

                dropdownElementLeft.appendChild(titleElement);
                dropdownElementLeft.appendChild(dateElement);

                // right part for dropdown element
                let dropdownElementRight = document.createElement('div');
                dropdownElementRight.setAttribute('class', 'sl-question-dropdown-list-right');
                let statusElement = document.createElement('div');
                statusElement.setAttribute('class', 'sl-question-dropdown-list-status');
                statusElement.setAttribute('data-status', question.status);
                statusElement.innerText = question.status_text;
                dropdownElementRight.appendChild(statusElement);

                // put everything together on the page
                dropdownElement.appendChild(dropdownElementLeft);
                dropdownElement.appendChild(dropdownElementRight);
                dropdown.appendChild(dropdownElement);
            });
        });

        buttonContainer.appendChild(questionsCount);
        buttonContainer.appendChild(dropdown);

        return buttonContainer;
    }

    /**
     * Open question history
     *
     * @param data
     * @returns {Promise<void>}
     */
    async openQuestionHistory(data) {
        let historyData = await this.openQuestionsCommunicationService.sendToBExtension('get_question_history', data);
        data['messages'] = historyData.messages;
        data['hostname'] = historyData.hostname;
        const historyPopup = new HistoryPopup(data);
        await historyPopup.init();
        const body = document.querySelector('body');

        let self = this;
        historyPopup.injectableElement().addEventListener('question-updated', async () => {
            self._injectableElement.dispatchEvent(new Event('question-updated', {bubbles: true}));
        });

        body.appendChild(historyPopup.injectableElement());
    }

    /**
     * Get questions for dropdown
     * @returns {Promise<*>}
     */
    async getQuestions() {
        return await this.openQuestionsCommunicationService.sendToBExtension('get_questions_for_company', this.serviceData);
    }

    get injectableElement() {
        return this._injectableElement;
    }
}

export {QuestionDropdown};