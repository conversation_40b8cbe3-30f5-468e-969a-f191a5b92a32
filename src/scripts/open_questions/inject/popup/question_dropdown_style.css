.sl-question-dropdown-container a {
    display: block;
    width: 20px;
    height: 20px;
    border-radius: 25px;
    background-color: #FF0000 !important;
    color: white !important;
    text-align: center;
    font-size: 12px;
    line-height: 20px;
    text-decoration: none;
    cursor: pointer;
    margin-top: 3px;
}

.sl-question-dropdown-list {
    border: 2px solid #e8e8e8;
    border-radius: 6px;
    width: 300px;
    top: 24px;
    position: absolute;
    background: white;
    max-height: 300px;
    overflow-y: auto;
    z-index: 999999999999;
}

.sl-question-dropdown-list > div {
    cursor: pointer;
    justify-content: center;
    display: flex;
    flex-wrap: wrap;
    font-family: "Proxima Nova", sans-serif;
    padding: 6px 10px;
    align-items: center;
    line-height: 1.2;
}

.sl-question-dropdown-list > div:hover {
    background: #fbfbfb;
}

.sl-question-dropdown-list-left {
    width: 70%;
    text-align: left;
}

.sl-question-dropdown-list-right {
    width: 30%;
    text-align: right;
}

.sl-question-dropdown-list-title {
    color: black;
    font-weight: 500;
    font-size: 12px;
    padding-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sl-question-dropdown-list-date {
    color: #949494;
    font-size: 10px;
}

.sl-question-dropdown-list-status {
    text-align: right;
    font-size: 10px;
}

.sl-question-dropdown-list-status[data-status="open"] {
    color: #ff470f;
}

.sl-question-dropdown-list-status[data-status="on_hold"] {
    color: #ff470f;
}

.sl-question-dropdown-list-status[data-status="pending"] {
    color: #ffd028;
}

.sl-question-dropdown-list-status[data-status="completed"] {
    color: #23d160;
}