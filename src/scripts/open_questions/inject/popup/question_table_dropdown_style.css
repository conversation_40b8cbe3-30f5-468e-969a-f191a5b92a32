.hix-question-table-count {
    display: block;
    width: 20px;
    height: 20px;
    border-radius: 25px;
    background-color: #FF0000 !important;
    color: white !important;
    text-align: center;
    font-size: 12px;
    line-height: 20px;
    text-decoration: none;
    cursor: pointer;
    margin-top: 3px;
}

.hix-question-table-dropdown-list {
    border: 2px solid #e8e8e8;
    border-radius: 6px;
    width: 400px;
    top: 24px;
    position: absolute;
    background: white;
    max-height: 300px;
    overflow-y: auto;
    z-index: 999999999999;
}

.hix-question-table-dropdown-list > div:first-child {
    padding: 6px 10px;
}

.hix-question-table-dropdown-search-input {
    width: 100%;
    margin: 4px 0;
    padding: 4px;
}

.hix-question-table-loading-wrapper {
    display: none;
    padding: 30px 0;
}

.hix-none {
    display: none;
}

.hix-block {
    display: block;
}

.hix-question-table-loading,
.hix-question-table-loading:after {
    border-radius: 50%;
    width: 1px;
    height: 1px;
}
.hix-question-table-loading {
    margin: 0 auto;
    font-size: 6px;
    position: relative;
    text-indent: -9999em;
    border-top: 1.1em solid #06aff8;
    border-right: 1.1em solid #06aff8;
    border-bottom: 1.1em solid #06aff8;
    border-left: 1.1em solid #ffffff;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation: load8 1.1s infinite linear;
    animation: load8 1.1s infinite linear;
}
@-webkit-keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.hix-question-table-questions-list > div {
    cursor: pointer;
    justify-content: center;
    display: flex;
    flex-wrap: wrap;
    font-family: Verdana, sans-serif;
    padding: 6px 10px;
    align-items: center;
    line-height: 1.2;
}

.hix-question-table-questions-list > div:hover {
    background: #fbfbfb;
}

.hix-question-table-dropdown-list-left {
    width: 65%;
    text-align: left;
}

.hix-question-table-dropdown-list-right {
    width: 35%;
    text-align: right;
    font-size: 12px;
}

.hix-question-table-dropdown-list-title {
    color: black;
    font-weight: 500;
    font-size: 12px;
    padding-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.hix-question-table-dropdown-list-date {
    color: #949494;
    font-size: 10px;
}
