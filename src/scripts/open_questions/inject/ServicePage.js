import {OpenQuestionsCommunicationService} from "./OpenQuestionsCommunicationService";
import browser from "webextension-polyfill";
import {SelectorHelper} from "./helpers/SelectorHelper";

class ServicePage {
    constructor(demo) {
        this.demo = demo;
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
        this.selectorHelper = new SelectorHelper();
    }

    /**
     * Check if company is blocked
     * @returns {Promise<boolean|*>}
     */
    async isCompanyBlocked(serviceName, externalCompanyId, externalCompanyName) {
        if (!externalCompanyId || !externalCompanyName) {
            return true;
        }
        let response = await this.openQuestionsCommunicationService.sendToBExtension(
            'get_hix_company',
            {
                'service_name': serviceName,
                'company_id': externalCompanyId,
                'external_company_name': externalCompanyName
            }
        );

        if (response) {
            if (response.company_is_blocked || !response.visible) {
                console.log('Company is blocked');
                return true;
            }
        }

        return false;
    }

    /**
     * Check if logged user is able to block companies
     * @returns {Promise<boolean|*>}
     */
    async isCompanyManager() {
        let user = await this.openQuestionsCommunicationService.sendToBExtension('get_user_profile');
        if (user) {
            return user.is_company_manager || user.is_account_manager;
        }

        return false;
    }

    /**
     * Service name
     * @returns {string}
     */
    getServiceName() {
        return '';
    }

    /**
     * Service category
     * @returns {string}
     */
    getServiceCategory() {
        return '';
    }

    /**
     * Get External Company Id
     * @returns {string}
     */
    getCompanyId() {
        return '';
    }

    /**
     * Get Company Name
     * @returns {string}
     */
    getCompanyName() {
        return '';
    }

    /**
     * Get Questions
     * @returns {Promise<*>}
     */
    async getQuestions() {
        return this.openQuestionsCommunicationService.sendToBExtension('get_questions_by_company_id', {
            'category': this.getServiceCategory(),
            'service_name': this.getServiceName(),
            'company_id': (await browser.storage.local.get(this.getServiceName() +'_company_id'))[this.getServiceName() + '_company_id']
        });
    }

    /**
     * Get Question types
     * @returns {Promise<*>}
     */
    async getQuestionTypes() {
        return this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
            category: this.getServiceCategory(),
            serviceName: this.getServiceName()
        });
    }

    /**
     * When question is updated, update cornerstone
     */
    async onQuestionUpdated() {
        await this.openQuestionsCommunicationService.sendToBExtension(
            'questions_updated',
            {
                'companyId': (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'],
                'companyCode': (await browser.storage.local.get(this.getServiceName() + '_external_company_code'))[this.getServiceName() + '_external_company_code'],
                'companyName': (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name'],
                'serviceName': this.getServiceName()
            }
        );
    }

    /**
     * Append secure login corner button
     * @returns {Promise<*>}
     */
    async appendCornerStone() {
        if (this.demo) {
            console.log('Cornerstone: Demo mode on');
            return;
        }
        await browser.storage.local.set({[this.getServiceName() + '_external_company_id']: this.getCompanyId()});
        await browser.storage.local.set({[this.getServiceName() + '_external_company_name']: this.getCompanyName()});
        await browser.storage.local.set({[this.getServiceName() + '_external_company_code']: this.getCompanyId()});

        await this.onQuestionUpdated();
    }

    async getTypes() {
        return this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
            category: this.getServiceCategory(),
            serviceName: this.getServiceName()
        });
    }

    /**
     * Create a list of columns selectors from table header
     * Example:
     *
     * {
     *     "name": "td:nth-child(3)"
     * }
     *
     * @param table
     * @returns {{}}
     */
    getColumnsSelectorsFromTableHeader(table) {
        let requiredColumns = {};
        let headerColumns = document.querySelectorAll(table.header.columns);
        if (headerColumns) {
            for (let i = 0; i < headerColumns.length; i++) {
                // get each language and each required field
                let headerColumnFields = table.header.fields;
                for (let language of Object.keys(headerColumnFields)) {
                    for (let field of Object.keys(headerColumnFields[language])) {
                        // if required column found, save css selector to column (example: "Reference" = name)
                        if (headerColumns[i].innerText.trim() === headerColumnFields[language][field]) {
                            requiredColumns[field] = [table.columns.column + ":nth-child(" + (parseInt(i) + 1) + ")"];
                        }
                    }
                }
            }
        }

        return requiredColumns;
    }
}

export {ServicePage}