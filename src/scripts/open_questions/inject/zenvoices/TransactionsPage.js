import {BasePage} from "./BasePage";
import axios from "axios";

class TransactionsPage extends BasePage
{
    constructor(selectors, demo) {
        super(selectors, demo);
        this.attributesIndexes = {
            name: -1,
            description: -1,
            amount: -1,
            invoice_number: -1,
            transaction_date: -1,
        };
    }
    async handle() {
        this.selectorHelper.waitForElement(this.selectors.table.header.selector).then(async () => {
            if (!this.demo && this.reportIsValid()) {
                this.getHeaderIndexes();
                await this.appendColumn();
                const searchButtons = document.querySelectorAll(this.selectors.table.search_buttons);
                for (let searchButton of searchButtons) {
                    searchButton.addEventListener('click', (e) => {
                        this.handleModal()
                    });
                }
            }
        });
    }

    handleModal() {
        this.selectorHelper.waitForElement(this.selectors.modal.selector).then(async (modal) => {
            this.questions = await this.getQuestions();
            this.appendButtonToModal(modal);

            modal.addEventListener('click', async (e) => {
                if (!e.target.classList.contains(this.selectors.modal.close)) {
                    this.appendButtonToModal(modal);
                }
            });

            modal.querySelector('button.' + this.selectors.modal.close).addEventListener('click', async (e) => {
                this.questions = await this.getQuestions();
                this.removeColumns();
                await this.handleRows();
            }, {once: true});
        });
    }

    removeColumns() {
        let hixColumns = document.querySelectorAll('.hix-table-columns');
        for (let column of hixColumns) {
            column.remove();
        }
    }

    appendButtonToModal(modal) {
        setTimeout(() => {
            let parentElement =  modal.querySelector(this.selectors.modal.buttons_bar);
            if (parentElement.childNodes.length && parentElement.childNodes.length < 4) {
                for (const [index, element] of Object.entries(this.selectors.modal.button.elements)) {
                    let container = document.createElement(element.type);
                    container.classList.add(...element.classes);
                    if (index === '0') {
                        parentElement.prepend(container);
                    } else {
                        parentElement.appendChild(container);
                    }
                    parentElement = container;
                }
                let index = modal.querySelector(this.selectors.modal.index).innerText.split('/')[0];
                const data = this.questionButtons[index - 1];
                const transactionData = {
                    "name": data.name,
                    "transaction_date": data.transaction_date,
                    "amount": data.amount,
                    "invoice_number": data.invoice_number,
                    "description": data.description
                }
                let matchingQuestion = this.handleMatchingQuestion(transactionData);
                this.handleNewQuestionButton(parentElement, data, matchingQuestion);
            }
        }, 1000);
    }

    getHeaderIndexes() {
        let headerColumns = document.querySelectorAll(this.selectors.table.header.columns_selector);
        if (headerColumns.length > 0) {
            let headers = this.selectors['headers_' + this.lang];
            for (const [key, value] of Object.entries(headers)) {
                for (let i = 0; i < headerColumns.length; i++) {
                    if (headerColumns[i].textContent.trim() === value) {
                        this.attributesIndexes[key] = i;
                        break;
                    }
                }
            }
        }
    }

    getRowData(row) {
        let data = {};
        for (let [attribute, index] of Object.entries(this.attributesIndexes)) {
            let field = '';
            let textElement = row.querySelector('td:nth-child(' + (index + 1) + ')');
            if (textElement) {
                field = textElement.innerText.trim();
            }
            data[attribute] = field.trim();
        }
        if (data.name === '') {
            data.name = data.invoice_number;
        }
        const amountAndCurrency = data.amount.split(' ');
        data.currency = amountAndCurrency[0].trim();
        data.amount = amountAndCurrency[1].trim();
        return data;
    }

    async getAttachments(row, rowData) {
        const transactionId = row.querySelector(this.selectors.transaction_id).id.match(/\d+/)[0];
        let documentData = await axios.post(this.selectors.document_url, {id: transactionId});
        rowData.attachments = [];
        if (documentData.data && documentData.data.result && documentData.data.result.financialTransaction) {
            for (let attachment of documentData.data.result.financialTransaction.attachments) {
                rowData.attachments.push(this.selectors.download_attachment_url + attachment.storageId)
            }
        }
        return rowData;
    }
}

export {TransactionsPage}