import {ServicePage} from "../ServicePage";
import {trans} from "../../../helpers/TranslationHelper";
import {NewStatusQuestion} from "../buttons/NewStatusQuestion";
import browser from "webextension-polyfill";
import axios from "axios";

class BasePage extends ServicePage {
    constructor(selectors, demo) {
        super();
        this.selectors = selectors;
        this.demo = demo;
        this.lang = 'nl';
        this.questionButtons = [];
    }

    async handle() {
        this.selectorHelper.waitForElement(this.selectors.table.header.selector).then(async () => {
            if (!this.demo && this.reportIsValid()) {
                await this.appendColumn();
            }
        });
    }

    reportIsValid() {
        const requiredColumnsNl = this.selectors.required_columns_nl;
        const requiredColumnsEn = this.selectors.required_columns_en;
        const columns = document.querySelectorAll(this.selectors.table.header.columns_selector);

        if (requiredColumnsNl.every((column) => {
            return Object.values(columns).some((cell) => {
                return column === cell.textContent.trim();
            });
        })) {
            this.lang = 'nl';
            return true;
        } else if (requiredColumnsEn.every((column) => {
            return Object.values(columns).some((cell) => {
                return column === cell.textContent.trim();
            });
        })) {
            this.lang = 'en';
            return true;
        }

        return false;
    }

    async appendColumn() {
        const header = document.querySelector(this.selectors.table.header.selector);
        const table = header.closest('table');
        const colgroup = table.childNodes[0];

        table.addEventListener('click', async (e) => {
            // This timeout is needed because it takes a while until the elements are loaded
            setTimeout(async () => {
                await this.handleRows();
                colgroup.childNodes[0].style.width = '150px';
            }, 1000);
        });

        const col = document.createElement('col');
        col.style.width = '150px';
        colgroup.prepend(col);

        let tdElement = document.createElement('td');
        tdElement.innerText = await trans('open_questions.title');
        tdElement.classList.add('hix-table-header');
        header.prepend(tdElement);

        this.external_company_id = (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'];
        this.external_company_name = (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name'];
        await this.appendNewQuestionButtons();
    }

    async appendNewQuestionButtons() {
        if (this.demo === false) {
            this.questions = await this.getQuestions();
            this.types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                category: this.getServiceCategory(),
                serviceName: this.getServiceName()
            });
        }
        await this.handleRows();
    }

    async handleRows() {
        const rows = document.querySelectorAll(this.selectors.table.rows.selector);
        if (rows.length > 0 && rows[0].cells[0].className !== 'hix-table-columns' ) {
            this.questionButtons = [];
            const table = rows[0].closest('table');
            const colgroup = table.childNodes[0];

            const col = document.createElement('col');
            col.style.width = '150px';
            colgroup.prepend(col);

            for (const row of rows) {
                let rowData = this.getRowData(row);
                let matchingQuestion = this.handleMatchingQuestion(rowData);

                rowData.service_name = this.getServiceName();
                rowData.category = this.getServiceCategory();
                rowData.external_company_id = this.external_company_id;
                rowData.external_company_name = this.external_company_name;
                rowData.page_title = this.getPageTitle();
                rowData.page_url = window.location.href;

                rowData = await this.getAttachments(row, rowData);

                const td = document.createElement('td');
                td.classList.add('hix-table-columns');
                td.classList.add('middleAlign');
                row.prepend(td);
                this.handleNewQuestionButton(td, rowData, matchingQuestion);
            }
        }
    }
    getRowData(row) {
        let data = {};
        for (let [attribute, selector] of Object.entries(this.selectors.table.columns)) {
            let field = '';
            let textElement = row.querySelector(selector);
            if (textElement) {
                field = textElement.title.trim();
            }
            data[attribute] = field.trim();
        }

        return data;
    }

    async getAttachments(row, rowData) {
        const transactionId = row.querySelector(this.selectors.transaction_id).id.match(/\d+/)[0];
        let documentUrl = this.selectors.document_url + transactionId;
        let documentData = await axios.get(documentUrl);
        rowData.attachments = [];
        if (documentData.data && documentData.data.result && documentData.data.result.attachments) {
            for (let attachment of documentData.data.result.attachments) {
                rowData.attachments.push(this.selectors.download_attachment_url + attachment.storageId)
            }
        }
        return rowData;
    }

    handleMatchingQuestion(data) {
        let matchingQuestion = null;
        if (this.questions) {
            matchingQuestion = this.questions.find((question) => {
                let differentAttribute = false;
                for (let attribute of Object.keys(data)) {
                    let dataAttribute = data[attribute].replace(/[\r\n]+/gm,"");
                    let questionAttribute = question[attribute].replace(/[\r\n]+/gm,"");

                    if (attribute === 'transaction_date') {
                        dataAttribute = new Date(dataAttribute).toDateString();
                        questionAttribute = new Date(questionAttribute).toDateString();
                    }
                    if (attribute === 'amount') {
                        dataAttribute = dataAttribute.replace('.', '').replace(',', '');
                        questionAttribute = questionAttribute.replace('.', '').replace(',', '');
                    }

                    // question has no match with given row
                    if (dataAttribute !== questionAttribute) {
                        differentAttribute = true;
                        break;
                    }
                }

                // no different attribute found, it's a match
                return differentAttribute === false && question.status !== 'deleted';
            })
        }

        return matchingQuestion;
    }

    handleNewQuestionButton(newColumn, data, matchingQuestion) {
        let newStatusQuestion = null;
        if (matchingQuestion && matchingQuestion.status !== 'deleted') {
            newStatusQuestion = new NewStatusQuestion(
                newColumn,
                matchingQuestion,
                this.getServiceCategory(),
                this.demo,
                this.types,
                matchingQuestion.status,
                matchingQuestion.status_text
            );
        } else {
            newStatusQuestion = new NewStatusQuestion(
                newColumn,
                data,
                this.getServiceCategory(),
                this.demo,
                this.types
            );
        }
        this.questionButtons.push(data);
        newStatusQuestion.injectableElement.addEventListener('question-updated', () => this.onQuestionUpdated());
    }

    getServiceName() {
        return 'zenvoices_open_questions';
    }

    getServiceCategory() {
        return 'ocr';
    }

    async onQuestionUpdated() {
        await this.openQuestionsCommunicationService.sendToBExtension(
            'questions_updated',
            {
                'companyId': (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'],
                'companyCode': (await browser.storage.local.get(this.getServiceName() + '_external_company_code'))[this.getServiceName() + '_external_company_code'],
                'companyName': (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name'],
                'serviceName': this.getServiceName()
            }
        );
    }

    async getQuestions() {
        return this.openQuestionsCommunicationService.sendToBExtension('get_questions_for_company', {
            'category': this.getServiceCategory(),
            'service_name': this.getServiceName(),
            'external_id': this.external_company_id
        });
    }

    getPageTitle() {
        let title = document.querySelector(this.selectors.page_title);
        let subtitle = document.querySelector(this.selectors.page_subtitle);
        if (title && subtitle) {
            return title.innerText.replace(/\d+/g, '').trim() + ' - ' + subtitle.innerText.replace(/\d+/g, '').trim();
        }

        return null;
    }
}

export {BasePage}
