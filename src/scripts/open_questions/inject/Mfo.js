import {MfoPage} from "./mfo/MfoPage";
import browser from "webextension-polyfill";

export class Mfo {
    constructor() {
        this.page = null;
    }

    async start(demo, selectors) {
        if (!(await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            this.page = new MfoPage(selectors, demo);
            this.page.start();
        }
    }
}

let mfo = new Mfo();

document.addEventListener('start', async function (e) {
    await mfo.start(
        e.detail.demo,
        e.detail.selectors,
        e.detail.notAllowedUrls
    )
})
