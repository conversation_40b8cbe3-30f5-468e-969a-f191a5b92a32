.message-row {
  margin-bottom: 20px;
}

.message-container {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  align-items: center;
}

.message-container.external {
  flex-direction: row-reverse;
}

.message-text-container {
  max-width: 70%;
}

.message-box {
  background: #F8F8F8;
  border-radius: 8px;
  vertical-align: middle;
  padding: 10px;
  word-wrap: break-word;
}

.message-box.external {
  background: #0F9BF3;
  color: white;
}

.message-box.external a {
    color: #e4e4e4;
    padding: 0;
}

.message-box.external a:hover {
    color: #b8b8b8;
}

.date-text {
  font-size: 10px;
  color: #999999;
  margin: 0;
}

.date-message-container {
  margin-top: 2px;
}

.empty-block {
  width: 30px;
}