import style from '!css-loader!./popup_style.css';
import styleMessage from '!css-loader!./message_style.css';
import styleLogo from '!css-loader!./../logo/style.css';
import {Message} from './Message';
import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {NotificationHelper} from "../../../tax/inject/helpers/NotificationHelper";
import {trans} from "../../../helpers/TranslationHelper";
import {CurrencyHelper} from "../helpers/CurrencyHelper";

class HistoryPopup {
    constructor(data) {
        this.data = data;
        this.questionId = data.id;
        this.companyName = data.name;
        this.questionType = data.type_text;
        this.amount = data.amount;
        this.messages = data.messages;
        this.status = data.status;
        this.hostname = data.hostname;

        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
    }

    async init() {
        const rootContainer = document.createElement('sl-question-history');
        const shadowRoot = rootContainer.attachShadow({mode: 'open'});

        const styleElement = document.createElement('style');
        styleElement.innerText = style + styleMessage + styleLogo;

        shadowRoot.appendChild(styleElement);

        // Root div
        const rootDiv = document.createElement('div');
        rootDiv.classList.add('root-container');
        shadowRoot.appendChild(rootDiv);

        // Main container
        const mainContainer = document.createElement('div');
        mainContainer.classList.add('main-container');
        rootDiv.appendChild(mainContainer);

        // Popup container
        const popupContainer = document.createElement('div');
        popupContainer.classList.add('history-container');
        mainContainer.appendChild(popupContainer);

        // Close button
        const closePopupButton = document.createElement('span');
        closePopupButton.classList.add('close');
        closePopupButton.classList.add('sl-icon-close-cross');
        closePopupButton.onclick = function () {
            rootContainer.style.display = "none";
        }
        mainContainer.appendChild(closePopupButton);

        // Header container
        const headerContainer = document.createElement('div');
        headerContainer.classList.add('header-container');
        popupContainer.appendChild(headerContainer);

        const questionHeader = document.createElement('div');
        questionHeader.classList.add('question-header');
        headerContainer.appendChild(questionHeader);

        // Title container
        const headerTitleContainer = document.createElement('div');
        headerTitleContainer.classList.add('title-container');
        questionHeader.appendChild(headerTitleContainer);

        // Header title
        const headerTitle = document.createElement('div');
        headerTitle.classList.add('title');
        headerTitle.innerText = this.companyName;
        headerTitleContainer.appendChild(headerTitle);

        // Header subtitle
        const headerSubtitle = document.createElement('div');
        headerSubtitle.classList.add('sub-text');
        headerSubtitle.innerText = this.questionType;
        headerTitleContainer.appendChild(headerSubtitle);

        // Amount
        if (this.amount) {
            const headerAmount = document.createElement('div');
            headerAmount.classList.add('title');
            let currencySymbol = "&euro; ";
            if (this.data.currency) {
                currencySymbol = CurrencyHelper.convertToSymbol(this.data.currency);
            }
            headerAmount.innerHTML = currencySymbol + ' ' + this.amount;
            questionHeader.appendChild(headerAmount);
        }

        // Separator
        const separatorDiv = document.createElement('div');
        separatorDiv.classList.add('separator');
        headerContainer.appendChild(separatorDiv);

        const separatorName = document.createElement('div');
        separatorName.classList.add('title');
        separatorName.innerText = await trans('common.history');
        separatorDiv.appendChild(separatorName);

        // Messages Container
        const messagesContainer = document.createElement('div');
        messagesContainer.classList.add('messages-container');
        popupContainer.appendChild(messagesContainer);

        const messagesSubContainer = document.createElement('div');
        messagesSubContainer.classList.add('messages-subcontainer');
        messagesContainer.appendChild(messagesSubContainer);

        for (let message of this.messages) {
            messagesSubContainer.appendChild(new Message(message, this.hostname).injectableElement);
        }

        // Actions bar
        if (this.status === 'open' || this.status === 'pending') {
            const actionsBar = document.createElement('div');
            actionsBar.classList.add('actions-bar');
            mainContainer.appendChild(actionsBar);

            // Reply block
            const replyBlock = document.createElement('div');
            const sendButton = document.createElement('button');
            const replyInput = document.createElement('input');

            replyBlock.classList.add('reply-block');
            actionsBar.appendChild(replyBlock);

            replyInput.classList.add('reply-input');
            replyBlock.addEventListener('input', () => this.updateSendButton(sendButton, replyInput.value))
            replyBlock.appendChild(replyInput);

            sendButton.classList.add('button', 'send-button');
            sendButton.disabled = true;
            const sendIcon = document.createElement('i');
            sendIcon.classList.add('sl-icon-send-message');
            sendButton.appendChild(sendIcon);
            replyBlock.appendChild(sendButton);
            sendButton.addEventListener('click', () => this.answerToQuestion(this.questionId, replyInput.value));

            // Action block
            const actionBlock = document.createElement('div');
            actionBlock.classList.add('action-block');
            actionsBar.appendChild(actionBlock);

            const removeButton = document.createElement('a');
            actionBlock.appendChild(removeButton);
            const removeIcon = document.createElement('i');
            removeIcon.classList.add('sl-icon-trash-can');
            removeButton.appendChild(removeIcon);
            const removeButtonSpan = document.createElement('span');
            removeButtonSpan.innerText = ' ' + await trans('common.remove');
            removeButton.appendChild(removeButtonSpan);
            removeButton.addEventListener('click', () => this.removeQuestion(this.questionId));

            const closeButton = document.createElement('a');
            actionBlock.appendChild(closeButton);
            const checkmarkIcon = document.createElement('i');
            checkmarkIcon.classList.add('sl-icon-check-mark');
            closeButton.appendChild(checkmarkIcon);
            const closeButtonSpan = document.createElement('span');
            closeButtonSpan.innerText = ' ' + await trans('open_questions.close');
            closeButton.appendChild(closeButtonSpan);
            closeButton.addEventListener('click', () => this.closeQuestion(this.questionId));
        } else {
            popupContainer.classList.add('add-border-bottom');
        }

        this._injectableElement = rootContainer;
    }

    async removeQuestion(questionId) {
        let removeData = {'id': questionId};
        try {
            await this.openQuestionsCommunicationService.sendToBExtension('remove_question', removeData);
            this._injectableElement.style.display = "none";
            this._injectableElement.dispatchEvent(new Event('question-updated'));

            NotificationHelper.sendToast('success', await trans('open_questions.question_removed'));
        } catch (e) {
            console.error(e)
            NotificationHelper.sendGenericError();
        }
    }

    async closeQuestion(questionId) {
        let closeData = {'id': questionId};
        try {
            await this.openQuestionsCommunicationService.sendToBExtension('close_question', closeData);
            this._injectableElement.style.display = "none";
            this._injectableElement.dispatchEvent(new Event('question-updated'));

            NotificationHelper.sendToast('success', await trans('open_questions.question_closed'));
        } catch (e) {
            console.error(e);
            NotificationHelper.sendGenericError();
        }
    }

    async answerToQuestion(questionId, answer) {
        let answerData = {'id': questionId, 'answer': answer};
        try {
            await this.openQuestionsCommunicationService.sendToBExtension('answer_question', answerData)
            await this.openQuestionsCommunicationService.sendToBExtension('launch_question_history', this.data)
            this._injectableElement.dispatchEvent(new Event('question-updated'));
            this._injectableElement.style.display = "none";
        } catch (e) {
            console.error(e)
            NotificationHelper.sendGenericError();
        }
    }

    updateSendButton(sendButton, answer){
        sendButton.disabled = !answer.trim();
    }

    injectableElement() {
        return this._injectableElement;
    }
}

export {HistoryPopup};