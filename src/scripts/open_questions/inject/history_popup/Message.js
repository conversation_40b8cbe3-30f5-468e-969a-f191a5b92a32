import style from '!css-loader!./message_style.css';
import {Logo} from '../logo/Logo';

class Message {
  constructor(message, hostname) {
    const styleElement = document.createElement('style');
    styleElement.innerText = style;
    styleElement.style.display = "none";
    
    // Message row
    const messageRow = document.createElement('div');
    messageRow.classList.add('message-row');

    // Message container
    const messageContainer = document.createElement('div');
    messageContainer.classList.add('message-container');
    if (message.is_own) {
      messageContainer.classList.add('external');
    }
    messageRow.appendChild(messageContainer);

    // Message logo
    let logoText = message.name;
    const logo = new Logo(logoText, this.logoIcon(message), message.background_color);
    messageContainer.appendChild(logo.injectableElement);

    // Message text container
    const messageTextContainer = document.createElement('div');
    messageTextContainer.classList.add('message-text-container');
    if (message.is_own) {
      messageTextContainer.classList.add('external');
    }
    messageContainer.appendChild(messageTextContainer);

    // Message box
    const messageBox = document.createElement('div');
    messageBox.classList.add('message-box');
    if (message.is_own) {
      messageBox.classList.add('external');
    }

    if (message.text.includes(':file:') || message.text.includes(':link:')) {
        let filename = encodeURIComponent(message.replaces.file_name);
        let baseUrl = 'https://' + hostname + '/new/open_questions/attachments/';
        let url = 'by_question_id/' + message.open_question_id + '/' + filename;
        if (message.replaces.attachment_id) {
            url = message.replaces.attachment_id + '/' + filename;
        }
        messageBox.innerHTML = message.text.replace(
            ':file:',
            '<a target="_blank" href="' + baseUrl + url + '">' + message.replaces.file_name + '</a>'
        ).replace(
            ':link:',
            '<a target="_blank" href="' + message.replaces.link_url + '">' + message.replaces.link_title + '</a>'
        );
    } else {
        messageBox.textContent = message.text;
    }

    messageTextContainer.appendChild(messageBox);

    // Message date container
    const messageDateContainer = document.createElement('div');
    messageDateContainer.classList.add('message-container', 'date-message-container');
    if (message.is_own) {
      messageDateContainer.classList.add('external');
    }
    messageRow.appendChild(messageDateContainer);

    // Empty block
    const emptyBlock = document.createElement('div');
    emptyBlock.classList.add('empty-block');
    messageDateContainer.appendChild(emptyBlock);

    // Date text
    const dateText = document.createElement('span');
    dateText.classList.add('date-text');
    dateText.innerText = message.date;
    messageDateContainer.appendChild(dateText);

    this._injectableElement = messageRow;
  }
  get injectableElement() {
    return this._injectableElement;
  }

  logoIcon(message) {
    return message.is_securelogin || message.is_hix ? 'https://securelogin.securelogin.nu/images/account/logo_circle.png' : '';
  }
}

export {Message};