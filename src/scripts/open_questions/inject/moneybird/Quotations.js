import {BasePage} from "./BasePage";

/**
 * Offertes
 */
export class Quotations extends BasePage {
    async handlePage() {
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }
        await this.handleTable(this.selectors.pages.quotations);
    }
}