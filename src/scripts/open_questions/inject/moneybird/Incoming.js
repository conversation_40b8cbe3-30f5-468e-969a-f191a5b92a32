import {BasePage} from "./BasePage";

/**
 * Inkomend
 */
export class Incoming extends BasePage {
    async handlePage() {
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }
        await this.handleTable(this.selectors.pages.incoming);
    }

    async handleTable(selectors) {
        let tableSelectors = selectors.table;
        let table = await this.getTable(
            tableSelectors.selector,
            this.getColumnsSelectorsFromTableHeader(tableSelectors) // get all table columns
        );
        table.setHeaderClasses(tableSelectors.header.cssClasses);
        table.setColumnClasses(tableSelectors.columns.cssClasses);
        table.setEmptyTableText(tableSelectors.empty_table_text);
        await table.start();
    }
}