import {BasePage} from "./BasePage";

/**
 * Facturen
 */
export class Invoices extends BasePage {
    async handlePage() {
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }
        await this.handleTable(this.selectors.pages.invoices);
    }
}