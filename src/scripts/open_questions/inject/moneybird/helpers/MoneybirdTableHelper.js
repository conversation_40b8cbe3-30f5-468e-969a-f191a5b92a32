import {TableHelper} from "../../helpers/TableHelper";

/**
 * Add Open questions column and handle questions
 */
class MoneybirdTableHelper extends TableHelper {
    async getTable(selector) {
        let table = document.querySelector(selector);
        this.removeColumn(table);
        this.checkTableRows(table);
        if (this.canAddTable) {
            table = await this.addColumn(table);
            table = await this.addHeader(table);
            if (table) {
                table.addEventListener('question-updated', async () => await this.onQuestionUpdated());
            }
        }
        return table;
    }

    /**
     * Handle currency by row
     * @param row
     * @returns {string}
     */
    handleCurrency(row) {
        let currency = 'EUR'; // EUR is default
        for (let [index, columnElement] of this.columnSelectors['amount'].entries()) {
            let textElement = row.querySelector(columnElement);
            if (textElement && textElement.textContent.includes('£')) {
                currency = 'GBP';
            }
            if (textElement && textElement.textContent.includes('$')) {
                currency = 'USD';
            }
        }
        return currency;
    }

    /**
     * Row data
     * @param row
     */
    handleAttributes(row) {
        let data = super.handleAttributes(row);
        data.currency = this.handleCurrency(row);
        if (this.rowUrlAttribute) {
            data.attachments = [
                window.location.origin + row.getAttribute(this.rowUrlAttribute) + this.extension
            ];
        }
        return data;
    }

    /**
     * Set attribute to grab the URL
     * @param attribute
     */
    setRowUrlAttribute(attribute) {
        this.rowUrlAttribute = attribute;
    }

    /**
     * Add extension to the url
     * @param extension
     */
    setRowExtension(extension) {
        this.extension = extension;
    }

    /**
     * Set empty table text
     * @param emptyTableText
     */
    setEmptyTableText(emptyTableText) {
        this.emptyTableText = emptyTableText;
    }

    /**
     * Use when we want to validate if we can add the button
     * @param data
     * @returns {boolean}
     */
    canAddQuestion(data) {
        return data.name && data.transaction_date && data.name !== '' && data.transaction_date !== '';
    }


    /**
     * Check if we find all required attributes
     *
     * @param table
     * @returns {*}
     */
    checkTableRows(table) {
        const tbody = table.querySelector('tbody');
        if (tbody === null) {
            return null;
        }
        this.canAddTable = false;
        upperLoop: for (let i = 0; i < tbody.rows.length; i++) {
            for (let attribute of Object.keys(this.columnSelectors)) {
                for (let [index, columnElement] of this.columnSelectors[attribute].entries()) {
                    let textElement = tbody.rows[i].querySelector(columnElement);
                    if (textElement) {
                        if (
                            this.emptyTableText
                            && this.emptyTableText.some(prefix => textElement.textContent.trim().startsWith(prefix))
                        ) {
                            this.canAddTable = false;
                            break upperLoop;
                        }
                        if (textElement.textContent.trim() && attribute === 'amount') {
                            this.canAddTable = true;
                        }
                    }
                }
            }
        }
    }
}

export {MoneybirdTableHelper};