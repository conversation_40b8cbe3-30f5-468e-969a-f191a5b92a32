import {MoneybirdTableHelper} from "./MoneybirdTableHelper";

/**
 * Add Open questions column and handle questions
 */
class MoneyBirdGeneralLedgerTableHelper extends MoneybirdTableHelper {
    /**
     * Use when we want to validate if we can add the button
     * @param data
     * @returns {boolean}
     */
    canAddQuestion(data) {
        return data.name && data.transaction_date && data.amount
            && data.name !== '' && data.transaction_date !== '' && data.amount !== '';
    }

    /**
     * Row data
     * @param row
     */
    handleAttributes(row) {
        let data = super.handleAttributes(row);
        data.currency = this.handleCurrency(row);
        if (data.name === '') {
            data.name = data.description;
            data.description = '';
        }

        return data;
    }
}

export {MoneyBirdGeneralLedgerTableHelper};