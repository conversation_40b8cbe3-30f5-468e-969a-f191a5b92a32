import {OpenQuestionsHelper} from "../helpers/OpenQuestionsHelper";
import {ServicePage} from "../ServicePage";
import {SelectorHelper} from "../helpers/SelectorHelper";
import {MoneybirdTableHelper} from "./helpers/MoneybirdTableHelper";
import browser from "webextension-polyfill";

/**
 * Moneybird common page class
 */
export class BasePage extends ServicePage {
    constructor(demo, selectors) {
        super();
        this.demo = demo;
        this.selectors = selectors;
        this.openQuestionsHelper = new OpenQuestionsHelper(this.demo);
        this.selectorHelper = new SelectorHelper();
    }

    async start() {
        if (this.selectors && !this.demo) {
            this.selectorHelper.waitForElement(this.selectors.companyName).then(() => {
                this.validatePage();
            });
        }
    }

    /**
     * Validate if we can handle page logic
     * @returns {Promise<void>}
     */
    async validatePage() {
        if (await this.isCompanyManager()) {
            return this.handlePage();
        }
        if (await this.isCompanyBlocked(this.getServiceName(), this.getCompanyId(), this.getCompanyName())) {
            return;
        }
        return this.handlePage();
    }

    /**
     * Handle page logic
     * @returns {Promise<void>}
     */
    async handlePage() {
        await this.appendCornerStone();
    }

    async handleTable(selectors) {
        let tableSelectors = selectors.table;
        this.selectorHelper.waitForElement(tableSelectors.selector).then(async () => {
            let table = await this.getTable(
                tableSelectors.selector,
                this.getColumnsSelectorsFromTableHeader(tableSelectors), // get all table columns,
                {},
                [
                    'attachments'
                ]
            );

            table.setHeaderClasses(tableSelectors.header.cssClasses);
            table.setColumnClasses(tableSelectors.columns.cssClasses);
            table.setRowUrlAttribute(tableSelectors.public_url);
            table.setRowExtension(tableSelectors.extension);
            await table.start();
        });
    }

    /**
     * Service name
     * @returns {string}
     */
    getServiceName() {
        return 'moneybird_open_questions';
    }

    /**
     * Moneybird open questions category
     * @returns {string}
     */
    getServiceCategory() {
        return 'bookkeeping';
    }

    /**
     * Get External Company Id
     * @returns {string}
     */
    getCompanyId() {
        return window.location.pathname.split('/')[this.selectors.companyIdUrlIndex];
    }

    /**
     * Get Company Name
     * @returns {string}
     */
    getCompanyName() {
        let companyName = document.querySelector(this.selectors.companyName);
        return companyName.textContent.trim();
    }

    /**
     * Get page title
     * @returns {*}
     */
    getPageTitle() {
        let title = document.querySelector(this.selectors.pageTitle);
        if (title) {
            return title.textContent.trim();
        }
        return null;
    }

    /**
     * When question is updated, update cornerstone
     */
    async onQuestionUpdated() {
        await this.openQuestionsCommunicationService.sendToBExtension(
            'questions_updated',
            {
                'companyId': (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'],
                'companyCode': (await browser.storage.local.get(this.getServiceName() + '_external_company_code'))[this.getServiceName() + '_external_company_code'],
                'companyName': (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name'],
                'serviceName': this.getServiceName(),
                'force': true
            }
        );
    }

    /**
     * Get table from selector and then use columns selector
     * @param selector
     * @param columnSelectors
     * @param additionalAttributes
     * @param ignoreAttributes
     * @returns {MoneybirdTableHelper}
     */
    async getTable(selector, columnSelectors, additionalAttributes = {}, ignoreAttributes = []) {
        let questions = null;
        let types = [];
        if (this.demo === false) {
            questions = await this.getQuestions();
            types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                category: this.getServiceCategory(),
                serviceName: this.getServiceName()
            });
        }
        additionalAttributes.page_title = this.getPageTitle();
        additionalAttributes.page_url = window.location.href;
        let table = new MoneybirdTableHelper(
            selector,
            columnSelectors,
            additionalAttributes,
            ignoreAttributes,
            questions,
            types,
            {
                'category': this.getServiceCategory(),
                'service_name': this.getServiceName(),
                'demo': this.demo
            }
        );
        table.setHeaderStyle('width: 15%;');
        return table;
    }
}