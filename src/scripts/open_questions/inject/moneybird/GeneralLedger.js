import {BasePage} from "./BasePage";
import {MoneyBirdGeneralLedgerTableHelper} from "./helpers/MoneyBirdGeneralLedgerTableHelper";

/**
 * Balans
 * Menu > Rapporten > Resultatenrekening> Select ledger OR
 * Menu > Rapporten > Balans> Select ledger
 */
export class GeneralLedger extends BasePage {
    async handlePage() {
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }
        await this.handleTable(this.selectors.pages.general_ledger);
    }

    /**
     * Get table from selector and then use columns selector
     * @param selector
     * @param columnSelectors
     * @param additionalAttributes
     * @param ignoreAttributes
     * @returns {MoneyBirdGeneralLedgerTableHelper}
     */
    async getTable(selector, columnSelectors, additionalAttributes = {}, ignoreAttributes = []) {
        let questions = null;
        let types = [];
        if (this.demo === false) {
            questions = await this.getQuestions();
            types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                category: this.getServiceCategory(),
                serviceName: this.getServiceName()
            });
        }
        additionalAttributes.page_title = this.getPageTitle();
        additionalAttributes.page_url = window.location.href;
        let table = new MoneyBirdGeneralLedgerTableHelper(
            selector,
            columnSelectors,
            additionalAttributes,
            [
                'document'
            ],
            questions,
            types,
            {
                'category': this.getServiceCategory(),
                'service_name': this.getServiceName(),
                'demo': this.demo
            }
        );
        table.setHeaderStyle('width: 15%;');
        return table;
    }

    async handleTable(selectors) {
        let tableSelectors = selectors.table;
        let table = await this.getTable(
            tableSelectors.selector,
            this.getColumnsSelectorsFromTableHeader(tableSelectors) // get all table columns,
        );
        table.setHeaderClasses(tableSelectors.header.cssClasses);
        table.setColumnClasses(tableSelectors.columns.cssClasses);
        table.setEmptyTableText(tableSelectors.empty_table_text);
        await table.start();
    }
}