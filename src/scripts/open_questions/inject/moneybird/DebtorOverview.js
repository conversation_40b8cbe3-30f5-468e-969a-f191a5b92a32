import {BasePage} from "./BasePage";

/**
 * Debiteurenoverzicht
 * Menu > Rapporten > Debiteurenoverzicht > Select the debtor
 */
export class DebtorOverview extends BasePage {
    async handlePage() {
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }
        await this.handleTable(this.selectors.pages.debtor_overview);
    }

    async handleTable(selectors) {
        let tableSelectors = selectors.table;
        this.selectorHelper.waitForElement(tableSelectors.selector).then(async () => {
            let columnsSelectors = this.getColumnsSelectorsFromTableHeader(tableSelectors);
            // add more "name" fields to the name
            columnsSelectors.name = [columnsSelectors.name + ' ' + tableSelectors.columns.name];
            // add more "description" fields to the description
            let descriptionColumn = columnsSelectors.description;
            columnsSelectors.description = [];
            tableSelectors.columns.description.forEach(function (additionalSelector) {
                columnsSelectors.description.push(descriptionColumn + ' ' + additionalSelector);
            });
            let table = await this.getTable(
                tableSelectors.selector,
                columnsSelectors
            );
            await table.start();
        });
        let table = await this.getTable(
            tableSelectors.selector,
            columnsSelectors
        );

        table.setEmptyTableText(tableSelectors.empty_table_text);
        await table.start();
    }
}