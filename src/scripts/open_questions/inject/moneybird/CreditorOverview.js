import {DebtorOverview} from "./DebtorOverview";

/**
 * Crediteurenoverzicht
 * Menu > Rapporten > Crediteurenoverzicht > Select the debtor
 */
export class CreditorOverview extends DebtorOverview {
    async handlePage() {
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }
        await this.handleTable(this.selectors.pages.creditor_overview);
    }
}