const currencyMap = {
    'EUR': '€',
    'USD': '$',
    'GBP': '£',
    'CNY': '¥',
    'DKK': 'DKK',
    'NOK': 'NOK',
    'SEK': 'SEK',
    'PLN': 'zł',
    'CHF': 'Fr.'
};

const symbolMap = {
    '€': 'EUR',
    '$': 'USD',
    '£': 'GBP',
    '¥': 'CNY',
    'DKK': 'DKK',
    'NOK': 'NOK',
    'SEK': 'SEK',
    'zł': 'PLN',
    'Fr.': 'CHF'
};

class CurrencyHelper {
    static convertToSymbol(currency) {
        return currencyMap[currency];
    }

    static convertToText(currency) {
        return symbolMap[currency];
    }
}

export {CurrencyHelper}