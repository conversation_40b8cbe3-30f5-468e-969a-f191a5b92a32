class SelectorHelper {
    helpSelector(selector, element = null, replaces) {
        if (element === null) {
            element = document;
        }

        if (replaces !== undefined) {
            replaces.forEach((replace) => {
                selector = selector.replace(':replace:', replace);
            })
        }

        // Handle nested iframes
        let indexIframe = selector.indexOf('iframe');
        if (indexIframe === 0) {
            const index = selector.indexOf(' ');

            if (index === -1) {
                return element.querySelector(selector);
            }

            return this.helpSelector(
                selector.substring(index + 1),
                element.querySelector(selector.substring(0, index)).contentWindow.document
            );
        } else if (indexIframe > 0) {
            return this.helpSelector(
                selector.substring(indexIframe),
                element.querySelector(selector.substring(0, indexIframe))
            )
        }
        return element.querySelector(selector);
    }

    /**
     * Wait for element
     * @param selector
     * @param wait
     * @returns {Promise<unknown>}
     */
    waitForElement(selector, wait = 5000) {
        return new Promise(resolve => {
            if (document.querySelector(selector)) {
                return resolve(document.querySelector(selector));
            }

            const observer = new MutationObserver(mutations => {
                if (document.querySelector(selector)) {
                    clearTimeout(timeout);
                    resolve(document.querySelector(selector));
                    observer.disconnect();
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            const timeout = setTimeout(
                () => {
                    observer.disconnect();
                    return resolve(null);
                },
                wait
            );
        });
    }

    /**
     * Sleep for ms
     * @param ms
     * @returns {Promise<unknown>}
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Try until it gets the result or timeout
     * @param func
     * @param timeoutMs
     * @returns {Promise<unknown>}
     */
    async sleepUntil(func, timeoutMs) {
        return new Promise((resolve, reject) => {
            const timeWas = new Date();
            const wait = setInterval(function() {
                if (func()) {
                    console.log("Resolved after", new Date() - timeWas, "ms");
                    clearInterval(wait);
                    resolve();
                } else if (new Date() - timeWas > timeoutMs) { // Timeout
                    console.log("Rejected after", new Date() - timeWas, "ms");
                    clearInterval(wait);
                    reject();
                }
            }, 2000);
        });
    }

    getTextFromSelector(selectors, element = null) {
        if (element === null) {
            element = document;
        }

        if (typeof selectors === 'string') {
            selectors = [selectors];
        }

        let text = '';
        selectors.some(selector => {
            text = element.querySelector(selector).textContent.trim();
            return Boolean(text);
        })
        return text;
    }

    /**
     * Frame selector
     * @returns {null|Document|*}
     */
    frameSelector(index) {
        return window.parent.frames[index].document;
    }

    /**
     * Grabs variables from page's side
     * @param code
     * @returns {any}
     */
    getPageValue(code) {
        const scripts = document.querySelectorAll('script');
        let digitalPostBoxesValue = null;

        for (let script of scripts) {
            if (script.src === '') {
                const content = script.textContent;
                if (content.includes(code)) {
                    let regex = new RegExp(`${code}\\s*=\\s*(.*?);`);
                    const match = script.textContent.match(regex);
                    if (match && match[1]) {
                        try {
                            // Safely parse the value if it's JSON-like or a simple value
                            digitalPostBoxesValue = JSON.parse(match[1]);
                        } catch (e) {
                            // If parsing as JSON fails, just assign the value as a string
                            digitalPostBoxesValue = match[1];
                        }
                        return digitalPostBoxesValue;
                    }
                }
            }
        }
        return digitalPostBoxesValue;
    }
}

export {SelectorHelper}