import {NewStatusQuestion} from "../buttons/NewStatusQuestion";
import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {trans} from "../../../helpers/TranslationHelper";
import browser from "webextension-polyfill";

/**
 * Add Open questions column and handle questions
 */
class TableHelper {
    constructor(
        selector,
        columnSelectors,
        additionalAttributes, // use this to add extra data to each row
        ignoreAttributes, // use to ignore attributes when validating if the question has a match
        questions,
        types,
        service,
        prepend = false
    ) {
        this.selector = selector;
        this.columnSelectors = columnSelectors;
        this.additionalAttributes = additionalAttributes;
        this.ignoreAttributes = ignoreAttributes;
        this.questions = questions;
        this.types = types ? types : [];
        this.service = service;
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
        this.tableHasRows = false;
        this.prepend = prepend;
        this.headerClasses = null;
        this.headerStyle = 'width: 12%;';
        this.columnClasses = null;
        this.addedQuestionButton = false;
    }

    async start() {
        this._injectableElement = await this.getTable(this.selector);
    }

    async getTable(selector) {
        let table = document.querySelector(selector);
        this.removeColumn(table);
        table = await this.addColumn(table);
        table = await this.addHeader(table);
        if (table) {
            table.addEventListener('question-updated', async () => await this.onQuestionUpdated());
        }

        return table;
    }

    /**
     * Remove hix column from table
     * @param table
     */
    removeColumn(table) {
        let tableHeaders = table.querySelectorAll('.hix-table-header');
        if (tableHeaders && tableHeaders.length) {
            tableHeaders.forEach(function (element) {
                element.parentNode.removeChild(element);
            });
        }

        let tableColumns = table.querySelectorAll('.hix-table-columns');
        if (tableColumns && tableColumns.length) {
            tableColumns.forEach(function (element) {
                element.parentNode.removeChild(element);
            });
        }
    }

    /**
     * Add classes to hix column table header
     * Usage: "class1 class2 class3"
     * @param classes
     */
    setHeaderClasses(classes) {
        this.headerClasses = classes;
    }

    /**
     * Add classes to HIX column table header
     * Usage: "width: 13%; position: relative;"
     * @param style
     */
    setHeaderStyle(style) {
        this.headerStyle = style;
    }

    /**
     * Add classes to hix column
     * Usage: "class1 class2 class3"
     * @param classes
     */
    setColumnClasses(classes) {
        this.columnClasses = classes;
    }

    /**
     * Add th element to table header
     * @param table
     * @returns {*}
     */
    async addHeader(table) {
        const thead = table.querySelector('thead');
        if (thead === null || this.tableHasRows === false) {
            return null;
        }
        for (let i = 0; i < thead.rows.length; i++) {
            if (i + 1 === thead.rows.length) {
                let thElement = document.createElement('th');
                thElement.innerText = await trans('open_questions.title');
                thElement.classList.add('hix-table-header');
                thElement.style = this.headerStyle;
                if (this.headerClasses) {
                    for (let headerClass of this.headerClasses.split(" ")) {
                        thElement.classList.add(headerClass);
                    }
                }
                thead.rows[i].appendChild(thElement);
            }
        }

        return table;
    }

    /**
     * Add column (td element) to each row of table body
     *
     * @param table
     * @returns {*}
     */
    async addColumn(table) {
        const tbody = table.querySelector('tbody');
        if (tbody === null) {
            return null;
        }

        for (let i = 0; i < tbody.rows.length; i++) {
            let row = tbody.rows[i];
            let data = this.handleAttributes(row);

            // each attribute in data from selectors.json is required, so we have everything to try to match the question
            let matchingQuestion = this.handleMatchingQuestion(data);

            // handle additional data for each row
            data = this.handleAdditionalAttributes(data);

            // add new column
            let newColumn = row.insertCell(row.cells.length);
            newColumn.classList.add('hix-table-columns');
            if (this.columnClasses) {
                for (let columnClass of this.columnClasses.split(" ")) {
                    newColumn.classList.add(columnClass);
                }
            }

            // add new question button
            this.handleNewQuestionButton(newColumn, data, matchingQuestion);
        }

        return table;
    }

    /**
     * Add new question button to the row
     * @param newColumn
     * @param data
     * @param matchingQuestion
     */
    handleNewQuestionButton(newColumn, data, matchingQuestion) {
        if (this.canAddQuestion(data)) {
            if (matchingQuestion && matchingQuestion.status !== 'deleted') {
                let statusQuestionButton = new NewStatusQuestion(
                    newColumn,
                    matchingQuestion,
                    this.service.category,
                    this.service.demo,
                    this.types,
                    matchingQuestion.status,
                    matchingQuestion.status_text
                );
            } else {
                let statusQuestionButton = new NewStatusQuestion(
                    newColumn,
                    data,
                    this.service.category,
                    this.service.demo,
                    this.types
                );
            }
            this.addedQuestionButton = true;
        }
    }

    /**
     * Use when we want to validate if we can add the button
     * @param data
     * @returns {boolean}
     */
    canAddQuestion(data) {
        return data.name;
    }

    /**
     * Each attribute in data from selectors.json is required, get them from the row
     *
     * "columnSelectors" will have all selectors from each attribute to get the text we want from each column of the table
     *
     * Example:
     * {
     *     name: ["td:nth-child(3) > span"],
     *     amount: ["td:nth-child(5) > span > snelstart-transaction-currency > span"],
     *     transaction_date: ["td:nth-child(2) > span"],
     *     description: [
     *         "td:nth-child(4) > span:nth-child(1)",
     *         "td:nth-child(4) > span:nth-child(2)"
     *     ]
     * }
     *
     * "description" is an array of two selectors, we combine the result from both selectors to get the full description
     *
     * @returns {{}}
     */
    handleAttributes(row) {
        let data = {};
        for (let attribute of Object.keys(this.columnSelectors)) {
            let field = '';
            for (let [index, columnElement] of this.columnSelectors[attribute].entries()) {
                let textElement = row.querySelector(columnElement);
                if (textElement) {
                    // flag to know that we found at least 1 value to add create question button
                    this.tableHasRows = true;
                    field = field + textElement.textContent.trim();
                    if ((index + 1) !== this.columnSelectors[attribute].length) {
                        field = field + ' - ';
                    }
                }
            }
            data[attribute] = this.sanitizeString(field.trim());
            if (attribute.includes('date')) {
                data[attribute] = this.sanitizeDate(field);
            }
        }

        return data;
    }

    /**
     * Add additional attributes to data
     * @param data
     * @returns {*}
     */
    handleAdditionalAttributes(data) {
        // handle additional data for each row
        for (let attribute of Object.keys(this.additionalAttributes)) {
            data[attribute] = this.additionalAttributes[attribute];
        }
        data.service_name = this.service.service_name;
        data.category = this.service.category;
        return data;
    }

    /**
     * Match row in a page with questions from hix
     *
     * Each field from selectors.json is required, so we have everything to try to match the question
     * @param data
     * @returns {null}
     */
    handleMatchingQuestion(data) {
        let matchingQuestion = null;
        if (this.questions) {
            matchingQuestion = this.questions.find((question) => {
                let differentAttribute = false;
                for (let attribute of Object.keys(data)) {
                    // ignore validation for this attribute
                    if (this.ignoreAttributes && this.ignoreAttributes.indexOf(attribute) !== -1) {
                        continue;
                    }

                    let dataAttribute = data[attribute].replace(/[\r\n]+/gm,"");
                    let questionAttribute = question[attribute].replace(/[\r\n]+/gm,"");

                    // question has no match with given row
                    if (dataAttribute !== questionAttribute) {
                        differentAttribute = true;
                        break;
                    }
                }

                // no different attribute found, it's a match
                return differentAttribute === false && question.status !== 'deleted';
            })
        }

        return matchingQuestion;
    }

    /**
     * Remove characters from string
     * @param input
     * @returns {*}
     */
    sanitizeString(input) {
        let forbiddenChars = ['€', '£', '$'];
        for (let char of forbiddenChars) {
            input = input.split(char).join('');
        }

        return input.trim();
    }

    /**
     * Remove characters from date
     * @param input
     * @returns {*}
     */
    sanitizeDate(input) {
        let forbiddenChars = ['(', ')'];
        for (let char of forbiddenChars) {
            input = input.split(char).join('');
        }

        return input.trim();
    }

    /**
     * When question is updated, update cornerstone
     */
    async onQuestionUpdated() {
        await this.openQuestionsCommunicationService.sendToBExtension(
            'questions_updated',
            {
                'companyId': (await browser.storage.local.get(this.service.service_name + '_external_company_id'))[this.service.service_name + '_external_company_id'],
                'companyCode': (await browser.storage.local.get(this.service.service_name + '_external_company_code'))[this.service.service_name + '_external_company_code'],
                'companyName': (await browser.storage.local.get(this.service.service_name +'_external_company_name'))[this.service.service_name + '_external_company_name'],
                'serviceName': this.service.service_name
            }
        );
    }

    get injectableElement() {
        return this._injectableElement;
    }
}

export {TableHelper};