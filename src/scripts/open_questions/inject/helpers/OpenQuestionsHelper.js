import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {QuestionDropdown} from "../popup/QuestionDropdown";

/**
 * TODO: Remove this file when basecone and nmbrs start using NewStatusQuestion component
 */
class OpenQuestionsHelper {
    constructor(demo) {
        this.demo = demo;
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
    }

    /**
     * Get Questions dropdown
     *
     * @returns {HTMLDivElement}
     */
    getQuestionsDropdown(
        serviceData
    ) {
        if (!this.demo) {
            const questionDropdown = new QuestionDropdown(serviceData);
            return questionDropdown.injectableElement;
        }
    }

    /**
     * Buttons container for new question button and dropdown list
     * @returns {HTMLDivElement}
     */
    getButtonsContainer(styleText) {
        let buttonsContainer = document.createElement('div');
        let style = document.createElement('style');
        style.innerHTML = styleText;
        buttonsContainer.setAttribute('class', 'sl-buttons-container');
        buttonsContainer.appendChild(style);
        return buttonsContainer;
    }
}

export {OpenQuestionsHelper}