import {NewStatusQuestion} from "../buttons/NewStatusQuestion";
import {TableHelper} from "./TableHelper";
import {trans} from "../../../helpers/TranslationHelper";

/**
 * Add Open questions column and handle questions
 *
 * Elements where we append the hix column are customisable
 */
class TableElementsHelper extends TableHelper {
    constructor(
        selector,
        columnSelectors,
        additionalAttributes, // use this to add extra data to each row
        ignoreAttributes, // use to ignore attributes when validating if the question has a match
        questions,
        types,
        service,
        prepend = false
    ) {
        super(selector, columnSelectors, additionalAttributes, ignoreAttributes, questions, types, service, prepend);
        this.tableHeaderElement = null;
        this.tableHeaderRowElement = null;
        this.tableHeaderRowElementClasses = null;
        this.tableHeaderRowElementStyle = 'white-space: nowrap;min-width: 160px;';
        this.tableBodyRowElement = null;
        this.tableBodyColumnElement = null;
        this.tableBodyColumnElementClasses = null;
        this.tableBodyColumnElementStyle = 'text-align: right;min-width: 160px;';
    }

    /**
     * Add element to table header
     * @param table
     * @returns {*}
     */
    async addHeader(table) {
        // look for table header
        const tableHeader = table.querySelector(this.tableHeaderElement);
        if (tableHeader === null || this.tableHasRows === false) {
            return;
        }

        // add the new element to the table header
        if (this.prepend) {
            tableHeader.prepend(await this.createTableHeaderRowElement());
        } else {
            tableHeader.appendChild(await this.createTableHeaderRowElement());
        }

        return table;
    }

    /**
     * Add column to each row of table body
     *
     * @param table
     * @returns {*}
     */
    async addColumn(table) {
        const tableBodyRowElements = table.querySelectorAll(this.tableBodyRowElement);
        for (let i = 0; i < tableBodyRowElements.length; i++) {
            let row = tableBodyRowElements[i];
            let data = this.handleAttributes(row);

            // each attribute in data from selectors.json is required so we have everything to try to match the question
            let matchingQuestion = this.handleMatchingQuestion(data);

            // handle additional data for each row
            data = this.handleAdditionalAttributes(data);

            // add new column
            let columnElement = this.createTableBodyRowElement();

            // add new question button
            this.handleNewQuestionButton(columnElement, data, matchingQuestion);

            // add new column
            if (this.prepend) {
                row.prepend(columnElement);
            } else {
                row.appendChild(columnElement);
            }
        }

        return table;
    }

    /**
     * Change table header elements
     * @param headerElement
     * @param rowElement
     * @param rowElementClasses
     * @param rowElementStyle
     */
    setTableHeaderElement(headerElement, rowElement, rowElementClasses, rowElementStyle = this.tableHeaderRowElementStyle) {
        this.tableHeaderElement = headerElement;
        this.tableHeaderRowElement = rowElement;
        this.tableHeaderRowElementClasses = rowElementClasses;
        this.tableHeaderRowElementStyle = rowElementStyle;
    }

    /**
     * Change table body elements
     * @param rowElement
     * @param columnElement
     * @param columnElementClasses
     * @param columnElementStyle
     */
    setTableBodyElements(rowElement, columnElement, columnElementClasses, columnElementStyle = this.tableBodyColumnElementStyle) {
        this.tableBodyRowElement = rowElement;
        this.tableBodyColumnElement = columnElement;
        this.tableBodyColumnElementClasses = columnElementClasses;
        this.tableBodyColumnElementStyle = columnElementStyle;
    }

    /**
     * Create table header row element
     * @returns {HTMLDivElement}
     */
    async createTableHeaderRowElement() {
        let headerElement = 'div';
        if (this.tableHeaderRowElement) {
            headerElement = this.tableHeaderRowElement;
        }

        let element = document.createElement(headerElement);
        element.innerText = await trans('open_questions.title');
        element.classList.add('hix-table-header');
        element.style = this.tableHeaderRowElementStyle;
        if (this.tableHeaderRowElementClasses) {
            element.classList.add(...this.tableHeaderRowElementClasses);
        }

        return element;
    }

    /**
     * Create table body row element
     * @returns {HTMLDivElement}
     */
     createTableBodyRowElement() {
        let columnElement = 'div';
        if (this.tableBodyColumnElement) {
            columnElement = this.tableBodyColumnElement;
        }

        let element = document.createElement(columnElement);
        element.classList.add('hix-table-columns');
        element.style = this.tableBodyColumnElementStyle;
        if (this.tableBodyColumnElementClasses) {
            element.classList.add(...this.tableBodyColumnElementClasses);
        }

        return element;
    }
}

export {TableElementsHelper};