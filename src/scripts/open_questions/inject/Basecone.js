import {UploadTagsView} from "./basecone/UploadTagsView";
import {BasePage} from "./basecone/BasePage";

export class Basecone {
    constructor() {
        this.basePage = null;
        this.uploadTags = null;
    }

    async start(demo, apiData, selectors) {
        if (!demo) {
            // handles cornerstone
            this.basePage = new BasePage();
            this.basePage.setAdministrationData(apiData, demo, selectors);
            await this.basePage.start();
        }
    }

    async handlePage(demo, selectors, apiData, accessToken) {
        if (!demo) {
            this.uploadTags = new UploadTagsView();
            this.uploadTags.setAdministrationData({}, demo, selectors);
            this.uploadTags.setSelectedAdministration(this.basePage.selectedAdministration);
            this.uploadTags.setApiData(apiData);
            this.uploadTags.setAccessToken(accessToken);
            await this.uploadTags.handlePage(demo);
        }
    }
}

let basecone = new Basecone();

document.addEventListener('baseconeStartCornerstone', async function (e) {
    await basecone.start(
        e.detail.demo,
        e.detail.apiData,
        e.detail.selectors
    );
});

document.addEventListener('baseconeStartPage', async function (e) {
    await basecone.handlePage(
        e.detail.demo,
        e.detail.selectors,
        e.detail.apiData,
        e.detail.accessToken
    );
});

