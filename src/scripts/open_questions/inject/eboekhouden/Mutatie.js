import {BasePage} from "./BasePage";

/**
 * Mutatie
 * Menu > Overzichten> Mutaties
 */
export class Mutatie extends BasePage {
    handleQuestionData(question) {
        // if both dont exist
        if (question.name === undefined && question.description === undefined) {
            return null;
        }

        // use description as name
        if (question.name === undefined || question.name === '') {
            question.name = question.description;
            question.description = '';
        }

        question.currency = 'EUR';

        return question;
    }

    /**
     * Page title
     * @returns {*|null}
     */
    getPageTitle() {
        return 'Mutaties';
    }

    async handlePage() {
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        await this.removeButtons();
        await this.appendButtons();
        await this.handleButtonsRemover();
    }
}
