import {BasePage} from "./BasePage";

/**
 * Grootboek
 * Menu > Overzichten > Grootboek
 */
export class Grootboek extends BasePage {
    /**
     * Parse fields
     * @param field
     * @param value
     * @returns {string}
     */
    parseField(field, value) {
        value = (value).toString();
        if (field === 'transaction_date') {
            value = this.parseDate(value);
        }

        if (field === 'debit_amount' || field === 'credit_amount') {
            value = this.parseAmount(value);
        }

        return value.trim();
    }

    handleQuestionData(question) {
        // if both dont exist
        if (question.name === undefined && question.description === undefined) {
            return null;
        }

        // date is required
        if (question.transaction_date === undefined) {
            return null;
        }

        // use description as name
        if (question.name === undefined || question.name === '') {
            if (question.description === undefined || question.description === '') {
                return null;
            }
            question.name = question.description;
            question.description = '';
        }

        if (question.debit_amount) {
            question.amount = question.debit_amount;
            delete question.debit_amount;
        }

        if (question.credit_amount) {
            question.amount = question.credit_amount;
            delete question.credit_amount;
        }

        question.currency = 'EUR';

        return question;
    }

    /**
     * Page title
     * @returns {*|null}
     */
    getPageTitle() {
        return 'Grootboek';
    }

    async handlePage() {
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        await this.removeButtons();
        await this.appendButtons();
        await this.handleButtonsRemover();
    }
}
