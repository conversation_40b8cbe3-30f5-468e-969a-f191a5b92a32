import {BasePage} from "./BasePage";

/**
 *  Importeren
 *  Menu > Invoeren > Importeren
 */
export class Importeren extends BasePage {
    /**
     * Parse fields
     * @param field
     * @param value
     * @returns {string}
     */
    parseField(field, value) {
        value = (value).toString();
        if (field === 'transaction_date') {
            value = this.parseDate(value);
        }
        if (field === 'amount') {
            value = this.parseAmount(value);
        }

        if (field === 'name') {
            let split = value.split(' ');
            let newName = '';
            for (let i = 0; i < split.length; i++) {
                newName = newName + split[i] + ' ';
                if (i === 3) { // get the name until the first 3 spaces
                    break;
                }
            }
            value = newName;
        }

        return value.trim();
    }

    /**
     * Page title
     * @returns {*|null}
     */
    getPageTitle() {
        return 'Importeren';
    }

    async handlePage() {
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        await this.removeButtons();
        await this.appendButtons();
        await this.handleButtonsRemover();
    }
}
