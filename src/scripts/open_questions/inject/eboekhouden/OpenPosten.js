import {BasePage} from "./BasePage";

/**
 * Open posten
 * Menu > Overzichten > Open Posten
 */
export class OpenPosten extends BasePage {
    handleQuestionData(question) {
        // if both dont exist
        if (question.name === undefined && question.description === undefined) {
            return null;
        }

        // use description as name
        if (question.name === undefined || question.name === '') {
            question.name = question.description;
            question.description = '';
        }

        question.currency = 'EUR';

        return question;
    }

    /**
     * Page title
     * @returns {*|null}
     */
    getPageTitle() {
        return 'Open posten';
    }

    async handlePage() {
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        await this.removeButtons();
        await this.appendButtons();
        await this.handleButtonsRemover();
    }
}
