import {OpenQuestionsHelper} from "../helpers/OpenQuestionsHelper";
import {ServicePage} from "../ServicePage";
import {SelectorHelper} from "../helpers/SelectorHelper";
import {NewQuestion} from "../buttons/NewQuestion";
import {QuestionTableDropdown} from "../popup/QuestionTableDropdown";
import browser from "webextension-polyfill";

/**
 * e-<PERSON>ekhouden common page class
 */
export class BasePage extends ServicePage {
    constructor(demo, selectors, page, apiData) {
        super();
        this.demo = demo;
        this.selectors = selectors;
        this.page = page;
        this.openQuestionsHelper = new OpenQuestionsHelper(this.demo);
        this.selectorHelper = new SelectorHelper();
        this.possibleQuestions = this.handleApiData(apiData);
    }

    async start() {
        if (!this.demo) {
            await this.validatePage();
        }
    }

    /**
     * Validate if we can handle page logic
     * @returns {Promise<void>}
     */
    async validatePage() {
        if (await this.isCompanyManager()) {
            return this.handlePage();
        }

        if (await this.isCompanyBlocked(this.getServiceName(), this.getCompanyId(), this.getCompanyName())) {
            return;
        }

        return this.handlePage();
    }

    /**
     * Handle page logic
     * @returns {Promise<void>}
     */
    async handlePage() {
        await this.appendCornerStone();
    }

    /**
     * Buttons Remover
     * @returns {Promise<void>}
     */
    async handleButtonsRemover() {
        let sidebar = this.selectorHelper.frameSelector(this.selectors.sidebar.index);
        for (let item in this.selectors.sidebar.items) {
            let menuItem = sidebar.querySelector(this.selectors.sidebar.items[item]);
            if (menuItem) {
                menuItem.addEventListener('click', () => {
                    this.removeButtons();
                });
            }
        }
    }

    /**
     * Page title
     * @returns {*|null}
     */
    getPageTitle() {
        return null;
    }

    /**
     * Service name
     * @returns {string}
     */
    getServiceName() {
        return 'eboekhouden_open_questions';
    }

    /**
     * Moneybird open questions category
     * @returns {string}
     */
    getServiceCategory() {
        return 'bookkeeping';
    }

    /**
     * Get External Company Id
     * @returns {string}
     */
    getCompanyId() {
        let frame = this.selectorHelper.frameSelector(this.selectors.navbar.index);
        return frame.querySelector(this.selectors.navbar.companyId).textContent.trim();
    }

    /**
     * Get Company Name
     * @returns {string}
     */
    getCompanyName() {
        let frame = this.selectorHelper.frameSelector(this.selectors.navbar.index);
        return frame.querySelector(this.selectors.navbar.companyName).textContent.trim();
    }

    /**
     * Handle api data with our columns selector
     * Result is an array of possible questions like:
     * [
     *      {
     *          "name": "SEPA OVERBOEKING IBAN: ******************",
     *          "description": "SEPA OVERBOEKING IBAN: ****************** BIC: ABNANL2ANAAM: KNMT OMSCHRIJVING: MAATSCHAPPIJCONTRI BUTIE 2022+KLACHTENREGELING 2022",
     *          "bookkeeping_number": "*********",
     *          "amount": -990,
     *          "transaction_date": "2022-1-1"
     *      }
     * ]
     */
    handleApiData(apiData) {
        if (apiData) {
            let handledQuestions = [];
            let columns = {};

            // gather all fields that we need to create questions from their api
            for (let field in this.page.columns) {
                let apiField = this.page.columns[field]; // api field from their side (ex: mutDatum)
                for (let x in apiData.colMetadata) {
                    let column = apiData.colMetadata[x];
                    if (column.name === apiField) {
                        // save index where field is (ex: field is transaction_date or bookkeeping_number)
                        columns[field] = column.index;
                    }
                }
            }

            // from their data, save questions from indexs
            for (let data in apiData.data) {
                let row = apiData.data[data];

                let question = {};
                for (let field in columns) {
                    let rowValue = row[columns[field]]; // columns[field] is the index (ex: bookkeeping_number: 0)
                    if (rowValue) {
                        question[field] = this.parseField(field, rowValue);
                    }
                }

                // validate if question can be added
                question = this.handleQuestionData(question);
                if (question) {
                    handledQuestions.push(question);
                }
            }

            return handledQuestions;
        }

        return [];
    }

    /**
     * Check if we try to insert same questions into possible questions
     * @param apiData
     */
    appendApiData(apiData) {
        let data = this.handleApiData(apiData);
        if (data.length) {
            for (let key in data) {
                let possibleQuestion = data[key];
                let matchingQuestion = this.possibleQuestions.find((question) => {
                    let differentAttribute = false;
                    for (let attribute of Object.keys(possibleQuestion)) {
                        // question has no match
                        if (possibleQuestion[attribute] !== question[attribute]) {
                            differentAttribute = true;
                            break;
                        }
                    }

                    return differentAttribute === false;
                });

                if (!matchingQuestion) {
                    this.possibleQuestions.push(data[key]);
                }
            }
        }
    }

    /**
     * Handle additional data
     * @param question
     * @returns {*}
     */
    handleQuestionData(question) {
        question.currency = 'EUR';

        return question;
    }

    /**
     * Parse fields
     * @param field
     * @param value
     * @returns {string}
     */
    parseField(field, value) {
        value = (value).toString();
        if (field === 'transaction_date') {
            value = this.parseDate(value);
        }

        if (field === 'amount') {
            value = this.parseAmount(value);
        }

        return value.trim();
    }

    /**
     * Parse date
     * @param value
     * @returns {string}
     */
    parseDate(value) {
        let date = new Date(value);
        let month = (date.getMonth() + 1);
        if (month < 10) {
            month = '0' + month;
        }
        let day = date.getDate();
        if (day < 10) {
            day = '0' + day;
        }

        return day + '-' + month + '-' + date.getFullYear();
    }

    /**
     * Parse amount
     * @param value
     * @returns {string}
     */
    parseAmount(value) {
        return (Number(value).toLocaleString('nl', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        })).toString();
    }

    /**
     * Remove buttons container
     */
    async removeButtons() {
        let elements = document.querySelectorAll('.sl-buttons-container');
        if (elements && elements.length) {
            elements.forEach(function (element) {
                element.parentNode.removeChild(element);
            });
        }
    }

    /**
     * Add container for new question button and questions dropdown list
     */
    async appendButtons() {
        if (this.possibleQuestions.length === 0) {
            console.log('No questions found!');
            return;
        }

        let buttonsContainer = document.querySelector('.sl-buttons-container');
        if (buttonsContainer === null) {
            buttonsContainer = this.openQuestionsHelper.getButtonsContainer('');
            let pageButtonContainer = document.querySelector('html');
            pageButtonContainer.appendChild(buttonsContainer);
        }

        let questionTableDropdown = await this.getQuestionsTableDropdownList();
        let questionButton = await this.getNewQuestionButton(questionTableDropdown);
        let style = document.createElement('style');
        style.innerHTML = `
            .sl-buttons-container {
                position: absolute;
                right: 260px;
                top: 16px;
                font-family: Arial,sans-serif;
            }
            .sl-buttons-container > div > div {
                display: inline-block;
                position: relative;
                margin: 0 10px;
                vertical-align: top;
            }`;
        let wrapper = document.createElement('div');
        wrapper.appendChild(style);
        wrapper.appendChild(questionButton);
        if (questionTableDropdown.injectableElement) {
            wrapper.prepend(questionTableDropdown.injectableElement);
        }
        buttonsContainer.replaceChildren(wrapper);
    }


    /**
     * Get new question button
     */
    async getNewQuestionButton(questionsTableDropdown) {
        const newQuestionButton = new NewQuestion({}, this.getServiceCategory(), this.demo);
        await newQuestionButton.init();
        newQuestionButton.injectableElement.addEventListener('click', async () => {
            newQuestionButton.setData({
                'service_name': this.getServiceName(),
                'external_company_id': this.getCompanyId(),
                'external_company_name': this.getCompanyName()
            });
            if (this.demo) {
                await newQuestionButton.openDemoPopup();
            } else {
                questionsTableDropdown.toggleDropdown();
            }
        });

        newQuestionButton.injectableElement.addEventListener('question-updated', async () => {
            this.onQuestionUpdated();
            await this.removeButtons();
            await this.appendButtons();
        });

        return newQuestionButton.injectableElement;
    }

    /**
     * Get question table dropdown list
     * @returns {Promise<*|null>}
     */
    async getQuestionsTableDropdownList() {
        if (!this.demo) {
            let questionTableDropdown = new QuestionTableDropdown({
                    'category': this.getServiceCategory(),
                    'service_name': this.getServiceName(),
                    'company_id': (await browser.storage.local.get(this.getServiceName() +'_company_id'))[this.getServiceName() + '_company_id'],
                    'page_title': this.getPageTitle(),
                    'page_url': window.location.href
                },
                this.possibleQuestions,
                [
                    'currency'
                ],
                await this.getQuestions(),
                await this.getQuestionTypes()
            );

            await questionTableDropdown.init().then(() => {
                questionTableDropdown.injectableElement.addEventListener('question-updated', () => {
                    this.onQuestionUpdated();
                    this.removeButtons();
                    this.appendButtons();
                });
            });

            return questionTableDropdown;
        }

        return null;
    }
}
