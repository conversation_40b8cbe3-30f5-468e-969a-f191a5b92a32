import {ServicePage} from "../ServicePage";
import {NewStatusQuestion} from "../buttons/NewStatusQuestion";
import {SelectorHelper} from "../helpers/SelectorHelper";
import axios from "axios";
import browser from "webextension-polyfill";

class BasePage extends ServicePage {
    constructor(selectors) {
        super();
        this.selectors = selectors;
        this.demo = false;
        this.selectorHelper = new SelectorHelper();
        this.pageTitle = null;
        this.pageUrl = null;
    }

    /**
     * Page title from where the question was created
     *
     * @returns {string}
     */
    getPageTitle(dropdownSelector) {
        let title = '';
        let selector = this.selectorHelper.helpSelector(this.selectors.page.breadcrumbs);
        if (selector) {
            title = selector.innerText.replace(/\s+/g, ' > ');
        }

        let dropdown = this.selectorHelper.helpSelector(dropdownSelector);
        if (dropdown) {
            if (title !== '') {
                title = title + ' > ';
            }

            title = title + dropdown.options[dropdown.selectedIndex].text;
        }

        return title;
    }

    /**
     * Service name
     * @returns {string}
     */
    getServiceName() {
        return 'twinfield_open_questions';
    }

    getServiceCategory() {
        return 'bookkeeping';
    }

    async onQuestionUpdated() {
        await this.openQuestionsCommunicationService.sendToBExtension(
            'questions_updated',
            {
                'companyId': (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'],
                'companyCode': (await browser.storage.local.get(this.getServiceName() + '_external_company_code'))[this.getServiceName() + '_external_company_code'],
                'companyName': (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name'],
                'serviceName': this.getServiceName()
            }
        );
    }

    createNewStatusQuestion(
        element,
        data,
        category,
        demo,
        types,
        status,
        statusText
    ) {
        let loading = true;
        if (status && status !== 'deleted') {
            loading = false;
        }
        let newStatusQuestion = new NewStatusQuestion(
            element,
            data,
            category,
            demo,
            types,
            status,
            statusText,
            loading
        );

        newStatusQuestion.injectableElement.addEventListener('new-question-click', async (event) => {
            data.public_url = await this.getBaseconeUrl(data.bookkeeping_number);
            newStatusQuestion.setData(data);
            newStatusQuestion.setLoading(false);
            await newStatusQuestion.openNewQuestionPopup(event);
            newStatusQuestion.setLoading(true);
        })
        newStatusQuestion.injectableElement.addEventListener('question-updated', () => this.onQuestionUpdated());

        return newStatusQuestion;
    }

     async handleNewQuestionButtonData(
        questionName,
        amount,
        transactionDate,
        currency,
        invoiceNumber = '',
        bookingNumber = '',
        description = "",
        pageTitle = "",
        pageUrl = "",
    ) {
        return {
            'service_name': this.getServiceName(),
            'name': questionName,
            'amount': amount,
            'transaction_date': transactionDate,
            'currency': currency,
            'invoice_number': invoiceNumber,
            'bookkeeping_number': bookingNumber,
            'description': description,
            'category': 'bookkeeping',
            'external_company_id': (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'],
            'external_company_name': (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name'],
            'company_id': (await browser.storage.local.get(this.getServiceName() + '_company_id'))[this.getServiceName() + '_company_id'],
            'page_title': pageTitle,
            'page_url': pageUrl
        };
    }

    async getBaseconeUrl(bookingNumber) {
        let url = 'https://' + window.location.hostname + '/input/default.aspx?code=INK&number=' + bookingNumber;
        const {data} = await axios.get(url);
        let htmlElement = document.createElement( 'html' );
        htmlElement.innerHTML = data;
        for (let a of htmlElement.getElementsByTagName('a')) {
            let href = a.href;
            if (href.includes('basecone') && !href.endsWith('=')) {
                return href;
            }
        }
        return null;
    }
}

export {BasePage}