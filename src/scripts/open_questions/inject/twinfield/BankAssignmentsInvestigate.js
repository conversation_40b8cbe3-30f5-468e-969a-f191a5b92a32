import {BasePage} from "./BasePage";
import {<PERSON><PERSON>rencyHelper} from "../helpers/CurrencyHelper";
import browser from "webextension-polyfill";

export class BankAssignmentsInvestigate extends BasePage {
    constructor() {
        super();
    }

    async start(demo, selectors) {
        this.demo = demo;
        this.selectors = selectors;
        await this.handlePage();
    }

    async handlePage() {
        // listen for url's changes to append buttons
        let previousUrl = '';
        const observer = new MutationObserver(async () => {
            if (location.href !== previousUrl) {
                previousUrl = location.href;
                await this.validateButtons();
            }
        });
        observer.observe(document, {
            subtree: true,
            childList: true
        });

        await this.validateButtons();
    }

    getPageTitle() {
        let title = '';
        let selector = this.selectorHelper.helpSelector(this.selectors.page.breadcrumbs);
        if (selector) {
            title = selector.innerText.replace(/\s+/g, ' > ').trim();
        }

        return title;
    }

    /**
     * Validate before appending buttons
     */
    validateButtons() {
        this.selectorHelper.waitForElement(this.selectors.list + ' > div').then(async () => {
            if (
                !await this.isCompanyBlocked(
                    this.getServiceName(),
                    (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'],
                    (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name']
                ) || this.demo === true
            ) {
                await this.appendButtons(document.querySelector(this.selectors.list));
            }
        });
    }

    /**
     * Append buttons on page
     * @param list
     * @returns {Promise<void>}
     */
    async appendButtons(list) {
        let questions = null;

        if (this.demo === false) {
            questions = await this.getQuestions();
            this.types = await this.getTypes();
        }

        this.pageTitle = this.getPageTitle();
        this.pageUrl = window.location.href;

        for (let transaction of list.children) {
            let buttonContainer = transaction.querySelector(this.selectors.buttonContainer);
            let questionName = this.selectorHelper.getTextFromSelector(this.selectors.name_fields, transaction);
            let description = this.selectorHelper.getTextFromSelector(this.selectors.description, transaction);

            let currency = CurrencyHelper.convertToText(
                this.selectorHelper.getTextFromSelector(this.selectors.amount, transaction)[0]
            );
            let amount = this.selectorHelper.getTextFromSelector(this.selectors.amount, transaction).substring(2).trim();
            let bookkeepingNumber = this.selectorHelper.getTextFromSelector(this.selectors.bookkeeping_number, transaction);
            let transactionDate = this.selectorHelper.getTextFromSelector(this.selectors.transaction_date, transaction).replaceAll("/", "-");

            let currentQuestion = null;

            if (Array.isArray(questions)) {
                currentQuestion = questions.find((question) => {
                    let currentAmountFormatted = question.amount.replace(',', '').replace('.', '');
                    let questionAmountFormatted = amount.replace(',', '').replace('.', '');

                    if ((question.name.trim() === questionName.trim() || question.description === questionName.trim()) &&
                        currentAmountFormatted === questionAmountFormatted &&
                        question.transaction_date === transactionDate &&
                        question.bookkeeping_number === bookkeepingNumber) {
                        return true;
                    }
                    return currentAmountFormatted === questionAmountFormatted &&
                        question.transaction_date === transactionDate &&
                        question.bookkeeping_number === bookkeepingNumber &&
                        question.status !== 'deleted';
                })
            }

            if (currentQuestion) {
                let statusQuestionButton = this.createNewStatusQuestion(
                    buttonContainer,
                    currentQuestion,
                    this.getServiceCategory(),
                    this.demo,
                    this.types,
                    currentQuestion.status,
                    currentQuestion.status_text
                );
            } else {
                let data = await this.handleNewQuestionButtonData(
                    questionName,
                    amount,
                    transactionDate,
                    currency,
                    null,
                    bookkeepingNumber,
                    description,
                    this.pageTitle,
                    this.pageUrl
                );
                let statusQuestionButton = this.createNewStatusQuestion(
                    buttonContainer,
                    data,
                    this.getServiceCategory(),
                    this.demo,
                    this.types
                );
            }
        }
    }
}

let bankAssignmentsInvestigate = new BankAssignmentsInvestigate();

document.addEventListener('startAngularPageEvent', async function (e) {
    await bankAssignmentsInvestigate.start(
        e.detail.demo,
        e.detail.selectors
    )
})

document.addEventListener('click', async (e) => {
    const target = e.target;
    const arrowLeft = 'fa-angle-left';
    const arrowRight = 'fa-angle-right';
    let buttonConditions =
        target.type === 'button'
        && target.childElementCount === 1
        && (target.children[0].classList.contains(arrowRight) || target.children[0].classList.contains(arrowLeft));

    if (target.classList.contains(arrowRight) || target.classList.contains(arrowLeft) || buttonConditions) {
        setTimeout(async () => {
            await bankAssignmentsInvestigate.handlePage();
        }, 1000);
    }
})