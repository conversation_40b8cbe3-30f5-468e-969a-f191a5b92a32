.sl-new-question-button {
    background-color: #06aff8;
    color: white !important;
    cursor: pointer;
    display: block;
    padding: 6px;
    text-decoration: none;
    line-height: 14px;
    font-size: 14px;
    min-width: 110px;
    min-height: 14px;
    text-align: center;
}
.sl-new-question-button:hover {
    background-color: #00a2e8;
}
.sl-new-question-button-loader,
.sl-new-question-button-loader:after {
    border-radius: 50%;
    width: 1px;
    height: 1px;
}
.sl-new-question-button-loader {
    margin: 0 auto;
    font-size: 6px;
    position: relative;
    text-indent: -9999em;
    border-top: 1.1em solid rgba(255, 255, 255, 0.2);
    border-right: 1.1em solid rgba(255, 255, 255, 0.2);
    border-bottom: 1.1em solid rgba(255, 255, 255, 0.2);
    border-left: 1.1em solid #ffffff;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation: load8 1.1s infinite linear;
    animation: load8 1.1s infinite linear;
}
@-webkit-keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
