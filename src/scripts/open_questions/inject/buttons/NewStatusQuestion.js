import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {DemoPopup} from "../demo_popup/DemoPopup";
import {QuestionPopup} from "../popup/QuestionPopup";
import {NotificationHelper} from "../../../tax/inject/helpers/NotificationHelper";
import {HistoryPopup} from "../history_popup/HistoryPopup";
import {QuickNewQuestion} from "./QuickNewQuestion";
import browser from "webextension-polyfill";
import {trans} from "../../../helpers/TranslationHelper";
import _ from "lodash";

class NewStatusQuestion {
    constructor(element, data, category, demo, types, status = null, statusText = null, loading = false) {
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
        this.data = data;
        this.demo = demo;
        this.types = types;
        this.status = status;
        this.category = category;
        this.loading = loading;
        this.allowOpenModal = true;

        this.init(element, statusText);
    }

    async init(element, statusText) {
        // remove appended button that was there before
        let button = element.querySelector('a.sl-new-question-button');

        if (button) {
            button.remove();
        }

        let _buttonElement = document.createElement('div');
        _buttonElement.style.display = 'flex';
        _buttonElement.style.alignItems = 'center';
        _buttonElement.style.gap = '4px';

        this._injectableElement = document.createElement('a');
        this._injectableElement.className = 'sl-new-question-button';
        this._injectableElement.style.verticalAlign = 'inherit';
        this._injectableElement.style = 'position: relative;';
        this._injectableElement.style.textWrap = 'nowrap';
        let throttleClick = _.debounce((event) => this.onClick(event), 500);
        this._injectableElement.addEventListener('click', (event) => throttleClick(event));
        _buttonElement.appendChild(this._injectableElement);
        element.appendChild(_buttonElement);
        element.addEventListener('click', (event) => event.stopPropagation());

        if (this.category === 'bookkeeping' && !this.demo) {
            let missingInvoice = this.types.find((type) => type.key === 'missing_invoice');
            if (missingInvoice) {
                this.quickNewQuestion = new QuickNewQuestion(browser.runtime.getURL("icons/attachment.svg"));
                this.quickNewQuestion.setTitle(await trans('open_questions.bookkeeping.missing_invoice.create_title'));
                this.quickNewQuestion.setTypeId(missingInvoice.id);
                this.quickNewQuestion.setPreferences(missingInvoice.preferences);
                this.quickNewQuestion.element().addEventListener('mousedown', (event) => this.onQuickMissingInvoiceMouseDown(event));
                window.addEventListener('storage', () => this.updateShowQuickInvoiceVisibility());

                // remove appended button that was there before
                let quickNewQuestionButton = element.querySelector('.sl-quick-new-question-button');
                if (quickNewQuestionButton) {
                    quickNewQuestionButton.remove();
                }

                _buttonElement.appendChild(this.quickNewQuestion.element());
            }
        }

        await this.handleStatusText(statusText);
    }

    async onQuickMissingInvoiceMouseDown(event) {
        event.stopPropagation();
        this._injectableElement.dispatchEvent(new Event('new-quick-question-clicked', {bubbles: true}));
        this.quickNewQuestion.setBlocked(true);

        let attachmentNeeded = false;
        let preferences = this.quickNewQuestion.getPreferences();
        if (preferences && preferences.hasOwnProperty('file_upload_required')) {
            attachmentNeeded = 'on';
        }

        let data = {
            company_id: (await browser.storage.local.get(this.data.service_name + '_company_id'))[this.data.service_name + '_company_id'],
            internal_note: "",
            type_id: this.quickNewQuestion.getTypeId(),
            attachment_needed: attachmentNeeded
        }

        this.createQuestion({
            ...this.data,
            ...data
        }, this.types).then(() => {
            this.quickNewQuestion.setBlocked(false);
        })
    }

    onClick(event) {
        if (this.allowOpenModal) {
            this.allowOpenModal = false;

            if (this.status) {
                this.openQuestionHistory(event, this.data);
            } else {
                if (this.demo) {
                    this.openDemoPopup(event);
                } else {
                    this._injectableElement.dispatchEvent(new Event('new-question-click', {bubbles: true}));
                    if (!this.loading) {
                        this.openNewQuestionPopup(event);
                    }
                }
            }
            this.allowOpenModal = true;
        }
    }

    /**
     * Set desired text
     * @param statusText
     */
    async handleStatusText(statusText) {
        const statusStyles = {
            open: '#ff470f',
            on_hold: '#ff470f',
            pending: '#ffd028',
            completed: '#23d160',
        };
        this._injectableElement.style.setProperty('cursor', 'pointer', 'important');
        this._injectableElement.style.setProperty('text-decoration', 'none', 'important');
        if (this.status && this.status !== 'deleted') {
            this._injectableElement.innerText = statusText;
            this._injectableElement.style.setProperty('color', statusStyles[this.status], 'important');
        } else {
            this._injectableElement.innerText = '+ ' + await trans('open_questions.new_question');
            this._injectableElement.style.setProperty('color', '#00AFF7', 'important');
            this.status = null;
        }
        this.updateShowQuickInvoiceVisibility()
    }

    async updateShowQuickInvoiceVisibility() {
        if (this.quickNewQuestion) {
            let companyId = (await browser.storage.local.get(this.data.service_name + '_company_id'))[this.data.service_name + '_company_id'];
            return this.quickNewQuestion.setVisibility(!this.status && companyId);
        }
    }

    /**
     * Open demo popup
     */
    async openDemoPopup() {
        const demoPopup = new DemoPopup();
        await demoPopup.init();
        const body = document.querySelector('body');
        body.appendChild(demoPopup.injectableElement);
    }

    /**
     * Set element status
     * @param data
     * @returns {Promise<void>}
     */
    async setStatus(data) {
        data['company_id'] = (await browser.storage.local.get(this.data.service_name + '_company_id'))[this.data.service_name + '_company_id'];
        const response = await this.openQuestionsCommunicationService.sendToBExtension('get_status', data);
        if (response && response.status) {
            this.status = response.status;
            this.data = {
                ...this.data,
                ...response
            };
            await this.handleStatusText(response.status_text);
        } else {
            this.status = null;
        }
    }

    /**
     * Open new question popup
     *
     * @param event
     * @returns {Promise<void>}
     */
    async openNewQuestionPopup(event) {
        event.stopPropagation();

        if (!this.loading) {
            let companies = await this.openQuestionsCommunicationService.sendToBExtension('get_companies_for_question', this.data);
            const types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                category: this.category,
                serviceName: this.data.service_name,
                data: this.data
            });

            const questionPopup = new QuestionPopup();
            await questionPopup.init(this.data, this.category, types, companies);

            questionPopup.injectableElement.addEventListener('question-updated', () => {
                this.setStatus(this.data);
                this._injectableElement.dispatchEvent(new Event('question-updated', {bubbles: true}));
            });

            const html = document.querySelector('html');
            html.appendChild(questionPopup.injectableElement);
            this._injectableElement.dispatchEvent(new Event('new-question-open', {bubbles: true}));
        }
    }

    async createQuestion(formData, types) {
        this.openQuestionsCommunicationService.sendToBExtension('create_open_question', {
            data: await this.handleFormDataByCategory(formData, types),
            category: this.category
        }).then(async () => {
            NotificationHelper.sendToast('success', await trans('open_questions.question_created'));
            await this.setStatus(this.data);
            this._injectableElement.dispatchEvent(new Event('question-updated', {bubbles: true}));
        })
    }

    /**
     * Download each url in attachments ( this function is replicated in QuestionPopup. TODO: find a way to not replicate this )
     * @param data
     * @returns {Promise<*[]>}
     */
    async handleAttachmentsDownload(data) {
        if (data.attachments === undefined) {
            return [];
        }
        let attachments = [];
        for (let attachment of data.attachments) {
            let response = await this.openQuestionsCommunicationService.sendToBExtension('download_file', {
                url: attachment
            });
            attachments.push(response);
        }
        return attachments;
    }

    /**
     * TODO: this should go inside QuestionPopup for now
     * @param formData
     * @param types
     * @returns {{}}
     */
    async handleFormDataByCategory(formData, types) {
        let attachments = await this.handleAttachmentsDownload(formData);

        if (!(await browser.storage.local.get(this.data.service_name + '_company_id'))[this.data.service_name + '_company_id'] && formData['company_id']) {
            await browser.storage.local.set({[this.data.service_name + '_company_id']: formData['company_id']});
        }

        let data = {
            service_name: this.data.service_name,
            type_id: formData['type_id'],
            internal_note: formData['internal_note'],
            company_id: formData['company_id'],
            attachment_needed: formData['attachment_needed'] === 'on',
            page_title: formData['page_title'],
            page_url: formData['page_url'],
            attachments: attachments,
        };

        let type = types.find(t => t.id === parseInt(formData['type_id']));

        if (type.key === 'missing_invoice') {
            this.data.public_url = null;
        }

        switch (this.category) {
            case 'bookkeeping':
                data.name = this.data.name;
                data.amount = this.data.amount;
                data.external_company_id = (await browser.storage.local.get(this.data.service_name + '_external_company_id'))[this.data.service_name + '_external_company_id'];
                data.external_company_name = (await browser.storage.local.get(this.data.service_name + '_external_company_name'))[this.data.service_name + '_external_company_name'];
                data.currency = this.data.currency;
                data.transaction_date = this.data.transaction_date;
                data.title = this.data.name;
                data.invoice_number = this.data.invoice_number;
                data.bookkeeping_number = this.data.bookkeeping_number;
                data.description = this.data.description;
                data.public_url = types.find((type) => parseInt(type.id) === parseInt(formData['type_id'])).key !== 'missing_invoice' ? data.public_url : null;
                break;
            case 'ocr':
                data.title = this.data.title;
                data.external_company_id = this.data.external_company_id;
                data.external_company_name = this.data.external_company_name;
                data.created_on_date = this.data.created_on_date;
                data.public_url = this.data.public_url;
                data.invoice_number = this.data.invoice_number;
                data.currency = this.data.currency;
                data.transaction_date = this.data.transaction_date;
                data.amount = this.data.amount;
                break;
            case 'wage':
                data.title = types.find(t => t.value === formData['type']).name;
                data.subtitle = this.data.subtitle;
                data.external_company_name = this.data.external_company_name;
                data.created_on_date = this.data.created_on_date;
                break;
        }

        return data;
    }

    /**
     * Open question history
     *
     * @param event
     * @param data
     * @returns {Promise<void>}
     */
    async openQuestionHistory(event, data) {
        let historyData = await this.openQuestionsCommunicationService.sendToBExtension('get_question_history', data);
        data['messages'] = historyData.messages;
        data['hostname'] = historyData.hostname;
        const historyPopup = new HistoryPopup(data);
        await historyPopup.init();
        const html = document.querySelector('html');

        historyPopup.injectableElement().addEventListener('question-updated', async () => {
            await this.setStatus(data);
            this._injectableElement.dispatchEvent(new Event('question-updated', {bubbles: true}));
        });

        this._injectableElement.dispatchEvent(new Event('new-question-open', {bubbles: true}));
        html.appendChild(historyPopup.injectableElement());
    }

    setData(data) {
        this.data = data;
    }

    setLoading(value) {
        this.loading = value;
    }

    /**
     * Button element created within class
     * @returns {HTMLAnchorElement}
     */
    get injectableElement() {
        return this._injectableElement;
    }
}

export {NewStatusQuestion};