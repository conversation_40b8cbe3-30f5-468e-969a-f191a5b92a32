class QuickNewQuestion {
    /**
     * @param {string} iconSrc
     */
    constructor(iconSrc) {
        this._element = document.createElement('img');
        this._element.setAttribute('src', iconSrc);
        this._element.style.height = '12px';
        this._element.style.verticalAlign = 'middle';
        this._element.style.cursor = 'pointer';
        this._element.style.margin = '0';
        this._element.hidden = true;
        this._element.className = 'sl-quick-new-question-button';
        this._element.addEventListener('mousedown', (event) => this.onMouseDown(event));

        this._isBlocked = false;
    }

    /**
     * @param {string} title
     */
    setTitle(title) {
        this._element.title = title;
    }

    /**
     * @param id
     */
    setTypeId(id) {
        this.type_id = id;
    }

    getTypeId() {
        return this.type_id;
    }

    /**
     * @param preferences
     */
    setPreferences(preferences) {
        this.preferences = preferences;
    }

    getPreferences() {
        return this.preferences;
    }

    /**
     * @return {HTMLImageElement}
     */
    element() {
        return this._element;
    }

    /**
     * @param {boolean} visibility
     */
    setVisibility(visibility) {
        this._element.hidden = !visibility;
    }

    /**
     * @param {boolean} blocked
     */
    setBlocked(blocked)
    {
        this._isBlocked = blocked;
    }

    onMouseDown(event) {
        // Prevent the event of mousedown to be triggered if the component is blocked.
        if (this._isBlocked) {
            event.stopImmediatePropagation();
        }
    }
}

export {QuickNewQuestion};