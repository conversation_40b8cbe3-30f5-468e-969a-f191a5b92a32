import style from '!css-loader!./new_question_button_style.css';
import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {DemoPopup} from "../demo_popup/DemoPopup";
import {QuestionPopup} from "../popup/QuestionPopup";
import {trans} from "../../../helpers/TranslationHelper";

class NewQuestion {
    constructor(data, category, demo) {
        this.data = data;
        this.demo = demo;
        this.category = category;
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
    }

    async init() {
        this._injectableElement = await this.getNewButton();
    }

    async getNewButton() {
        let newQuestionButton = document.createElement('div');
        newQuestionButton.setAttribute('class', 'sl-new-question-button');
        newQuestionButton.innerText = '+ ' + await trans('open_questions.new_question');
        newQuestionButton.appendChild(this.getStyleElement());

        return newQuestionButton;
    }

    getStyleElement() {
        const styleElement = document.createElement('style');
        styleElement.innerText = style;

        return styleElement;
    }

    async setLoading(enabled) {
        this.loading = enabled;
        if (this.loading) {
            let loadingElement = document.createElement('div');
            loadingElement.setAttribute('class', 'sl-new-question-button-loader');
            this._injectableElement.innerText = '';
            this._injectableElement.appendChild(this.getStyleElement());
            this._injectableElement.appendChild(loadingElement);
        } else {
            this._injectableElement.innerText = '+ ' + await trans('open_questions.new_question');
            this._injectableElement.appendChild(this.getStyleElement());
        }
    }

    setData(data) {
        this.data = data;
    }

    handleListeners() {
        if (this.demo) {
            this._injectableElement.addEventListener('click', async (event) => {
                event.stopPropagation();
                await this.openDemoPopup();
            });
        } else {
            this._injectableElement.addEventListener('click', async (event) => {
                event.stopPropagation();
                await this.openNewQuestionPopup();
            });
        }
    }

    /**
     * Open demo popup
     */
    async openDemoPopup() {
        const demoPopup = new DemoPopup();
        await demoPopup.init();
        const html = document.querySelector('html');
        html.appendChild(demoPopup.injectableElement);
    }

    /**
     * Open new question popup
     *
     * @returns {Promise<void>}
     */
    async openNewQuestionPopup() {
        let companies = await this.openQuestionsCommunicationService.sendToBExtension('get_companies_for_question', this.data);
        const types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
            category: this.category,
            serviceName: this.data.service_name,
            data: this.data
        });

        const questionPopup = new QuestionPopup();
        await questionPopup.init(this.data, this.category, types, companies);

        questionPopup.injectableElement.addEventListener('question-updated', () => {
            this._injectableElement.dispatchEvent(new Event('question-updated'));
        });

        const html = document.querySelector('html');

        html.appendChild(questionPopup.injectableElement);
    }

    get injectableElement() {
        return this._injectableElement;
    }
}

export {NewQuestion};