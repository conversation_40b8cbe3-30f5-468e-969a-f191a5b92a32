import {OpenQuestionsHelper} from "../helpers/OpenQuestionsHelper";
import {ServicePage} from "../ServicePage";
import {SelectorHelper} from "../helpers/SelectorHelper";
import {NewQuestion} from "../buttons/NewQuestion";
import browser from "webextension-polyfill";

export class LoketPageView extends ServicePage {
    constructor(selectors, demo, notAllowedUrls) {
        super();
        this.selectors = selectors;
        this.demo = demo;
        this.notAllowedUrls = notAllowedUrls;
        this.openQuestionsHelper = new OpenQuestionsHelper(this.demo);
        this.selectorHelper = new SelectorHelper();
    }

    async start() {
        if (this.isPageStarted) {
            await this.toggleButtons();
            return;
        }
        this.isPageStarted = true;
        await this.handlePage();
    }

    /**
     * Handle page logic
     * @returns {Promise<void>}
     */
    async handlePage() {
        // listen for url's changes to hide buttons from page
        let previousUrl = '';
        const observer = new MutationObserver(async () => {
            if (location.href !== previousUrl) {
                previousUrl = location.href;
                await this.handleNotAllowedPages(previousUrl);
                await this.toggleButtons();
            }
        });
        observer.observe(document, {
            subtree: true,
            childList: true
        });
    }

    /**
     * Handle cornerstone and buttons to show or not on the page
     * @param url
     * @returns {Promise<void>}
     */
    async handleNotAllowedPages(url) {
        let notAllowedUrl = this.notAllowedUrls.find((urlPath) => {
            return url.includes(urlPath);
        });
        if (notAllowedUrl) {
            await this.openQuestionsCommunicationService.sendToBExtension('hide_cornerstone');
            await this.openQuestionsCommunicationService.sendToBExtension('hide_cornerstone_lock');
        }
    }

    /**
     * Toggle new question button and dropdown list
     */
    async toggleButtons() {
        await this.removeButtons();
        await this.validateAppendButtons();
    }

    /**
     * Validate if we can add buttons to the page
     * @returns {Promise<void>}
     */
    async validateAppendButtons() {
        let companyIsBlocked = await this.isCompanyBlocked(
            this.getServiceName(),
            this.getCompanyId(),
            this.getSelectedSubtitle()
        ) && this.demo === false;

        if (await this.isCompanyManager()) {
            await this.appendCornerStone();
        }

        if (companyIsBlocked) {
            return;
        }

        return this.appendButtons();
    }

    /**
     * Add container for new question button and questions dropdown list
     */
    async appendButtons() {
        // if nothing is selected dont append buttons
        if (this.getSelectedQuestionSubject() === null) {
            return;
        }

        let buttonsContainer = this.openQuestionsHelper.getButtonsContainer(`
            .sl-buttons-container {
                margin-left: auto;
                margin-right: 5px;
                top: 46px;
                z-index: 99999999;
            }
            .sl-buttons-container > div {
                display: inline-block;
                position: relative;
                margin: 0 10px;
                vertical-align: top;
            }
        `);

        await this.appendCornerStone();
        await this.appendNewQuestionButton(buttonsContainer);
        await this.appendQuestionsDropdownList(buttonsContainer);

        let pageButtonContainer = document.querySelector(this.selectors.breadcrumbs);
        pageButtonContainer.appendChild(buttonsContainer);
    }

    /**
     * Remove buttons container
     */
    async removeButtons() {
        let elements = document.querySelectorAll('.sl-buttons-container');
        if (elements && elements.length) {
            elements.forEach(function (element) {
                element.parentNode.removeChild(element);
            });
        }
        await this.openQuestionsCommunicationService.sendToBExtension('hide_cornerstone');
    }

    /**
     * Append new question button to container
     */
    async appendNewQuestionButton(container) {
        let newQuestionButton = new NewQuestion({
            'service_name': this.getServiceName(),
            'external_company_id': this.getExternalId(),
            'external_company_name': this.getSelectedSubtitle(),
            'subtitle': this.getSelectedSubtitle(),
            'question_subject': this.getSelectedQuestionSubject(),
            'page_title': this.getPageTitle(),
            'page_url': window.location.href,
        }, 'wage', this.demo);

        await newQuestionButton.init();
        newQuestionButton.handleListeners();
        newQuestionButton.injectableElement.addEventListener('question-updated', async () => {
            await this.onQuestionUpdated()
            await this.toggleButtons();
        });

        container.appendChild(newQuestionButton.injectableElement);
    }

    /**
     * Append question dropdown list
     */
    async appendQuestionsDropdownList(container) {
        let questionsDropdown = await this.openQuestionsHelper.getQuestionsDropdown({
            'category': 'wage',
            'service_name': this.getServiceName(),
            'external_id': this.getExternalId()
        });

        if (questionsDropdown) {
            questionsDropdown.addEventListener('question-updated', async () => {
                await this.onQuestionUpdated();
                await this.toggleButtons();
            });

            container.prepend(questionsDropdown);
        }
    }

    /**
     * Append secure login corner button
     * @returns {Promise<*>}
     */
    async appendCornerStone() {
        if (this.demo) {
            console.log('Cornerstone: Demo mode on');
            return;
        }
        await browser.storage.local.set({[this.getServiceName() + '_external_company_id']: this.getExternalId()});
        await browser.storage.local.set({[this.getServiceName() + '_external_company_name']: this.getSelectedSubtitle()});
        await browser.storage.local.set({[this.getServiceName() + '_external_company_code']: this.getExternalId()});

        await this.onQuestionUpdated();
    }

    /**
     * Questions updated action
     */
    async onQuestionUpdated() {
        await this.openQuestionsCommunicationService.sendToBExtension(
            'questions_updated',
            {
                'companyId': (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'],
                'companyCode': (await browser.storage.local.get(this.getServiceName() + '_external_company_code'))[this.getServiceName() + '_external_company_code'],
                'companyName': (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name'],
                'serviceName': this.getServiceName()
            }
        );
    }

    /**
     * Returns selected question subject from user
     * @returns {string|null}
     */
    getSelectedQuestionSubject() {
        if (document.querySelector(this.selectors.companySelected)) {
            return 'company';
        }
        if (document.querySelector(this.selectors.employeeSelected)) {
            return 'employee';
        }

        return null;
    }

    /**
     * Selected subtitle from user (company or employee selected)
     * @returns {*|null}
     */
    getSelectedSubtitle() {
        if (this.getSelectedQuestionSubject() === null) {
            return null;
        }

        return this.getSelectedQuestionSubject() === 'company' ? this.getCompanyName() : this.getEmployeeName();
    }

    /**
     * Selected subtitle from user (company or employee selected)
     * @returns {*|null}
     */
    getExternalId() {
        if (this.getSelectedQuestionSubject() === null) {
            return null;
        }

        return this.getSelectedQuestionSubject() === 'company' ? this.getCompanyId() : this.getEmployeeId();
    }

    /**
     * Service name
     * @returns {string}
     */
    getServiceName() {
        return 'loket_open_questions';
    }

    /**
     * Get page title
     * @returns {string}
     */
    getPageTitle() {
        let title = '';
        let employee = document.querySelector(this.selectors.title.employer);
        if (employee) {
            title = employee.textContent.trim();
        }

        let links = document.querySelectorAll(this.selectors.title.links);

        if (links.length) {
            links.forEach(link => {
                if (title !== '') {
                    title = title + " > " + link.textContent.trim();
                }

            });
        }

        return title;
    }

    /**
     * Get company id
     * @returns {string}
     */
    getCompanyId() {
        return window.location.pathname.split('/')[this.selectors.administrationCompanyIdUrlIndex];
    }

    /**
     * Get employee id
     * @returns {string}
     */
    getEmployeeId() {
        let url = document.querySelector(this.selectors.administrationEmployeeSelectedUrl);
        return url.getAttribute('href').split('/')[this.selectors.administrationEmployeeIdUrlIndex];
    }

    /**
     * Get company name
     * @returns {*|null}
     */
    getCompanyName() {
        let company = document.querySelector(this.selectors.companyName);
        return company ? company.innerText.trim() : null;
    }

    /**
     * Get employee name
     * @returns {*|null}
     */
    getEmployeeName() {
        let employee = document.querySelector(this.selectors.employeeName);
        return employee ? employee.innerText.trim() : null;
    }
}
