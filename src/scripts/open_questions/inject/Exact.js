import {OpenQuestionsCommunicationService} from './OpenQuestionsCommunicationService';
import {GeneralLedgersTable} from './exact/GeneralLedgersTable';
import {SaleOutstandingItemsTable} from './exact/SaleOutstandingItemsTable';
import {PurchaseOutstandingItemsTable} from './exact/PurchaseOutstandingItemsTable';
import {SelectorHelper} from './helpers/SelectorHelper';
import {CflStatementsTable} from "./exact/CflStatementsTable";
import {ServicePage} from "./ServicePage";
import {SalesAgeingAnalysisTable} from "./exact/SalesAgeingAnalysisTable";
import browser from "webextension-polyfill";

export class Exact extends ServicePage {
    constructor() {
        super();
        this.questions = [];
        this.selectors = null;
        this.types = [];
        this.selectorHelper = new SelectorHelper();
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
    }

    async start(demo, selectors) {
        this.demo = demo;
        this.selectors = selectors;
        if (!(await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            await this.listenIframe();
        }
    }

    getServiceName() {
        return 'exact_open_questions';
    }

    getServiceCategory() {
        return 'bookkeeping';
    }

    async getQuestions() {
        let data = {
            'category': this.getServiceCategory(),
            'service_name': this.getServiceName(),
            'company_id': (await browser.storage.local.get(this.getServiceName() +'_company_id'))[this.getServiceName() + '_company_id'],
        }
        return this.openQuestionsCommunicationService.sendToBExtension('get_questions_by_company_id', data);
    }

    /**
     * Append secure login corner button
     * @returns {Promise<*>}
     */
    async appendCornerStone() {
        if (this.demo) {
            console.log('Cornerstone: Demo mode on');
            return;
        }

        await this.onQuestionUpdated();
    }

    /**
     * Listen to iframes in page and tries to get Hix company based on Exact administration ID
     *
     * @returns {Promise<void>}
     */
    async listenIframe() {
        if (this.selectors === null) {
            return;
        }

        this.selectorHelper.waitForElement(this.selectors.iframe.selector).then(async (iframe) => {
            let openQuestionsColumn = iframe.contentWindow.document.querySelector('#open_questions_column');
            if (!openQuestionsColumn) {
                let administrationIdRegex = new RegExp(this.selectors.iframe.administration_id.regex);
                let administrationId = iframe.src.match(administrationIdRegex)[0];
                let administrationNames = this.selectors.iframe.administration_names;
                let adminName = '';

                // Tries to find the available admin name on the page
                for (let key in administrationNames) {
                    adminName = document.querySelector(administrationNames[key]);
                    if (adminName) {
                        break;
                    }
                }

                let externalName = adminName.innerText;
                let externalNameParts = externalName.split(' - ');
                if (externalNameParts.length > 1) {
                    externalName = externalNameParts[1];
                }
                let externalAdministrationName = externalName.replace(/(\r\n|\n|\r)/g, '');

                let companyBlocked = await this.isCompanyBlocked(this.getServiceName(), administrationId, externalAdministrationName) && this.demo === false;
                if (await this.isCompanyManager() && companyBlocked) {
                    await browser.storage.local.set({[this.getServiceName() + '_external_company_id']: administrationId});
                    await browser.storage.local.set({[this.getServiceName() + '_external_company_name']: externalAdministrationName});
                    await browser.storage.local.set({[this.getServiceName() + '_external_company_code']: administrationId});

                    await this.appendCornerStone();
                    return;
                }

                if (companyBlocked) {
                    return;
                }

                return this.appendColumn(iframe, administrationId, externalAdministrationName);
            }
        });
    }

    async appendColumn(iframe, administrationId, externalAdministrationName) {
        await browser.storage.local.set({[this.getServiceName() + '_external_company_id']: administrationId});
        await browser.storage.local.set({[this.getServiceName() + '_external_company_name']: externalAdministrationName});
        await browser.storage.local.set({[this.getServiceName() + '_external_company_code']: administrationId});

        await this.appendCornerStone();

        await this.modifyCreditorsDebtorsTable(iframe);
    }

    /**
     * If a valid table is found, modified that table appending the open questions column
     *
     * @param element
     */
    async modifyCreditorsDebtorsTable(element) {
        let iframeSource = element.contentWindow.document.URL;
        let allowedSources = this.selectors.iframe.allowed_sources_new;
        let tableKey = null;

        // Tries to find the selector key based in the iframe source
        for (const [key, source] of Object.entries(allowedSources)) {
            const index = source.findIndex(element => {
                if (iframeSource.includes(element)) {
                    return true;
                }
            });
            if (index > -1) {
                tableKey = key;
                break;
            }
        }

        if (tableKey) {
            if (this.demo === false) {
                this.questions = await this.getQuestions();
                this.types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                    category: this.getServiceCategory(),
                    serviceName: this.getServiceName()
                });
            }

            let table = this.selectorHelper.helpSelector(this.selectors.iframe[tableKey].selector);

            if (table) {
                table.addEventListener('question-updated', () => this.onQuestionUpdated());
                switch (tableKey) {
                    case 'general_ledger_table':
                        return new GeneralLedgersTable(
                            table,
                            this.selectors,
                            this.questions,
                            this.types,
                            this.demo,
                            (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id']
                        );
                    case 'sale_outstanding_items_table':
                        return new SaleOutstandingItemsTable(
                            table,
                            this.selectors,
                            this.questions,
                            this.types,
                            this.demo,
                            (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id']
                        );
                    case 'purchase_general_ledger_table':
                        return new PurchaseOutstandingItemsTable(
                            table,
                            this.selectors,
                            this.questions,
                            this.types,
                            this.demo,
                            (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id']
                        );
                    case 'cfl_statements_table':
                        return new CflStatementsTable(
                            table,
                            this.selectors,
                            this.questions,
                            this.types,
                            this.demo,
                            (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id']
                        );
                    case 'sales_ageing_analysis_table':
                        return new SalesAgeingAnalysisTable(
                            table,
                            this.selectors,
                            this.questions,
                            this.types,
                            this.demo,
                            (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id']
                        );
                }
            }
        }
    }
}

let exact = new Exact();

document.addEventListener('start', async function (e) {
    await exact.start(e.detail.demo, e.detail.selectors);
}, {once: true});