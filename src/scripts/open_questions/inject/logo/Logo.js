import style from '!css-loader!./style.css';

class Logo {
  constructor(
      text = null,
      image = null,
      backgroundColor = '#fff',
      color = '#fff',
      width = 30,
      height = 30

    ) {
    const styleElement = document.createElement('style');
    styleElement.innerText = style;
    styleElement.style.display = "none";
    // Logo Container
    const logoContainer = document.createElement('div');
    logoContainer.classList.add('logo-container');
    logoContainer.style.width = width + 'px';
    logoContainer.style.height = height + 'px';
    logoContainer.style.lineHeight = (height - 2) + 'px';
    logoContainer.style.color = color;

    // Logo background SVG
    let xmlns = "http://www.w3.org/2000/svg";
    const logoBackground = document.createElementNS(xmlns, "svg");
    logoBackground.classList.add('logo-container-background');
    logoBackground.setAttributeNS(null, 'width', width);
    logoBackground.setAttributeNS(null, 'height', height);
    logoBackground.setAttributeNS(null, 'viewBox', "0 0 " + width + " " + height);

    let g = document.createElementNS(xmlns, "g");
    logoBackground.appendChild(g);
    let rect = document.createElementNS(xmlns, "rect");
    rect.setAttributeNS(null, 'fill', backgroundColor);
    rect.setAttributeNS(null, 'x', '0');
    rect.setAttributeNS(null, 'y', '0');
    rect.setAttributeNS(null, 'width', width);
    rect.setAttributeNS(null, 'height', height);
    rect.setAttributeNS(null, 'rx', '4');
    g.appendChild(rect);
    logoContainer.appendChild(logoBackground);

    // Logo content container
    const logoContentContainer = document.createElement('div');
    logoContentContainer.classList.add('logo-container-content');
    logoContentContainer.style.width = width + 'px';
    logoContentContainer.style.height = height + 'px';
    logoContainer.appendChild(logoContentContainer);

    // Logo image
    if (image) {
      const logoImg = document.createElement('img');
      logoImg.style.maxWidth = (width - 4) + 'px';
      logoImg.style.maxHeight = (height - 4) + 'px';
      logoImg.src = image;
      logoContentContainer.appendChild(logoImg);
    } else if (text) {
      const logoText = document.createElement('div');
      logoText.style.fontSize = this.fontSize(width, height) + 'px';
      logoText.innerText = this.shortText(text);
      logoContentContainer.appendChild(logoText);
    }

    this._injectableElement = logoContainer;

  }

  get injectableElement() {
    return this._injectableElement;
  }

  fontSize(width, height) {
    return Math.round(2 * Math.min(height, width) * 0.4 / 2);
  }

  shortText(text) {
    let charsToAvoid = ['(', ')', '*', '+', '-', '_', '!', '@', '#', '%', '&', '/', '\\'];
    const upperText = (text || '').toUpperCase();
    const splits = upperText.split(/\s/);
    if (splits.length === 0) {
      return null;
    }
    if (splits.length === 1) {
      return splits[0].slice(0, 2);
    }
    let firstWord = splits[0];
    let lastWord = splits[splits.length-1];
    let firstLetter = firstWord.slice(0, 1);
    let secondLetter = lastWord.slice(0, 1);
    let i = 1;
    let j = 1;
    while(charsToAvoid.includes(firstLetter)) {
      firstLetter = firstWord.slice(i, i+1);
      i++;
    }
    while(charsToAvoid.includes(secondLetter)) {
      secondLetter = lastWord.slice(j, j+1);
      j++;
    }

    return firstLetter + secondLetter;
  }
}

export {Logo};