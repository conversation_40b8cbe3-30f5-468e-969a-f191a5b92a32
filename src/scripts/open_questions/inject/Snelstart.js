import {ShoppingView} from "./snelstart/ShoppingView";
import {CashierAndBankView} from "./snelstart/CashierAndBankView";
import {BookingDetailsView} from "./snelstart/BookingDetailsView";
import {InvoicesView} from "./snelstart/InvoicesView";
import {SnelstartPage} from "./snelstart/SnelstartPage";

export class Snelstart {
    constructor() {
        this.snelstart = null;
        this.shopping = null;
        this.cashierAndBank = null;
        this.bookingDetails = null;
        this.invoices = null;
    }

    async start(demo, selectors, notAllowedUrls) {
        // Demo mode does not work for this service, due to the csp not allowing to load images.
        if (!demo) {
            // SnelstartPage handles cornerstone
            this.snelstart = new SnelstartPage(demo, selectors, notAllowedUrls);
            this.snelstart.start();
        }
    }

    async startRequestedPage(demo, selectors, allowedUrls) {
        if (!demo) {
            let url = window.location.pathname;
            if (url.includes(allowedUrls.shopping)) {
                this.shopping = new ShoppingView(demo, selectors);
                this.shopping.start();
            }
            if (url.includes(allowedUrls.cashierAndBank)) {
                this.cashierAndBank = new CashierAndBankView(demo, selectors);
                this.cashierAndBank.start();
            }
            if (url.includes(allowedUrls.bookingDetails)) {
                this.bookingDetails = new BookingDetailsView(demo, selectors);
                this.bookingDetails.start();
            }
            if (url.includes(allowedUrls.invoices)) {
                this.invoices = new InvoicesView(demo, selectors);
                this.invoices.start();
            }
        }
    }
}

let snelstart = new Snelstart();

document.addEventListener('start', async function (e) {
    await snelstart.start(
        e.detail.demo,
        e.detail.selectors,
        e.detail.notAllowedUrls
    )
})

document.addEventListener('startRequestedPage', async function (e) {
    await snelstart.startRequestedPage(
        e.detail.demo,
        e.detail.selectors,
        e.detail.allowedUrls
    )
})

