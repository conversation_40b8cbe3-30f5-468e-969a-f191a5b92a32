import {LoketPageView} from "./loket/LoketPageView";
import browser from "webextension-polyfill";

export class Loket {
    constructor() {
        this.loketPage = null;
    }

    async start(demo, selectors, notAllowedUrls) {
        if (!(await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            if (this.loketPage === null) {
                this.loketPage = new LoketPageView(selectors, demo, notAllowedUrls);
            }
            this.loketPage.start();
        }
    }
}

let loket = new Loket()

document.addEventListener('start', async function (e) {
    await loket.start(
        e.detail.demo,
        e.detail.selectors,
        e.detail.notAllowedUrls
    )
})
