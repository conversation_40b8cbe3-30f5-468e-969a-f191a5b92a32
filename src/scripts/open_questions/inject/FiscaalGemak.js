import {AppPage} from "./fiscaal_gemak/AppPage";
import {SpaPage} from "./fiscaal_gemak/SpaPage";
import {DeclarationOverviewPage} from "./fiscaal_gemak/DeclarationOverviewPage";
import {DeclarationEditPage} from "./fiscaal_gemak/DeclarationEditPage";
import browser from "webextension-polyfill";

export class FiscaalGemak {
    constructor() {
        this.document = null;
        this.availablePages = {
            'spa_pages': SpaPage,
            'app_pages': AppPage,
            'declaration_overview': DeclarationOverviewPage,
            'declaration_edit': DeclarationEditPage,
        }
    }

    async start(demo, settings) {
        if (!(await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            if (this.document === null) {
                let url = window.location.href;
                for (let key in settings.allowed_pages) {
                    if (url.match(settings.allowed_pages[key]) && this.availablePages[key]) {
                        this.document = new this.availablePages[key](settings.selectors[key], demo);
                        break;
                    }
                }
            }

            if (this.document) {
                await this.document.start();
            }
        }
    }
}

let fiscaalGemak = new FiscaalGemak();

document.addEventListener('start', async function (e) {
    await fiscaalGemak.start(
        e.detail.demo,
        e.detail.settings
    )
}, {once : true})

