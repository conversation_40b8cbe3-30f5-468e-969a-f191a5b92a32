import {DossierPage} from "./nextens/DossierPage";
import {ChangingDataPage} from "./nextens/ChangingDataPage";
import browser from "webextension-polyfill";

export class Nextens {
    constructor() {
        this.document = null;
        this.availablePages = {
            'dossier': DossierPage,
            'changing_data': ChangingDataPage,
            'changing_data_person': ChangingDataPage,
        }
    }

    async start(demo, settings) {
        if (!(await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            if (this.document === null) {
                let url = window.location.href;
                for (let key in settings.allowed_pages) {
                    if (url.includes(settings.allowed_pages[key])) {
                        if (this.availablePages[key]) {
                            this.document = new this.availablePages[key](settings.selectors[key], demo);
                            break;
                        }
                    }
                }
            }
            if (this.document) {
                this.document.start();
            }
        }
    }
}

let nextens = new Nextens();

document.addEventListener('start', async function (e) {
    await nextens.start(
        e.detail.demo,
        e.detail.settings
    )
}, {once : true})

