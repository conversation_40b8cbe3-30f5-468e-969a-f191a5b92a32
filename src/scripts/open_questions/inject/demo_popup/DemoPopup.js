import style from '!css-loader!./style.css';
import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {trans} from "../../../helpers/TranslationHelper";
import browser from "webextension-polyfill";

class DemoPopup {
    constructor() {
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
    }

    async init() {
        const rootContainer = document.createElement('sl-open-questions-demo-popup');
        const shadowRoot = rootContainer.attachShadow({mode: 'open'});

        const styleElement = document.createElement('style');
        styleElement.innerText = style;
        styleElement.style.display = "none";

        shadowRoot.appendChild(styleElement);

        // region Main container
        const rootDiv = document.createElement('div');

        rootDiv.classList.add('full-screen');
        shadowRoot.appendChild(rootDiv);

        //endregion

        // region Popup
        const popupContainer = document.createElement('div');
        popupContainer.classList.add('popup-container');
        rootDiv.appendChild(popupContainer);

        // endregion

        // Close button
        const closePopupButton = document.createElement('span');
        closePopupButton.classList.add('close');
        closePopupButton.classList.add('sl-icon-close-cross');
        closePopupButton.onclick = function () {
            rootDiv.style.display = "none";
        }
        popupContainer.appendChild(closePopupButton);

        // End close button

        // Content container
        const contentContainer = document.createElement('div');
        contentContainer.classList.add('content-container');
        popupContainer.appendChild(contentContainer);

        // Header container
        const headerContainer = document.createElement('div');
        headerContainer.classList.add('header-container');
        contentContainer.appendChild(headerContainer);

        // Header title
        const headerTitle = document.createElement('div');
        headerTitle.classList.add('header-title');
        headerTitle.textContent = await trans('open_questions.module');
        headerContainer.appendChild(headerTitle);

        // Header subtitle container
        const headerSubtitleContainer = document.createElement('div');
        headerSubtitleContainer.classList.add('header-subtitle-container');
        headerContainer.appendChild(headerSubtitleContainer);

        // Header subtitle
        const headerSubtitle = document.createElement('div');
        headerSubtitle.classList.add('header-subtitle');
        headerSubtitle.textContent = 'powered by';
        headerSubtitleContainer.appendChild(headerSubtitle);

        // Hix logo
        const Logo = document.createElement('div');
        Logo.classList.add('logo');
        headerSubtitleContainer.appendChild(Logo);

        // Content body
        const contentBody = document.createElement('div');
        contentBody.classList.add('content-body');
        contentContainer.appendChild(contentBody);

        // Image on the left
        const imagePopup = document.createElement('div');
        imagePopup.classList.add('image-popup');
        contentBody.appendChild(imagePopup);

        // Info container
        const infoContainer = document.createElement('div');
        infoContainer.classList.add('info-container');
        contentBody.appendChild(infoContainer);

        let user = this.openQuestionsCommunicationService.sendToBExtension('get_user_profile');
        if (user && !user.is_external) {
            // Button to redirect to new services page
            const redirectButton = document.createElement('button');
            redirectButton.classList.add('button');
            redirectButton.classList.add('is-info');
            redirectButton.textContent = await trans('open_questions.demo.i_want_it');
            redirectButton.addEventListener('click',
                () => this.openQuestionsCommunicationService.sendToBExtension('redirect_to_services')
            );
            infoContainer.appendChild(redirectButton);

            // More info button
            const moreInfoButton = document.createElement('button');
            moreInfoButton.classList.add('button');
            moreInfoButton.classList.add('is-text');
            moreInfoButton.textContent = await trans('open_questions.demo.learn_more');
            moreInfoButton.addEventListener('click',
                () => window.open('https://vimeo.com/545937002', '_blank')
            );
            infoContainer.appendChild(moreInfoButton);
        } else {
            infoContainer.textContent = await trans('open_questions.demo.contact_for_information');
        }

        // Checkbox to disable demo mode
        const checkbox = document.createElement('input');
        checkbox.setAttribute('type', 'checkbox');
        checkbox.setAttribute('name', 'hide_demo_mode');
        checkbox.onclick = function () {
            browser.storage.local.set({'hide-demo-popup': '1'});
            location.reload();
        }
        contentContainer.appendChild(checkbox);

        const checkboxLabel = document.createElement('label');
        checkboxLabel.setAttribute('for', 'hide_demo_mode');
        checkboxLabel.textContent = await trans('open_questions.demo.hide');
        contentContainer.appendChild(checkboxLabel);


        // Initial opening
        const opening = document.createElement('div');
        opening.classList.add('opening');
        rootDiv.appendChild(opening);

        const imageLeft = document.createElement('div');
        imageLeft.classList.add('image-left');
        opening.appendChild(imageLeft);

        const imageMiddle = document.createElement('div');
        imageMiddle.classList.add('image-middle');
        opening.appendChild(imageMiddle);

        const imageRight = document.createElement('div');
        imageRight.classList.add('image-right');
        opening.appendChild(imageRight);

        // End initial opening
        this._injectableElement = rootContainer;
    }

    get injectableElement() {
        return this._injectableElement;
    }
}

export {DemoPopup};