.full-screen {
    --border-color: rgba(0, 0, 0, 0.15);
    --sl-color: #0AAAEE;

    position: absolute;
    top: 0;
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2147483647;
    background: rgba(0,0,0,0.3);

    font-family: Proxima Nova, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #353535;
    overflow: hidden;
}

.full-screen .popup-container {
    padding: 1.5em;
    position: relative;
    background: url('https://securelogin.securelogin.nu/ext/img/demo-popup/bg.png');
    background-size: cover;
    width: 40%;
}

.sl-icon-close-cross {
    height: 10px;
}

.close {
    position: absolute;
    top: 15px;
    right: 15px;
    cursor: pointer;
    content: url('https://securelogin.securelogin.nu/ext/img/demo-popup/close.svg');
}

.content-container {
    display: block;
}

.header-container {
    display: block;
    text-align: center;
}

.header-title {
    font-size: 36px;
    font-weight: bold;
    color: var(--sl-color);
}

.header-subtitle-container {
    display: flex;
    justify-content: center;
}

.header-subtitle {
    color: grey;
    font-size: 20px;
}

.logo::before {
    margin-left: 10px;
    width: 200px;
    background: url('https://securelogin.securelogin.nu/ext/img/demo-popup/sl-logo.svg');
    background-repeat: no-repeat;
    background-size: contain;
    display: block;
    content: '';
    height: 30px;
}

.opening {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    animation-name: hide-div;
    animation-duration: 0.1s;
    animation-delay: 2s;
    animation-fill-mode: forwards;
}

.content-body {
    display: flex;
}

.button {
    padding: calc(.375em - 1px) .75em;
    height: 46px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
}

.button.is-text {
    background-color: transparent;
    border-color: transparent;
    color: #000;
    text-decoration: underline;
}
.button.is-text:hover {
     background-color: #f5f5f5;
     color: #363636;
 }

.button.is-info {
    background-color: #209cee;
    border-color: transparent;
    color: #fff;
}
.button.is-info:hover {
     background-color: #1496ed;
 }

.image-popup {
    width: 60%;
}

.image-popup::before {
    width: 100%;
    background: url('https://securelogin.securelogin.nu/ext/img/demo-popup/img-popup.png');
    background-repeat: no-repeat;
    background-size: contain;
    display: block;
    content: '';
    height: 300px;
}

.image-left::before {
    position: relative;
    background: url('https://securelogin.securelogin.nu/ext/img/demo-popup/img-left.png');
    background-repeat: no-repeat;
    background-size: contain;
    display: block;
    content: '';
    height: 540px;
    width: 320px;
    animation-name: move-up;
    animation-duration: 2s;
    animation-delay: 1s;
    animation-fill-mode: forwards;
}

.image-middle::before {
    position: relative;
    background: url('https://securelogin.securelogin.nu/ext/img/demo-popup/img-middle.png');
    background-repeat: no-repeat;
    background-size: contain;
    display: block;
    content: '';
    height: 540px;
    width: 320px;
    animation-name: move-down;
    animation-duration: 2s;
    animation-delay: 1s;
    animation-fill-mode: forwards;
}

.image-right::before {
    position: relative;
    background: url('https://securelogin.securelogin.nu/ext/img/demo-popup/img-right.png');
    background-repeat: no-repeat;
    background-size: contain;
    display: block;
    content: '';
    height: 540px;
    width: 320px;
    animation-name: move-up;
    animation-duration: 2s;
    animation-delay: 1s;
    animation-fill-mode: forwards;
}

.info-container {
    width: 40%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

@keyframes hide-div {
    from   {left: 50%}
    to  {left: 200%}
}

@keyframes move-down {
    from   {left:0; top:0;}
    to  {left:0; top:2000px;}
}

@keyframes move-up {
    from   {left:0; top:0;}
    to  {left:0; top:-2000px;}
}

@media screen and (max-width: 900px) {
    .content-body {
        flex-wrap: wrap;
    }

    .content-body > div {
        width: 100%;
    }
}