import {NewStatusQuestion} from "../buttons/NewStatusQuestion";

class BaseconeNewStatusQuestion extends NewStatusQuestion {
    async onClick(event) {
        if (this.data.status === 'completed' || this.data.status === 'pending' || this.status) {
            super.onClick(event);
            return;
        }
        await this.getDocumentInformation().then(async (response) => {
            if (response.publicUrl) {
                this.data.public_url = response.publicUrl;
            }
            super.onClick(event);
        }).catch(async () => {
            super.onClick(event);
        });
    }

    /**
     * Get document configuration information from Basecone
     * @returns {Promise<void>}
     */
    async getDocumentInformation() {
        return await this.openQuestionsCommunicationService.sendToBExtension('get_service_options', {
            'service_name': this.data.service_name,
            'documentId': this.data.document_id,
            'accessToken': this.data.access_token
        });
    }

}

export {BaseconeNewStatusQuestion};