import {TableElementsHelper} from "../helpers/TableElementsHelper";
import {BaseconeNewStatusQuestion} from "./BaseconeNewStatusQuestion";

/**
 * Add Open questions column and handle questions
 */
class BaseconeTableElementsHelper extends TableElementsHelper {
    /**
     * Add column to each row of table body
     *
     * @param table
     * @returns {*}
     */
    async addColumn(table) {
        const tableBodyRowElements = table.querySelectorAll(this.tableBodyRowElement);
        for (const tableBodyRowElement of tableBodyRowElements) {
            let data = this.handleAttributes(tableBodyRowElement);

            // each attribute in data from selectors.json is required so we have everything to try to match the question
            let matchingQuestion = this.handleMatchingQuestion(data);

            // handle additional data for each row
            data = this.handleAdditionalAttributes(data);

            let columnElement = this.createTableBodyRowElement();

            if (this.canAddQuestion(data)) {
                if (matchingQuestion && matchingQuestion.status !== 'deleted') {
                    matchingQuestion.service_name = data.service_name;
                    new BaseconeNewStatusQuestion(
                        columnElement,
                        matchingQuestion,
                        this.service.category,
                        this.service.demo,
                        this.types,
                        matchingQuestion.status,
                        matchingQuestion.status_text
                    );
                } else {
                    new BaseconeNewStatusQuestion(
                        columnElement,
                        data,
                        this.service.category,
                        this.service.demo,
                        this.types
                    );
                }

                this.addedQuestionButton = true;
            }
            // add new column
            if (this.prepend) {
                tableBodyRowElement.prepend(columnElement);
            } else {
                tableBodyRowElement.appendChild(columnElement);
            }
        }

        return table;
    }

    /**
     * Add additional attributes to data
     * @param data
     * @returns {*}
     */
    handleAdditionalAttributes(data) {
        if (this.additionalAttributes.documents) {
            let document = this.additionalAttributes.documents.find((document) => {
                return document.displayName.trim() === data.name && this.handleApiDate(document.dateCreated) === this.handleCreatedOnDate(data.created_on_date)
            });
            if (document) {
                data.document_id = document.uniqueId;
                data.page_title = document.displayName;
            }
        }

        data.service_name = this.service.service_name;
        data.category = this.service.category;
        data.access_token = this.additionalAttributes.accessToken;
        data.external_company_id = this.additionalAttributes.external_company_id;
        data.external_company_name = this.additionalAttributes.external_company_name;

        return data;
    }

    /**
     * Handle date and return timestamp
     * Example: 24-08-2021 12:07:39
     * @param date
     */
    handleCreatedOnDate(date) {
        let createdOnDate = date.split(' ');
        date = createdOnDate[0].split('-');

        return Date.parse(date[2] + '-' + date[1] + '-' + date[0] + 'T' + createdOnDate[1]);
    }

    /**
     * Handle date and return timestamp
     * Example: 2021-08-20T07:51:57.613
     * @param date
     */
    handleApiDate(date) {
        date = date.split('.');
        return Date.parse(date[0]);
    }
}

export {BaseconeTableElementsHelper};