import {BasePage} from "./BasePage";
import {BaseconeTableElementsHelper} from "./BaseconeTableElementsHelper";

/**
 * Uploaden & Taggen page
 */
export class UploadTagsView extends BasePage {
    async handlePage() {
        await this.handleCornerStone();

        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                await this.getCompanyName()
            )
        ) {
            return;
        }

        await this.handleTable();
    }

    async handleTable() {
        if (this.demo === false) {
            this.questions = await this.getQuestions();
            this.types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                category: this.getServiceCategory(),
                serviceName: this.getServiceName()
            });
        }

        await this.getTable();
        // wait for the user scroll on the div to handle table
        let timer = null;
        let self = this;
        let scrollableDiv = document.querySelector(this.selectors.table.uploadTags.selector + ' ' + this.selectors.table.uploadTags.scrollable);
        scrollableDiv.addEventListener('scroll', function() {
            if (timer !== null) {
                clearTimeout(timer);
            }
            timer = setTimeout(async () => {
                self.questions = await self.getQuestions();
                await self.getTable();
            }, 150);
        }, false);
    }

    /**
     * Get table from selector and then use columns selector
     * @returns {BaseconeTableElementsHelper}
     */
    async getTable() {
        let tableHelper = new BaseconeTableElementsHelper(
            this.selectors.table.uploadTags.selector,
            this.selectors.table.uploadTags.columns,
            {
                'external_company_id': this.getCompanyId(),
                'external_company_name': await this.getCompanyName(),
                'documents': this.apiData.documents,
                'accessToken': this.accessToken
            },
            [
                'transaction_date'
            ],
            this.questions,
            this.types,
            {
                'category': this.getServiceCategory(),
                'service_name': this.getServiceName(),
                'demo': this.demo
            }
        );
        tableHelper.setTableHeaderElement(
            this.selectors.table.uploadTags.header.selector,
            this.selectors.table.uploadTags.header.element,
            this.selectors.table.uploadTags.header.classes,
            'white-space: nowrap;min-width: 160px;right: 0;'
        );
        tableHelper.setTableBodyElements(
            this.selectors.table.uploadTags.body.selector,
            this.selectors.table.uploadTags.body.element,
            this.selectors.table.uploadTags.body.classes,
            'text-align: right;min-width: 160px;right: 0;'
        );

        if (this.types.length > 0) {
            await tableHelper.start();
        }

        return tableHelper;
    }

    /**
     * Get Questions
     * @returns {Promise<*>}
     */
    async getQuestions() {
        return this.openQuestionsCommunicationService.sendToBExtension('get_questions_for_company', {
            'category': this.getServiceCategory(),
            'service_name': this.getServiceName(),
            'external_id': this.getCompanyId()
        });
    }
}