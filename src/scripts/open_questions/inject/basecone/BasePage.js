import {ServicePage} from "../ServicePage";

/**
 * * Basecone base page (<PERSON>les cornerstone)
 */
export class BasePage extends ServicePage {
    constructor() {
        super();
        this.selectedAdministration = null;
        this.accessToken = null;
        this.apiData = null;
        this.questions = [];
        this.types = [];
    }

    async start() {
        await this.validatePage();
    }

    async setAdministrationData(data, demo, selectors) {
        this.demo = demo;
        this.selectors = selectors;
        this.selectorHelper.waitForElement(this.selectors.company.name).then((element) => {
            let companyName = this.getCompanyName();
            if (companyName && data.items && data.items.name === companyName) {
                this.selectedAdministration = data.items;
            }
        });
    }

    /**
     * Saves selected administration
     * @param data
     */
    setSelectedAdministration(data) {
        this.selectedAdministration = data;
    }

    /**
     * Set Access token
     * @param token
     * @returns void
     */
    setAccessToken(token) {
        this.accessToken = token;
    }

    /**
     * Set api data
     * @param data
     * @returns void
     */
    setApiData(data) {
        this.apiData = data;
    }

    /**
     * Validate if we can handle page logic
     * @returns {Promise<void>}
     */
    async validatePage() {
        if (await this.isCompanyManager()) {
            return this.handleCornerStone();
        }

        if (await this.isCompanyBlocked(
            this.getServiceName(),
            this.getCompanyId(),
            await this.getCompanyName()
        )
        ) {
            return;
        }

        return this.handleCornerStone();
    }

    async handleCornerStone() {
        await this.openQuestionsCommunicationService.sendToBExtension('hide_cornerstone');
        await this.openQuestionsCommunicationService.sendToBExtension('hide_cornerstone_lock');
        await this.appendCornerStone();
    }

    /**
     * Service name
     * @returns {string}
     */
    getServiceName() {
        return 'basecone_open_questions';
    }

    /**
     * Service category
     * @returns {string}
     */
    getServiceCategory() {
        return 'ocr';
    }

    /**
     * Get company Id
     * @returns {string|null}
     */
    getCompanyId() {
        return this.selectedAdministration ? this.selectedAdministration.id : null;
    }

    /**
     * Get company name
     * @returns {string}
     */
    getCompanyName() {
        let companyName = document.querySelector(this.selectors.company.name);
        return companyName ? companyName.textContent.trim() : '';
    }
}