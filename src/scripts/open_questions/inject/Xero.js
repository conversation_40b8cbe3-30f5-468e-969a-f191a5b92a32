import {BasePage} from "./xero/BasePage";
import browser from "webextension-polyfill";
import {AccountTransactions} from "./xero/AccountTransactions";
import {AgedPayablesSummary} from "./xero/AgedPayablesSummary";
import {AgedReceivablesSummary} from "./xero/AgedReceivablesSummary";
import {SelectorHelper} from "./helpers/SelectorHelper";

export class Xero {
    constructor() {
        this.page = null;
        this.availablePages = {
            'accountTransactions': AccountTransactions,
            'agedPayablesSummary': AgedPayablesSummary,
            'agedReceivablesSummary': AgedReceivablesSummary,
        }
        this.selectorHelper = new SelectorHelper();
    }

    async start(demo, selectors) {
        if (!(await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            await (new BasePage(demo, selectors)).start();
        }
    }

    async startRequestedPage(demo, selectors, page) {
        if (!(await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            this.selectorHelper.waitForElement(selectors.header.title).then(async () => {
                let pages = selectors.allowedPages;
                for (let language of Object.keys(pages)) {
                    for (let page of Object.keys(pages[language])) {
                        if (this.page === null && this.availablePages[page] && this.getPageTitle(selectors.header.title) === pages[language][page]) {
                            this.page = new this.availablePages[page](demo, selectors);
                        }
                        if (this.page) {
                            this.page.start();
                        }
                    }
                }
            });
        }
    }

    /**
     * Get page title
     * @returns {*}
     */
    getPageTitle(selector) {
        let title = document.querySelector(selector);
        if (title) {
            return title.textContent.trim();
        }

        return null;
    }
}

let xero = new Xero();

document.addEventListener('start', async function (e) {
    await xero.start(
        e.detail.demo,
        e.detail.selectors
    )
}, {once: true})

document.addEventListener('startRequestedPage', async function (e) {
    await xero.startRequestedPage(
        e.detail.demo,
        e.detail.selectors,
        e.detail.page
    )
})