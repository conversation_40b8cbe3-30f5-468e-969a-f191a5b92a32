import {PurchaseInvoicesView} from "./boekhound_gemak/PurchaseInvoicesView";
import {BoekhoudGemakPage} from "./boekhound_gemak/BoekhoudGemakPage";
import {SaleInvoicesView} from "./boekhound_gemak/SaleInvoicesView";
import {GeneralLedger} from "./boekhound_gemak/GeneralLedger";
import {FinancialStatementsView} from "./boekhound_gemak/FinancialStatementsView";
import browser from "webextension-polyfill";
import {FinancialCashView} from "./boekhound_gemak/FinancialCashView";

export class BoekhoudGemak {
    constructor() {
        this.purchaseInvoices = null;
        this.saleInvoices = null;
        this.generalLedger = null;
        this.financialStatements = null;
        this.financialCash = null;
    }

    async start(demo, selectors, notAllowedUrls) {
        if ((await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            this.boekhoudGemak = new BoekhoudGemakPage(demo, selectors, notAllowedUrls);
            await this.boekhoudGemak.start();
        }
    }

    async startRequestedPage(demo, selectors, allowedUrls) {
        if (!(await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            let url = window.location.pathname;
            if (url.includes(allowedUrls.purchase_invoices)) {
                this.purchaseInvoices = new PurchaseInvoicesView(demo, selectors);
                this.purchaseInvoices.start();
            }
            if (url.includes(allowedUrls.sale_invoices)) {
                this.saleInvoices = new SaleInvoicesView(demo, selectors);
                this.saleInvoices.start();
            }
            if (url.includes(allowedUrls.general_ledger)) {
                this.generalLedger = new GeneralLedger(demo, selectors);
                this.generalLedger.start();
            }
            if (url.includes(allowedUrls.financial_statements)) {
                this.financialStatements = new FinancialStatementsView(demo, selectors);
                this.financialStatements.start();
            }
            if (url.includes(allowedUrls.financial_cash)) {
                this.financialCash = new FinancialCashView(demo, selectors);
                this.financialCash.start();
            }
        }
    }
}

let boekhoudGemak = new BoekhoudGemak()

document.addEventListener('start', async function (e) {
    await boekhoudGemak.start(
        e.detail.demo,
        e.detail.selectors,
        e.detail.notAllowedUrls
    )
}, {once : true})

document.addEventListener('startRequestedPage', async function (e) {
    await boekhoudGemak.startRequestedPage(
        e.detail.demo,
        e.detail.selectors,
        e.detail.allowedUrls
    )
})

