import {OpenQuestionsHelper} from "../helpers/OpenQuestionsHelper";
import {ServicePage} from "../ServicePage";
import {MfoTableHelper} from "./helpers/MfoTableHelper";
import browser from "webextension-polyfill";

export class MfoPage extends ServicePage {
    constructor(selectors, demo) {
        super();
        this.selectors = selectors;
        this.demo = demo;
        this.openQuestionsHelper = new OpenQuestionsHelper(this.demo);
        this.appendedColumn = false;
    }

    async start() {
        this.selectorHelper.waitForElement(this.selectors.table.selector).then(async () => {
            await this.validateAppendColumn();
        });
    }

    /**
     * Validate if we can add buttons to the page
     * @returns {Promise<TableHelper>}
     */
    async validateAppendColumn() {
        let companyIsBlocked = await this.isCompanyBlocked(
            this.getServiceName(),
            this.getCompanyId(),
            this.getCompanyName()
        ) && this.demo === false;
        if (await this.isCompanyManager()) {
            await this.appendCornerStone();
            if (companyIsBlocked) {
                return;
            }
            return this.appendColumn();
        }
        if (companyIsBlocked) {
            return;
        }
        return this.appendColumn();
    }

    async appendColumn() {
        let questions = null;
        let types = [];
        if (this.demo === false) {
            questions = await this.getQuestions();
            types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                category: this.getServiceCategory(),
                serviceName: this.getServiceName()
            });
        }
        let table = new MfoTableHelper(
            this.selectors.table.selector,
            this.selectors.table.columns,
            {
                external_company_id: this.getCompanyId(),
                external_company_name: this.getCompanyName(),
                page_title: this.getPageTitle(),
                page_url: window.location.href,
                currency: 'EUR',
                searchAttachmentsVariable: this.selectors.attachments.searchVariable,
                urlForAttachment: this.selectors.attachments.url
            },
            [
                'attachment',
                'attachments'
            ],
            questions,
            types,
            {
                'category': this.getServiceCategory(),
                'service_name': this.getServiceName(),
                'demo': this.demo
            }
        );
        await table.start();
        if (this.appendedColumn === false) {
            this.appendedColumn = true;
            table.injectableElement.addEventListener('question-updated', async () => {
                await this.appendColumn();
                await this.appendCornerStone();
            });
        }
        return table;
    }

    /**
     * Get Questions
     * @returns {Promise<*>}
     */
    async getQuestions() {
        return this.openQuestionsCommunicationService.sendToBExtension('get_questions_by_company_id', {
            'category': this.getServiceCategory(),
            'service_name': this.getServiceName(),
            'external_id': this.getCompanyId(),
            'company_id': (await browser.storage.local.get(this.getServiceName() + '_company_id'))[this.getServiceName() + '_company_id']
        });
    }

    /**
     * Service name
     * @returns {string}
     */
    getServiceName() {
        return 'mfo_open_questions';
    }

    /**
     * Service category
     * @returns {string}
     */
    getServiceCategory() {
        return 'ocr';
    }

    /**
     * Get External Company Id
     * @returns {string}
     */
    getCompanyId() {
        for (let id of this.selectors.company.ids) {
            let companyId = document.querySelector(id);
            if (companyId !== null) {
                return companyId.getAttribute('value');
            }
        }
    }

    /**
     * Get Company Name
     * @returns {string}
     */
    getCompanyName() {
        let company = document.querySelector(this.selectors.company.name);
        return company.textContent.trim();
    }

    /**
     * Get page title
     * @returns {string}
     */
    getPageTitle() {
        let title = document.querySelector(this.selectors.pageTitle);
        return title.textContent.trim();
    }
}