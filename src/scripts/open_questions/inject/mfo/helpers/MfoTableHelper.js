import {trans} from "../../../../helpers/TranslationHelper";
import {TableHelper} from "../../helpers/TableHelper";
import {SelectorHelper} from "../../helpers/SelectorHelper";

/**
 * Add Open questions column and handle questions
 */
class MfoTableHelper extends TableHelper {
    /**
     * Add th element to table header
     * @param table
     * @returns {*}
     */
    async addHeader(table) {
        const thead = table.querySelector('thead');
        if (thead === null || this.tableHasRows === false) {
            return null;
        }
        for (let i = 0; i < thead.rows.length; i++) {
            if (i === 0) {
                let thElement = document.createElement('th');
                thElement.innerText = await trans('open_questions.title');
                thElement.classList.add('hix-table-header');
                if (this.headerClasses) {
                    for (let headerClass of this.headerClasses.split(" ")) {
                        thElement.classList.add(headerClass);
                    }
                }
                thElement.style = this.headerStyle;
                thead.rows[i].appendChild(thElement);
            }
        }
        return table;
    }

    handleAttributes(row) {
        let data = {};
        for (let attribute of Object.keys(this.columnSelectors)) {
            let field = '';
            for (let [index, columnElement] of this.columnSelectors[attribute].entries()) {
                let textElement = row.querySelector(columnElement);
                if (textElement) {
                    // flag to know that we found at least 1 value to add create question button
                    this.tableHasRows = true;
                    if (attribute === 'description') {
                        field = textElement.getAttribute('data-content').trim();
                        continue;
                    }
                    if (attribute === 'attachment') {
                        field = textElement.getAttribute('data-title-text').trim();
                        continue;
                    }
                    field = field + textElement.textContent.trim();
                    if ((index + 1) !== this.columnSelectors[attribute].length) {
                        field = field + ' - ';
                    }
                }
            }
            data[attribute] = this.sanitizeString(field.trim());
            if (attribute.includes('date')) {
                data[attribute] = this.sanitizeDate(field);
                const [day, month, year] = data[attribute].split('-');
                data[attribute] = `${day}-${month}-20${year}`;
            }
        }
        data = this.handleAttachments(data);
        return data;
    }

    /**
     * Parse amount
     * @param value
     * @returns {string}
     */
    parseAmount(value) {
        return (Number(value).toLocaleString('nl', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        })).toString();
    }

    /**
     * Scrape page for attachments and match them with our row
     */
    handleAttachments(row) {
        let selectorHelper = new SelectorHelper()
        if (row.attachment) {
            let documents = selectorHelper.getPageValue(this.additionalAttributes.searchAttachmentsVariable);
            if (documents) {
                for (let document of documents) {
                    if ((document.FirmName && document.FirmName === row.name)
                        && (document.Subject && document.Subject === row.description)
                        && (document.Amount && this.parseAmount(document.Amount) === row.amount)
                    ) {
                        row.attachments = [
                            window.location.origin + this.additionalAttributes.urlForAttachment.replace("documentId", document.fk_document_id)
                        ];
                    }
                }
            }
        }
        return row;
    }

    onQuestionUpdated() {
        // replaced parent because this is listened in MfoPage.js -> addEventListener('question-updated')
    }
}

export {MfoTableHelper};