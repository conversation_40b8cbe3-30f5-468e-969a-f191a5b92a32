import {ServicePage} from "./ServicePage";
import browser from "webextension-polyfill";
import {SelectorHelper} from './helpers/SelectorHelper';
import {BankTransactions} from "./minox/BankTransactions";
import {Scan} from "./minox/Scan";
import {DebtorsCreditors} from "./minox/DebtorsCreditors";

export class Minox extends ServicePage {
    constructor() {
        super();
        this.selectorHelper = new SelectorHelper();
    }

    async start(demo, settings) {
        if (!(await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            await this.setPages(settings, demo);
            await this.setAdministrationData(settings);

            if (
                await this.isCompanyBlocked(
                    this.getServiceName(),
                    (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'],
                    (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name']
                )
            ) {
                return;
            }

            await this.selectorHelper.sleep(1000);
            this.startCurrentPage(demo, settings);

            document.addEventListener('click', async (e) => {
                await this.handleEventStartCurrentPage(e.target, demo, settings);
            });

            const elementToObserve = document.querySelector('body');

            const observer = new MutationObserver(mutationsList => {
                for (const mutation of mutationsList) {
                    // Check if child nodes were added or removed
                    if (
                        mutation.type === 'childList'
                        && mutation.addedNodes.length > 0
                        && Object.values(settings.allowed_pages).includes(mutation.addedNodes[0].innerText)
                    ) {
                        this.startCurrentPage(demo, settings);
                        break;
                    }
                }
            });

            observer.observe(elementToObserve, { childList: true, subtree: true });

        }
    }

    async handleEventStartCurrentPage(target, demo, settings) {
        console.log(target)
        if (target.classList.value === 'fa fa-stack-2x fa-check' || (target.nodeName === 'LI' && target.classList.contains('groen'))) {
            await this.selectorHelper.sleep(2000);
            await this.setAdministrationData(settings);
        }
        else if (target.nodeName === 'BUTTON' && !target.classList.value.includes('active')) {
            await this.selectorHelper.sleep(1000);
            this.startCurrentPage(demo, settings);
        }
        else if (
            target.classList.value.includes('mnx-list-cell-content ng-binding')
        ) {
            await this.selectorHelper.waitForElement('.tabSelected').then(async (tabSelected) => {
                if (tabSelected && [settings.allowed_pages.debtors, settings.allowed_pages.creditors].includes(tabSelected.innerText.trim())) {
                    let parent = target.parentElement.parentElement;
                    if (parent.children[0].classList.contains('hix-table-columns')) {
                        return;
                    }
                    document.querySelectorAll('.hix-table-columns').forEach(element => {
                        element.remove();
                    })
                    document.querySelectorAll('.hix-table-header').forEach(element => {
                        element.remove();
                    })
                    await this.selectorHelper.sleep(1000);
                    this.startCurrentPage(demo, settings);
                }
            });
        }
        else if (
            target.innerText
            && (
                Object.values(settings.allowed_pages).includes(target.innerText.trim())
                || ['Inkoop', 'Verkoop'].includes(target.innerText.trim())
            )
        ) {
            if (document.querySelector('.tabSelected').innerText.trim() !== target.innerText) {
                await this.selectorHelper.sleep(1000);
                this.startCurrentPage(demo, settings);
            }
        }
    }

    startCurrentPage(demo, settings) {
        const page = this.getCurrentPage(settings);
        if (page) {
            this.pages[page].handle();
        }
    }

    async setPages(settings, demo) {
        this.pages = {
            bank_transactions: new BankTransactions(settings.selectors.bank_transactions, demo),
            scan: new Scan(settings.selectors.scan, demo),
            debtors: new DebtorsCreditors(settings.selectors.debtors, demo),
            creditors: new DebtorsCreditors(settings.selectors.creditors, demo)
        }

        this.demo = demo;
    }

    async setAdministrationData(settings) {
        await this.selectorHelper.sleepUntil(() => document.querySelector(settings.selectors.administration), 5000)
            .then(async () => {
                let administrationElement = await this.selectorHelper.waitForElement(settings.selectors.administration);
                if (administrationElement) {
                    let content = administrationElement.innerText;
                    if (content) {
                        let contentArray = content.split(' - ', 2);
                        let administrationId = contentArray[0] + '-' + contentArray[1];
                        // get everything after the second occurrence of ' - ' for the administration name
                        let administrationNameIndex = content.split(' - ', 2).join(' - ').length + 2;
                        let administrationName = content.substring(administrationNameIndex);
                        await browser.storage.local.set({[this.getServiceName() + '_external_company_id']: administrationId});
                        await browser.storage.local.set({[this.getServiceName() + '_external_company_name']: administrationName});
                        await browser.storage.local.set({[this.getServiceName() + '_external_company_code']: administrationId});
                        if (this.demo === false) {
                            await this.onQuestionUpdated();
                        }
                    }
                }
            }).catch(() => {
                console.log('Minox - timeout looking for admin data');
            });
    }

      getCurrentPage(settings) {
        let selectedTab = document.querySelector(settings.selectors.selected_tab);
        if (selectedTab) {
            let selectedTabText = selectedTab.innerText.trim();
            for (const [key, page] of Object.entries(settings.allowed_pages)) {
                if (page === selectedTabText) {
                    return key;
                }
            }
        }

    }

    getServiceName() {
        return 'minox_open_questions';
    }

    getServiceCategory() {
        return 'bookkeeping';
    }

    async getQuestions() {
        let data = {
            'category': this.getServiceCategory(),
            'service_name': this.getServiceName(),
            'company_id': (await browser.storage.local.get(this.getServiceName() +'_company_id'))[this.getServiceName() + '_company_id'],
        }
        return this.openQuestionsCommunicationService.sendToBExtension('get_questions_by_company_id', data);
    }
}

let minox = new Minox();

document.addEventListener('start', async function (e) {
    await minox.start(
        e.detail.demo,
        e.detail.settings
    )
}, {once: true})

