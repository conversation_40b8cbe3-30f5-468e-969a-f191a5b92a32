import {BoekhoudGemakPage} from "./BoekhoudGemakPage";
import {FinancialStatementsTableHelper} from "./helpers/FinancialStatementsTableHelper";

/**
 * Financial Statements (Financieel -> Boeken -> Bank -> Nog te matchen mutaties)
 */
export class FinancialStatementsView extends BoekhoudGemakPage {
    async handlePage() {
        await this.appendCornerStone();

        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        this.selectorHelper.waitForElement(this.selectors.financial_statements.table.selector + ' ' + this.selectors.financial_statements.table.header_element).then(async () => {
            await this.handleTable();

            let self = this;
            let timer = null;
            let scrollableDiv = document.querySelector(this.selectors.financial_statements.table.selector + ' ' + this.selectors.financial_statements.table.scrollable_div);
            // wait for the user scroll on the div to handle table
            scrollableDiv.addEventListener('scroll', function() {
                if (timer !== null) {
                    clearTimeout(timer);
                }
                timer = setTimeout(async () => {
                    await self.handleTable();
                }, 150);
            }, false);
        });

        // remove gap from table
        this.selectorHelper.waitForElement(this.selectors.financial_statements.table.selector + ' ' + this.selectors.financial_statements.scrollbar_gap).then((element) => {
            element.remove();
        });
    }

    async handleTable() {
        let table = await this.getTable(
            this.selectors.financial_statements.table.selector,
            this.selectors.financial_statements.columns
        );

        // add handle new table header element
        table.setTableHeaderElement(
            this.selectors.financial_statements.table.header_element,
            this.selectors.financial_statements.table.header_row_element,
            this.selectors.financial_statements.table.header_row_element_classes
        );

        // add handle new table column element
        table.setTableBodyElements(
            this.selectors.financial_statements.table.body_row_element,
            this.selectors.financial_statements.table.body_column_element,
            this.selectors.financial_statements.table.body_column_element_classes
        );

        await table.start();
    }

    /**
     * Get table from selector and then use columns selector
     * @param selector
     * @param columnSelectors
     * @param additionalAttributes
     * @param ignoreAttributes
     * @returns {FinancialStatementsTableHelper}
     */
    async getTable(selector, columnSelectors, additionalAttributes = {}, ignoreAttributes = []) {
        let questions = null;
        let types = [];
        if (this.demo === false) {
            questions = await this.getQuestions();
            types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                category: this.getServiceCategory(),
                serviceName: this.getServiceName()
            });
        }

        additionalAttributes.page_title = this.getPageTitle();
        additionalAttributes.page_url = window.location.href;

        return new FinancialStatementsTableHelper(
            selector,
            columnSelectors,
            additionalAttributes,
            ignoreAttributes,
            questions,
            types,
            {
                'category': this.getServiceCategory(),
                'service_name': this.getServiceName(),
                'demo': this.demo
            }
        );
    }
}
