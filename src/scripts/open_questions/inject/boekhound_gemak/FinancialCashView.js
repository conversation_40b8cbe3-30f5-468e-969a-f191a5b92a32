import {BoekhoudGemakPage} from "./BoekhoudGemakPage";

/**
 * Financial Cash (Financieel -> <PERSON><PERSON><PERSON> -> Kas -> Transactie details)
 */
export class FinancialCashView extends BoekhoudGemakPage {
    async handlePage() {
        await this.appendCornerStone();

        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        this.selectorHelper.waitForElement(this.selectors.financial_cash.table.selector + ' ' + this.selectors.financial_cash.table.header_element).then(async () => {
            await this.handleTable();
        });
    }

    async handleTable() {
        let table = await this.getTable(
            this.selectors.financial_cash.table.selector,
            this.selectors.financial_cash.columns
        );

        // add handle new table header element
        table.setTableHeaderElement(
            this.selectors.financial_cash.table.header_element,
            this.selectors.financial_cash.table.header_row_element,
            this.selectors.financial_cash.table.header_row_element_classes
        );

        // add handle new table column element
        table.setTableBodyElements(
            this.selectors.financial_cash.table.body_row_element,
            this.selectors.financial_cash.table.body_column_element,
            this.selectors.financial_cash.table.body_column_element_classes
        );

        await table.start();
    }
}
