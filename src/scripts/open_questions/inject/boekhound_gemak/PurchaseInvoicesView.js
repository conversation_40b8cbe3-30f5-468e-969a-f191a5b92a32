import {BoekhoudGemakPage} from "./BoekhoudGemakPage";

/**
 * Invoices (Openstaande posten crediteuren)
 */
export class PurchaseInvoicesView extends BoekhoudGemakPage {
    async handlePage() {
        await this.appendCornerStone();
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        this.selectorHelper.waitForElement(this.selectors.purchase_invoices.table.selector + ' ' + this.selectors.purchase_invoices.table.header_element).then(async () => {
            await this.handleTable();
        });

        this.selectorHelper.waitForElement(this.selectors.purchase_invoices.dropdown.selector).then(async () => {
            if (this.observerSet !== true) {
                // Observe when user change dropdown to "per factuur"
                const observer = new MutationObserver(() => {
                    this.handleTable();
                });
                observer.observe(document.querySelector(this.selectors.purchase_invoices.dropdown.selector), {
                    subtree: true,
                    childList: true,
                    attributes: true,
                    characterData: true
                });
            }
        });
    }

    async handleTable() {
        let table = await this.getTable(
            this.selectors.purchase_invoices.table.selector,
            this.selectors.purchase_invoices.columns
        );

        // add handle new table header element
        table.setTableHeaderElement(
            this.selectors.purchase_invoices.table.header_element,
            this.selectors.purchase_invoices.table.header_row_element,
            this.selectors.purchase_invoices.table.header_row_element_classes
        );

        // add handle new table column element
        table.setTableBodyElements(
            this.selectors.purchase_invoices.table.body_row_element,
            this.selectors.purchase_invoices.table.body_column_element,
            this.selectors.purchase_invoices.table.body_column_element_classes
        );

        this.selectorHelper.waitForElement(this.selectors.purchase_invoices.dropdown.selector).then(async () => {
            await this.selectorHelper.sleep(1000);
            let dropdown = document.querySelector(this.selectors.purchase_invoices.dropdown.selector);
            let content = this.selectors.purchase_invoices.dropdown.content;
            for (let key in content) {
                if (content[key] === dropdown.innerText) {
                    table.start();
                }
            }
        });
    }
}
