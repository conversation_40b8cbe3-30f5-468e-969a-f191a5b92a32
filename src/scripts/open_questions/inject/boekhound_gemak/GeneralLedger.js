import {BoekhoudGemakPage} from "./BoekhoudGemakPage";
import {GeneralLedgerTableHelper} from "./helpers/GeneralLedgerTableHelper";

/**
 * General Ledger (Info en rapportage)
 */
export class GeneralLedger extends BoekhoudGemakPage {
    async handlePage() {
        await this.appendCornerStone();
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        this.selectorHelper.waitForElement(this.selectors.general_ledger.table.selector + ' ' + this.selectors.general_ledger.table.header_element).then(async () => {
            await this.handleTable();
        });

        this.selectorHelper.waitForElement(this.selectors.general_ledger.tab.selector, 3000).then(async (tab) => {
            tab.addEventListener('click', () => {
                this.selectorHelper.waitForElement(this.selectors.general_ledger.table.selector + ' ' + this.selectors.general_ledger.table.header_element).then(async () => {
                    await this.handleTable();
                });
            });
            this.selectorHelper.waitForElement(this.selectors.general_ledger.table.selector + ' ' + this.selectors.general_ledger.table.header_element, 3000).then(async () => {
                await this.selectorHelper.sleep(2000);
                await this.handleTable();
            });
        });
    }

    async handleTable() {
        let questions = null;
        let types = [];
        if (this.demo === false) {
            questions = await this.getQuestions();
            types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                category: this.getServiceCategory(),
                serviceName: this.getServiceName()
            });
        }

        let table = new GeneralLedgerTableHelper(
            this.selectors.general_ledger.table.selector,
            this.selectors.general_ledger.columns,
            {
                'page_title': this.getPageTitle(),
                'page_url': window.location.href
            },
            [],
            questions,
            types,
            {
                'category': this.getServiceCategory(),
                'service_name': this.getServiceName(),
                'demo': this.demo
            }
        );

        // add handle new table header element
        table.setTableHeaderElement(
            this.selectors.general_ledger.table.header_element,
            this.selectors.general_ledger.table.header_row_element,
            this.selectors.general_ledger.table.header_row_element_classes,
            this.selectors.general_ledger.headers
        );

        // add handle new table column element
        table.setTableBodyElements(
            this.selectors.general_ledger.table.body_row_element,
            this.selectors.general_ledger.table.body_column_element,
            this.selectors.general_ledger.table.body_column_element_classes
        );

        this.selectorHelper.waitForElement(this.selectors.general_ledger.tab.selector).then(async () => {
            if (this.isSelectedTab()) {
                table.start();
            }
        });
    }

    // "mutaties" tab is selected
    isSelectedTab() {
        return document.querySelector(this.selectors.general_ledger.tab.selector);
    }
}
