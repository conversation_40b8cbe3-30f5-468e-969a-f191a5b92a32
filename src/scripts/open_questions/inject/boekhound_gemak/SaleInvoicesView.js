import {BoekhoudGemakPage} from "./BoekhoudGemakPage";
import {SaleInvoicesViewTableHelper} from "./helpers/SaleInvoicesViewTableHelper";

/**
 * Invoices (openstaande posten)
 */
export class SaleInvoicesView extends BoekhoudGemakPage {
    async handlePage() {
        await this.appendCornerStone();
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        this.selectorHelper.waitForElement(this.selectors.sale_invoices.table.selector + ' ' + this.selectors.sale_invoices.table.header_element).then(async () => {
            await this.handleTable();
        });

        this.selectorHelper.waitForElement(this.selectors.sale_invoices.dropdown.selector).then(async () => {
            if (this.observerSet !== true) {
                // Observe when user change dropdown to "per factuur"
                const observer = new MutationObserver(() => {
                    this.handleTable();
                });
                observer.observe(document.querySelector(this.selectors.sale_invoices.dropdown.selector), {
                    subtree: true,
                    childList: true,
                    attributes: true,
                    characterData: true
                });
            }
        });
    }

    async handleTable() {
        let table = await this.getTable(
            this.selectors.sale_invoices.table.selector,
            this.selectors.sale_invoices.columns
        );

        // add handle new table header element
        table.setTableHeaderElement(
            this.selectors.sale_invoices.table.header_element,
            this.selectors.sale_invoices.table.header_row_element,
            this.selectors.sale_invoices.table.header_row_element_classes
        );

        // add handle new table column element
        table.setTableBodyElements(
            this.selectors.sale_invoices.table.body_row_element,
            this.selectors.sale_invoices.table.body_column_element,
            this.selectors.sale_invoices.table.body_column_element_classes
        );

        this.selectorHelper.waitForElement(this.selectors.sale_invoices.dropdown.selector).then(async () => {
            let dropdown = document.querySelector(this.selectors.sale_invoices.dropdown.selector);
            let content = this.selectors.sale_invoices.dropdown.content;
            for (let key in content) {
                if (content[key] === dropdown.innerText) {
                    table.start();
                }
            }
        });
    }

    async getTable(selector, columnSelectors, additionalAttributes = {}, ignoreAttributes = []) {
        let questions = null;
        let types = [];
        if (this.demo === false) {
            questions = await this.getQuestions();
            types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                category: this.getServiceCategory(),
                serviceName: this.getServiceName()
            });
        }

        additionalAttributes.page_title = this.getPageTitle();
        additionalAttributes.page_url = window.location.href;

        return new SaleInvoicesViewTableHelper(
            selector,
            columnSelectors,
            additionalAttributes,
            ignoreAttributes,
            questions,
            types,
            {
                'category': this.getServiceCategory(),
                'service_name': this.getServiceName(),
                'demo': this.demo
            },
            false,
            this.selectors.sale_invoices.special_columns
        );
    }
}
