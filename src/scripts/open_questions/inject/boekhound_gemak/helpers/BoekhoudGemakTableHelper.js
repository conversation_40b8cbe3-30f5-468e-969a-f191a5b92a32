import {TableElementsHelper} from "../../helpers/TableElementsHelper";
import {NewStatusQuestion} from "../../buttons/NewStatusQuestion";

/**
 * Add Open questions column and handle questions
 */
class BoekhoudGemakTableHelper extends TableElementsHelper {
    /**
     * Create table header row element
     * @returns {HTMLDivElement}
     */
    async createTableHeaderRowElement() {
        let element = await super.createTableHeaderRowElement();
        element.style.color = 'rgb(175 180 184)';
        return element;
    }

    /**
     * Handle currency by row
     * @param row
     * @returns {string}
     */
    handleCurrency(row) {
        let currency = 'EUR'; // EUR is default
        for (let [index, columnElement] of this.columnSelectors['amount'].entries()) {
            let textElement = row.querySelector(columnElement);
            if (textElement && textElement.textContent.includes('£')) {
                currency = 'GBP';
            }
            if (textElement && textElement.textContent.includes('$')) {
                currency = 'USD';
            }
        }

        return currency;
    }

    /**
     * Sanitize string
     * @param input
     * @returns {*}
     */
    sanitizeString(input) {
        let forbiddenChars = ['€', '£', '$'];
        for (let char of forbiddenChars) {
            input = input.split(char).join('');
        }

        // remove "(number)" from strings, example: (7432)
        input = input.replace(/(?<=\().*(?=\))/, "").replace("()","");

        return input.trim();
    }

    async addColumn(table) {
        const tableBodyRowElements = table.querySelectorAll(this.tableBodyRowElement);

        for (let i = 0; i < tableBodyRowElements.length; i++) {
            let row = tableBodyRowElements[i];
            let data = this.handleAttributes(row);

            // each attribute in data from selectors.json is required so we have everything to try to match the question
            let matchingQuestion = this.handleMatchingQuestion(data);

            data.service_name = this.service.service_name;
            data.category = this.service.category;
            data.currency = this.handleCurrency(row);

            // handle additional data for each row
            for (let attribute of Object.keys(this.additionalAttributes)) {
                data[attribute] = this.additionalAttributes[attribute];
            }

            // add new column
            let columnElement = this.createTableBodyRowElement();

            // add new question button
            this.handleNewQuestionButton(columnElement, data, matchingQuestion);

            // add new column
            row.appendChild(columnElement);
        }

        return table;
    }
}

export {BoekhoudGemakTableHelper};