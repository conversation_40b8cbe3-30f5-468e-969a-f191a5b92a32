import {BoekhoudGemakTableHelper} from "./BoekhoudGemakTableHelper";

/**
 * Add Open questions column and handle questions
 */
class SaleInvoicesViewTableHelper extends BoekhoudGemakTableHelper {

    constructor(selector, columnSelectors, additionalAttributes, ignoreAttributes, questions, types, service, prepend, specialColumns) {
        super(selector, columnSelectors, additionalAttributes, ignoreAttributes, questions, types, service, prepend);
        this.specialColumns = specialColumns;
    }

    handleAttributes(row) {
        let data = super.handleAttributes(row);

        let invoiceNumber = row.querySelector(this.specialColumns.invoice_number[0]);
        if (invoiceNumber) {
            data['description'] = this.sanitizeString(row.querySelector(this.specialColumns.description[0]).textContent);
        } else {
            invoiceNumber = row.querySelector(this.specialColumns.invoice_number[1]);
        }

        data['invoice_number'] = this.sanitizeString(invoiceNumber.textContent);

        return data;
    }
}

export {SaleInvoicesViewTableHelper};