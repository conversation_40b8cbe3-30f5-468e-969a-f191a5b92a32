import {BoekhoudGemakTableHelper} from "./BoekhoudGemakTableHelper";
import {NewStatusQuestion} from "../../buttons/NewStatusQuestion";
import {trans} from "../../../../helpers/TranslationHelper";

/**
 * Add Open questions column and handle questions
 */
class FinancialStatementsTableHelper extends BoekhoudGemakTableHelper {
    /**
     * Handle currency by row
     * @param row
     * @param column
     * @returns {string}
     */
    handleCurrency(row, column) {
        let currency = 'EUR'; // EUR is default
        if (this.columnSelectors[column]) {
            for (let [index, columnElement] of this.columnSelectors[column].entries()) {
                let textElement = row.querySelector(columnElement);
                if (textElement && textElement.textContent.includes('£')) {
                    currency = 'GBP';
                }
                if (textElement && textElement.textContent.includes('$')) {
                    currency = 'USD';
                }
            }
        }

        return currency;
    }

    /**
     * Return "Af" amount column if more than 0, or we use "Bij" column amount
     * @returns {string}
     * @param amount
     */
    handleSelectedAmountColumn(amount) {
        if (parseInt(amount) > 0) {
            return 'amount';
        }

        return 'receive_amount';
    }

    /**
     * Match row in a page with questions from secure login
     *
     * Each field from selectors.json is required so we have everything to try to match the question
     * @param data
     * @returns {null}
     */
    handleMatchingQuestion(data) {
        let matchingQuestion = null;
        if (this.questions) {
            matchingQuestion = this.questions.find((question) => {
                let differentAttribute = false;
                for (let attribute of Object.keys(data)) {
                    // ignore validation for this attribute
                    if (this.ignoreAttributes && this.ignoreAttributes.indexOf(attribute) !== -1) {
                        continue;
                    }

                    if (attribute === 'amount') {
                        // validate if amount coming from backend is "receive_amount"
                        let column = this.handleSelectedAmountColumn(data.amount);
                        if (data[column] !== question[attribute]) {
                            differentAttribute = true;
                        }
                        break;
                    }

                    // question has no match with given row
                    if (data[attribute] !== question[attribute]) {
                        differentAttribute = true;
                        break;
                    }
                }

                // no different attribute found, its a match
                return differentAttribute === false && question.status !== 'deleted';
            })
        }

        return matchingQuestion;
    }

    addColumn(table) {
        const tableBodyRowElements = table.querySelectorAll(this.tableBodyRowElement);

        for (let i = 0; i < tableBodyRowElements.length; i++) {
            let row = tableBodyRowElements[i];
            let data = this.handleAttributes(row);

            // each attribute in data from selectors.json is required so we have everything to try to match the question
            let matchingQuestion = this.handleMatchingQuestion(data);

            data.service_name = this.service.service_name;
            data.category = this.service.category;

            // handle additional data for each row
            for (let attribute of Object.keys(this.additionalAttributes)) {
                data[attribute] = this.additionalAttributes[attribute];
            }

            let column = this.handleSelectedAmountColumn(data.amount);
            data.currency = this.handleCurrency(row, column);
            if (column === 'receive_amount') {
                data.amount = data.receive_amount;
            }

            let columnElement = this.createTableBodyRowElement();
            if (matchingQuestion && matchingQuestion.status !== 'deleted') {
                new NewStatusQuestion(columnElement, matchingQuestion, this.service.category, this.service.demo, this.types, matchingQuestion.status, matchingQuestion.status_text);
            } else {
                new NewStatusQuestion(columnElement, data, this.service.category, this.service.demo, this.types);
            }

            row.appendChild(columnElement);
        }

        return table;
    }

    /**
     * Create table header row element
     * @returns {HTMLDivElement}
     */
    async createTableHeaderRowElement() {
        let headerElement = 'div';
        if (this.tableHeaderRowElement) {
            headerElement = this.tableHeaderRowElement;
        }

        let element = document.createElement(headerElement);
        element.innerText = await trans('open_questions.title');
        element.classList.add('hix-table-header');
        element.style = 'white-space: nowrap; padding: 12px 10px;width: 140px;text-align: center;overflow: hidden;text-overflow: ellipsis;';
        if (this.tableHeaderRowElementClasses) {
            element.classList.add(...this.tableHeaderRowElementClasses);
        }

        return element;
    }

    /**
     * Create table body row element
     * @returns {HTMLDivElement}
     */
    createTableBodyRowElement() {
        let columnElement = 'div';
        if (this.tableBodyColumnElement) {
            columnElement = this.tableBodyColumnElement;
        }

        let element = document.createElement(columnElement);
        element.classList.add('hix-table-columns');
        element.style = 'text-align: right;padding: 16px 10px;';
        if (this.tableBodyColumnElementClasses) {
            element.classList.add(...this.tableBodyColumnElementClasses);
        }

        return element;
    }
}

export {FinancialStatementsTableHelper};