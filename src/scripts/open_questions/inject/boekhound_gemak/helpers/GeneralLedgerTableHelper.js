import {NewStatusQuestion} from "../../buttons/NewStatusQuestion";
import {BoekhoudGemakTableHelper} from "./BoekhoudGemakTableHelper";

/**
 * Add Open questions column and handle questions for General Ledger Page
 */
class GeneralLedgerTableHelper extends BoekhoudGemakTableHelper {
    /**
     * Handle currency by value
     * @returns {string}
     * @param value
     */
    handleCurrency(value) {
        let currency = 'EUR'; // EUR is default
        if (value && value.includes('£')) {
            currency = 'GBP';
        }
        if (value && value.includes('$')) {
            currency = 'USD';
        }

        return currency;
    }

    /**
     * Add column to each row of table body
     *
     * @param table
     * @returns {*}
     */
    addColumn(table) {
        const tableBodyRowElements = table.querySelectorAll(this.tableBodyRowElement);
        for (let i = 0; i < tableBodyRowElements.length; i++) {
            let row = tableBodyRowElements[i];
            let columns = tableBodyRowElements[i].querySelectorAll(this.tableBodyColumnElement);
            let data = this.handleRowColumns(columns);

            // if required columns are not found don't add row
            if (data === false) {
                continue;
            }

            // flag to know that we found at least 1 value to add create question button
            this.tableHasRows = true;

            // each attribute in data from selectors.json is required so we have everything to try to match the question
            let matchingQuestion = this.handleMatchingQuestion(data);

            // handle additional data for each row
            data.service_name = this.service.service_name;
            data.category = this.service.category;
            for (let attribute of Object.keys(this.additionalAttributes)) {
                data[attribute] = this.additionalAttributes[attribute];
            }

            let statusQuestionButton = null;
            let columnElement = this.createTableBodyRowElement();

            if (matchingQuestion && matchingQuestion.status !== 'deleted') {
                statusQuestionButton = new NewStatusQuestion(
                    columnElement,
                    matchingQuestion,
                    this.service.category,
                    this.service.demo,
                    this.types,
                    matchingQuestion.status,
                    matchingQuestion.status_text
                );
            } else {
                statusQuestionButton = new NewStatusQuestion(
                    columnElement,
                    data,
                    this.service.category,
                    this.service.demo,
                    this.types
                );
            }

            // add new column
            row.appendChild(columnElement);
        }

        return table;
    }

    /**
     * Handle logic for each row column
     * @param columns
     * @returns {{transaction_date: string, bookkeeping_number: string, amount: string, name: string}|boolean}
     */
    handleRowColumns(columns) {
        // for each table body column, use the same index from table header columns we got earlier to get each value
        let bookeepingNumber = '';
        let name = '';
        let amount = '';
        let transactionDate = '';
        let currency = '';
        let description = '';

        for (let columnIndex = 0; columnIndex < columns.length; columnIndex++) {
            for (let headerName in this.headerIndexes) {
                if (this.headerIndexes[headerName] === columnIndex) {
                    currency = this.handleCurrency(columns[columnIndex].innerText.trim());
                    let value = this.sanitizeString(columns[columnIndex].innerText.trim());
                    if (headerName === 'bookkeeping_number') {
                        bookeepingNumber = value;
                    } else if (headerName === 'transaction_date') {
                        transactionDate = value;
                    } else if (headerName === 'name' && value !== '') {
                        name = value;
                    } else if (headerName === 'description' && name === '' && value !== '') {
                        name = value;
                    } else if (headerName === 'debit' && value !== '0,00') {
                        amount = value;
                    } else if (headerName === 'credit' && value !== '0,00') {
                        amount = '-' + value;
                    }  else if (headerName === 'document_number') {
                        description = value;
                    }
                }
            }

        }

        // if required columns are not found don't add row
        if (bookeepingNumber === '' || name === '' || amount === '' || transactionDate === '') {
            return false;
        }

        return  {
            bookkeeping_number: bookeepingNumber,
            name: name,
            amount: amount,
            transaction_date: transactionDate,
            currency: currency,
            description: description
        };
    }

    /**
     * Change table header elements
     * @param headerElement
     * @param rowElement
     * @param rowElementClasses
     * @param rowHeaderNames
     */
    setTableHeaderElement(headerElement, rowElement, rowElementClasses, rowHeaderNames) {
        this.tableHeaderElement = headerElement;
        this.tableHeaderRowElement = rowElement;
        this.tableHeaderRowElementClasses = rowElementClasses;
        this.tableHeaderRowNames = rowHeaderNames;

        // set header columns indexes from table
        this.headerIndexes = {};
        let table = document.querySelector(this.selector);
        let headers = table.querySelectorAll(this.tableHeaderRowElement);
        for (let i = 0; i < headers.length; i++) {
            // get by colum name the column index
            let headerName = headers[i].querySelector(this.tableHeaderRowNames.selector);
            if (headerName) {
                let headerColumns = this.tableHeaderRowNames.columns;
                for (let name in headerColumns) {
                    // found a column, save index (will be the same index as the body column)
                    if (headerName.innerText.trim() === headerColumns[name]) {
                        this.headerIndexes[name] = i;
                    }
                }
            }
        }
    }

    /**
     * Create table body row element
     * @returns {HTMLDivElement}
     */
    createTableBodyRowElement() {
        let columnElement = 'div';
        if (this.tableBodyColumnElement) {
            columnElement = this.tableBodyColumnElement;
        }

        let element = document.createElement(columnElement);
        element.classList.add('hix-table-columns');
        element.style = 'text-align: right;';
        element.style = 'border-left: 1px solid #d2d6d9;';
        element.style = 'padding-left: 10px;';
        if (this.tableBodyColumnElementClasses) {
            element.classList.add(...this.tableBodyColumnElementClasses);
        }

        return element;
    }
}

export {GeneralLedgerTableHelper};