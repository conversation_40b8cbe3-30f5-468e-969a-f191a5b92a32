import {BasePage} from "./BasePage";

/**
 * Aged Receivables Summary detail page
 */
export class AgedReceivablesSummary extends BasePage {
    async handlePage() {
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        await this.listen();
    }

    async listen() {
        if (this.isObserverSet) {
            return;
        }
        this.isObserverSet = true;

        let selectors = this.selectors.pages.agedReceivablesSummary;

        await this.handleUpdateButton(selectors);

        // try until timeout's or gets the table
        this.selectorHelper.sleepUntil(() => this.tableHasRows(selectors.table), 6000)
            .then(async () => {
                await this.handleTable(selectors);
            }).catch(() => {
            console.log('Xero - timeout looking for table')
        });
    }
}
