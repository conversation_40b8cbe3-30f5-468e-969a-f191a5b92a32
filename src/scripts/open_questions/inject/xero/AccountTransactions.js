import {BasePage} from "./BasePage";

/**
 * Account Transactions
 */
export class AccountTransactions extends BasePage {
    async handlePage() {
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        await this.listen();
    }

    async listen() {
        if (this.isObserverSet) {
            return;
        }
        this.isObserverSet = true;

        let selectors = this.selectors.pages.accountTransactions;

        await this.handleUpdateButton(selectors);

        // try until timeout's or gets the table
        this.selectorHelper.sleepUntil(() => this.tableHasRows(selectors.table), 6000)
            .then(async () => {
                await this.handleTable(selectors);
            }).catch(() => {
            console.log('Xero - timeout looking for table')
        });
    }

    /**
     * Handle sort buttons on xero table
     * @param selectors
     * @returns {Promise<void>}
     */
    async handleTableSortButtons(selectors) {
        if (this.tableSortButtons) {
            return;
        }

        // wait for table to appear
        this.selectorHelper.waitForElement(selectors.table.selector).then(async () => {
            document.querySelectorAll(selectors.table.selector + ' ' + selectors.table.header.column).forEach(element => {
                element.addEventListener('click', () => {
                    this.handleTable(selectors);
                });
            });
            this.tableSortButtons = true;
        });
    }

    async handleTable(selectors) {
        await this.handleTableSortButtons(selectors);

        let tableSelectors = selectors.table;
        let table = await this.getTable(
            tableSelectors.selector,
            this.getColumnsSelectorsFromTableHeader(tableSelectors), // get all table columns
            {
                'currency': 'GBP'
            }
        );
        table.setHeaderClasses(tableSelectors.header.cssClasses);
        table.setColumnClasses(tableSelectors.columns.cssClasses);
        await table.start();
    }
}