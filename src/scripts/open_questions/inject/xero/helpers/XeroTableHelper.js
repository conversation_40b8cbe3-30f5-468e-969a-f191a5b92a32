import {TableHelper} from "../../helpers/TableHelper";
import {NewStatusQuestion} from "../../buttons/NewStatusQuestion";
import {trans} from "../../../../helpers/TranslationHelper";

/**
 * Add Open questions column and handle questions
 */
class XeroTableHelper extends TableHelper {
    /**
     * Add th element to table header
     * @param table
     * @returns {*}
     */
    async addHeader(table) {
        const thead = table.querySelector('thead');
        if (thead === null || this.tableHasRows === false) {
            return null;
        }
        for (let i = 0; i < thead.rows.length; i++) {
            if (i + 1 === thead.rows.length) {
                let thElement = document.createElement('th');
                thElement.classList.add('hix-table-header');
                if (this.headerClasses) {
                    for (let headerClass of this.headerClasses.split(" ")) {
                        thElement.classList.add(headerClass);
                    }
                }
                thElement.style = 'width: 12%;border-bottom: 1px solid #ccced2;';
                let divElement = document.createElement('div');
                divElement.innerText = await trans('open_questions.title');
                divElement.style = 'position: relative; top: -5px;';
                thElement.appendChild(divElement);
                thead.rows[i].appendChild(thElement);
            }
        }
        return table;
    }

    /**
     * Add column (td element) to each row of table body
     *
     * @param table
     * @returns {*}
     */
    addColumn(table) {
        const tbody = table.querySelector('tbody');
        if (tbody === null) {
            return null;
        }
        for (let i = 0; i < tbody.rows.length; i++) {
            let row = tbody.rows[i];
            let data = this.handleAttributes(row);
            // each attribute in data from selectors.json is required so we have everything to try to match the question
            let matchingQuestion = this.handleMatchingQuestion(data);
            // handle additional data for each row
            for (let attribute of Object.keys(this.additionalAttributes)) {
                data[attribute] = this.additionalAttributes[attribute];
            }
            data.service_name = this.service.service_name;
            data.category = this.service.category;
            if (data.credit) { // used by account transactions page
                data.amount = data.debit;
                if (data.credit !== '-') {
                    data.amount = '-' + data.credit;
                }
            }
            // add new column
            if (data.name && data.transaction_date) {
                let newColumn = row.insertCell(row.cells.length);
                newColumn.classList.add('hix-table-columns');
                newColumn.style = 'text-align: center;font-size:13px;';
                if (this.columnClasses) {
                    for (let columnClass of this.columnClasses.split(" ")) {
                        newColumn.classList.add(columnClass);
                    }
                }
                if (matchingQuestion && matchingQuestion.status !== 'deleted') {
                    new NewStatusQuestion(
                        newColumn,
                        matchingQuestion,
                        this.service.category,
                        this.service.demo,
                        this.types,
                        matchingQuestion.status,
                        matchingQuestion.status_text
                    );
                } else {
                    new NewStatusQuestion(
                        newColumn,
                        data,
                        this.service.category,
                        this.service.demo,
                        this.types
                    );
                }
            }
        }
        return table;
    }

    /**
     * Return which amount was sent
     * @returns {string}
     * @param amount
     */
    handleAmountColumn(amount) {
        if (parseInt(amount) < 0) {
            return 'credit';
        }

        return 'debit';
    }

    /**
     * Format amount so we have the same value from BE and their FE value the same
     * @returns {number}
     * @param amount
     */
    handleAmount(amount) {
        amount = amount || "";
        let decimal = '.';
        amount = amount.replace(/[^0-9$.,]/g, '');
        if (amount.indexOf(',') > amount.indexOf('.')) {
            decimal = ',';
        }
        if ((amount.match(new RegExp("\\" + decimal, "g")) || []).length > 1) {
            decimal = "";
        }
        if (decimal !== "" && (amount.length - amount.indexOf(decimal) - 1 == 3) && amount.indexOf("0" + decimal) !== 0) {
            decimal = "";
        }
        amount = amount.replace(new RegExp("[^0-9$" + decimal + "]", "g"), "");
        amount = amount.replace(',', '.');
        return parseFloat(amount);
    }

    /**
     * Parse their date to format 2023-01-01
     * @param date
     */
    handleDate(date) {
        date = new Date(Date.parse(date));
        let leadingZero = '';
        if (date.getMonth() + 1 < 10) {
            leadingZero = 0;
        }

        return ('0' + date.getDate()).slice(-2) + '-' + leadingZero + (date.getMonth() + 1) + '-' + date.getFullYear();
    }

    /**
     * Match row in a page with questions from secure login
     *
     * Each field from selectors.json is required so we have everything to try to match the question
     * @param data
     * @returns {null}
     */
    handleMatchingQuestion(data) {
        let matchingQuestion = null;
        if (this.questions) {
            matchingQuestion = this.questions.find((question) => {
                let differentAttribute = false;
                for (let attribute of Object.keys(data)) {
                    // ignore validation for this attribute
                    if (this.ignoreAttributes && this.ignoreAttributes.indexOf(attribute) !== -1) {
                        continue;
                    }
                    if (attribute === 'transaction_date') {
                        if (this.handleDate(data.transaction_date) !== question[attribute]) {
                            differentAttribute = true;
                        }
                        continue;
                    }
                    if (attribute === 'credit' || attribute === 'debit') { // used by account transactions page
                        let column = this.handleAmountColumn(question['amount']);
                        let amount = question['amount'].replace('-', '');
                        if (this.handleAmount(data[column]) !== this.handleAmount(amount)) {
                            differentAttribute = true;
                        }
                        break;
                    }
                    if (attribute === 'amount') { // used by aged payables and receivables details page summary
                        let amount = question['amount'].replace('-', '');
                        if (this.handleAmount(data['amount']) !== this.handleAmount(amount)) {
                            differentAttribute = true;
                        }
                        break;
                    }
                    // question has no match with given row
                    if (data[attribute] !== question[attribute]) {
                        differentAttribute = true;
                        break;
                    }
                }
                // no different attribute found, its a match
                return differentAttribute === false && question.status !== 'deleted';
            });
        }
        return matchingQuestion;
    }
}

export {XeroTableHelper};