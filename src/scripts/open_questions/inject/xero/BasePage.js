import {OpenQuestionsHelper} from "../helpers/OpenQuestionsHelper";
import {ServicePage} from "../ServicePage";
import {SelectorHelper} from "../helpers/SelectorHelper";
import browser from "webextension-polyfill";
import {XeroTableHelper} from "./helpers/XeroTableHelper";

/**
 * Xero common page class
 */
export class BasePage extends ServicePage {
    constructor(demo, selectors, notAllowedUrls = []) {
        super();
        this.demo = demo;
        this.selectors = selectors;
        this.notAllowedUrls = notAllowedUrls;
        this.openQuestionsHelper = new OpenQuestionsHelper(this.demo);
        this.selectorHelper = new SelectorHelper();
    }

    async start() {
        this.selectorHelper.waitForElement(this.selectors.header.company).then(() => {
            this.validatePage();
        });
    }

    /**
     * Validate if we can handle page logic
     * @returns {Promise<void>}
     */
    async validatePage() {
        if (await this.isCompanyManager()) {
            return this.handlePage();
        }
        if (await this.isCompanyBlocked(this.getServiceName(), this.getCompanyId(), this.getCompanyName())) {
            return;
        }
        return this.handlePage();
    }

    /**
     * Handle page logic
     * @returns {Promise<void>}
     */
    async handlePage() {
        await this.appendCornerStone();
    }

    /**
     * Service name
     * @returns {string}
     */
    getServiceName() {
        return 'xero_open_questions';
    }

    /**
     * Snelstart open questions category
     * @returns {string}
     */
    getServiceCategory() {
        return 'bookkeeping';
    }

    /**
     * Get External Company Id
     * @returns {string}
     */
    getCompanyId() {
        return window.location.pathname.split('/')[this.selectors.companyIdUrlIndex];
    }

    /**
     * Get Company Name
     * @returns {string}
     */
    getCompanyName() {
        let companyName = document.querySelector(this.selectors.header.company);
        return companyName.textContent.trim();
    }

    /**
     * Get page title
     * @returns {*}
     */
    getPageTitle() {
        let title = document.querySelector(this.selectors.header.title);
        if (title) {
            return title.textContent.trim();
        }
        return null;
    }

    /**
     * Handle table logic to add SL column
     * @param selectors
     * @returns {Promise<void>}
     */
    async handleTable(selectors) {
        let tableSelectors = selectors.table;
        let dropdown = selectors.dropdown;
        let element = document.querySelector(dropdown.selector);
        for (let key in dropdown.text) {
            if (dropdown.text[key] === element.innerText.trim()) {
                let table = await this.getTable(
                    tableSelectors.selector,
                    this.getColumnsSelectorsFromTableHeader(tableSelectors), // get all table columns
                    {
                        'currency': 'GBP'
                    }
                );
                table.setHeaderClasses(tableSelectors.header.cssClasses);
                table.setColumnClasses(tableSelectors.columns.cssClasses);
                await table.start();
            }
        }
    }

    /**
     * Check if table from selectors has rows
     * @param tableSelectors
     * @returns {boolean}
     */
    tableHasRows(tableSelectors) {
        let rows = document.querySelectorAll(tableSelectors.selector + ' ' + tableSelectors.rows);
        return !!rows.length;
    }

    /**
     * Wait for update button
     * @returns {Promise<void>}
     * @param selectors
     */
    async handleUpdateButton(selectors) {
        this.selectorHelper.sleepUntil(() => document.querySelector(selectors.button.update), 10000)
            .then(async () => {
                const observer = new MutationObserver(async () => {
                    if (this.tableHasRows(selectors.table)) {
                        await this.handleTable(selectors);
                    }
                });
                observer.observe(document.querySelector(selectors.button.update), {
                    attributes: true,
                    attributeFilter: ['class']
                });
            }).catch(() => {
            console.log('Xero - timeout looking for button')
        });
    }

    /**
     * Get table from selector and then use columns selector
     * @param selector
     * @param columnSelectors
     * @param additionalAttributes
     * @param ignoreAttributes
     * @returns {XeroTableHelper}
     */
    async getTable(selector, columnSelectors, additionalAttributes = {}, ignoreAttributes = []) {
        let questions = null;
        let types = [];
        if (this.demo === false) {
            questions = await this.getQuestions();
            types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                category: this.getServiceCategory(),
                serviceName: this.getServiceName()
            });
        }
        additionalAttributes.page_title = this.getPageTitle();
        additionalAttributes.page_url = window.location.href;
        return new XeroTableHelper(
            selector,
            columnSelectors,
            additionalAttributes,
            ignoreAttributes,
            questions,
            types,
            {
                'category': this.getServiceCategory(),
                'service_name': this.getServiceName(),
                'demo': this.demo
            }
        );
    }
}