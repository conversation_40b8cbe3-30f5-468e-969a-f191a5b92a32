import {CornerStone} from "./cornerstone/CornerStone";
import {HistoryPopup} from "./history_popup/HistoryPopup";
import {OpenQuestionsCommunicationService} from "./OpenQuestionsCommunicationService";
import browser from "webextension-polyfill";
import {LogSender} from "../../helpers/LogSender";

export class OpenQuestionsClient {
    constructor() {
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
        this.cornerStone = null;
    }

    start(data) {
        setTimeout(function () {
            this.launchCornerStone(data);
        }.bind(this), 1000)
    }

    async launchCornerStone(data, responseData) {
        LogSender.debug('OpenQuestionsClient launchCornerStone called');
        if ((await browser.storage.local.get(data.serviceName + '_company_id'))[data.serviceName + '_company_id'] !== responseData.company_id) {
            await browser.storage.local.set({[data.serviceName + '_company_id']: responseData.company_id});
            window.dispatchEvent(new Event('storage')); // Chrome bug, this event is not triggered on the same page.
        }

        if (this.cornerStone === null || !document.querySelector('sl-corner-stone')) {
            const html = document.querySelector('html');
            this.cornerStone = new CornerStone(data);
            await this.cornerStone.init();
            await this.cornerStone.handleAppendCornerStone(html);
        }

        let hide = responseData === ''
            || responseData.company_id === null
            || typeof responseData.company_name === 'undefined'
            || !responseData.visible;

        this.cornerStone.setHidden(hide);

        let user = await this.openQuestionsCommunicationService.sendToBExtension('get_user_profile');
        if (user) {
            let isManager = user.is_company_manager || user.is_account_manager;
            this.cornerStone.hideLockButton(!isManager);
        }

        // We need to set the external data regardless if the company is hidden
        // Because else we do not know the company id when blocking and unblocking.
        this.cornerStone.setCompanyExternalData(data);
        this.cornerStone.setCompanyIsBlocked(responseData.company_is_blocked);

        if (hide) {
            LogSender.debug('OpenQuestionsClient clearing company ID from local storage');
            await browser.storage.local.remove(data.serviceName + '_company_id');
        } else {
            if (responseData.company_is_blocked) {
                LogSender.debug('OpenQuestionsClient hiding corner stone because company is blocked');
                this.cornerStone.setHidden(true);
                return;
            }

            this.cornerStone.updateCompanyName(responseData.company_name);
            this.cornerStone.setOpenQuestionsCount(responseData.statistics.open_questions);
            this.cornerStone.setPendingQuestionsCount(responseData.statistics.pending_questions);
            this.cornerStone.setClosedQuestionsCount(responseData.statistics.closed_questions);
        }
    }

    hideCornerStone() {
        if (this.cornerStone !== null) {
            this.cornerStone.setHidden(true);
        }
    }

    hideCornerStoneLockButton() {
        if (this.cornerStone !== null) {
            this.cornerStone.hideLockButton(true);
        }
    }

    /**
     * Check if logged user is able to block companies
     * @returns {Promise<boolean|*>}
     */
    async isCompanyManager() {
        let user = await this.openQuestionsCommunicationService.sendToBExtension('get_user_profile');
        if (user) {
            return user.is_company_manager || user.is_account_manager;
        }

        return false;
    }

    async launchHistory(data) {
        const questionHistory = new HistoryPopup(data);
        await questionHistory.init();
        const body = document.querySelector('body');

        body.appendChild(questionHistory.injectableElement());
    }

    async setTwinfieldAdministration(url) {
        await this.openQuestionsCommunicationService.sendToBExtension('get_twinfield_administration', {url: url});
    }
}

let openQuestionClient = new OpenQuestionsClient()

document.addEventListener('launchCornerStone', async function (e) {
    await openQuestionClient.launchCornerStone(e.detail.data, e.detail.responseData)
})

document.addEventListener('launchHistory', async function (e) {
    await openQuestionClient.launchHistory(e.detail.data)
})

document.addEventListener('hideCornerStone', function () {
    openQuestionClient.hideCornerStone()
})

document.addEventListener('hideCornerStoneLockButton',  function () {
    openQuestionClient.hideCornerStoneLockButton()
})

document.addEventListener('setTwinfieldAdministration', async function (e) {
    await openQuestionClient.setTwinfieldAdministration(e.detail.url);
})
