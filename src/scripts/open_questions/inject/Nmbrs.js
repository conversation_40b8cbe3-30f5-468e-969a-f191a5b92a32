import {ExplorerIndexView} from "./nmbrs/ExplorerIndexView";
import browser from "webextension-polyfill";

export class Nmbrs {
    constructor() {
        this.explorer = null;
    }

    async start(demo, selectors) {
        if (!(await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            if (this.explorer === null) {
                this.explorer = new ExplorerIndexView(selectors, demo);
            }
            this.explorer.start();
        }
    }
}

let nmbrs = new Nmbrs();

document.addEventListener('start', async function (e) {
    await nmbrs.start(
        e.detail.demo,
        e.detail.selectors
    )
}, {once : true})
