import {BasePage} from "./BasePage";
import {DebtorsCreditorsTableHelper} from "./helpers/DebtorsCreditorsTableHelper";

class DebtorsCreditors extends BasePage {
    constructor(selectors, demo) {
        super(selectors, demo, DebtorsCreditorsTableHelper);
    }

    async handleTable(columns) {
        let name = document.querySelector(this.selectors.table.name).innerText.trim();
        name = name.split(' - ')[1];
        let table = await this.getTable(this.selectors.table.selector, columns, {currency: 'EUR', name: name});

        // add handle new table header element
        table.setTableHeaderElement(
            this.selectors.table.header.selector,
            this.selectors.table.header_row_element,
            this.selectors.table.header_row_element_classes
        );

        // add handle new table column element
        table.setTableBodyElements(
            this.selectors.table.body_row_element,
            this.selectors.table.body_column_element,
            this.selectors.table.body_column_element_classes

        );

        await table.start();
    }
}

export {DebtorsCreditors}