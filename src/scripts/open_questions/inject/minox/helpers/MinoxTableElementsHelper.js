import {TableElementsHelper} from "../../helpers/TableElementsHelper";
import {trans} from "../../../../helpers/TranslationHelper";

/**
 * Add Open questions column and handle questions
 */
class MinoxTableElementsHelper extends TableElementsHelper {
    /**
     * Create table header row element
     * @returns {HTMLDivElement}
     */
    async createTableHeaderRowElement() {
        let headerElement = 'div';
        if (this.tableHeaderRowElement) {
            headerElement = this.tableHeaderRowElement;
        }

        let element = document.createElement(headerElement);
        element.innerText = await trans('open_questions.title');
        element.classList.add('hix-table-header');
        element.style = 'white-space: nowrap;min-width: 160px;padding-left:9px;';
        if (this.tableHeaderRowElementClasses) {
            element.classList.add(...this.tableHeaderRowElementClasses);
        }

        return element;
    }

    /**
     * Create table body row element
     * @returns {HTMLDivElement}
     */
    createTableBodyRowElement() {
        let columnElement = 'div';
        if (this.tableBodyColumnElement) {
            columnElement = this.tableBodyColumnElement;
        }

        let element = document.createElement(columnElement);
        element.classList.add('hix-table-columns');
        element.style = 'white-space: nowrap;min-width: 160px;padding-left:9px;';
        if (this.tableBodyColumnElementClasses) {
            element.classList.add(...this.tableBodyColumnElementClasses);
        }

        return element;
    }
}

export {MinoxTableElementsHelper};