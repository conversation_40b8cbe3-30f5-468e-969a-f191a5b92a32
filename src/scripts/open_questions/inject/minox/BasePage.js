import {ServicePage} from "../ServicePage";
import {SelectorHelper} from "../helpers/SelectorHelper";
import {MinoxTableElementsHelper} from "./helpers/MinoxTableElementsHelper";

class BasePage extends ServicePage {
    constructor(selectors, demo, tableHelper = MinoxTableElementsHelper) {
        super();
        this.selectors = selectors;
        this.demo = demo;
        this.selectorHelper = new SelectorHelper();
        this.tableHelper = tableHelper;
    }

    async getHeaderCells() {
        return await this.selectorHelper.waitForElement(this.selectors.table.header.new_selector).then((headerCell) => {
            if (headerCell) {
                return this.selectors.table.header.new_selector;
            }

            return this.selectors.table.header.selector;
        });
    }

    async handle() {
        await this.selectorHelper.waitForElement(await this.getHeaderCells()).then(async (headerCells) => {
            if (headerCells) {
                headerCells = headerCells.children;
                let columns = this.getColumnsFromTableHeader(headerCells, this.selectors.table.header.fields);
                if (Object.entries(columns).length === Object.entries(this.selectors.table.header.fields.nl).length) {
                    await this.handleTable(columns);
                    await this.handleScrollableTable(columns);
                    this.adjustStyling();
                }
            }
        });
    }

    async handleScrollableTable(columns) {
        let timer = null;
        let self = this;
        await this.selectorHelper.waitForElement(await this.getTableSelector() + ' ' + this.selectors.table.scrollable_element).then((scrollableDiv) => {
            if (scrollableDiv) {
                // wait for the user scroll on the div to handle table
                scrollableDiv.addEventListener('scroll', function() {
                    if (timer !== null) {
                        clearTimeout(timer);
                    }
                    timer = setTimeout(async () => {
                        await self.handleTable(columns);
                        self.adjustStyling();
                    }, 150);
                }, false);
            }
        });
    }

    /**
     * Create a list of columns selectors from table header
     * Example:
     *
     * {
     *     "name": "td:nth-child(3)"
     * }
     *
     * @returns {{}}
     * @param headerColumns
     * @param headerColumnFields
     */
    getColumnsFromTableHeader(headerColumns, headerColumnFields) {
        let requiredColumns = {};
        headerColumns = Array.from(headerColumns);
        if (headerColumns[0].classList.contains('hix-table-header')) {
            headerColumns.splice(0, 1);
        }
        for (let i = 0; i < headerColumns.length; i++) {
            // get each language and each required field
            for (let language of Object.keys(headerColumnFields)) {
                for (let field of Object.keys(headerColumnFields[language])) {
                    // if required column found, save css selector to column (example: "Reference" = name)
                    if (headerColumns[i].innerText && headerColumns[i].innerText.trim() === headerColumnFields[language][field]) {
                        requiredColumns[field] = ["div:nth-child(" + (parseInt(i) + 1) + ")"];
                    }
                }
            }
        }

        return requiredColumns;
    }

    async getTable(selector, columnSelectors, additionalAttributes = {}, ignoreAttributes = []) {
        let questions = null;
        let types = [];
        if (this.demo === false) {
            questions = await this.getQuestions();
            types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                category: this.getServiceCategory(),
                serviceName: this.getServiceName()
            });
        }

        additionalAttributes.page_title = this.getPageTitle();
        additionalAttributes.page_url = this.getPageUrl();

        return new this.tableHelper(
            selector,
            columnSelectors,
            additionalAttributes,
            ignoreAttributes,
            questions,
            types,
            {
                'category': this.getServiceCategory(),
                'service_name': this.getServiceName(),
                'demo': this.demo
            },
            true
        );
    }

    async getTableSelector() {
        return await this.selectorHelper.waitForElement(this.selectors.table.new_selector).then((element) => {
            if (element) {
                return this.selectors.table.new_selector;
            } else {
                return this.selectors.table.selector;
            }
        });
    }

    getBodyRowElement() {
        return this.selectors.table.body_row_element;
    }

    async handleTable(columns) {
        let selector = await this.getTableSelector();
        await this.selectorHelper.waitForElement(selector).then(async (table) => {
            if (table) {
                let table = await this.getTable(selector, columns, {'currency': 'EUR'});

                // add handle new table header element
                table.setTableHeaderElement(
                    await this.getHeaderCells(),
                    this.selectors.table.header_row_element,
                    this.selectors.table.header_row_element_classes
                );

                // add handle new table column element
                table.setTableBodyElements(
                    await this.getBodyRowElement(),
                    this.selectors.table.body_column_element,
                    this.selectors.table.body_column_element_classes
                );

                await table.start();
            }
        });
    }

    getServiceName() {
        return 'minox_open_questions';
    }

    getServiceCategory() {
        return 'bookkeeping';
    }

    getPageUrl() {
        return window.location.origin + window.location.pathname
    }

    getPageTitle() {
        return document.querySelector(this.selectors.page_title).innerText.trim();
    }

    adjustStyling() {
        let header = document.querySelector('.hix-table-header');
        if (header) {
            header.style.paddingLeft = '9px';
        }
        let columns = document.querySelectorAll('.hix-table-columns');
        if (columns) {
            columns.forEach(cell => {
                cell.style.paddingLeft = '9px';
            });
        }
    }
}

export {BasePage}