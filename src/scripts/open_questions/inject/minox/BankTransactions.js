import {BasePage} from "./BasePage";
class BankTransactions extends BasePage {
    constructor(selectors, demo) {
        super(selectors, demo);
    }

    async getBodyRowElement() {
        let bodyRowElement =  await this.selectorHelper.waitForElement(this.selectors.table.new_body_row_element);
        if (bodyRowElement) {
            return this.selectors.table.new_body_row_element;
        }

        return super.getBodyRowElement();
    }

    adjustStyling() {
        this.selectorHelper.waitForElement(this.selectors.table.header.selector).then((element) => {
            if (element) {
                element.lastElementChild.style.flexGrow = 0;
                element.lastElementChild.style.flexShrink = 0;
            }
        });
        this.selectorHelper.waitForElement(this.selectors.table.body.selector).then((element) => {
            if (element) {
                element.lastElementChild.style.width = '2000px';
            }
        });
        super.adjustStyling();
    }
}

export {BankTransactions}