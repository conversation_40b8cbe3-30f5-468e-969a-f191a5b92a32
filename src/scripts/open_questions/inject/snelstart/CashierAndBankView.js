import {SnelstartPage} from "./SnelstartPage";

/**
 * Cashier and Bank (Kas en bank)
 */
export class CashierAndBankView extends SnelstartPage {
    async handlePage() {
        await this.appendCornerStone();
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        this.selectorHelper.waitForElement(this.selectors.cashierAndBank.table + ' tbody tr').then(async () => {
            this.getTable(
                this.selectors.cashierAndBank.table,
                this.selectors.cashierAndBank.columns,
                {
                    'currency': 'EUR',
                    'bookkeeping_number': '',
                    'page_title': this.getPageTitle(),
                    'page_url': this.getPageUrl()
                }
            );
        });
    }
}
