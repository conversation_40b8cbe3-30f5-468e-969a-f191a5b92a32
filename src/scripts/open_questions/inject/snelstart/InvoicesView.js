import {SnelstartPage} from "./SnelstartPage";

/**
 * Invoices (Facturen)
 */
export class InvoicesView extends SnelstartPage {
    async handlePage() {
        await this.appendCornerStone();
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        this.selectorHelper.waitForElement(this.selectors.invoices.table + ' tbody tr').then(async () => {
            let table = await this.getTable(
                this.selectors.invoices.table,
                this.selectors.invoices.columns,
                {
                    'currency': 'EUR',
                    'bookkeeping_number': '',
                    'page_title': this.getPageTitle(),
                    'page_url': this.getPageUrl()
                },
                [
                    'invoice_number'
                ]
            );

            if (table._injectableElement) {
                // add colspan to fix their mobile view
                let columns = table._injectableElement.querySelectorAll('.hix-table-columns');
                for (let i = 0; i < columns.length; i++) {
                    columns[i].setAttribute('colspan', 2);
                }

                // Add styling
                let styling = this.selectors.invoices.styling;
                for (let key in styling) {
                    const field = await this.selectorHelper.helpSelector(this.selectors.invoices.headers[key]);
                    field.style = styling[key];
                }
            }
        });
    }
}
