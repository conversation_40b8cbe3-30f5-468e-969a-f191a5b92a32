import {SnelstartPage} from "./SnelstartPage";

/**
 * Booking Details (Boeking details)
 */
export class BookingDetailsView extends SnelstartPage {
    /**
     * Get page title from selected row
     * @returns {string|null}
     */
    getPageTitle() {
        let title = document.querySelector(this.selectors.bookingDetails.title);
        if (title) {
            return title.textContent.trim();
        }

        return null;
    }

    async handlePage() {
        await this.appendCornerStone();
        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        this.selectorHelper.waitForElement(this.selectors.bookingDetails.table + ' tbody tr').then(async () => {
            this.getTable(
                this.selectors.bookingDetails.table,
                this.selectors.bookingDetails.columns,
                {
                    'currency': 'EUR',
                    'bookkeeping_number': '',
                    'page_title': this.getPageTitle(),
                    'page_url': window.location.href
                },
                [
                    'description',
                    'invoice_number'
                ]
            );
        });
    }
}
