import {SnelstartPage} from "./SnelstartPage";

/**
 * Shopping (Inkopen)
 */
export class ShoppingView extends SnelstartPage {
    async handlePage() {
        await this.appendCornerStone();

        if (
            await this.isCompanyBlocked(
                this.getServiceName(),
                this.getCompanyId(),
                this.getCompanyName()
            )
        ) {
            return;
        }

        this.selectorHelper.waitForElement(this.selectors.shopping.table + ' tbody tr').then(async () => {
            this.getTable(
                this.selectors.shopping.table,
                this.selectors.shopping.columns,
                {
                    'currency': 'EUR',
                    'bookkeeping_number': '',
                    'page_title': this.getPageTitle(),
                    'page_url': this.getPageUrl()
                },
                [
                    'description',
                    'invoice_number'
                ]
            );
        });
    }
}
