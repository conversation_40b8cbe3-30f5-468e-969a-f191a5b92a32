import {OpenQuestionsHelper} from "../helpers/OpenQuestionsHelper";
import {ServicePage} from "../ServicePage";
import {TableHelper} from "../helpers/TableHelper";
import {SelectorHelper} from "../helpers/SelectorHelper";

/**
 * Snelstart common page class
 */
export class SnelstartPage extends ServicePage {
    constructor(demo, selectors, notAllowedUrls = []) {
        super();
        this.demo = demo;
        this.selectors = selectors;
        this.notAllowedUrls = notAllowedUrls;
        this.openQuestionsHelper = new OpenQuestionsHelper(this.demo);
        this.selectorHelper = new SelectorHelper();
    }

    async start() {
        await this.listen();
    }

    listen() {
        this.selectorHelper.waitForElement(this.selectors.header.administrationName).then(() => {
            this.validatePage();
        });
    }

    /**
     * Validate if we can handle page logic
     * @returns {Promise<void>}
     */
    async validatePage() {
        if (await this.isCompanyManager()) {
            return this.handlePage();
        }

        if (await this.isCompanyBlocked(
            this.getServiceName(),
            this.getCompanyId(),
            this.getCompanyName()
        )
        ) {
            return;
        }

        return this.handlePage();
    }

    /**
     * Handle page logic
     * @returns {Promise<void>}
     */
    async handlePage() {
        // listen for url's changes for cornerstone updates
        let previousUrl = '';
        const observer = new MutationObserver(async () => {
            if (location.href !== previousUrl) {
                previousUrl = location.href;
                await this.handleCornerStone(previousUrl);
            }
        });
        observer.observe(document, {
            subtree: true,
            childList: true
        });

        await this.handleCornerStone(previousUrl);
    }

    /**
     * Handle cornerstone to show or not on the page
     * @param url
     * @returns {Promise<void>}
     */
    async handleCornerStone(url) {
        let notAllowedUrl = this.notAllowedUrls.find((urlPath) => {
            return url.includes(urlPath);
        });
        if (notAllowedUrl) {
            await this.openQuestionsCommunicationService.sendToBExtension('hide_cornerstone');
            await this.openQuestionsCommunicationService.sendToBExtension('hide_cornerstone_lock');
        } else {
            await this.appendCornerStone();
        }
    }

    /**
     * Service name
     * @returns {string}
     */
    getServiceName() {
        return 'snelstart_open_questions';
    }

    /**
     * Snelstart open questions category
     * @returns {string}
     */
    getServiceCategory() {
        return 'bookkeeping';
    }

    /**
     * Get External Company Id
     * @returns {string}
     */
    getCompanyId() {
        return window.location.pathname.split('/')[this.selectors.administrationIdUrlIndex];
    }

    /**
     * Get Company Name
     * @returns {string}
     */
    getCompanyName() {
        let companyName = document.querySelector(this.selectors.header.administrationName);
        return companyName.textContent.trim();
    }

    /**
     * Get page title from selected row
     * @returns {string|null}
     */
    getPageTitle() {
        let title = document.querySelector(this.selectors.selectedRow);
        if (title) {
            return title.textContent.trim();
        }

        return null;
    }

    /**
     * Get page link from selected row
     * @returns {string|null}
     */
    getPageUrl() {
        let url = document.querySelector(this.selectors.selectedRow);
        if (url) {
            return url.href;
        }

        return null;
    }

    /**
     * Get table from selector and then use columns selector
     * @param selector
     * @param columnSelectors
     * @param additionalAttributes
     * @param ignoreAttributes
     * @returns {TableHelper}
     */
    async getTable(selector, columnSelectors, additionalAttributes, ignoreAttributes = []) {
        let questions = null;
        let types = [];
        if (this.demo === false) {
            questions = await this.getQuestions();
            types = await this.openQuestionsCommunicationService.sendToBExtension('get_question_types', {
                category: this.getServiceCategory(),
                serviceName: this.getServiceName()
            });
        }

        let tableHelper = new TableHelper(
            selector,
            columnSelectors,
            additionalAttributes,
            ignoreAttributes,
            questions,
            types,
            {
                'category': this.getServiceCategory(),
                'service_name': this.getServiceName(),
                'demo': this.demo
            }
        );

        if (types.length > 0) {
            await tableHelper.start();
        }

        return tableHelper;
    }
}
