import {SelectorHelper} from "./helpers/SelectorHelper";
import {trans} from "../../helpers/TranslationHelper";
import {BasePage} from "./twinfield/BasePage";
import browser from "webextension-polyfill";

export class Twinfield extends BasePage {
    constructor() {
        super();
        this.lang = '';
        this.currency = null;
        this.types = [];
        this.selectorHelper = new SelectorHelper();
        this.loadingButton = null;
        this.attributesIndexes = {
            question_name: -1,
            amount: -1,
            invoice_number: -1,
            bookkeeping_number: -1,
            transaction_date: -1,
        };
    }

    async start(demo, settings) {
        console.log('Twinfield start');
        this.loadingButton = null;
        this.demo = demo;
        this.settings = settings;
        this.selectors = settings.application.selectors;
        if (!(await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            await this.listenIframe();
        }
    }

    /**
     * Write to local storage. Has to be done in injected script for it to be available for other injected components
     */
    async setData(externalCompanyId, externalCompanyCode, externalCompanyName, companyId = null) {
        // These values are being placed in the localstorage to be accessed in the Kolommenbalans.
        await browser.storage.local.set({[this.getServiceName() + '_external_company_id']: externalCompanyId});
        await browser.storage.local.set({[this.getServiceName() + '_external_company_code']: externalCompanyCode});
        await browser.storage.local.set({[this.getServiceName() + '_external_company_name']: externalCompanyName});

        // This value is being placed in the localstorage to be access between all NewStatusQuestion instances.
        if (companyId) {
            await browser.storage.local.set({[this.getServiceName() + '_company_id']: companyId});
        } else {
            await browser.storage.local.remove(this.getServiceName() +'_company_id');
        }
    }

    async listenIframe() {
        try {
            if (this.selectors) {
                this.handleAddTableColumn();
            }
        } catch (e) {
            // TODO make a better way to handle this exception
            console.error(e)
        }
    }

    handleAddTableColumn() {
        let iframe = document.querySelector(this.selectors.iframe.selector);
        let form = document.querySelector(this.selectors.form.selector);
        if (iframe) {
            this.modifyCreditorsDebtorsTable();
        } else if (form) {
            this.modifyColumnBalanceTable();
        }
    }

    async modifyCreditorsDebtorsTable() {
        let form = this.selectorHelper.helpSelector(this.selectors.iframe.form.selector);
        if (form) {
            let externalCompanyId = (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'];
            let externalCompanyName = (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name'];
            if (
                externalCompanyId
                && externalCompanyName
                && await this.isCompanyBlocked(this.getServiceName(), externalCompanyId, externalCompanyName)
                && this.demo === false
            ) {
                return;
            }

            return this.handleCreditorsDebtorsTable(form);
        }
    }

    async handleCreditorsDebtorsTable(form) {
        if (this.demo === false) {
            await this.onQuestionUpdated();
        }

        this.pageTitle = this.getPageTitle(this.selectors.iframe.form.dropdown);
        this.pageUrl = this.getPageUrl(form.action);

        let formActionParamsString = form.action.split('?')[1];
        let formActionParams = new URLSearchParams(formActionParamsString);
        let report = formActionParams.get('report');

        let table = this.selectorHelper.helpSelector(this.selectors.iframe.form.creditors_debtors_table.selector);

        if (table && this.selectors.iframe.form.allowed_reports.includes(report)) {

            if (this.loadingButton === null && this.settings.hasOwnProperty('properties') && this.settings.properties.hasOwnProperty('be_auto_load_rows')) {
                const rows = table.querySelectorAll('tr');
                if (rows.length > this.settings.properties.be_auto_load_rows) {
                    await this.injectLoadingButton(table);
                    return;
                }
            }

            let alignments = this.selectors.iframe.form.creditors_debtors_table.rows.top.alignment;
            for (let alignment of alignments) {
                if (alignment.reports.includes(report) && this.reportIsValid(alignment)) {
                    this.getHeaderIndexes(alignment);
                    await this.appendColumn(table, report);
                    break;
                }
            }
        }
    }

    /**
     * Page url from where the question was created
     * @param url
     * @returns {string}
     */
    getPageUrl(url) {
        url = new URL(url);
        url.searchParams.delete('tempxml');

        return url.href;
    }

    /**
     * Add blue manual loading button above creditors/debtors table
     * @param table DOM element for creditors/debtors table
     */
    async injectLoadingButton(table) {
        let button = document.createElement('div');
        button.textContent = await trans('open_questions.load_questions');
        button.style = 'color: #fff; background-color: #0F9BF3; padding: 8px 16px; border-radius: 4px; float: right; margin-bottom: 10px; cursor: pointer;';
        button.addEventListener('click', (event) => {
            event.stopPropagation();
            this.startAddingColumn();
        });
        this.loadingButton = button;
        table.parentElement.insertBefore(button, table);
    }

    /**
     * Is called when user manually clicked the loading button
     */
    startAddingColumn() {
        this.loadingButton.style.visibility = 'hidden';
        this.handleAddTableColumn();
    }

    getHeaderIndexes(alignment) {
        let header = this.selectorHelper.helpSelector(alignment.header);
        if (header) {
            let headerCells = header.cells;
            let alignmentHeaders = [];

            if (this.lang === 'nl') {
                alignmentHeaders = alignment.headers_nl;
            } else if (this.lang === 'en') {
                alignmentHeaders = alignment.headers_en;
            }
            for (const [key, value] of Object.entries(alignmentHeaders)) {
                for (let i = 0; i < headerCells.length; i++) {
                    if (headerCells[i].textContent.startsWith(value)) {
                        this.attributesIndexes[key] = i;
                        break;
                    }
                }
            }
        }
    }

    async modifyColumnBalanceTable() {
        let form = this.selectorHelper.helpSelector(this.selectors.form.selector);
        if (form) {
            let externalCompanyId = (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'];
            let externalCompanyName = (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name'];

            if (
                externalCompanyId
                && externalCompanyName
                && await this.isCompanyBlocked(
                    this.getServiceName(),
                    externalCompanyId,
                    externalCompanyName
                ) && this.demo === false
            ) {
                return;
            }

            return this.handleColumnBalanceTable(form);
        }
    }

    async handleColumnBalanceTable(form) {
        if (this.demo === false) {
            await this.onQuestionUpdated();
        }
        this.pageTitle = this.getPageTitle(this.selectors.form.dropdown);
        this.pageUrl = this.getPageUrl(form.action);

        let formActionParamsString = form.action.split('?')[1];
        let formActionParams = new URLSearchParams(formActionParamsString);
        let report = formActionParams.get('report');

        let table = this.selectorHelper.helpSelector(this.selectors.form.creditors_debtors_table.selector);

        let alignment = this.selectors.form.creditors_debtors_table;
        if (table && this.selectors.form.allowed_reports.includes(report) && this.reportIsValid(alignment)) {

            if (this.loadingButton === null && this.settings.hasOwnProperty('properties') && this.settings.properties.hasOwnProperty('be_auto_load_rows')) {
                const rows = table.querySelectorAll('tr');
                if (rows.length > this.settings.properties.be_auto_load_rows) {
                    await this.injectLoadingButton(table);
                    return;
                }
            }

            this.getHeaderIndexes(alignment);
            await this.appendColumnCBT(table, report);
        }
    }

    // Check for each alignment if the report have the correct alignment configured
    reportIsValid(alignment) {
        let header = this.selectorHelper.helpSelector(alignment.header);
        if (header) {
            let headerCells = header.cells;
            let requiredColumnsNl = alignment.required_columns_nl;
            let requiredColumnsEn = alignment.required_columns_en;

            if (requiredColumnsNl.every((column) => {
                return Object.values(headerCells).some((cell) => {
                    return cell.textContent.startsWith(column);
                });
            })) {
                this.lang = 'nl';
                return true;
            } else if (requiredColumnsEn.every((column) => {
                return Object.values(headerCells).some((cell) => {
                    return cell.textContent.startsWith(column);
                })
            })) {
                this.lang = 'en';
                return true;
            }
        }

        return false;
    }


    //TODO this function and appendColumnCBT are 95% the same so try to merge this together
    async appendColumn(table, report) {
        // Add column element to column group to maintain table aspect ratio
        let colGroup = table.childNodes[0];
        let col = document.createElement('col');
        col.setAttribute('width', '100px');
        colGroup.appendChild(col);

        const thead = table.querySelector('thead');
        let i;

        for (i = 0; i < thead.rows.length; i++) {
            let newCell = thead.rows[i].insertCell(thead.rows[i].cells.length);
            newCell.classList.add('dhse');
            newCell.style.whiteSpace = 'nowrap';
            newCell.style.textOverflow = 'ellipsis';
            newCell.style.overflow = 'hidden';

            if (i + 1 === thead.rows.length) {
                newCell.innerText = await trans('open_questions.title');
            }
        }

        let questions = null;
        if (this.demo === false) {
            questions = await this.getQuestions();
            this.types = await this.getTypes();
        }

        await this.insertCells(table, questions, report);
    }

    async insertCells(table, questions, report) {
        let questionName = '';
        const creditorsDebtorsTable = this.selectors.iframe.form.creditors_debtors_table;

        const tbody = table.querySelector('tbody');
        let chunk = 100;
        let tbodyLength = tbody.rows.length;

        for (let y = 0; y < tbodyLength; y += chunk) {
            let lastIndex = y + chunk;
            if (lastIndex > tbodyLength) {
                lastIndex = tbodyLength - 1;
            }

            for (let i = y; i < lastIndex; i++) {
                let row = tbody.rows[i];
                let newCell = row.insertCell(row.cells.length);

                // Depending on the row class we inject different elements and classes
                switch (row.className) {
                    case creditorsDebtorsTable.rows.middle.class:
                        const middleRow = creditorsDebtorsTable.rows.middle;

                        if (middleRow.reports_exception.includes(report)) {
                            questionName = row.cells[this.attributesIndexes.question_name].textContent.trim();
                        }

                        let amount = row.cells[this.attributesIndexes.amount].textContent.trim();
                        let invoiceNumber = row.cells[this.attributesIndexes.invoice_number].textContent.trim();
                        let bookingNumber = row.cells[this.attributesIndexes.bookkeeping_number].textContent.trim();
                        let transactionDate = row.cells[this.attributesIndexes.transaction_date].textContent.replaceAll("/", "-").trim();

                        let currentQuestion = null;

                        // Because users can change amount format in Twinfield we remove dots and command
                        // to check if the values match
                        if (Array.isArray(questions)) {
                            currentQuestion = this.getCurrentQuestion(
                                questions,
                                questionName,
                                amount,
                                transactionDate,
                                invoiceNumber,
                                bookingNumber
                            );
                        }

                        newCell.classList.add(creditorsDebtorsTable.rows.middle.columns.right_text_class);

                        if (currentQuestion) {
                            // save description as a name when creating or deleting question
                            if (currentQuestion.name === '' && currentQuestion.description) {
                                currentQuestion.name = currentQuestion.description;
                            }

                            let statusQuestionButton = this.createNewStatusQuestion(
                                newCell,
                                currentQuestion,
                                'bookkeeping',
                                this.demo,
                                this.types,
                                currentQuestion.status,
                                currentQuestion.status_text
                            );
                        } else {
                            let data = await this.handleNewQuestionButtonData(
                                questionName,
                                amount,
                                transactionDate,
                                this.currency ?? 'EUR',
                                invoiceNumber,
                                bookingNumber,
                                '',
                                this.pageTitle,
                                this.pageUrl
                            );

                            let statusQuestionButton = this.createNewStatusQuestion(
                                newCell,
                                data,
                                'bookkeeping',
                                this.demo,
                                this.types
                            );
                        }

                        break;
                    case '':
                        newCell.classList.add(creditorsDebtorsTable.columns.empty);
                        break;
                    case creditorsDebtorsTable.rows.top.class:
                        const topRow = creditorsDebtorsTable.rows.top;

                        const currencyElement = this.selectorHelper.helpSelector(topRow.columns.currency, null, [i + 1]);
                        if (!this.currency && currencyElement.textContent.trim() !== '') {
                            this.currency = currencyElement.textContent.split(":")[1].trim();
                        }

                        const questionElement = this.selectorHelper.helpSelector(topRow.columns.question_name, null, [i + 1]);
                        if (questionElement !== null && report !== 'GLO') {
                            questionName = questionElement.textContent.split(/-(.+)/)[1];
                            if (questionName) {
                                questionName = questionName.trim();
                            }
                        }

                        newCell.classList.add(creditorsDebtorsTable.columns.top);
                        break;
                }
            }

            await new Promise(resolve => setTimeout(resolve, 10));
        }
    }

    async appendColumnCBT(table, report) {
        let questions = null;
        if (this.demo === false) {
            questions = await this.getQuestions();
            this.types = await this.getTypes();
        }

        let i;
        let questionName = '';

        // Add column element to column group to maintain table aspect ratio
        let colGroup = table.childNodes[0];
        let col = document.createElement('col');
        col.setAttribute('width', '100px');
        colGroup.appendChild(col);

        let currency = null;

        const thead = table.querySelector('thead');
        for (i = 0; i < thead.rows.length; i++) {
            let newCell = thead.rows[i].insertCell(thead.rows[i].cells.length);
            newCell.classList.add('dhse');
            newCell.style.whiteSpace = 'nowrap';
            newCell.style.textOverflow = 'ellipsis';
            newCell.style.overflow = 'hidden';

            if (i + 1 === thead.rows.length) {
                newCell.innerText = await trans('open_questions.title');
            }
        }

        const creditorsDebtorsTable = this.selectors.form.creditors_debtors_table;
        const tbody = table.querySelector('tbody');
        for (i = 0; i < tbody.rows.length; i++) {
            let row = tbody.rows[i];
            let newCell = row.insertCell(row.cells.length);

            // Depending on the row class we inject different elements and classes
            switch (row.className) {
                case creditorsDebtorsTable.rows.middle.class:
                    const middleRow = creditorsDebtorsTable.rows.middle;
                    if (middleRow.reports_exception.includes(report)) {
                        questionName = row.cells[this.attributesIndexes.question_name].textContent;
                    }

                    let amount = row.cells[this.attributesIndexes.amount].textContent.trim();
                    let invoiceNumber = row.cells[this.attributesIndexes.invoice_number].textContent.trim();
                    let bookingNumber = row.cells[this.attributesIndexes.bookkeeping_number].textContent.trim();
                    let transactionDate = row.cells[this.attributesIndexes.transaction_date].textContent.replaceAll("/", "-").trim();

                    let currentQuestion = null;

                    // Because users can change amount format in Twinfield we remove dots and command to check if the
                    // values match. For this report we also remove the minus because it doesn't matter if is a positive
                    // or negative value.
                    if (Array.isArray(questions)) {
                        currentQuestion = this.getCurrentQuestion(
                            questions,
                            questionName,
                            amount,
                            transactionDate,
                            invoiceNumber,
                            bookingNumber,
                            true
                        );
                    }

                    newCell.classList.add(creditorsDebtorsTable.rows.middle.columns.right_text_class);

                    try {
                        const dataCreditCorrection = this.correctDataCredit(amount);

                        if (dataCreditCorrection) {
                            amount = dataCreditCorrection.amount
                            if (dataCreditCorrection.questionName) {
                                questionName = dataCreditCorrection.questionName
                            }
                        }
                    } catch (ex) {
                        console.error(ex)
                    }

                    if (currentQuestion) {
                        let statusQuestionButton = this.createNewStatusQuestion(
                            newCell,
                            currentQuestion,
                            'bookkeeping',
                            this.demo,
                            this.types,
                            currentQuestion.status,
                            currentQuestion.status_text
                        );
                    } else {
                        let data = await this.handleNewQuestionButtonData(
                            questionName,
                            amount,
                            transactionDate,
                            currency ?? 'EUR',
                            invoiceNumber,
                            bookingNumber,
                            '',
                            this.pageTitle,
                            this.pageUrl
                        );
                        let statusQuestionButton = this.createNewStatusQuestion(
                            newCell,
                            data,
                            'bookkeeping',
                            this.demo,
                            this.types
                        );
                    }


                    break;
                case '':
                    newCell.classList.add(creditorsDebtorsTable.columns.empty);
                    break;
                case creditorsDebtorsTable.rows.top.class:
                    const topRow = creditorsDebtorsTable.rows.top;

                    const currencyElement = this.selectorHelper.helpSelector(topRow.columns.currency, null, [i + 1]);
                    if (!currency && currencyElement.textContent.trim() !== '') {
                        currency = currencyElement.textContent;
                    }

                    const questionElement = this.selectorHelper.helpSelector(topRow.columns.question_name, null, [i + 1]);
                    if (questionElement !== null && report !== 'GLO') {
                        questionName = questionElement.textContent.split(/-(.+)/)[1];
                    }
                    newCell.classList.add(creditorsDebtorsTable.columns.top);
                    break;
            }
        }
    }

    correctDataCredit(amount) {
        const criteriaTable = document.querySelector("#trPromptsHeader")?.parentElement;
        if (!criteriaTable) {
            return null
        }

        const tableRows = criteriaTable.querySelectorAll('tr:not(:nth-child(1))');
        const tableData = new Map([...tableRows.values()].map(tableRow => {
            const children = tableRow.children;
            return [children.item(0).textContent, children.item(1).textContent]
        }))

        const grootboekrekeningType = tableData.get("Grootboekrekening");
        if (!grootboekrekeningType?.startsWith("Crediteuren (")) {
            return null
        }

        let questionName;
        if (tableData.has("Relatie/kostenplaats")) {
            let relatie = tableData.get("Relatie/kostenplaats");
            relatie = relatie.substring(0, relatie.lastIndexOf('(')).trim()
            questionName = relatie;
        }

        amount = amount.replace("-", "")
        return {
            amount,
            questionName
        }
    }

    getCurrentQuestion(
        questions,
        questionName,
        amount,
        transactionDate,
        invoiceNumber,
        bookingNumber,
        ignoreNegativeAmount = false
    ) {
        return questions.find((question) => {
            let currentAmountFormatted = question.amount.replace(',', '').replace('.', '');
            let questionAmountFormatted = amount.replace(',', '').replace('.', '');

            if (ignoreNegativeAmount) {
                currentAmountFormatted = currentAmountFormatted.replace('-', '');
                questionAmountFormatted = questionAmountFormatted.replace('-', '');
            }

            let nameCondition = question.name.trim() === questionName.trim() || question.description === questionName.trim();
            let invoiceBookkeepingNumberCondition = question.invoice_number === invoiceNumber || question.bookkeeping_number === bookingNumber;

            if (
                nameCondition
                && currentAmountFormatted === questionAmountFormatted
                && question.transaction_date === transactionDate
                && invoiceBookkeepingNumberCondition
            ) {
                return true;
            }

            return currentAmountFormatted === questionAmountFormatted &&
                question.transaction_date === transactionDate &&
                question.invoice_number === invoiceNumber &&
                question.status !== 'deleted';
        })
    }
}

let twinfield = new Twinfield();

document.addEventListener('setData', async function (e) {
    await twinfield.setData(
        e.detail.externalCompanyId,
        e.detail.externalCompanyCode,
        e.detail.externalCompanyName,
        e.detail.companyId
    )
})

document.addEventListener('start', async function (e) {
    e.stopImmediatePropagation();

    await twinfield.start(
        e.detail.demo,
        e.detail.settings
    )
})