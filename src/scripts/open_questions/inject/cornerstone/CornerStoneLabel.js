class CornerStoneLabel {
    constructor(element, name) {
        this.ammountElement = null;
        this.element = null;

        const companyOption = document.createElement('div');
        companyOption.classList.add('sl-company-option');
        element.appendChild(companyOption);

        this.element = companyOption;

        const companyOptionAmount = document.createElement('span');
        companyOptionAmount.classList.add('sl-amount');
        companyOption.appendChild(companyOptionAmount);

        this.ammountElement = companyOptionAmount;

        const companyOptionText = document.createElement('span');
        companyOptionText.innerText = name;
        companyOption.appendChild(companyOptionText);
    }

    setAmount(amount) {
        this.ammountElement.innerText = amount;
    }
}

export {CornerStoneLabel};