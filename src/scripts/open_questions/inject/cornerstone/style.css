div {
    display: block;
}

#sl-corner-stone {
    width: auto;
    height: 40px;
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 10000001;
    transition: 0.5s height;
    user-select: none;
    font-family: Proxima Nova, sans-serif;
}

#sl-corner-stone.sl-company-options-open {
    height: 160px;
}

#sl-corner-stone.hide #sl-company-options {
    display: none;
}

#sl-corner-stone.hide #sl-company-footer {
    display: none;
}

#sl-company-options {
    background-color: #0F9BF3;
    width: 190px;
    height: 0;
    transition: 0.5s height;
    border-radius: 5px 5px 0 0;
    z-index: 10000002;
    overflow: hidden;
}

.sl-company-option {
    z-index: 10000003;
    padding: 10px;
    line-height: 20px;
    font-size: 14px;
    color: #FFFFFF;
}

.sl-company-option:hover {
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.2);
}

.sl-company-options-open > #sl-company-options {
    height: 120px;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
}

#sl-company-footer {
    display: inline-block;
    height: 40px;
    width: 190px;
    z-index: 10000004;
    cursor: pointer;
    background-color: #0F9BF3;
    border-radius: 5px;
    transition-delay: 0.45s;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
    float: left;
}

.sl-company-options-open > #sl-company-footer {
    border-radius: 0 0 5px 5px;
    transition-delay: 0.0s;
}

#sl-company-name-container {
    width: 80%;
    height: 100%;
    display: inline-block;
}

#sl-company-options-toggle {
    display: inline-block;
    transition: 0.5s transform;
}

.sl-company-options-open > #sl-company-footer > #sl-company-options-toggle {
    transform: rotate(180deg);
}

#sl-toggle-icon {
    fill: #FFFFFF;
    line-height: 20px;
    font-size: 14px;
    height: 18px;
}

#sl-company-name {
    color: #FFFFFF;
    font-size: 14px;
    padding: 11px 0 0 10px;
    line-height: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: bold;
}

.sl-amount {
    padding-right: 5px;
    font-weight: bold;
}

#sl-company-lock {
    display: inline-block;
    height: 40px;
    width: 40px;
    z-index: 10000004;
    background-color: #0F9BF3;
    border-radius: 5px;
    transition-delay: 0.45s;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
    margin: 0 10px;
}

#sl-company-lock.hide {
    display: none;
}

#sl-company-lock.clickable {
    cursor: pointer;
}

#sl-company-lock.locked {
    background-color: orange;
}

.block-icon {
    width: 22px;
    height: 22px;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjBweCIgeT0iMHB4Igp3aWR0aD0iMzAiIGhlaWdodD0iMzAiCnZpZXdCb3g9IjAgMCAzMCAzMCIKc3R5bGU9IiBmaWxsOiNmZmZmZmY7Ij4gICAgPHBhdGggZD0iTSAxNSAyIEMgMTEuMTQ1NjY2IDIgOCA1LjE0NTY2NjEgOCA5IEwgOCAxMSBMIDYgMTEgQyA0Ljg5NSAxMSA0IDExLjg5NSA0IDEzIEwgNCAyNSBDIDQgMjYuMTA1IDQuODk1IDI3IDYgMjcgTCAyNCAyNyBDIDI1LjEwNSAyNyAyNiAyNi4xMDUgMjYgMjUgTCAyNiAxMyBDIDI2IDExLjg5NSAyNS4xMDUgMTEgMjQgMTEgTCAyMiAxMSBMIDIyIDkgQyAyMiA1LjI3MTU4MjMgMTkuMDM2NTgxIDIuMjY4NTY1MyAxNS4zNTU0NjkgMi4wNzIyNjU2IEEgMS4wMDAxIDEuMDAwMSAwIDAgMCAxNSAyIHogTSAxNSA0IEMgMTcuNzczNjY2IDQgMjAgNi4yMjYzMzM5IDIwIDkgTCAyMCAxMSBMIDEwIDExIEwgMTAgOSBDIDEwIDYuMjI2MzMzOSAxMi4yMjYzMzQgNCAxNSA0IHoiPjwvcGF0aD48L3N2Zz4=') 50% 50% no-repeat;
    background-size: 100%;
    margin: 0 auto;
    top: 8px;
    position: relative;
}
