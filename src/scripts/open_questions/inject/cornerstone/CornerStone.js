import style from '!css-loader!./style.css';
import {CornerStoneLabel} from "./CornerStoneLabel";
import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {CornerStoneLockButton} from "./CornerStoneLockButton";
import {trans} from "../../../helpers/TranslationHelper";
import browser from "webextension-polyfill";
import {LogSender} from "../../../helpers/LogSender";

class CornerStone {
    constructor(data) {
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
        this.data = data;
        this.dragging = false;
        this.currentX = 0;
        this.currentY = 0;
        this.initialX = 0;
        this.initialY = 0;
        this.xOffset = 0;
        this.yOffset = 0;
        this._toggled = false;
        this.clickThreshold = 5; // Prevent small movements from triggering click
    }

    async handleAppendCornerStone(element) {
        await this.loadSavedPosition();

        element.appendChild(this._injectableElement);

        if (this.isOutOfViewport()) {
            LogSender.debug('CornerStone detected outside viewport');
            this.xOffset = 0;
            this.yOffset = 0;
            this.setTranslate(0, 0, this._cornerStone);
        }
    }

    async init() {
        const rootContainer = document.createElement('sl-corner-stone');
        const shadowRoot = rootContainer.attachShadow({mode: 'open'});

        const styleElement = document.createElement('style');
        styleElement.innerText = style;
        styleElement.style.display = "none";
        shadowRoot.appendChild(styleElement);

        this._cornerStone = document.createElement('div');
        this._cornerStone.setAttribute('draggable', 'true');
        this._cornerStone.setAttribute('id', 'sl-corner-stone');

        // handle drag events
        this._cornerStone.addEventListener('mousedown', this.dragStart.bind(this));
        this._cornerStone.addEventListener('touchstart', this.dragStart.bind(this));
        this._cornerStone.addEventListener('mouseleave', this.dragEnd.bind(this));

        shadowRoot.appendChild(this._cornerStone);
        LogSender.debug('CornerStone added element to shadowRoot');

        // region Company options

        const companyOptionsDiv = document.createElement('div');
        companyOptionsDiv.setAttribute('id', 'sl-company-options');
        this._cornerStone.appendChild(companyOptionsDiv);

        this._open_questions_label = new CornerStoneLabel(
            companyOptionsDiv,
            await trans('open_questions.status.open')
        );
        this._pending_questions_label = new CornerStoneLabel(
            companyOptionsDiv,
            await trans('open_questions.status.pending')
        );
        this._closed_questions_label = new CornerStoneLabel(
            companyOptionsDiv,
            await trans('open_questions.status.completed')
        );
        // endregion

        // region Company footer

        const companyFooter = document.createElement('div');
        companyFooter.setAttribute('id', 'sl-company-footer');
        this._cornerStone.appendChild(companyFooter);

        companyFooter.addEventListener("click", this.companyOptionsToggle.bind(this));
        this._open_questions_label.element.addEventListener("click", this.openQuestionPage.bind(this, 'open'));
        this._pending_questions_label.element.addEventListener("click", this.openQuestionPage.bind(this, 'pending'));
        this._closed_questions_label.element.addEventListener("click", this.openQuestionPage.bind(this, 'completed'));

        const companyNameContainer = document.createElement('div');
        companyNameContainer.setAttribute('id', 'sl-company-name-container');
        companyFooter.appendChild(companyNameContainer);

        const companyName = document.createElement('div');
        companyName.setAttribute('id', 'sl-company-name');
        companyNameContainer.appendChild(companyName);
        this._companyName = companyName;

        const companyOptionsToggle = document.createElement('div');
        companyOptionsToggle.setAttribute('id', 'sl-company-options-toggle');
        companyFooter.appendChild(companyOptionsToggle);

        const toggleIcon = document.createElement('div');
        toggleIcon.setAttribute('id', 'sl-toggle-icon');
        toggleIcon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path d="M0 16.67l2.829 2.83 9.175-9.339 9.167 9.339 2.829-2.83-11.996-12.17z"/></svg>';
        toggleIcon.classList.add('sl-icon-simple-arrow-up');
        companyOptionsToggle.appendChild(toggleIcon);

        const lockButton = new CornerStoneLockButton(this.data);
        this._lockButton = lockButton;
        lockButton._injectableElement.addEventListener('click', (e) => {
            if (!this.dragging) {
                lockButton.lockCompany();
            }
        });

        this._cornerStone.appendChild(lockButton._injectableElement);

        this._injectableElement = rootContainer;
    }

    setCompanyIsBlocked(flag) {
        this._lockButton.setLocked(flag);
    }

    setCompanyExternalData(data) {
        this.data = data;
        this._lockButton.setCompanyExternalData(this.data);
    }

    setOpenQuestionsCount(count) {
        this._open_questions_label.setAmount(count);
    }

    setPendingQuestionsCount(count) {
        this._pending_questions_label.setAmount(count);
    }

    setClosedQuestionsCount(count) {
        this._closed_questions_label.setAmount(count);
    }

    updateCompanyName(name) {
        this._companyName.innerText = name;
    }

    companyOptionsToggle() {
        if (!this.dragging) {
            this.toggled = !this.toggled;
        }
    }

    setHidden(hidden) {
        if (hidden) {
            this._cornerStone.classList.add('hide');
        } else {
            this._cornerStone.classList.remove('hide');
        }
    }

    hideLockButton(hidden) {
        if (this._lockButton) {
            this._lockButton.setHidden(hidden);
        }
    }

    async openQuestionPage(status) {
        if (this._companyName.innerText) {
            await this.openQuestionsCommunicationService.sendToBExtension('redirect_to_open_questions', {
                companyName: this._companyName.innerText,
                status: status
            });
        }
    }

    set toggled(value) {
        if (this._toggled === value) {
            return;
        }

        this._toggled = value;

        if (value) {
            this._cornerStone.classList.add('sl-company-options-open');
        } else {
            this._cornerStone.classList.remove('sl-company-options-open');
        }
    }

    get toggled() {
        return this._toggled;
    }

    get injectableElement() {
        return this._injectableElement;
    }

    isOutOfViewport() {
        var element = this._cornerStone;
        let rect = element.getBoundingClientRect();

        return rect.x < 0 || (rect.x + element.offsetWidth) > window.innerWidth
            || rect.y < 0 || (rect.y + element.offsetHeight) > window.innerHeight;
    }

    // load saved cornerstone position
    async loadSavedPosition() {
        if (await this.hasSavedPosition()) {
            let position = JSON.parse((await browser.storage.local.get('cornerstone_position')).cornerstone_position);
            this.xOffset = position[this.data.serviceName].xOffset;
            this.yOffset = position[this.data.serviceName].yOffset;
            this.setTranslate(position[this.data.serviceName].xOffset, position[this.data.serviceName].yOffset, this._cornerStone);
            LogSender.debug('CornerStone loaded saved position');
        }
    }

    // save cornerstone position
    async savePosition() {
        let position = {
            xOffset: this.xOffset,
            yOffset: this.yOffset
        }
        let cornerStonePosition = (await browser.storage.local.get('cornerstone_position')).cornerstone_position
        if (cornerStonePosition) {
            let storedPositions = JSON.parse(cornerStonePosition);
            storedPositions[this.data.serviceName] = position;
            await browser.storage.local.set({'cornerstone_position': JSON.stringify(storedPositions)});
            LogSender.debug('CornerStone saved updated position');
        } else {
            let storedPositions = {};
            storedPositions[this.data.serviceName] = position;

            await browser.storage.local.set({'cornerstone_position': JSON.stringify(storedPositions)});
        }
    }

    async hasSavedPosition() {
        let cornerStonePosition = (await browser.storage.local.get('cornerstone_position')).cornerstone_position
        if (this.data.serviceName && cornerStonePosition) {
            let storedPositions = JSON.parse(cornerStonePosition);
            return this.data.serviceName in storedPositions;
        }

        return false;
    }

    dragStart(e) {
        e.preventDefault();
        this.initialX = e.clientX || e.touches[0].clientX;
        this.initialY = e.clientY || e.touches[0].clientY;
        this.dragging = false;
        document.onmouseup = this.dragEnd.bind(this);
        document.onmousemove = this.drag.bind(this);
        document.touchend = this.dragEnd.bind(this);
        document.touchmove = this.drag.bind(this);
    }

    drag(e) {
        e.preventDefault();
        let clientX = e.clientX || e.touches[0].clientX;
        let clientY = e.clientY || e.touches[0].clientY;

        let deltaX = Math.abs(clientX - this.initialX);
        let deltaY = Math.abs(clientY - this.initialY);

        if (deltaX > this.clickThreshold || deltaY > this.clickThreshold) {
            this.dragging = true; // Only mark as dragging if movement exceeds threshold
        }
        if (this.dragging) {
            this.currentX = this.initialX - e.clientX;
            this.currentY = this.initialY - e.clientY;
            this.initialX = e.clientX;
            this.initialY = e.clientY;
            this.xOffset -= this.currentX;
            this.yOffset -= this.currentY;
            this.setTranslate(this.xOffset, this.yOffset, this._cornerStone);
        }
    }

    async dragEnd() {
        if (this.dragging) {
            await this.savePosition();
            this.dragging = false;
        }

        document.onmouseup = null;
        document.onmousemove = null;
        document.ontouchend = null;
        document.ontouchmove = null;
    }

    setTranslate(xPos, yPos, el) {
        el.style.transform = "translate3d(" + xPos + "px, " + yPos + "px, 0)";
    }
}

export {CornerStone};