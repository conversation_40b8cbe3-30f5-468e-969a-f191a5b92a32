import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {NotificationHelper} from "../../../tax/inject/helpers/NotificationHelper";
import {trans} from "../../../helpers/TranslationHelper";
import {LogSender} from "../../../helpers/LogSender";

class CornerStoneLockButton {
    constructor(data) {
        this.data = data;
        this.lockButton = false;
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
        this._injectableElement = this.getElement();
        this.handleListeners();
        this.handleUserIsCompanyManager();
    }

    getElement() {
        const companyLock = document.createElement('div');
        companyLock.setAttribute('id', 'sl-company-lock');

        const lockIcon = document.createElement('div');
        lockIcon.setAttribute('class', 'block-icon');
        companyLock.appendChild(lockIcon);

        return companyLock;
    }

    setLocked(flag) {
        if (flag) {
            this._injectableElement.classList.add('locked');
            this.lockButton = true;
        } else {
            this._injectableElement.classList.remove('locked');
            this.lockButton = false;
        }
        this.handleUserIsCompanyManager();
    }

    async handleListeners() {
        let user = await this.openQuestionsCommunicationService.sendToBExtension('get_user_profile');
        if (user.is_company_manager || user.is_account_manager) {
            this._injectableElement.classList.add('clickable');
        }
    }

    async handleUserIsCompanyManager() {
        let user = await this.openQuestionsCommunicationService.sendToBExtension('get_user_profile');
        if (user.is_company_manager || user.is_account_manager) {
            this.setHidden(false);
        } else {
            this.setHidden(true);
        }
    }

    setCompanyExternalData(data) {
        this.data = data;
    }

    setHidden(hidden) {
        if (hidden) {
            this._injectableElement.classList.add('hide');
        } else {
            this._injectableElement.classList.remove('hide');
        }
    }

    async lockCompany() {
        LogSender.debug('Lock button click detected');
        if (confirm(await this.getLockConfirmMessage())) {
            LogSender.debug('User confirmed company lock toggle');
            let companyUpdate = await this.openQuestionsCommunicationService.sendToBExtension('update_company_configuration', {
                'data': {
                    'externalId': this.data.companyId,
                    'externalName': this.data.companyName,
                    'serviceName': this.data.serviceName,
                    'blocked': !this.lockButton
                },
            });
            if (companyUpdate) {
                this.toggleLock();
                NotificationHelper.sendToast('success', await this.getLockedConfirmMessage());
                LogSender.debug('Company lock updated');
            } else {
                NotificationHelper.sendGenericError();
                LogSender.error('Company lock toggle failed');
            }
        }
    }

    toggleLock() {
        this.lockButton = !this.lockButton;
        if (this.lockButton) {
            this._injectableElement.classList.add('locked');
        } else {
            this._injectableElement.classList.remove('locked');
        }
    }

    async getLockConfirmMessage() {
        return this.lockButton ? await trans('open_questions.lock.unlock_message') : await trans('open_questions.lock.lock_message');
    }

    async getLockedConfirmMessage() {
        return this.lockButton ? await trans('open_questions.lock.locked') : await trans('open_questions.lock.unlocked');
    }
}

export {CornerStoneLockButton};