import {OpenQuestionsHelper} from "../helpers/OpenQuestionsHelper";
import {NewQuestion} from "../buttons/NewQuestion";
import {ServicePage} from "../ServicePage";
import browser from "webextension-polyfill";

export class ExplorerIndexView extends ServicePage {
    constructor(selectors, demo) {
        super();
        this.selectors = selectors;
        this.demo = demo;
    }

    start() {
        this.openQuestionsHelper = new OpenQuestionsHelper(this.demo);
        this.listen();
    }

    listen() {
        const url = window.location.href;
        let self = this;
        // had to implement this because of the await taking to long sometimes.
        let selectorFound = false;
        const observer = new MutationObserver(async function () {
            if ((self.getSelectedCompanyName() || self.getSelectedEmployeeName()) && !selectorFound) {
                if (url.includes(self.selectors.employeeUrlContains) && self.getSelectedEmployeeId()) {
                    selectorFound = true;
                    await self.validateComponents('employee');
                } else if (url.includes(self.selectors.companyUrlContains) && self.getSelectedCompanyId()) {
                    selectorFound = true;
                    await self.validateComponents('company')
                }

                if (selectorFound) {
                    observer.disconnect();
                }
            }
        });

        const config = {childList: true};
        observer.observe(document.body, config);
    }
    /**
     * Service name
     * @returns {string}
     */
    getServiceName() {
        return 'nmbrs_open_questions';
    }

    getSubtitle(question_subject) {
        if (question_subject === 'employee') {
            return this.getSelectedEmployeeName();
        } else {
            return this.getSelectedCompanyName();
        }
    }

    getLeftMenuElement() {
        let leftMenu = this.getSelectorFromPage(this.selectors.leftMenu);
        return leftMenu ? leftMenu.shadowRoot : null;
    }

    getSelectedCompanyElement() {
        let leftMenu = this.getLeftMenuElement();
        return leftMenu ? this.getSelectorFromPage(this.selectors.selectedCompany, leftMenu) : null;
    }

    getSelectedCompanyId() {
        let company = this.getSelectedCompanyElement();
        return company ? company.getAttribute('data-testid') : null;
    }

    getSelectedCompanyName() {
        let company = this.getSelectedCompanyElement();
        return company ? company.getAttribute('data-testvalue') : null;
    }

    getSelectedEmployeeElement() {
        let leftMenu = this.getLeftMenuElement();
        return leftMenu ? this.getSelectorFromPage(this.selectors.selectedEmployee, leftMenu) : null;
    }

    getSelectedEmployeeId() {
        let employee = this.getSelectedEmployeeElement();
        return employee ? employee.getAttribute('data-testid') : null;
    }

    getSelectedEmployeeName() {
        let employee = this.getSelectedEmployeeElement();
        return employee ? employee.getAttribute('data-testvalue') : null;

    }

    getSelectorFromPage(selector, parentElement = null) {
        if (parentElement === null) {
            parentElement = document;
        }
        return parentElement.querySelector(selector) ?? null;
    }

    handleSelectedId(subject) {
        if (subject === 'company') {
            return this.getSelectedCompanyId();
        }
        if (subject === 'employee') {
            return this.getSelectedEmployeeId();
        }

        return null;
    }

    /**
     * Validate if we can add components to the page
     * @returns {Promise<void>}
     */
    async validateComponents(question_subject) {
        let companyBlocked = await this.isCompanyBlocked(
            this.getServiceName(),
            this.handleSelectedId(question_subject),
            this.getSubtitle(question_subject)
        ) && this.demo === false;

        if (await this.isCompanyManager() && companyBlocked) {
            return this.appendCornerStone(question_subject);
        }

        if (companyBlocked) {
            return;
        }

        return this.appendComponents(question_subject);
    }

    /**
     * Add new question button and dropdown
     */
    async appendComponents(question_subject) {
        let buttonContainer = this.openQuestionsHelper.getButtonsContainer(`
            .sl-buttons-container {
                margin-right: 10px;
                display: inline-block;
                position: relative;
                top: 3px;
                vertical-align: top;
            }
            .sl-buttons-container > div {
                display: inline-block;
                margin: 0 5px;
                vertical-align: top;
            }
        `);
        await this.createNewQuestionButton(question_subject, buttonContainer);
        await this.createQuestionsDropdownList(question_subject, buttonContainer);
        await this.appendCornerStone(question_subject);

        let pageButtonContainer = document.querySelector(this.selectors.buttonContainer);
        pageButtonContainer.prepend(buttonContainer);
    }

    /**
     * Remove button and dropdown
     */
    async removeButtonContainer() {
        let element = document.querySelector('.sl-buttons-container');
        if (element) {
            element.parentNode.removeChild(element);
        }
    }

    async reloadComponents() {
        const url = window.location.href;
        await this.removeButtonContainer();
        if (url.includes(this.selectors.employeeUrlContains)) {
            await this.appendComponents('employee');
        } else if (url.includes(this.selectors.companyUrlContains)) {
            await this.appendComponents('company')
        }
    }

    /**
     * Get document title from selected row
     * @returns {string|null}
     */
    getPageTitle() {
        let list = document.querySelector(this.selectors.document.list);
        let parent = list.closest(this.selectors.document.parent);
        let title = parent.querySelector(this.selectors.document.title);
        if (title) {
            return title.textContent.trim();
        }

        return null;
    }

    /**
     * Add new question button
     */
    async createNewQuestionButton(question_subject, container) {
        let newQuestionButton = new NewQuestion({
            'service_name': this.getServiceName(),
            'external_company_id': this.handleSelectedId(question_subject),
            'external_company_name': this.getSubtitle(question_subject),
            'subtitle': this.getSubtitle(question_subject),
            'question_subject': question_subject,
            'page_title': question_subject.charAt(0).toUpperCase() + question_subject.slice(1) + ' > ' + this.getSubtitle(question_subject),
            'page_url': window.location.href
        }, 'wage', this.demo);
        await newQuestionButton.init();
        newQuestionButton.handleListeners();
        newQuestionButton._injectableElement.addEventListener('question-updated', () => {
            this.onQuestionUpdated();
            this.reloadComponents();
        });

        container.prepend(newQuestionButton._injectableElement);
    }

    /**
     * Create question dropdown list
     */
    async createQuestionsDropdownList(question_subject, container) {
        let externalId = this.handleSelectedId(question_subject);
        if (externalId) {
            let questionsDropdown = await this.openQuestionsHelper.getQuestionsDropdown({
                'category': 'wage',
                'service_name': this.getServiceName(),
                'external_id': externalId
            });
            if (questionsDropdown) {
                questionsDropdown.addEventListener('question-updated', () => {
                    this.onQuestionUpdated();
                    this.reloadComponents();
                });
                container.prepend(questionsDropdown);
            }
        }
    }

    /**
     * Gathers the Hix company
     * Append secure login corner button
     * @returns {Promise<*>}
     */
    async appendCornerStone(question_subject) {
        if (this.demo) {
            console.log('Cornerstone: Demo mode on');
            return;
        }
        await browser.storage.local.set({[this.getServiceName() + '_external_company_id']: this.handleSelectedId(question_subject)});
        await browser.storage.local.set({[this.getServiceName() + '_external_company_name']: this.getSubtitle(question_subject)});
        await browser.storage.local.set(
            {
                [this.getServiceName() + '_external_company_code']: (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id']
            });

        await this.onQuestionUpdated();
    }

    async onQuestionUpdated() {
        await this.openQuestionsCommunicationService.sendToBExtension(
            'questions_updated',
            {
                'companyId': (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'],
                'companyCode': (await browser.storage.local.get(this.getServiceName() + '_external_company_code'))[this.getServiceName() + '_external_company_code'],
                'companyName': (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name'],
                'serviceName': this.getServiceName()
            }
        );
    }
}
