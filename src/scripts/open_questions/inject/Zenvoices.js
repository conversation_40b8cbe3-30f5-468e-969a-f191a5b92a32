import {ServicePage} from "./ServicePage";
import browser from "webextension-polyfill";
import {SelectorHelper} from './helpers/SelectorHelper';
import {BasePage} from "./zenvoices/BasePage";
import axios from "axios";
import {TransactionsPage} from "./zenvoices/TransactionsPage";

export class Zenvoices extends ServicePage {
    constructor() {
        super();
        this.selectorHelper = new SelectorHelper();
    }

    async start(demo, settings) {
        if (!(await browser.storage.local.get('hide-demo-popup'))['hide-demo-popup'] || !demo) {
            await this.setPages(settings, demo);
            await this.setAdministrationData(settings);

            if (
                await this.isCompanyBlocked(
                    this.getServiceName(),
                    (await browser.storage.local.get(this.getServiceName() + '_external_company_id'))[this.getServiceName() + '_external_company_id'],
                    (await browser.storage.local.get(this.getServiceName() + '_external_company_name'))[this.getServiceName() + '_external_company_name']
                )
            ) {
                return;
            }

            this.startCurrentPage(demo, settings);
            this.observeUrlChange(demo, settings);
        }
    }

    observeUrlChange(demo, settings) {
        let oldHref = document.location.href;
        let bodyList = document.querySelector("body")

        this.selectorHelper.waitForElement(settings.selectors.administration_header).then(() => {
            let observer = new MutationObserver(async (mutations) => {
                for (let mutation of mutations) {
                    if ( mutation.target.parentNode) {
                        if (
                            mutation.type === "characterData"
                            && mutation.target.parentNode.classList.contains(settings.selectors.administration_class)
                        ) {
                            await this.setAdministrationData(settings);
                        }
                    }
                }
                if (oldHref !== document.location.href) {
                    oldHref = document.location.href;
                    this.startCurrentPage(demo, settings);
                }
            });
            let config = {
                childList: true,
                subtree: true,
                characterData: true
            };

            observer.observe(bodyList, config);
        });
    }

    startCurrentPage(demo, settings) {
        const page = this.getCurrentPage(settings);
        if (page) {
            this.pages[page].handle();
        }
    }

    async setPages(settings, demo) {
        this.pages = {
            inbox_page: new BasePage(settings.selectors.inbox_page, demo),
            inbox_import_failed_page: new BasePage(settings.selectors.inbox_import_failed_page, demo),
            transactions_incomplete_page: new TransactionsPage(settings.selectors.transactions_incomplete_page, demo),
            transactions_to_approve_page: new TransactionsPage(settings.selectors.transactions_to_approve_page, demo),
            transactions_drafts_page: new TransactionsPage(settings.selectors.transactions_drafts_page, demo)
        }

        this.demo = demo;
    }

    async setAdministrationData(settings) {
        axios.get(settings.selectors.administration_endpoint).then(async (response) => {
            if (response.data && response.data.result && response.data.result.administration) {
                const administration = response.data.result.administration;
                await browser.storage.local.set({[this.getServiceName() + '_external_company_id']: administration.id});
                await browser.storage.local.set({[this.getServiceName() + '_external_company_name']: administration.name});
                await browser.storage.local.set({[this.getServiceName() + '_external_company_code']: administration.id});
                if (this.demo === false) {
                    await this.onQuestionUpdated();
                }
            }
        })
    }

      getCurrentPage(settings) {
        let selectedTabPath = window.location.hash;
        for (const [key, page] of Object.entries(settings.allowed_pages)) {
            if (page === selectedTabPath) {
                return key;
            }
        }
        return null
    }

    getServiceName() {
        return 'zenvoices_open_questions';
    }

    getServiceCategory() {
        return 'ocr';
    }

    async getQuestions() {
        let data = {
            'category': this.getServiceCategory(),
            'service_name': this.getServiceName(),
            'company_id': (await browser.storage.local.get(this.getServiceName() + '_company_id'))[this.getServiceName() + '_company_id'],
        }
        return this.openQuestionsCommunicationService.sendToBExtension('get_questions_by_company_id', data);
    }
}

let zenvoices = new Zenvoices();

document.addEventListener('start', async function (e) {
    await zenvoices.start(
        e.detail.demo,
        e.detail.settings
    )
}, {once: true})