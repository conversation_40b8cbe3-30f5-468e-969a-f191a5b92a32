import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {NewStatusQuestion} from "../buttons/NewStatusQuestion";
import {trans} from "../../../helpers/TranslationHelper";
import {BasePage} from "./BasePage";
import {LogSender} from "../../../helpers/LogSender";

/**
 * Pages using this:
 *  - Financieel -> Boekingen en grootboekrekeningen -> Ka<PERSON>n(Overzichten) -> Grootboekrekening = 2200
 */
class GeneralLedgersTable extends BasePage {
    constructor(table, selectors, questions, types, demo) {
        LogSender.debug('Exact/GeneralLedgersTable constructor called');
        super();

        this.selectors = selectors;
        this.questions = questions;
        this.lang = '';
        this.types = types;
        this.demo = demo;
        this.attributesIndexes = {
            question_name: -1,
            description: -1,
            credit_amount: -1,
            debit_amount: -1,
            bookkeeping_number: -1,
            transaction_date: -1,
        };

        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();

        if (this.reportIsValid(table)) {
            this.getHeaderIndexes(table)
            this.appendColumn(table);
        }
    }

    reportIsValid(table) {
        let tableHeader = table.tHead;
        if (tableHeader) {
            let headerCells = tableHeader.rows[0].cells;
            let requiredColumnsNl = this.selectors.iframe.general_ledger_table.required_columns_nl;
            let requiredColumnsEn = this.selectors.iframe.general_ledger_table.required_columns_en;

            if (requiredColumnsNl.every((column) => {
                return Object.values(headerCells).some((cell) => {
                    return column === cell.textContent.trim();
                });
            })) {
                this.lang = 'nl';
                return true;
            } else if (requiredColumnsEn.every((column) => {
                return Object.values(headerCells).some((cell) => {
                    return column === cell.textContent.trim();
                });
            })) {
                this.lang = 'en';
                return true;
            }
        }
        return false;
    }

    getHeaderIndexes(table) {
        let tableHeader = table.tHead;
        if (tableHeader) {
            let headerCells = tableHeader.rows[0].cells;
            let headers = [];
            if (this.lang === 'nl') {
                headers = this.selectors.iframe.general_ledger_table.headers_nl;
            } else if (this.lang === 'en') {
                headers = this.selectors.iframe.general_ledger_table.headers_en;
            }
            for (const [key, value] of Object.entries(headers)) {
                for (let i = 0; i < headerCells.length; i++) {
                    if (headerCells[i].textContent.trim() === value) {
                        this.attributesIndexes[key] = i;
                        break;
                    }
                }
            }
        }
    }

    async appendColumn(table) {
        LogSender.debug('Exact/GeneralLedgersTable appendColumn called');
        let i;
        let currency = 'EUR'; // there is no place with the currency
        let classesToIgnore = this.selectors.iframe.general_ledger_table.classes_to_ignore;
        let tableHeader = table.tHead;
        let tableBody = table.tBodies[0];

        // If it is table header.
        // Before getting the table we want, the user needs to input a number and load the table.
        // However the table is already there but with the headers only.
        // That is why we only load if the rows length is bigger than 4
        if (tableBody.rows.length > 4) {
            let headerCell = document.createElement("th");
            headerCell.id = 'open_questions_column';
            headerCell.style.textAlign = 'right';
            tableHeader.rows[0].appendChild(headerCell);
            headerCell.innerText = await trans('open_questions.title');
        }

        for (i = 0; i < tableBody.rows.length; i++) {
            let row = tableBody.rows[i];

            if (!classesToIgnore.includes(row.className)) {
                let newCell = document.createElement("td");
                newCell.style.textAlign = 'right';
                row.appendChild(newCell);

                let description = row.cells[this.attributesIndexes.description].textContent;

                let questionNameRegex = new RegExp(this.selectors.iframe.general_ledger_table.name.regex);
                let questionName = row.cells[this.attributesIndexes.question_name].textContent.match(questionNameRegex)[0].trim();
                if (questionName === '') {
                    questionName = description;
                }

                let debitAmount = this.getCellTextContent(this.attributesIndexes.debit_amount, row);

                let creditAmount = '-' + this.getCellTextContent(this.attributesIndexes.credit_amount, row);
                let amount = debitAmount && debitAmount.trim() !== '' ? debitAmount : creditAmount;

                let bookingNumber = this.getCellTextContent(this.attributesIndexes.bookkeeping_number, row);

                let transactionDate = this.getCellTextContent(this.attributesIndexes.transaction_date, row);

                let currentQuestion = null;
                if (this.questions) {
                    currentQuestion = this.questions.find((question) => {
                        return question.name === questionName &&
                            question.amount === amount &&
                            question.transaction_date === transactionDate &&
                            question.bookkeeping_number === bookingNumber &&
                            question.status !== 'deleted'
                    })
                }

                let data = {
                    'service_name': 'exact_open_questions',
                    'name': questionName,
                    'amount': amount,
                    'transaction_date': transactionDate,
                    'currency': currency,
                    'invoice_number': '',
                    'bookkeeping_number': bookingNumber,
                    'description': description,
                    'category': 'bookkeeping',
                    'page_title': this.getPageTitle(),
                    'page_url': window.location.href
                };

                if (currentQuestion && currentQuestion.status !== 'deleted') {
                    let statusQuestionButton = new NewStatusQuestion(
                        newCell,
                        currentQuestion,
                        'bookkeeping',
                        this.demo,
                        this.types,
                        currentQuestion.status,
                        currentQuestion.status_text
                    );
                } else {
                    let statusQuestionButton = new NewStatusQuestion(
                        newCell,
                        data,
                        'bookkeeping',
                        this.demo,
                        this.types
                    );
                }
            }
        }
    }
}

export {GeneralLedgersTable}