import {SelectorHelper} from "../helpers/SelectorHelper";

class BasePage {
    constructor(table, selectors, questions, types, demo) {
        this.selectors = selectors;
        this.questions = questions;
        this.types = types;
        this.demo = demo;
        this.selectorHelper = new SelectorHelper();
    }

    /**
     * Page title from where the question was created
     *
     * @returns {string|null}
     */
    getPageTitle() {
        let title = document.querySelector(this.selectors.iframe.selector).contentWindow.document.querySelector(this.selectors.iframe.title);
        if (title) {
            return title.textContent.trim();
        }

        return null;
    }

    /**
     * Remove numbers from name
     * Question names in Exact are like '12 - Name' or 'Name - 12' so we need to get rid of the ID
     * @param nameBeforeRelation
     * @param questionNameSelector
     * @returns {*}
     */
    handleQuestionName(nameBeforeRelation, questionNameSelector) {
        let questionNameParts = questionNameSelector.textContent.split(" - ");
        // Question names in Exact are like '12 - Name' or 'Name - 12' so we need to get rid of the ID
        if (questionNameParts.length === 2) {
            return nameBeforeRelation ? questionNameParts[0] : questionNameParts[1];
        } else if (questionNameParts.length > 2) {
            questionNameParts.pop();
            return questionNameParts.join(' ');
        }
        return questionNameParts[0];
    }

    getCellTextContent(index, row, defaultValue = null) {
        return index !== null && index !== -1 && index !== undefined ? row.cells[index]?.textContent || defaultValue : defaultValue;
    }
}

export {BasePage}