import {OpenQuestionsCommunicationService} from '../OpenQuestionsCommunicationService';
import {NewStatusQuestion} from '../buttons/NewStatusQuestion';
import {trans} from '../../../helpers/TranslationHelper';
import {BasePage} from './BasePage';
import {LogSender} from '../../../helpers/LogSender';

/**
 * Pages using this:
 * - Financieel -> Bank en kas -> Af te handelen
 */
class CflStatementsTable extends BasePage {
    constructor(table, selectors, questions, types, demo) {
        LogSender.debug('Exact/CflStatementTable constructor called');
        super();

        this.selectors = selectors;
        this.questions = questions;
        this.lang = '';
        this.types = types;
        this.demo = demo;
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();
        this.newExactUI = false;

        this.attributesIndexes = {
            payment_reference: -1,
            description: -1,
            amount: -1,
            bookkeeping_number: -1,
            transaction_date: -1,
            currency: -1,
        };

        if (this.reportIsValid(table)) {
            this.getHeaderIndexes(table);
            this.appendColumn(table);
        }
    }

    reportIsValid(table) {
        let tableHeader = table.tHead;

        this.newExactUI = !Boolean(tableHeader.rows[2]);

        if (tableHeader) {
            let headerCells = tableHeader.rows[this.newExactUI ? 1 : 2].cells;
            let requiredColumnsNl = this.selectors.iframe.cfl_statements_table.required_columns_nl;
            let requiredColumnsEn = this.selectors.iframe.cfl_statements_table.required_columns_en;

            if (requiredColumnsNl.every((column) => {
                return Object.values(headerCells).some((cell) => {
                    return column === cell.textContent.trim();
                });
            })) {
                this.lang = 'nl';
                return true;
            } else if (requiredColumnsEn.every((column) => {
                return Object.values(headerCells).some((cell) => {
                    return column === cell.textContent.trim();
                });
            })) {
                this.lang = 'en';
                return true;
            }
        }
        return false;
    }

    getHeaderIndexes(table) {
        let tableHeader = table.tHead;
        if (tableHeader) {
            let headerCells = tableHeader.rows[this.newExactUI ? 1 : 2].cells;
            let headers = [];
            if (this.lang === 'nl') {
                headers = this.selectors.iframe.cfl_statements_table.headers_nl;
            } else if (this.lang === 'en') {
                headers = this.selectors.iframe.cfl_statements_table.headers_en;
            }
            for (const [key, value] of Object.entries(headers)) {
                for (let i = 0; i < headerCells.length; i++) {
                    if (headerCells[i].textContent.trim() === value) {
                        this.attributesIndexes[key] = i;
                        break;
                    }
                }
            }
        }
    }

    async appendColumn(table) {
        LogSender.debug('Exact/CflStatementsTable appendColumn called');
        let classesToIgnore = this.selectors.iframe.cfl_statements_table.classes_to_ignore;
        let tableHeader = table.tHead;
        let tableBody = table.tBodies[0];

        // Just to fill an empty cell in the header
        let auxHeaderCell = document.createElement('th');
        tableHeader.rows[this.newExactUI ? 0 : 1].appendChild(auxHeaderCell);

        let headerCell = document.createElement('th');
        headerCell.id = 'open_questions_column';
        headerCell.style.textAlign = 'right';
        tableHeader.rows[this.newExactUI ? 1 : 2].appendChild(headerCell);
        headerCell.innerText = await trans('open_questions.title');

        let savedSplitRow = null; // row with Gesplitst column
        let splittedRows = 0;

        for (let i = 0; i < tableBody.rows.length; i++) {
            let row = tableBody.rows[i];

            if (i === 0 || row.cells.length < this.selectors.iframe.cfl_statements_table.minimum_amount_columns) {
                continue;
            }

            if (!classesToIgnore.includes(row.className)) {
                let newCell = document.createElement('td');
                newCell.style.textAlign = 'right';
                newCell.style.borderBottom = '0';
                row.appendChild(newCell);

                // if we find "Gesplitst column" we save it to use its columns, and we set how many splitted rows are
                if (this.isRowWithSplitColumn(row)) {
                    splittedRows = row.cells[0].rowSpan - 1; // the first one is the current one, so we remove it
                    savedSplitRow = row;
                    continue;
                }

                // default row found, reset "Gesplitst column"
                if (splittedRows === 0) {
                    savedSplitRow = null;
                    this.handleDefaultRow(newCell, row);
                } else {
                    this.handleSavedSplitRow(newCell, savedSplitRow, row);
                    splittedRows--;
                }
            }
        }
    }

    handleDefaultRow(newCell, row) {
        let payment_reference = row.cells[this.attributesIndexes.payment_reference] ? row.cells[this.attributesIndexes.payment_reference].innerText : null;
        let name = this.getCellTextContent(this.attributesIndexes.description, row);
        let amount = this.getCellTextContent(this.attributesIndexes.amount, row);
        let currency = this.getCellTextContent(this.attributesIndexes.currency, row, 'EUR');
        let bookkeepingNumber = this.getCellTextContent(this.attributesIndexes.bookkeeping_number, row);
        let transactionDate = this.getCellTextContent(this.attributesIndexes.transaction_date, row);

        if (name && amount && bookkeepingNumber && transactionDate) {
            let data = this.handleNewQuestionButtonData(
                name,
                amount,
                transactionDate,
                currency,
                bookkeepingNumber,
                payment_reference,
            );

            this.addNewQuestionButton(newCell, data);
        }
    }

    handleSavedSplitRow(newCell, savedSplitRow, row) {
        // check which selectors to use
        let splitRowSelector = this.selectors.iframe.cfl_statements_table.gesplitst_row_type_2;
        if (row.querySelector('td:first-child').getAttribute('rowspan')) {
            splitRowSelector = this.selectors.iframe.cfl_statements_table.gesplitst_row_type_1;
        }
        if (row.querySelector('td:first-child').getAttribute('rowspan') && row.querySelector('td:first-child').getAttribute('colspan')) {
            if (row.querySelectorAll('td').length === 14) {
                splitRowSelector = this.selectors.iframe.cfl_statements_table.gesplitst_row_type_3;
            }
        }

        let savedRowCount = this.countColomns(savedSplitRow);
        let currentRowCount = this.countColomns(row);

        let payment_reference = '';
        let name = '';
        let amount = '';
        let currency = '';
        let index = savedRowCount - currentRowCount;
        for (let i = 0; i < row.cells.length; i++) {
            if (this.attributesIndexes.payment_reference === index) {
                payment_reference = row.cells[i].textContent || null;
            }

            if (this.attributesIndexes.description === index) {
                name = row.cells[i].textContent || null;
            }

            if (this.attributesIndexes.amount === index) {
                amount = row.cells[i].textContent || null;
            }

            if (this.attributesIndexes.currency === index) {
                currency = row.cells[i].textContent || null;
            }

            if (row.cells[i].colSpan) {
                index += row.cells[i].colSpan;
            } else {
                index++;
            }
        }

        // The column for currency is not mandatory. If we can't find it the default value is EUR
        if (!currency) {
            currency = 'EUR';
        }
        let bookkeepingNumber = row.querySelector(splitRowSelector.bookkeeping_number) ? row.querySelector(splitRowSelector.bookkeeping_number).textContent : null;
        let transactionDate = savedSplitRow.cells[this.attributesIndexes.transaction_date] ? savedSplitRow.cells[this.attributesIndexes.transaction_date].textContent : null;

        let data = this.handleNewQuestionButtonData(
            name,
            amount,
            transactionDate,
            currency,
            bookkeepingNumber,
            payment_reference,
        );

        this.addNewQuestionButton(newCell, data);
    }

    isRowWithSplitColumn(row) {
        for (let i = 0; i < row.cells.length; i++) {
            if (row.cells[i].textContent === 'Gesplitst') {
                return true;
            }
        }

        return false;
    }

    handleNewQuestionButtonData(
        name,
        amount,
        transactionDate,
        currency,
        bookkeepingNumber,
        description,
    ) {
        return {
            'service_name': 'exact_open_questions',
            'name': name,
            'amount': amount,
            'transaction_date': transactionDate,
            'currency': currency,
            'invoice_number': '',
            'bookkeeping_number': bookkeepingNumber,
            'description': description,
            'category': 'bookkeeping',
            'page_title': this.getPageTitle(),
            'page_url': window.location.href,
        };
    }

    addNewQuestionButton(newCell, data) {
        let currentQuestion = null;
        if (this.questions) {
            currentQuestion = this.questions.find((question) => {
                return question.name === data.name &&
                    question.amount === data.amount &&
                    question.transaction_date === data.transaction_date &&
                    question.bookkeeping_number === data.bookkeeping_number &&
                    question.status !== 'deleted';
            });
        }

        if (currentQuestion) {
            return new NewStatusQuestion(
                newCell,
                currentQuestion,
                'bookkeeping',
                this.demo,
                this.types,
                currentQuestion.status,
                currentQuestion.status_text,
            );
        }

        return new NewStatusQuestion(
            newCell,
            data,
            'bookkeeping',
            this.demo,
            this.types,
        );
    }

    countColomns(row) {
        let colomnCount = 0;

        for (let i = 0; i < row.cells.length; i++) {
            if (row.cells[i].colSpan > 0) {
                colomnCount += row.cells[i].colSpan;
            } else {
                colomnCount++;
            }
        }

        return colomnCount;
    }
}

export {CflStatementsTable};