import {OpenQuestionsCommunicationService} from "../OpenQuestionsCommunicationService";
import {NewStatusQuestion} from "../buttons/NewStatusQuestion";
import {trans} from "../../../helpers/TranslationHelper";
import {BasePage} from "./BasePage";
import {LogSender} from "../../../helpers/LogSender";

/**
 * Pages using this:
 * - Verkoop -> Openstaande posten -> Overzicht
 */
class SaleOutstandingItemsTable extends BasePage {
    constructor(table, selectors, questions, types, demo) {
        LogSender.debug('Exact/SaleOutstandingItemsTable constructor called');
        super();

        this.selectors = selectors;
        this.questions = questions;
        this.lang = '';
        this.types = types;
        this.demo = demo;
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();

        this.attributesIndexes = {
            description: -1,
            amount: -1,
            bookkeeping_number: -1,
            transaction_date: -1,
            invoice_number: -1,
            currency: -1
        };

        if (this.reportIsValid(table)) {
            this.getHeaderIndexes(table)
            this.appendColumn(table);
        }
    }

    reportIsValid(table) {
        let tableHeader = table.tHead;
        if (tableHeader) {
            let headerCells = tableHeader.rows[0].cells;
            let requiredColumnsNl = this.selectors.iframe.sale_outstanding_items_table.required_columns_nl;
            let requiredColumnsEn = this.selectors.iframe.sale_outstanding_items_table.required_columns_en;

            if (requiredColumnsNl.every((column) => {
                return Object.values(headerCells).some((cell) => {
                    return column === cell.textContent.trim();
                });
            })) {
                this.lang = 'nl';
                return true;
            } else if (requiredColumnsEn.every((column) => {
                return Object.values(headerCells).some((cell) => {
                    return column === cell.textContent.trim();
                });
            })) {
                this.lang = 'en';
                return true;
            }
        }
        return false;
    }

    getHeaderIndexes(table) {
        let tableHeader = table.tHead;
        if (tableHeader) {
            let headerCells = tableHeader.rows[0].cells;
            let headers = [];
            if (this.lang === 'nl') {
                headers = this.selectors.iframe.sale_outstanding_items_table.headers_nl;
            } else if (this.lang === 'en') {
                headers = this.selectors.iframe.sale_outstanding_items_table.headers_en;
            }
            for (const [key, value] of Object.entries(headers)) {
                for (let i = 0; i < headerCells.length; i++) {
                    if (headerCells[i].textContent.trim() === value) {
                        this.attributesIndexes[key] = i;
                        break;
                    }
                }
            }
        }
    }

    async appendColumn(table) {
        LogSender.debug('Exact/SaleOutStandingItemsTable appendColumn called');
        let classesToIgnore = this.selectors.iframe.sale_outstanding_items_table.classes_to_ignore;
        let attributesClass = this.selectors.iframe.sale_outstanding_items_table.attributes_class;
        let tableHeader = table.tHead;
        let tableBody = table.tBodies[0];

        let headerCell = document.createElement("th");
        headerCell.id = 'open_questions_column';
        headerCell.style.textAlign = 'right';
        tableHeader.rows[0].appendChild(headerCell);
        headerCell.innerText = await trans('open_questions.title');

        let nameRelationOrder = this.selectorHelper.helpSelector(
            this.selectors.iframe.sale_outstanding_items_table.name_relation_sort
        );
        let nameBeforeRelation = nameRelationOrder && nameRelationOrder.parentElement.className === 'tabSelected';

        let questionName = '';
        for (let i = 0; i < tableBody.rows.length; i++) {
            let row = tableBody.rows[i];

            if (!classesToIgnore.includes(row.className)) {
                let questionNameSelector = this.selectorHelper.helpSelector(
                    this.selectors.iframe.sale_outstanding_items_table.name,
                    null,
                    [i + 1]
                )
                if (questionNameSelector && row.className !== attributesClass) {
                    questionName = this.handleQuestionName(nameBeforeRelation, questionNameSelector);
                    continue;
                }

                let newCell = document.createElement("td");
                newCell.style.textAlign = 'right';
                row.appendChild(newCell);

                let description = this.getCellTextContent(this.attributesIndexes.description, row);
                let amount = this.getCellTextContent(this.attributesIndexes.amount, row);
                let invoiceNumber = this.getCellTextContent(this.attributesIndexes.invoice_number, row);
                let currency = this.getCellTextContent(this.attributesIndexes.currency, row, 'EUR');
                let bookkeepingNumber = this.getCellTextContent(this.attributesIndexes.bookkeeping_number, row);
                let transactionDate = this.getCellTextContent(this.attributesIndexes.transaction_date, row);

                let data = {
                    'service_name': 'exact_open_questions',
                    'name': questionName,
                    'amount': amount,
                    'transaction_date': transactionDate,
                    'currency': currency,
                    'invoice_number': invoiceNumber,
                    'bookkeeping_number': bookkeepingNumber,
                    'description': description,
                    'category': 'bookkeeping',
                    'page_title': this.getPageTitle(),
                    'page_url': window.location.href
                };

                let currentQuestion = null;
                if (this.questions) {
                    currentQuestion = this.questions.find((question) => {
                        return question.name === questionName.trim() &&
                            question.amount === amount.trim() &&
                            question.transaction_date === transactionDate.trim() &&
                            question.invoice_number === invoiceNumber.trim() &&
                            question.bookkeeping_number === bookkeepingNumber.trim() &&
                            question.status !== 'deleted'
                    })
                }

                if (currentQuestion) {
                    let statusQuestionButton = new NewStatusQuestion(
                        newCell,
                        currentQuestion,
                        'bookkeeping',
                        this.demo,
                        this.types,
                        currentQuestion.status,
                        currentQuestion.status_text
                    );
                } else {
                    let statusQuestionButton = new NewStatusQuestion(
                        newCell,
                        data,
                        'bookkeeping',
                        this.demo,
                        this.types
                    );
                }
            }
        }
    }
}

export {SaleOutstandingItemsTable}