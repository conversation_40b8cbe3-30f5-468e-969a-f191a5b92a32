import globalCommunicationListener from "../../GlobalCommunicationListener";
import browser from "webextension-polyfill";
import _ from "lodash";
import {LogHelper} from "../../helpers/LogHelper";

const TRANSLATION_URL_PREFIX = '/api/v1/browser_extension/language/';

class TranslationModule {

    constructor() {
        globalCommunicationListener.addListener('translate', this.translate.bind(this));
        this.language = 'nl';
    }

    setLanguage(lang) {
        this.language = lang;
    }

    async translate(message) {
        let data = message.data;
        let translations = (await browser.storage.local.get('translations')).translations[this.language];
        if (!translations) {
            LogHelper.debug('TranslationModule needs to request language data');
            translations = (await browser.storage.local.get('translations')).translations[this.language];

            // if unable to get the language now, we just cannot translate.
            if (!translations) {
                return data.key;
            }
        }

        let args = data.key.split('.');

        let result = translations;
        _.forEach(args, (arg) => {
            if (!result[arg]) {
                result = data.key;
                return false;
            }
            result = result[arg];
        })

        if (data.replace) {
            result = this.replace(result, data.replace);
        }
        return result;
    }

    replace(translation, replace) {
        _.forEach(replace, (value, key) => {
            translation = translation.replaceAll('{{' + key + '}}', value);
        });
        return translation;
    }

    /**
     * Update translations in local storage for a specific language.
     *
     * @returns {Promise<void>}
     */
     async loadTranslations() {
        LogHelper.debug('TranslationModule called loadTranslations(' + this.language + ')');
        let existingTranslations= await browser.storage.local.get('translations');
        browser.storage.local.get('host/hostname/domain').then((result) => {
            const options = {
                method: "GET",
            };
            let url = 'https://' + result['host/hostname/domain'] + TRANSLATION_URL_PREFIX + this.language + '.json';
            fetch(url, options)
                .then((response) => response.json())
                .then((data) => {
                    let updatedTranslations = existingTranslations
                    updatedTranslations[this.language] = data;
                    browser.storage.local.set({'translations': updatedTranslations});
                });
        });
    }
}

Object.freeze(TranslationModule);
export default TranslationModule;