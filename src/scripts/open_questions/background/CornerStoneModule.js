import browser from "webextension-polyfill";
import globalCommunicationListener from "../../GlobalCommunicationListener";
import {<PERSON>b<PERSON><PERSON><PERSON>} from "../../helpers/TabHelper";
import {LogHelper} from "../../helpers/LogHelper";
import {CompanyApi} from "../api/CompanyApi";

class CornerStoneModule {
    constructor() {
        LogHelper.debug('CornerStoneModule constructor called');
        globalCommunicationListener.addListener('questions_updated', this.onQuestionsChanged.bind(this));
        globalCommunicationListener.addListener('hide_cornerstone', this.hide.bind(this));
        globalCommunicationListener.addListener('hide_cornerstone_lock', this.hideLockButton.bind(this));
    }

    /**
     * Event when questions are created, updated or deleted.
     * @param data
     */
    async onQuestionsChanged(data) {
        LogHelper.debug('CornerStoneModule detected question changed')
        await this.launch(data.data)
    }

    /**
     * Launch the cornerstone, if it is not started, it will create it, otherwise just updates it
     */
    async launch(data, response) {

        if (typeof response === 'undefined') {
            try {
                LogHelper.info('CornerStoneModule fetching company information', data);
                const hostname =  (await browser.storage.local.get('host/hostname/domain'))['host/hostname/domain'];
                LogHelper.debug('Using hostname ' + hostname);
                response = await CompanyApi.getCompanyInfo(hostname, data.companyId, data.companyCode, data.companyName, data.serviceName);
                LogHelper.debug('CornerStoneModule received company information', response);
            } catch(e) {
                LogHelper.error('CornerStoneModule launch encountered error');
                LogHelper.error(e);
            }
        }

        const currentTab = await TabHelper.getCurrentTab();

        let responseData = '';
        if (response && response.data && response.status === 200) {
            responseData = response.data;
        } else {
            LogHelper.debug('CornerStoneModule received unexpected response', response);
        }

        try {
            LogHelper.debug('CornerStoneModule executing dispatchLaunchCornerStone, tab ID: ' + currentTab.id);
            let injectionResult = await browser.scripting.executeScript(
                {
                    target: {tabId: currentTab.id},
                    func: dispatchLaunchCornerStone,
                    args: [data, responseData]
                });
            if (injectionResult[0] && injectionResult[0].frameId === 0) {
                console.error('CornerStoneModule failed to inject script', injectionResult, data, responseData);
            }
        } catch (e) {
            LogHelper.error('CornerStoneModule failed to execute script', e, data, responseData);
        }
    }

    /**
     * Hide cornerstone
     * @returns {Promise<void>}
     */
    async hide() {
        await browser.scripting.executeScript({
            target: {tabId: (await TabHelper.getCurrentTab()).id},
            func: dispatchHideCornerStone
        })
    }

    /**
     * Hide cornerstone lock button
     * @returns {Promise<void>}
     */
    async hideLockButton() {
        await browser.scripting.executeScript({
            target: {tabId: (await TabHelper.getCurrentTab()).id},
            func: dispatchHideCornerStoneLockButton
        })
    }
}

export default CornerStoneModule;

function dispatchLaunchCornerStone(data, responseData) {
    console.debug('CornerStoneModule called dispatchLaunchCornerStone()');
    let event = new CustomEvent("launchCornerStone", {
        "detail": {
            data: data,
            responseData: responseData
        }
    });
    console.debug('CornerStoneModule dispatching event launchCornerStone');
    document.dispatchEvent(event);
}

function dispatchHideCornerStone() {
    console.debug('CornerStoneModule dispatching event hideCornerStone');
    document.dispatchEvent(new CustomEvent("hideCornerStone"));
}

function dispatchHideCornerStoneLockButton() {
    console.debug('CornerStoneModule dispatching event hideCornerStoneLockButton');
    document.dispatchEvent(new CustomEvent("hideCornerStoneLockButton"));
}