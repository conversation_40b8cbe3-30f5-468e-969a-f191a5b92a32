import browser from 'webextension-polyfill';
import {ServiceModule} from "./ServiceModule";

class MoneybirdModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        this.selectors = this.settings.application.selectors;
        this.apiUrls = this.settings.application.apiUrls;
        this.urlTracker = {};
        console.log('Moneybird module constructor called');
    }

    async navigationListener(details) {
        if (details.frameId === 0 && this.demo !== true) {
            await browser.scripting.executeScript({
                target: {tabId: details.tabId},
                files: ['/dist/openquestions-client.js']
            });
            this.handleRequestedPageStart();
        }
    }

    /**
     * Handle each ajax call from user for the desired page
     */
    handleRequestedPageStart() {
        if (this.isRequestedPageDataSet) {
            return;
        }

        this.isRequestedPageDataSet = true;

        browser.webNavigation.onHistoryStateUpdated.addListener(async (details) => {
            if (!this.active) return;
            const tabId = details.tabId;
            const previousUrl = this.urlTracker[tabId] || null;
            const currentUrl = details.url;

            if (previousUrl !== currentUrl) {
                if (previousUrl !== null) {
                    const previousAdministration = URL.parse(previousUrl).pathname.split('/')[1];
                    const currentAdministration =  URL.parse(currentUrl).pathname.split('/')[1];
                    if (previousAdministration !== currentAdministration  && (await browser.storage.local.get('can-use-open-questions'))['can-use-open-questions'] !== null) {
                        await browser.scripting.executeScript({
                            target: {tabId: details.tabId},
                            files: ['/dist/openquestions-moneybird.js']
                        });
                        await browser.scripting.executeScript({
                            target: {tabId: details.tabId},
                            func: dispatchStart,
                            args: [this.demo, this.selectors]
                        });
                    }
                }
                this.urlTracker[tabId] = currentUrl; // Update the URL tracker
            }
        }, {
            url: [{ hostContains: "moneybird.com" }]
        });

        // wait for each api url and then setData for injected js start
        browser.webRequest.onCompleted.addListener(
            async (details) => {
                if (!this.active) return;
                if ((details.method === 'GET')
                    && (await browser.storage.local.get('can-use-open-questions'))['can-use-open-questions'] !== null
                ) {
                    await browser.scripting.executeScript({
                        target: {tabId: details.tabId},
                        files: ['/dist/openquestions-moneybird.js']
                    });
                    await browser.scripting.executeScript({
                        target: {tabId: details.tabId},
                        func: dispatchStart,
                        args: [this.demo, this.selectors]
                    });
                    await browser.scripting.executeScript({
                        target: {tabId: details.tabId},
                        func: dispatchStartRequestedPage,
                        args: [this.demo, this.selectors]
                    });
                }
            },
            {
                urls: this.apiUrls,
                types: ['xmlhttprequest', 'main_frame']
            },
        );
    }
}

export {MoneybirdModule}

function dispatchStart(demo, selectors) {
    let startEvent = new CustomEvent("start", {
        "detail": {
            demo: demo,
            selectors: selectors
        }
    });
    document.dispatchEvent(startEvent);
}

function dispatchStartRequestedPage(demo, selectors) {
    let startEvent = new CustomEvent("startRequestedPage", {
        "detail": {
            demo: demo,
            selectors: selectors
        }
    });
    // This timeout is needed because sometimes it takes a little longer to have the selected page in moneybird
    setTimeout(() => {
        document.dispatchEvent(startEvent);
    }, 1000);
}