import browser from 'webextension-polyfill';
import {ServiceModule} from "./ServiceModule";

class BaseconeModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        this.selectors = this.settings.application.selectors;
        this.apiUrls = this.settings.application.apiUrls;

        console.log('Basecone module constructor called with demo mode: ' + this.demo);
    }

    async navigationListener(details) {
        if (details.frameId === 0 && this.demo !== true) {
            await browser.scripting.executeScript({
                target: {tabId: details.tabId},
                files: ['/dist/openquestions-client.js']
            });
            await browser.scripting.executeScript({
                target: {tabId: details.tabId},
                files: ['/dist/openquestions-basecone.js']
            });
            this.handlePageStart();
        }
    }

    handlePageStart() {
        if (this.isPageStarted) {
            return;
        }

        this.isPageStarted = true;

        browser.webRequest.onBeforeSendHeaders.addListener(
            async (details) => {
                if (
                    !details.requestHeaders.find(({name}) => name === 'hix_extension') // don't listen for our request to get data
                    && (await browser.storage.local.get('can-use-open-questions'))['can-use-open-questions'] !== null
                ) {
                    const result = (await fetch(details.url, {headers: this.handleHeaders(details.requestHeaders)}));
                    const apiData = (await result.json());
                    if (this.isAdministration(details.url)) {
                        await browser.scripting.executeScript({
                            target: {tabId: details.tabId},
                            func: dispatchStart,
                            args: [
                                this.demo,
                                apiData,
                                this.selectors
                            ]
                        });
                    } else if(details.url.includes('taggable-documents')) {
                        await browser.scripting.executeScript({
                            target: {tabId: details.tabId},
                            func: dispatchHandlePage,
                            args: [
                                this.demo,
                                this.selectors,
                                apiData,
                                this.getAuthorizationHeader(details.requestHeaders)
                            ]
                        });
                    }
                }

            },
            {
                urls: this.apiUrls
            },
            ['requestHeaders']
        );
    }

    /**
     * Check if url is this one (split because the taggable-documents api url filter was also giving true)
     * Example: https://public-apigateway.basecone.com/base-data/companies/bc19974b-5182-4e1f-b14e-8881ec9a28b7
     * @param url
     * @returns {boolean}
     */
    isAdministration(url) {
        url = url.split('/');
        return url.length === 6 && !url[url.length-1].includes('info');
    }

    /**
     * Get Authorization header
     * @param requestHeaders
     */
    getAuthorizationHeader(requestHeaders) {
        let header = requestHeaders.find((header) => header.name === 'Authorization');
        if (header) {
            return header.value.split(' ')[1]; // remove bearer
        }

        return null;
    }

    /**
     * Copy basecone request headers and add our own
     * @param requestHeaders
     * @returns {{hix_extension: boolean}}
     */
    handleHeaders(requestHeaders) {
        let headers = {};
        for (let header of requestHeaders) {
            headers[header.name] = header.value;
        }

        return {
            ...headers,
            ...{
                hix_extension: true
            },
        };
    }

    /**
     * Get document information from Basecone
     * @returns {Promise<AxiosResponse<any>>}
     * @param document
     */
    async getServiceOptions(document) {
        return (await browser.openQuestionsApi.axios()).get('/new/open_questions/basecone/document', {
            params: {
                documentId: document.data.documentId,
                accessToken: document.data.accessToken
            }
        });
    }
}

export {BaseconeModule}

function dispatchStart(demo, apiData, selectors) {
    let startEvent = new CustomEvent("baseconeStartCornerstone", {
        "detail": {
            demo: demo,
            apiData: apiData,
            selectors: selectors
        }
    });
    document.dispatchEvent(startEvent);
}

function dispatchHandlePage(demo, selectors, apiData, accessToken) {
    let startEvent = new CustomEvent("baseconeStartPage", {
        "detail": {
            demo: demo,
            selectors: selectors,
            apiData: apiData,
            accessToken: accessToken
        }
    });
    document.dispatchEvent(startEvent);
}