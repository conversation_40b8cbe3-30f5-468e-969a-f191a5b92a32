import browser from "webextension-polyfill";
import {ServiceModule} from "./ServiceModule";

class SnelstartModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        this.selectors = this.settings.application.selectors;
        this.loginUrl = this.settings.application.loginUrl;
        this.apiUrls = this.settings.application.apiUrls;
        this.allowedUrls = this.settings.application.allowedUrls;
        this.notAllowedUrls = this.settings.application.notAllowedUrls;
        console.log('Snelstart module constructor called with demo mode: ' + this.demo);
    }

    async navigationListener(details) {
        await browser.scripting.executeScript(
            {
                target: {tabId: details.tabId},
                files: ['/dist/openquestions-client.js']
            });
        await browser.scripting.executeScript(
            {
                target: {tabId: details.tabId},
                files: ['/dist/openquestions-snelstart.js']
            });
        // Demo mode does not work for this service, due to the csp not allowing to load images.
        if (details.frameId === 0 && this.demo !== true) {
            this.handleRequestedPageStart();
        }
    }

    /**
     * Handle each ajax call from user for the desired page
     */
    handleRequestedPageStart() {
        if (this.isRequestedPageDataSet) {
            return;
        }

        this.isRequestedPageDataSet = true;

        // wait for each api url and then setData for injected js start
        browser.webRequest.onCompleted.addListener(
            async (details) => {
                for (let apiUrl of this.apiUrls) {
                    if (apiUrl.includes(this.removeQueryParameters(details.url))
                        && (details.method === 'GET' || details.method === 'POST')
                        && (await browser.storage.local.get('can-use-open-questions'))['can-use-open-questions'] !== null
                    ) {
                        await browser.scripting.executeScript(
                            {
                                target: {tabId: details.tabId},
                                func: dispatchStart,
                                args: [this.demo, this.selectors, this.notAllowedUrls]
                            }
                        );

                        await browser.scripting.executeScript(
                            {
                                target: {tabId: details.tabId},
                                func: dispatchStartRequestedPage,
                                args: [this.demo, this.selectors, this.allowedUrls]
                            }
                        );
                    }
                }
            },
            {
                urls: this.apiUrls,
                types: ['xmlhttprequest', 'main_frame']
            },
        );
    }

    /**
     * Remove query parameters from url
     * @param url
     * @returns {string}
     */
    removeQueryParameters(url) {
        let urlObject = new URL(url);
        return urlObject.origin + urlObject.pathname; // remove query parameters
    }
}

export {SnelstartModule}

function dispatchStart(demo, selectors, notAllowedUrls) {
    let startEvent = new CustomEvent("start", {
        "detail": {
            demo: demo,
            selectors: selectors,
            notAllowedUrls: notAllowedUrls
        }
    });
    document.dispatchEvent(startEvent);
}

function dispatchStartRequestedPage(demo, selectors, allowedUrls) {
    let startEvent = new CustomEvent("startRequestedPage", {
        "detail": {
            demo: demo,
            selectors: selectors,
            allowedUrls: allowedUrls
        }
    });
    document.dispatchEvent(startEvent);
}