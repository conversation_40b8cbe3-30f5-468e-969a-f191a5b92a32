import browser from 'webextension-polyfill';
import {ServiceModule} from "./ServiceModule";

class FiscaalGemakModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        console.log('Fiscaal gemak module constructor called');
    }

    async navigationListener(details) {
        // Demo mode does not work for this service, due to the csp not allowing to load images.
        if (details.frameId === 0 && this.demo !== true) {
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-client.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-fiscaal-gemak.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    func: dispatchStart,
                    args: [this.demo, this.settings.application]
                }
            );
        }
    }
}

export {FiscaalGemakModule}

function dispatchStart(demo, settings) {
    let startEvent = new CustomEvent("start", {
        "detail": {
            demo: demo,
            settings: settings
        }
    });
    document.dispatchEvent(startEvent);
}