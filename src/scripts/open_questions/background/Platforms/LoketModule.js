import browser from 'webextension-polyfill';
import {ServiceModule} from "./ServiceModule";

class LoketModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        console.log('Loket module constructor called');
    }

    async navigationListener(details) {
        // Demo mode does not work for this service, due to the csp not allowing to load images.
        if (details.frameId === 0 && this.demo !== true) {
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-client.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-loket.js']
                });
            this.handleRequestedPageStart();
        }
    }

    /**
     * Handle each ajax call from user for the desired page
     */
    handleRequestedPageStart() {
        if (this.isRequestedPageDataSet) {
            return;
        }

        this.isRequestedPageDataSet = true;

        // wait for each api url and then setData for injected js start
        browser.webRequest.onCompleted.addListener(
            async (details) => {
                if (details.method === 'GET' && (await browser.storage.local.get('can-use-open-questions'))['can-use-open-questions'] !== null) {
                    await browser.scripting.executeScript(
                        {
                            target: {tabId: details.tabId},
                            func: dispatchStart,
                            args: [this.demo, this.settings.application.selectors, this.settings.application.notAllowedUrls]
                        }
                    );
                }
            },
            {
                urls: this.settings.application.apiUrls,
                types: ['xmlhttprequest', 'main_frame']
            },
        );
    }
}

export {LoketModule}

function dispatchStart(demo, selectors, notAllowedUrls) {
    let startEvent = new CustomEvent("start", {
        "detail": {
            demo: demo,
            selectors: selectors,
            notAllowedUrls: notAllowedUrls
        }
    });
    document.dispatchEvent(startEvent);
}