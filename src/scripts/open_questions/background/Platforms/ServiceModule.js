import browser from "webextension-polyfill";

class ServiceModule {
    constructor(settings) {
        this.demo = false;
        this.active = true;
        if(settings.hasOwnProperty('mode')) {
            this.demo = settings.mode === 'demo';
        }
        this.settings = settings;
        this.navigationListeners = this.navigationListener.bind(this);
    }

    onLoginChange(active) {
        this.active = active;
        if (this.active === false) {
            this.removeNavigatorListeners();
        }
    }

    /**
     * Only listen to URLs defined in the urlFilter property. 
     * URLs are matched using an UrlFilter list 
     * @link https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/API/events/UrlFilter
     */ 
    registerNavigatorListeners() {
        if (this.settings.application.hasOwnProperty('urlFilter')) {
            browser.webNavigation.onDOMContentLoaded.addListener(this.navigationListeners, {url: this.settings.application.urlFilter});
        } else {
            console.warn('No urlFilter property found in application settings');
        }
    }

    removeNavigatorListeners() {
        browser.webNavigation.onDOMContentLoaded.removeListener(this.navigationListeners);
    }

    async navigationListener(details) {}
}

export {ServiceModule}