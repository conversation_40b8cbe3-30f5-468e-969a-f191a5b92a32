import browser from 'webextension-polyfill';
import {ServiceModule} from "./ServiceModule";

class NmbrsModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        console.log('Nmbrs module constructor called');
    }

    async navigationListener(details) {
        if (details.frameId === 0) {
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-client.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-nmbrs.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    func: dispatchStart,
                    args: [this.demo, this.settings.application.selectors]
                });
        }
    }
}

export {NmbrsModule}

function dispatchStart(demo, selectors) {
    let startEvent = new CustomEvent("start", {
        "detail": {
            demo: demo,
            selectors: selectors
        }
    });
    document.dispatchEvent(startEvent);
}