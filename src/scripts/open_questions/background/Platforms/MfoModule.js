import browser from 'webextension-polyfill';
import {ServiceModule} from "./ServiceModule";

class MfoModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        this.selectors = this.settings.application.selectors;
        this.apiUrls = this.settings.application.apiUrls;
        console.log('MFO module constructor called');
    }

    getReferenceName() {
        return 'mfo_open_questions';
    }

    async navigationListener(details) {
        if (details.frameId === 0) {
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-client.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-mfo.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    func: dispatchStart,
                    args: [this.demo, this.selectors]
                });
        }
    }
}

export {MfoModule}

function dispatchStart(demo, selectors) {
    let startEvent = new CustomEvent("start", {
        "detail": {
            demo: demo,
            selectors: selectors
        }
    });
    document.dispatchEvent(startEvent);
}
