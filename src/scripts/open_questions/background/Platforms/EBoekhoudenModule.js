import browser from 'webextension-polyfill';
import {ServiceModule} from "./ServiceModule";

class EBoekhoudenModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        this.selectors = this.settings.application.selectors;
        this.apiUrls = this.settings.application.apiUrls;
        this.notAllowedApiUrls = this.settings.application.notAllowedApiUrls;
        this.pages = this.settings.application.pages;
        console.log('e-Boekhouden module constructor called');
    }

    async navigationListener(details) {
        if (details.frameId === 0) {
            await browser.scripting.executeScript({
                target: {tabId: details.tabId},
                files: ['/dist/openquestions-client.js']
            });
            await browser.scripting.executeScript({
                target: {tabId: details.tabId},
                files: ['/dist/openquestions-eboekhouden.js']
            });
            await this.handlePageStart(details.tabId);
        }
    }

    /**
     * Handle each ajax call from user for the desired page
     */
    async handlePageStart() {
        if (this.isPageListener) {
            return;
        }

        this.isPageListener = true;

        browser.webRequest.onCompleted.addListener(
            async (details) => {
                if (details.method === 'GET'
                    && details.frameId !== 0 // all requests we do with fetch are from frameId 0, we dont need to listen for those (when using fetch() in browser ext it would also listen for our requests)
                    && (await browser.storage.local.get('can-use-open-questions'))['can-use-open-questions'] !== null
                ) {
                    await browser.scripting.executeScript(
                        {
                            target: {tabId: details.tabId},
                            func: dispatchStart,
                            args: [this.demo, this.selectors]
                        }
                    );

                    for (let index in this.pages) {
                        let page = this.pages[index];
                        if (page.name === this.getPageFromUrl(details.url)) {
                            const result = (await fetch(details.url));
                            const apiData = (await result.json());
                            await browser.scripting.executeScript(
                                {
                                    target: {tabId: details.tabId},
                                    func: dispatchStartRequestedPage,
                                    args: [this.demo, this.selectors, page, apiData]
                                }
                            );
                        }
                    }
                }
            },
            {
                urls: this.apiUrls,
                types: ['xmlhttprequest', 'main_frame']
            },
        );

        browser.webRequest.onCompleted.addListener(
            async (details) => {
                if (details.method === 'GET'
                    && details.frameId !== 0 // all requests we do with fetch are from frameId 0, we dont need to listen for those (when using fetch() it would also listen for our requests)
                    && (await browser.storage.local.get('can-use-open-questions'))['can-use-open-questions'] !== null
                ) {
                    await browser.scripting.executeScript(
                        {
                            target: {tabId: details.tabId},
                            func: dispatchRemoveButtons,
                            args: [this.demo]
                        }
                    );
                }
            },
            {
                urls: this.notAllowedApiUrls,
                types: ['xmlhttprequest', 'main_frame']
            },
        );
    }

    /**
     * Split url so we know which endpoint and page it is
     * @param url
     * @returns {*}
     */
    getPageFromUrl(url) {
        url = url.split('/');
        return url[5];
    }
}

export {EBoekhoudenModule}

function dispatchStart(demo, selectors) {
    let startEvent = new CustomEvent("startEBoekhouden", {
        "detail": {
            demo: demo,
            selectors: selectors
        }
    });
    document.dispatchEvent(startEvent);
}

function dispatchStartRequestedPage(demo, selectors, page, apiData) {
    let startEvent = new CustomEvent("startEBoekhoudenRequestedPage", {
        "detail": {
            demo: demo,
            selectors: selectors,
            page: page,
            apiData: apiData
        }
    });
    document.dispatchEvent(startEvent);
}

function dispatchRemoveButtons(demo) {
    let startEvent = new CustomEvent("removeEBoekhoudenButtons", {
        "detail": {
            demo: demo
        }
    });
    document.dispatchEvent(startEvent);
}