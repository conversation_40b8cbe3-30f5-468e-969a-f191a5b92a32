import browser from "webextension-polyfill";
import {ServiceModule} from "./ServiceModule";
import {LogHelper} from "../../../helpers/LogHelper";
class ExactModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        LogHelper.info('Exact module constructor called with demo mode: ' + this.demo);
    }

    async navigationListener(details) {
        LogHelper.info('ExactModule navigationListener called');
        if (details.frameId === 0) {
            try {
                await browser.scripting.executeScript(
                    {
                        target: {tabId: details.tabId},
                        files: ['/dist/openquestions-client.js']
                    });
            } catch (e) {
                LogHelper.error('ExactModule failed to execute script openquestions-client', e, details);
                throw e;
            }
        }

        try {
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-exact.js']
                });
        } catch (e) {
            LogHelper.error('ExactModule failed to execute script openquestions-exact', e, details);
            throw e;
        }

        try {
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    func: dispatchStart,
                    args: [this.demo, this.settings.application.selectors]
                });
        } catch (e) {
            LogHelper.error('ExactModule failed to execute script dispatchStart', e, details);
            throw e;
        }
    }
}

export {ExactModule}

function dispatchStart(demo, selectors) {
    let startEvent = new CustomEvent("start", {
        "detail": {
            demo: demo,
            selectors: selectors
        }
    });
    document.dispatchEvent(startEvent);
}