import browser from 'webextension-polyfill';
import {ServiceModule} from "./ServiceModule";
import CornerStoneModule from "../CornerStoneModule";
import globalCommunicationListener from "../../../GlobalCommunicationListener";
import {LogHelper} from "../../../helpers/LogHelper";

class TwinfieldModule extends ServiceModule {

    constructor(settings) {
        super(settings);
        LogHelper.info('Twinfield module constructor called');
        this.openQuestionsApi = browser.openQuestionsApi;
        this.isOnBeforeRequestListenerSet = false;
        this.subFrameId = null;
        this.cornerStoneModule = new CornerStoneModule();
        globalCommunicationListener.addListener('get_twinfield_administration', this.requestAndSetAdministration.bind(this));
    }

    async navigationListener(details) {
        LogHelper.info('TwinfieldModule navigationListener called', details);
        if (details.frameId === 0) {

            try {
                await browser.scripting.executeScript(
                    {
                        target: {tabId: details.tabId},
                        func: observeAdministration,
                        args: [details]
                    });
            } catch (e) {
                LogHelper.error('TwinfieldModule failed to execute script observeAdministration', e, details);
                throw e;
            }

            try {
                await browser.scripting.executeScript(
                    {
                        target: {tabId: details.tabId},
                        files: ['/dist/openquestions-client.js']
                    });
            } catch (e) {
                LogHelper.error('TwinfieldModule failed to execute script openquestions-client', e, details);
                throw e;
            }

            try {
                await browser.scripting.executeScript(
                    {
                        target: {tabId: details.tabId},
                        files: ['/dist/openquestions-twinfield-angular.js']
                    });
            } catch (e) {
                LogHelper.error('TwinfieldModule failed to execute script openquestions-twinfield-angular', e, details);
                throw e;
            }

            let hostname = (new URL(details.url)).hostname;
            await this.requestAndSetAdministration({data: {url: 'https://api.' + hostname + '/api'}})
            this.registerAngularListener();
            this.subFrameId = null;

            for (let [key, allowedUrl] of Object.entries(this.settings.application.allowed_pages)) {
                if (details.url.includes(allowedUrl) &&
                    (await browser.storage.local.get('can-use-open-questions'))['can-use-open-questions'] !== null)
                {
                    key = key.replace('_v2.0.0', '');
                    try {
                        await browser.scripting.executeScript(
                            {
                                target: {tabId: details.tabId},
                                func: dispatchStartAngularPage,
                                args: [this.demo, this.settings.application.selectors[key]]
                            });
                    } catch (e) {
                        LogHelper.error('TwinfieldModule failed to execute script dispatchStartAngularPage', e, details);
                        throw e;
                    }
                    break;
                }
            }

            if (details.url.includes('report=GLO')) {
                try {
                    await browser.scripting.executeScript(
                        {
                            target: {tabId: details.tabId},
                            files: ['/dist/openquestions-twinfield.js']
                        });
                } catch (e) {
                    LogHelper.error('TwinfieldModule failed to execute script openquestions-twinfield', e, details);
                    throw e;
                }
            }
        } else if (!this.subFrameId || details.parentFrameId === -1) {
            this.subFrameId = details.frameId;
            // Script needs to be injected on every page because for switching administrations we call setData to store data in the domain's local storage
            try {
                await browser.scripting.executeScript(
                    {
                        target: {tabId: details.tabId},
                        files: ['/dist/openquestions-twinfield.js']
                    });
            } catch (e) {
                LogHelper.error('TwinfieldModule failed to execute script openquestions-twinfield', e, details);
                throw e;
            }
        }

        for (let i in this.settings.application.reportUrls) {
            if (details.url.match(this.settings.application.reportUrls[i]) !== null) {
                // only call start method for URLs that match a report URL regex
                try {
                    await browser.scripting.executeScript(
                        {
                            target: {tabId: details.tabId},
                            func: dispatchStart,
                            args: [this.demo, this.settings]
                        }
                    );
                } catch (e) {
                    LogHelper.error('TwinfieldModule failed to execute script dispatchStart', e, details);
                    throw e;
                }
                break;
            }
        }
    }

    registerAngularListener() {
        if (this.isRequestedPageDataSet) {
            return;
        }

        browser.tabs.onUpdated.addListener(
            async (tabId, changeInfo, tab) => {
                for (let [key, allowedUrl] of Object.entries(this.settings.application.allowed_pages)) {
                    if (changeInfo.status === 'complete' &&
                        tab.url.includes(allowedUrl) &&
                        (await browser.storage.local.get('can-use-open-questions'))['can-use-open-questions'] !== null)
                    {
                        key = key.replace('_v2.0.0', '');

                        await browser.scripting.executeScript(
                            {
                                target: {tabId: tabId},
                                func: dispatchStartAngularPage,
                                args: [this.demo, this.settings.application.selectors[key]]
                            });
                        this.isRequestedPageDataSet = true;
                        break;
                    }
                }
            });
    }

    /**
     * Make request to Twinfield API to fetch administration/company info.
     * Make request to Hix to find matching company.
     * Update company info in local storage and update corner stone
     */
    async requestAndSetAdministration(message) {
        const result = (await fetch(message.data.url));
        const data = (await result.json());

        if (!data.companyId || !data.companyCode || !data.companyCode) {
            LogHelper.error('TwinfieldModule unable to set administration with incomplete data ', data);
            return;
        }

        let response = await browser.openQuestionsApi.getCompanyInformation(
            data.companyId,
            data.companyCode,
            data.companyName,
            'twinfield_open_questions'
        );

        if (response && response.data && response.data.company_id) {
            LogHelper.debug('TwinfieldModule received company info', response.data);
            await browser.storage.local.set({'twinfield_open_questions_company_id': response.data.company_id});

            if (!this.demo) {
                LogHelper.debug('TwinfieldModule is launching CornerStoneModule...', data);
                await this.cornerStoneModule.launch({
                    'companyId': data.companyId,
                    'companyCode': data.companyCode,
                    'companyName': data.companyName.replace(/'/g, "\\'"),
                    'serviceName': 'twinfield_open_questions'
                });
            }

        } else {
            LogHelper.debug('TwinfieldModule removing company ID from browser storage');
            await browser.storage.local.remove('twinfield_open_questions_company_id');
        }

        await browser.storage.local.set({['twinfield_open_questions_external_company_id']: data.companyId});
        await browser.storage.local.set({['twinfield_open_questions_external_company_code']: data.companyCode});
        await browser.storage.local.set({['twinfield_open_questions_external_company_name']: data.companyName.replace(/'/g, "\\'")});
    }
}

export {TwinfieldModule}

function dispatchStart(demo, settings) {
    let startEvent = new CustomEvent("start", {
        "detail": {
            demo: demo,
            settings: settings
        }
    });
    document.dispatchEvent(startEvent);
}

function dispatchStartAngularPage(demo, selectors) {
    let startAngularPageEvent = new CustomEvent("startAngularPageEvent", {
        "detail": {
            demo: demo,
            selectors: selectors
        }
    });
    document.dispatchEvent(startAngularPageEvent);
}

function observeAdministration(details) {
    document.addEventListener('click', async (e) => {
        if (e.target.id.includes('ui-id-') || window.location.hash === '#/Companies/All') {
            let event = new CustomEvent("setTwinfieldAdministration", {
                "detail": {
                    url: 'https://api.' + window.location.host + '/api'
                }
            });
            setTimeout(() => {
                document.dispatchEvent(event)
            }, 1000);
        }
    });
}