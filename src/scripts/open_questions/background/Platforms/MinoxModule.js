import {ServiceModule} from "./ServiceModule";
import browser from "webextension-polyfill";

class MinoxModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        console.log('Minox module constructor called');
    }

    async navigationListener(details) {
        if (details.frameId === 0) {
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-client.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-minox.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    func: dispatchStart,
                    args: [this.demo, this.settings.application]
                }
            );
        }
    }
}

export {MinoxModule}

function dispatchStart(demo, settings) {
    let startEvent = new CustomEvent("start", {
        "detail": {
            demo: demo,
            settings: settings
        }
    });
    document.dispatchEvent(startEvent);
}