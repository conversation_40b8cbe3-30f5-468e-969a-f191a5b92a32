import browser from 'webextension-polyfill';
import {ServiceModule} from "./ServiceModule";

class XeroModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        this.selectors = this.settings.application.selectors;
        this.apiUrls = this.settings.application.apiUrls;
        console.log('Xero module constructor called');
    }

    async navigationListener(details) {
        if (details.frameId === 0) {
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-client.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-xero.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    func: dispatchStart,
                    args: [this.demo, this.settings.application.selectors]
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    func: dispatchStartRequestedPage,
                    args: [this.demo, this.selectors]
                }
            );
        }
    }
}

export {XeroModule}

function dispatchStart(demo, selectors) {
    let startEvent = new CustomEvent("start", {
        "detail": {
            demo: demo,
            selectors: selectors
        }
    });
    document.dispatchEvent(startEvent);
}

function dispatchStartRequestedPage(demo, selectors, page) {
    let startEvent = new CustomEvent("startRequestedPage", {
        "detail": {
            demo: demo,
            selectors: selectors,
            page: page
        }
    });
    document.dispatchEvent(startEvent);
}