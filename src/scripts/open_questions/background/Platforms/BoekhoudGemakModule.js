import browser from "webextension-polyfill";
import {ServiceModule} from "./ServiceModule";

class BoekhoudGemakModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        this.selectors = this.settings.application.selectors;
        this.pageStartUrl = this.settings.application.pageStartUrl;
        this.apiUrls = this.settings.application.apiUrls;
        this.allowedUrls = this.settings.application.allowedUrls;
        this.notAllowedUrls = this.settings.application.notAllowedUrls;
        console.log('Boekhoud Gemak module constructor called with demo mode: ' + this.demo);
    }

    async navigationListener(details) {
        await browser.scripting.executeScript(
            {
                target: {tabId: details.tabId},
                files: ['/dist/openquestions-client.js']
            });

        await browser.scripting.executeScript(
            {
                target: {tabId: details.tabId},
                files: ['/dist/openquestions-boekhoud-gemak.js']
            });
        this.handlePageStart();
        this.handleRequestedPageStart();
    }

    /**
     * Start boekhoud gemak page
     */
    handlePageStart() {
        if (this.isPageDataSet) {
            return;
        }

        this.isPageDataSet = true;

        // each get request will handle the cornerstone
        browser.webRequest.onCompleted.addListener(
            async (details) => {
                for (let url of this.pageStartUrl) {
                    if (url.includes(this.removeQueryParameters(details.url))
                        && (details.method === 'GET')
                        && (details.method === 'POST')
                        && (details.method === 'PUT')
                        && (await browser.storage.local.get('can-use-open-questions'))['can-use-open-questions'] !== null
                    ) {
                        await browser.scripting.executeScript(
                            {
                                target: {tabId: details.tabId},
                                func: dispatchStart,
                                args: [this.demo, this.selectors, this.notAllowedUrls]
                            }
                        );
                    }
                }
            },
            {
                urls: this.pageStartUrl,
                types: ['xmlhttprequest', 'main_frame'],
            },
        );
    }

    /**
     * Handle each ajax call from user for the desired page
     */
    handleRequestedPageStart() {
        if (this.isRequestedPageDataSet) {
            return;
        }

        this.isRequestedPageDataSet = true;

        browser.tabs.onUpdated.addListener(
            async (tabId, changeInfo, tab) => {
                for (let allowedUrl of Object.values(this.allowedUrls)) {
                    if (changeInfo.title &&
                        tab.url.includes(allowedUrl) &&
                        (await browser.storage.local.get('can-use-open-questions'))['can-use-open-questions'] !== null)
                    {
                        await browser.scripting.executeScript(
                            {
                                target: {tabId: tabId},
                                func: dispatchStartRequestedPage,
                                args: [this.demo, this.selectors, this.allowedUrls]
                            }
                        );
                        break;
                    }
                }
            });

    }

    /**
     * Remove query parameters from url
     * @param url
     * @returns {string}
     */
    removeQueryParameters(url) {
        let urlObject = new URL(url);
        return urlObject.origin + urlObject.pathname; // remove query parameters
    }
}

export {BoekhoudGemakModule}

function dispatchStart(demo, selectors, notAllowedUrls) {
    let startEvent = new CustomEvent("start", {
        "detail": {
            demo: demo,
            selectors: selectors,
            notAllowedUrls: notAllowedUrls
        }
    });
    document.dispatchEvent(startEvent);
}

function dispatchStartRequestedPage(demo, selectors, allowedUrls) {
    let startEvent = new CustomEvent("startRequestedPage", {
        "detail": {
            demo: demo,
            selectors: selectors,
            allowedUrls: allowedUrls
        }
    });
    document.dispatchEvent(startEvent);
}