import browser from 'webextension-polyfill';
import {ServiceModule} from "./ServiceModule";

class NextensModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        console.log('Nextens module constructor called');
    }

    async navigationListener(details) {
        if (details.frameId === 0) {
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-client.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    files: ['/dist/openquestions-nextens.js']
                });
            await browser.scripting.executeScript(
                {
                    target: {tabId: details.tabId},
                    func: dispatchStart,
                    args: [this.demo, this.settings.application]
                }
            );
        }
    }
}

export {NextensModule}

function dispatchStart(demo, settings) {
    let startEvent = new CustomEvent("start", {
        "detail": {
            demo: demo,
            settings: settings
        }
    });
    document.dispatchEvent(startEvent);
}