import browser from "webextension-polyfill";
import globalCommunicationListener from "../../GlobalCommunicationListener";
import {TwinfieldModule} from "./Platforms/TwinfieldModule";
import {ExactModule} from "./Platforms/ExactModule";
import {BaseconeModule} from "./Platforms/BaseconeModule";
import {NmbrsModule} from "./Platforms/NmbrsModule";
import {SnelstartModule} from "./Platforms/SnelstartModule";
import {NextensModule} from "./Platforms/NextensModule";
import {LoketModule} from "./Platforms/LoketModule";
import {FiscaalGemakModule} from "./Platforms/FiscaalGemakModule";
import {BoekhoudGemakModule} from "./Platforms/BoekhoudGemakModule";
import {XeroModule} from "./Platforms/XeroModule";
import {MinoxModule} from "./Platforms/MinoxModule";
import {MoneybirdModule} from "./Platforms/MoneybirdModule";
import CornerStoneModule from "./CornerStoneModule";
import {TabHelper} from "../../helpers/TabHelper";
import {EBoekhoudenModule} from "./Platforms/EBoekhoudenModule";
import {MfoModule} from "./Platforms/MfoModule";
import {ZenvoicesModule} from "./Platforms/ZenvoicesModule";
import {BrowserExtensionApi} from "../../hix/BrowserExtensionApi";
import {LogHelper} from "../../helpers/LogHelper";

class OpenQuestionsModule {
    constructor() {
        this.cornerstone = new CornerStoneModule();

        this.availableModules = {
            'twinfield_open_questions': TwinfieldModule,
            'exact_open_questions': ExactModule,
            'basecone_open_questions': BaseconeModule,
            'snelstart_open_questions': SnelstartModule,
            'nmbrs_open_questions': NmbrsModule,
            'nextens_open_questions': NextensModule,
            'fiscaal_gemak_open_questions': FiscaalGemakModule,
            'loket_open_questions': LoketModule,
            'boekhoud_gemak_open_questions': BoekhoudGemakModule,
            'xero_open_questions': XeroModule,
            'minox_open_questions': MinoxModule,
            'moneybird_open_questions': MoneybirdModule,
            'eboekhouden_open_questions': EBoekhoudenModule,
            'mfo_open_questions': MfoModule,
            'zenvoices_open_questions': ZenvoicesModule
        };
        this.activeModules = {};

        globalCommunicationListener.addListener('get_questions_for_company', this.getQuestionsForCompany.bind(this));
        globalCommunicationListener.addListener('get_questions_by_company_id', this.getQuestionsByCompanyId.bind(this));
        globalCommunicationListener.addListener('create_open_question', this.createOpenQuestion.bind(this));
        globalCommunicationListener.addListener('get_status', this.getQuestionStatus.bind(this));
        globalCommunicationListener.addListener('launch_question_history', this.launchQuestionHistory.bind(this));
        globalCommunicationListener.addListener('get_question_history', this.getQuestionHistory.bind(this));
        globalCommunicationListener.addListener('remove_question', this.removeQuestion.bind(this));
        globalCommunicationListener.addListener('close_question', this.closeQuestion.bind(this));
        globalCommunicationListener.addListener('answer_question', this.answerQuestion.bind(this));
        globalCommunicationListener.addListener('get_hix_company', this.getHixCompany.bind(this));
        globalCommunicationListener.addListener('get_service_options', this.getServiceOptions.bind(this));
        globalCommunicationListener.addListener('get_question_types', this.getQuestionTypes.bind(this));
        globalCommunicationListener.addListener('get_companies_for_question', this.getCompaniesForQuestion.bind(this));
        globalCommunicationListener.addListener('get_user_profile', this.getUserProfile.bind(this));
        globalCommunicationListener.addListener('update_company_configuration', this.updateCompanyConfiguration.bind(this));
        globalCommunicationListener.addListener('redirect_to_open_questions', this.redirectToOpenQuestionPage.bind(this));
        globalCommunicationListener.addListener('redirect_to_services', this.redirectToNewServicesPage.bind(this));
        globalCommunicationListener.addListener('download_file', this.downloadFile.bind(this));
    }

    async updateSettings(timestamp) {
        return await (new BrowserExtensionApi()).updateSettings(timestamp).finally(async () => {
            await this.setServiceModulesFromLocalStorage();
        });
    }

    /**
     * This call will loop through initiated modules and check whether they are available in the settings.
     * If the setting is available and mode is set to active we will set demo mode to false.
     */
    async setServiceModulesFromLocalStorage() {
        let settings = (await browser.storage.local.get('settings')).settings;
        if (settings) {
            await this.setServiceModules(settings);
        } else {
            LogHelper.debug('OpenQuestionsModule unable to load settings from storage');
        }
    }

    /**
     * Iterate through services list from Herald settings file that is copied to local storage.
     * Module is only instantiated when found in settings and then added to active modules list.
     */
    async setServiceModules(localStorageSettings) {
        LogHelper.debug('OpenQuestionsModule called setServiceModules()');
        this.resetActiveModules();
        const settings = JSON.parse(localStorageSettings);
        for (const serviceName in settings.services) {
            LogHelper.debug('OpenQuestionsModule registering module ' + serviceName);
            if (this.availableModules.hasOwnProperty(serviceName)) { // module for service is available
                let module = new this.availableModules[serviceName](settings.services[serviceName]);
                module.registerNavigatorListeners();
                this.activeModules[serviceName] = module;
            }
        }
    }

    /**
     * Remove listeners and delete reference to module so that it can be cleaned up by
     * the garbage collector.
     */
    resetActiveModules() {
        for (const i in this.activeModules) {
            this.activeModules[i].active = false;
            this.activeModules[i].removeNavigatorListeners();
            delete this.activeModules[i];
        }
    }

    onUserLoginChange(isLoggedIn) {
        for (const i in this.activeModules) {
            this.activeModules[i].onLoginChange(isLoggedIn);
        }
    }

    async getHixCompany(message) {
        const response = await browser.openQuestionsApi.getCompanyInformation(
            message.data.company_id,
            message.data.company_id,
            message.data.external_company_name,
            message.data.service_name
        );

        if (response && response.data) {
            return response.data;
        }

        return null;
    }

    /**
     * This function is only used by the Basecone module. The message should add service_name if used by others
     */
    async getServiceOptions(message) {
        if (this.activeModules.hasOwnProperty('basecone_open_questions')) {
            const response = await this.activeModules.basecone_open_questions.getServiceOptions(message);
            return response.data;
        }
    }

    async getQuestionTypes(message) {
        let {category, serviceName, data} = message.data;
        return (await browser.openQuestionsApi.getQuestionTypes(category, serviceName, data)).data;
    }

    async getQuestionsForCompany(message) {
        const response = await browser.openQuestionsApi.getQuestionsForCompany(message.data);
        if (response && response.data) {
            return response.data;
        } else {
            return null;
        }
    }

    async getQuestionsByCompanyId(message) {
        const response = await browser.openQuestionsApi.getQuestionsByCompanyId(message.data);
        if (response && response.data) {
            return response.data;
        } else {
            return null;
        }
    }

    async getCompaniesForQuestion() {
        return await browser.openQuestionsApi.getUserCompanies();
    }

    /**
     * Transform base64 into blob
     * @param dataURI
     * @returns {Blob}
     */
    dataURItoBlob(dataURI) {
        let binary = atob(dataURI.split(',')[1]);
        let array = [];
        for (let i = 0; i < binary.length; i++) {
            array.push(binary.charCodeAt(i));
        }
        // separate out the mime component
        let mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
        return new Blob([new Uint8Array(array)], {type: mimeString});
    }

    async createOpenQuestion(message) {
        let data = message.data.data;
        let category = message.data.category;
        let formData = new FormData();
        for (let key in data) {
            // handle attachments
            if (key === 'attachments' && data[key].length) {
                for (let attachment of data[key]) {
                    formData.append('attachments[]', this.dataURItoBlob(attachment.base64), attachment.filename);
                }
                continue;
            }
            if (typeof data[key]  == 'boolean') {
                data[key] = data[key] ? 1 : 0;
            }

            // form data does not accept null or undefined values. It will convert null to a string 'null'
            if (data[key] !== undefined && data[key] !== null) {
                formData.append(key, data[key]);
            }
        }
        await browser.openQuestionsApi.createQuestion(formData, category).then(response => {
            return response.data;
        })
    }

    async downloadFile(message) {
        return await browser.openQuestionsApi.downloadFile(message.data.url);
    }

    async getQuestionStatus(message) {
        const response = await browser.openQuestionsApi.getStatus(message.data);
        if (response && response.data) {
            return response.data;
        } else {
            LogHelper.error('Unable to get question status', message);
            return null;
        }
    }

    async getQuestionHistory(message) {
        const response = await browser.openQuestionsApi.getQuestionHistory(message.data.id);
        let data = null;
        if (response && response.data) {
            data = {
                'service_name': message.data.service_name,
                'id': message.data.id,
                'name': message.data.name,
                'type': message.data.type,
                'status': message.data.status,
                'amount': message.data.amount,
                'messages': response.data,
                'hostname': (await browser.storage.local.get('host/hostname/domain'))['host/hostname/domain']
            };
        }

        return data;
    }

    async launchQuestionHistory(message) {
        const response = await browser.openQuestionsApi.getQuestionHistory(message.data.id);
        if (response && response.data) {
            let data = {
                'service_name': message.data.service_name,
                'id': message.data.id,
                'name': message.data.name,
                'type_text': message.data.type,
                'status': message.data.status,
                'amount': message.data.amount,
                'messages': response.data,
                'hostname': (await browser.storage.local.get('host/hostname/domain'))['host/hostname/domain']
            };
            data = JSON.stringify(data);
            await browser.scripting.executeScript(
                {
                    target: {tabId: (await TabHelper.getCurrentTab()).id},
                    func: dispatchLaunchHistory,
                    args: [data]
                });
        }
    }

    async updateCompanyConfiguration(message) {
        return await browser.openQuestionsApi.updateCompanyConfiguration(message.data);
    }

    async removeQuestion(message) {
        const question = await browser.openQuestionsApi.removeQuestion(message.data.id);
        return !!question;
    }

    async closeQuestion(message) {
        const question = await browser.openQuestionsApi.closeQuestion(message.data.id);
        return !!question;
    }

    async answerQuestion(message) {
        const question = await browser.openQuestionsApi.answerQuestion(message.data.id, message.data.answer);
        return !!question;
    }

    async redirectToNewServicesPage() {
        let hostname = (await browser.storage.local.get('host/hostname/domain'))['host/hostname/domain']
        await browser.tabs.create({url: 'https://' + hostname + '/new/manage/services/new'});
    }

    async redirectToOpenQuestionPage(message) {
        let newUrl =  'https://'
            + (await browser.storage.local.get('host/hostname/domain'))['host/hostname/domain']
            + '/open_questions?q=' + encodeURIComponent(message.data.companyName)
            + '&status=' + message.data.status;

        await browser.tabs.create({url: newUrl});
    }

    async getUserProfile() {
        return (await browser.storage.local.get('user_info')).user_info;
    }
}

// Singleton
Object.freeze(OpenQuestionsModule);
export default OpenQuestionsModule;

function dispatchLaunchHistory(data) {
    let startEvent = new CustomEvent("launchHistory", {
        "detail": {data: data}
    });
    document.dispatchEvent(startEvent);
}