import {LogHelper} from "../../helpers/LogHelper";
import axios from "axios";

class CompanyApi {

    static async getCompanyInfo(hostname, companyId, companyCode, companyName, serviceName) {
        LogHelper.debug('CompanyApi getCompanyInfo called');
        if (companyId && companyCode && companyName && serviceName) {
            let url = 'open_questions/securelogin_company/' + companyId + '/' + companyCode + '/' + serviceName;
            return axios.get(url, {
                baseURL: `https://${hostname}`,
                withCredentials: true,
                params: {
                    external_name: encodeURIComponent(companyName)
                }
            }).then(response => {
                LogHelper.debug('CompanyApi returning response.', response);
                return response;
            }).catch(e => {
                LogHelper.error('CompanyApi unable to get company information.', e);
                return null;
            });
        } else {
            LogHelper.warn('CompanyApi unable to get company information. Missing parameters', companyId, companyCode, companyName, serviceName);
            return null;
        }
    }
}

export {
    CompanyApi
};