import {BaseHixApi} from '../../hix/BaseHixApi';
import {Caching} from '../../utils/Caching';
import Axios from "axios";
import {LogHelper} from "../../helpers/LogHelper";

const cache = new Caching()

class OpenQuestionsApi extends BaseHixApi {
    servicesWithCompanyId = [
        'twinfield_open_questions',
        'snelstart_open_questions',
        'boekhoud_gemak_open_questions',
        'exact_open_questions',
        'minox_open_questions',
        'xero_open_questions',
        'moneybird_open_questions',
        'mfo_open_questions',
        'basecone_open_questions',
        'zenvoices_open_questions'
    ];

    async createQuestion(formData, category) {
        return (await this.axios()).post('/new/open_questions/' + category + '/question/create', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    }

    async getQuestionsForCompany(data) {
        let url = '/new/open_questions/' + data.category + '/company/questions';

        return (await this.axios()).get(url, {params: data});
    }

    async getQuestionsByCompanyId(data) {
        let url = '/new/open_questions/' + data.category + '/company/' + data.company_id + '/index';

        if (data.company_id) {
            return (await this.axios()).get(url, {params: data});
        }

        return null;
    }

    /**
     * @param data
     * @returns {Promise<AxiosResponse<any>|null>}
     */
    async getStatus(data) {
        let companyId = null;
        let service_name = data['service_name'];
        if (this.servicesWithCompanyId.includes(service_name)) {
            companyId = data['company_id'];
        }

        if (companyId) {
            let params = {
                company_id: companyId,
                service_name: service_name,
                title: data['name'],
                amount: data['amount'],
                transaction_date: data['transaction_date'],
            };
            if (data['invoice_number']) {
                params['invoice_number'] = data['invoice_number'];
            }
            if (data['bookkeeping_number']) {
                params['bookkeeping_number'] = data['bookkeeping_number'];
            }
            return (await this.axios()).get('/open_questions/status', {
                params: params
            });
        }
        return null;
    }

    async updateCompanyConfiguration(data) {
        return (await this.axios()).patch('/open_questions/company/configuration', data).then(() => {
            return true;
        }).catch((e) => {
            LogHelper.error(e);
            return false;
        })
    }

    async getUserCompanies() {
        return (await (await this.axios()).get('/member/unarchived_companies')).data;
    }

    async getQuestionHistory(questionId) {
        return (await this.axios()).get(`/open_questions/${questionId}/audit_log`);
    }

    async removeQuestion(questionId) {
        await (await this.axios()).post('/open_questions/delete', {'open_question_id': questionId});
    }

    async closeQuestion(questionId) {
        await (await this.axios()).post('/open_questions/close', {'open_question_id': questionId});
    }

    async answerQuestion(questionId, answer) {
        await (await this.axios()).post(`/open_questions/${questionId}/reply`, {'answer': answer});
    }

    async getCompanyInformation(companyId, companyCode, companyName, serviceName) {
        LogHelper.debug('OpenQuestionsApi getCompanyInformation called');
        if (companyId && companyCode && companyName && serviceName) {
            let url = 'open_questions/securelogin_company/' + companyId + '/' + companyCode + '/' + serviceName;
            return (await this.axios()).get(url, {
                params: {
                    external_name: encodeURIComponent(companyName)
                }
            }).then(response => {
                LogHelper.debug('OpenQuestionsApi returning response.', response);
                return response;
            }).catch(e => {
                LogHelper.error('OpenQuestionsApi unable to get company information.', e);
                return null;
            })
        } else {
            LogHelper.warn('OpenQuestionsApi unable to get company information. Missing parameters', companyId, companyCode, companyName, serviceName);
            return null;
        }
    }

    async getQuestionTypes(category, serviceName, data) {
        return (await this.axios()).get(`open_questions/question/types`, {
            params: {
                category: category,
                service_name: serviceName,
                data: JSON.stringify(data)
            }
        });
    }

    /**
     * Download file and return a base64
     * @param url
     * @returns {Promise<string>}
     */
    async downloadFile(url) {
        return await Axios.create({})
            .get(url, {responseType: 'arraybuffer'})
            .then((response) => {
                let base64 = btoa(new Uint8Array(response.data).reduce((data, byte) => data + String.fromCharCode(byte), ''));
                let contentDisposition = response.headers['content-disposition'];
                if (contentDisposition) {
                    let fileNameMatch = contentDisposition.match(/filename=["']?([^;"']+)/);
                    if (fileNameMatch && fileNameMatch.length === 2) {
                        return {
                            filename: fileNameMatch[1],
                            base64: `data:${response.headers['content-type'].toLowerCase()};base64,${base64}`
                        }
                    }
                }
                return null;
            }).catch((error) => {
                LogHelper.error(error);
                return null;
            });
    }
}

export {
    OpenQuestionsApi
};