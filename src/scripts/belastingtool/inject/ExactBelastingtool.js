import {SelectorHelper} from "../../open_questions/inject/helpers/SelectorHelper";
import {CflStatementsTable} from "./exact/CflStatementsTable";

class ExactBelastingtool {
    constructor() {
        this.selectorHelper = new SelectorHelper();
    }

    async start(selectors) {
        this.selectors = selectors;
        await this.listenIframe();
    }

    async listenIframe() {
        this.selectorHelper.waitForElement(this.selectors.iframe.selector).then(async (iframe) => {
            let iframeSource = iframe.src;
            let allowedSources = this.selectors.iframe.allowed_sources;
            let tableKey = null;
            for (const [key, source] of Object.entries(allowedSources)) {
                const index = source.findIndex(element => {
                    if (iframeSource.includes(element)) {
                        return true;
                    }
                });
                if (index > -1) {
                    tableKey = key;
                    break;
                }
            }
            if (tableKey) {
                let table = this.selectorHelper.helpSelector(this.selectors.iframe[tableKey].selector);
                if (table) {
                    switch (tableKey) {
                        case 'cfl_statements_table':
                            return new CflStatementsTable(table, this.selectors);
                        default:
                            console.log('Table not found');
                    }
                }
            }
        });
    }
}

let exactBelastingtool = new ExactBelastingtool();

document.addEventListener('startBelastingtool', async function (e) {
    await exactBelastingtool.start(e.detail.selectors);
}, {once: true});