import style from '!css-loader!./payment_reference_style.css';
import {trans} from "../../../helpers/TranslationHelper";
import browser from "webextension-polyfill";
import {sendToBExtension} from "../../../custom_sso/inject/CommunicationService";
import {NotificationHelper} from "../../../tax/inject/helpers/NotificationHelper";

class PaymentReference {
    constructor(paymentReference) {
        this.paymentReference = paymentReference;
    }

    async init() {
        this._injectableElement = await this.getNewButton();
    }

    async getNewButton() {
        let buttonContainer = document.createElement('div');
        buttonContainer.setAttribute('class', 'hix-belastingtool-dropdown-container');
        const styleElement = document.createElement('style');
        styleElement.innerText = style;
        buttonContainer.appendChild(styleElement);

        let paymentReferenceElem = document.createElement('a');
        paymentReferenceElem.innerText = this.paymentReference;

        let dropdown = document.createElement('div');
        dropdown.setAttribute('class', 'hix-belastingtool-dropdown-content');
        dropdown.setAttribute('style', 'display:none;');

        let header =  document.createElement('div');
        header.setAttribute('class', 'hix-dropdown-header');

        let imageElement = document.createElement('img');
        imageElement.setAttribute('src', browser.runtime.getURL('icons/belastingtool.svg'));
        imageElement.style.height = '12px';
        header.appendChild(imageElement);

        let closeElement = document.createElement('span');
        closeElement.setAttribute('class', 'hix-dropdown-close clickable');
        closeElement.innerHTML = '&#10005;';
        closeElement.addEventListener('click', function () {
            if (dropdown.style.display === 'none') {
                dropdown.style.display = 'block';
            } else {
                dropdown.style.display = 'none';
            }
        });
        header.appendChild(closeElement);

        dropdown.appendChild(header);

        let bodyElement = document.createElement('div');
        bodyElement.setAttribute('class', 'hix-belastingtool-dropdown-content-body');
        let titleElement = document.createElement('div');
        titleElement.setAttribute('class', 'hix-belastingtool-dropdown-content-title');
        titleElement.innerText = await trans('belastingtool.translate');
        bodyElement.appendChild(titleElement);

        let optionsContainer = document.createElement('div');
        optionsContainer.setAttribute('class', 'hix-belastingtool-dropdown-content-options');

        let checkmark = document.createElement('img');
        checkmark.setAttribute('src', browser.runtime.getURL('icons/checkmark.svg'));
        checkmark.setAttribute('class', 'clickable');
        checkmark.style.height = '14px';
        checkmark.addEventListener('click', () => {
            sendToBExtension('getPaymentReference', {payment_reference: this.paymentReference}).then((response) => {
                titleElement.innerText = response;
                checkmark.remove();
                cross.remove();
                let copyButton = document.createElement('div');
                copyButton.setAttribute('class', 'hix-copy-button');
                copyButton.addEventListener('click', function () {
                    navigator.clipboard.writeText(response);
                });
                optionsContainer.appendChild(copyButton);
            }).catch(async () => {
                NotificationHelper.sendToast('error', await trans('belastingtool.translation_failed'));
                dropdown.style.display = 'none';
            })
        });

        let cross = document.createElement('img');
        cross.setAttribute('src', browser.runtime.getURL('icons/cross.svg'));
        cross.setAttribute('class', 'clickable');
        cross.style.height = '14px';
        cross.addEventListener('click', function () {
            if (dropdown.style.display === 'none') {
                dropdown.style.display = 'block';
            } else {
                dropdown.style.display = 'none';
            }
        });

        optionsContainer.appendChild(checkmark);
        optionsContainer.appendChild(cross);
        bodyElement.appendChild(optionsContainer);
        dropdown.appendChild(bodyElement);

        paymentReferenceElem.addEventListener('click', function () {
            if (dropdown.style.display === 'none') {
                dropdown.style.display = 'block';
            } else {
                dropdown.style.display = 'none';
            }
        });

        buttonContainer.appendChild(paymentReferenceElem);
        buttonContainer.appendChild(dropdown);
        return buttonContainer;
    }

    get injectableElement() {
        return this._injectableElement;
    }
}

export {PaymentReference};