import {OpenQuestionsCommunicationService} from "../../../open_questions/inject/OpenQuestionsCommunicationService";
import {PaymentReference} from "../button/PaymentReference";

/**
 * Pages using this:
 * - Financieel -> Bank en kas -> Af te handelen
 */
class CflStatementsTable {
    constructor(table, selectors) {
        this.selectors = selectors;
        this.lang = '';
        this.openQuestionsCommunicationService = new OpenQuestionsCommunicationService();

        this.payment_reference_index = -1;

        if (this.reportIsValid(table)) {
            this.getPaymentReferenceIndex(table)
            this.editColumn(table);
        }
    }

    reportIsValid(table) {
        let tableHeader = table.tHead;
        if (tableHeader) {
            let headerCells = tableHeader.rows[2].cells;
            let requiredColumnNl = this.selectors.iframe.cfl_statements_table.required_column_nl;
            let requiredColumnEn = this.selectors.iframe.cfl_statements_table.required_column_en;

            if (Object.values(headerCells).some((cell) => {
                return requiredColumnNl === cell.textContent.trim();
            })) {
                this.lang = 'nl';
                return true;
            }

            if (Object.values(headerCells).some((cell) => {
                return requiredColumnEn === cell.textContent.trim();
            })) {
                this.lang = 'en';
                return true;
            }
        }
        return false;
    }

    getPaymentReferenceIndex(table) {
        let tableHeader = table.tHead;
        if (tableHeader) {
            let headerCells = tableHeader.rows[2].cells;
            let header = this.selectors.iframe.cfl_statements_table['payment_reference_' + this.lang];
            for (let i = 0; i < headerCells.length; i++) {
                if (headerCells[i].textContent.trim() === header) {
                    this.payment_reference_index = i;
                    break;
                }
            }
        }
    }

    async editColumn(table) {
        let tableBody = table.tBodies[0];
        for (let row of tableBody.rows) {
            let paymentReferenceCell = row.cells[this.payment_reference_index];
            let paymentReference = paymentReferenceCell ? paymentReferenceCell.textContent.trim() : null;
            if (paymentReference) {
                let button = new PaymentReference(paymentReference);
                await button.init();
                paymentReferenceCell.innerHTML = '';
                paymentReferenceCell.append(button.injectableElement);
            }
        }
    }
}

export {CflStatementsTable}