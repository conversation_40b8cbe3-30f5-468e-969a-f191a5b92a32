import {ServiceModule} from "../../../open_questions/background/Platforms/ServiceModule";
import browser from "webextension-polyfill";

class ExactBelastingtoolModule extends ServiceModule {
    constructor(settings) {
        super(settings);
        console.log('Exact Belastingtool module constructor called');
    }

    async navigationListener(details) {
        await browser.scripting.executeScript(
            {
                target: {tabId: details.tabId},
                files: ['/dist/belastingtool-exact.js']
            });
        await browser.scripting.executeScript(
            {
                target: {tabId: details.tabId},
                func: dispatchStart,
                args: [this.settings.application.selectors]
            });
    }
}

export {ExactBelastingtoolModule}

function dispatchStart(selectors) {
    let startEvent = new CustomEvent("startBelastingtool", {
        "detail": {
            selectors: selectors
        }
    });
    document.dispatchEvent(startEvent);
}