import {ExactBelastingtoolModule} from './Platforms/ExactBelastingtoolModule';
import browser from "webextension-polyfill";
import globalCommunicationListener from "../../GlobalCommunicationListener";
import BelastingtoolApi from "../api/BelastingtoolApi";
import {BrowserExtensionApi} from "../../hix/BrowserExtensionApi";

class BelastingtoolModule {
    constructor() {
        this.availableModules = {
            'exact_belastingtool': ExactBelastingtoolModule
        }

        this.activeModules = {};
        globalCommunicationListener.addListener('getPaymentReference', this.getPaymentReference.bind(this));
    }

    async getPaymentReference(message) {
        let response = await BelastingtoolApi.getPaymentReference(message.data.payment_reference);
        if (response && response.data) {
            return response.data;
        } else {
            return null;
        }
    }

    async updateSettings(timestamp) {
        return await (new BrowserExtensionApi()).updateSettings(timestamp).finally(async () => {
            await this.setServiceModulesFromLocalStorage();
        });
    }

    async setServiceModulesFromLocalStorage() {
        let settings = (await browser.storage.local.get('settings')).settings;
        if (settings) {
            await this.setServiceModules(settings);
        }
    }

    async setServiceModules(localStorageSettings) {
        this.resetActiveModules();
        const settings = JSON.parse(localStorageSettings);
        for (const serviceName in settings.services) {
            if (this.availableModules.hasOwnProperty(serviceName)) {
                let module = new this.availableModules[serviceName](settings.services[serviceName]);
                module.registerNavigatorListeners();
                this.activeModules[serviceName] = module;
            }
        }
    }

    resetActiveModules() {
        for (const i in this.activeModules) {
            this.activeModules[i].removeNavigatorListeners();
            delete this.activeModules[i];
        }
    }

    onUserLoginChange(isLoggedIn) {
        for (const i in this.activeModules) {
            this.activeModules[i].onLoginChange(isLoggedIn);
        }
    }
}
Object.freeze(BelastingtoolModule);
export default BelastingtoolModule;