class DataUtil {
  /**
   * @param {Blob} blob
   * @returns {Promise<Base64Encoded>}
   */
  static blobToBase64(blob) {
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    return new Promise(resolve => {
      reader.onloadend = () => {
        const split = reader.result.indexOf(',');
        resolve(new Base64Encoded(reader.result.slice(0, split), reader.result.slice(split + 1)));
      };
    });
  }
}

class Base64Encoded {
  constructor(encoding, base64) {
    this._encoding = encoding;
    this._base64 = base64;
  }

  get encoding() {
    return this._encoding;
  }

  get base64() {
    return this._base64;
  }

  get combinedString() {
    return `${this.encoding},${this.base64}`
  }
}

export {
  DataUtil,
  Base64Encoded
}