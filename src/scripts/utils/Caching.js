import dayjs from 'dayjs';

/**
 * @property {Map<string, CacheEntry>}
 */
class Caching {
    constructor() {
        this.cache = new Map();

        // Garbage collect every 5 minutes
        this.interval = setInterval(this.removeAllExpiredCacheEntries, 5 * 60 * 1000)
    }

    /**
     * @param {string} key
     * @param {any} value
     * @param {number} ttl in Seconds
     * @return {any} value
     */
    set(key, value, ttl= 500) {
        this.cache.set(key, new CacheEntry(
            value,
            dayjs().add(ttl, "seconds")
        ))

        return value
    }

    /**
     * @param {string} key
     */
    get(key) {
        const cachedValue = this.cache.get(key);

        if (!cachedValue || cachedValue.isExpired())
            return null;

        return cachedValue.data
    }

    /**
     * @param {string} key
     * @param {function} fallback
     * @param {number} ttl in Seconds
     * @returns {Promise<any>}
     */
    async getOtherwiseSet(key, fallback, ttl= 500) {
        const cachedValue = this.get(key);

        if (cachedValue)
            return cachedValue

        const fallBackValue = await fallback()

        return this.set(key, fallBackValue, ttl)
    }

    /**
     * @private
     * @return {Promise<void>}
     */
    async removeAllExpiredCacheEntries() {
        if (this.cache) {
            for (let [key, value] of this.cache.entries()) {
                if (value.isExpired()) {
                    this.cache.delete(key)
                }
            }
        }
    }

    destroy() {
        clearInterval(this.interval)
        this.cache.clear()
    }
}

class CacheEntry {
    /**
     *
     * @param {any} data
     * @param {dayjs.Dayjs} ttlDate
     */
    constructor(data, ttlDate) {
        this.data = data
        this.ttlDate = ttlDate
    }

    isExpired() {
        return dayjs().isAfter(this.ttlDate)
    }
}

export {
    Caching
}