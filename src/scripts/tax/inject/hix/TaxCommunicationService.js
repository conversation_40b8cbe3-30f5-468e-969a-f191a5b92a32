import browser from 'webextension-polyfill'
import {TaxScrapingData} from "../../shared/TaxScrapingData";

class TaxCommunicationService {
    async sendToBExtension(data) {
        if (!(data instanceof TaxScrapingData))
            throw new Error("data is not instanceof TaxScrapingData")

        return await browser.runtime.sendMessage({
            action: 'process_tax_data',
            data: JSON.stringify(data),
        });
    }
}

export {TaxCommunicationService}