button {
    background-color: #0<PERSON>A<PERSON>;
    padding: 0px;
    height: 42px;
    width: 50px;
    cursor: pointer;
    border-radius: 4px;
    border: none;
}

button:hover {
    background-color: #098fc6;
}

button img {
    /* Hix logo */
    content: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMzExIiBoZWlnaHQ9IjIxMiIgdmlld0JveD0iMCAwIDMxMSAyMTIiPgogIDxkZWZzPgogICAgPGNsaXBQYXRoIGlkPSJjbGlwLXBhdGgiPgogICAgICA8cmVjdCBpZD0iUmVjdGFuZ2xlXzQyIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSA0MiIgd2lkdGg9IjI1My45MTgiIGhlaWdodD0iMTUwIiBmaWxsPSIjZjlmOWY5Ii8+CiAgICA8L2NsaXBQYXRoPgogIDwvZGVmcz4KICA8ZyBpZD0ibG9nb19oaXhfd2l0IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgxMDkzMyAtMTM5OSkiPgogICAgPGcgaWQ9Ikdyb3VwXzUwOSIgZGF0YS1uYW1lPSJHcm91cCA1MDkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0xMDkwNC45NTkgMTQzMCkiPgogICAgICA8ZyBpZD0iR3JvdXBfMTAxIiBkYXRhLW5hbWU9Ikdyb3VwIDEwMSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCAwKSIgY2xpcC1wYXRoPSJ1cmwoI2NsaXAtcGF0aCkiPgogICAgICAgIDxwYXRoIGlkPSJQYXRoXzM5IiBkYXRhLW5hbWU9IlBhdGggMzkiIGQ9Ik0yNTMuOTE4LDBIMjYuMTc1TDAsMjUuMDQ3VjE1MEgyMjMuMjExbDI3LjEtMjguMzY1LTI2Ljk5NC01Ny4wNVpNMTAxLjkwOSwxMTguODgzSDczLjY0di00Mi4xSDYwLjE0OXY0Mi4xSDMxLjg4VjE0LjIxMUg2MC4xNDlWNTEuMjI1SDczLjY0VjE0LjIxMWgyOC4yNjlabTQyLjkxMiwwSDExNi41NTNWMTQuMjExaDI4LjI2OVptODguNDY5LDBIMjAxLjlsLTUuMjItMTQuOTE1YTEyNi42NDksMTI2LjY0OSwwLDAsMS00LjA2OC0xMy4xNTEsMTI3LjM1MiwxMjcuMzUyLDAsMCwxLTQuNDc1LDE0LjNsLTUuMjIsMTMuNzYzSDE1Mi4xNDNsMjUuNDIyLTU0LjMtMjQtNTAuMzdoMzEuNDU2bDQuNjc3LDE0LjE3cS43NDcsMS45NjUsMS40NTksNC42MDl0MS40NTcsNS45NjdxMS42OTUtNi43NzksMi44NDctMTAuNDRMMTk5LjgsMTQuMjExSDIzMS40NkwyMDcuNiw2NC41ODFaIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwIDApIiBmaWxsPSIjZjlmOWY5Ii8+CiAgICAgIDwvZz4KICAgIDwvZz4KICAgIDxyZWN0IGlkPSJSZWN0YW5nbGVfMzMyIiBkYXRhLW5hbWU9IlJlY3RhbmdsZSAzMzIiIHdpZHRoPSIzMTEiIGhlaWdodD0iMjEyIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgtMTA5MzMgMTM5OSkiIGZpbGw9Im5vbmUiLz4KICA8L2c+Cjwvc3ZnPgo=");
    width: 80%;
    height: auto;
}

button.loading img {
    display: none;
}

button span.loader {
    display: none;
    width: 16px;
    height: 16px;
    border-color: transparent #fff #fff transparent;
    border-style: solid;
    border-width: 2px;
    border-radius: 16px;
    transform: rotate(
            0deg
    );
    -webkit-animation: rotate .5s linear infinite;
    animation: rotate .5s linear infinite;
    margin-left: .5em;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

button.loading span.loader {
    display: block;
}