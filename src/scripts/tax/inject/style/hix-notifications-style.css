.hix-question-notification-wrapper {
    position: fixed;
    top: 40px;
    right: 60px;
    color: white;
    z-index: 2147483648;
    width: 300px;
    animation: fadeIn 0.1s ease-in;
    transition: opacity 3s ease-in-out;
}

.hix-question-notification {
    padding: 10px;
    border-radius: 6px;
    font-family: "Proxima Nova", sans-serif;
    font-weight: 500;
    font-size: 18px;
    text-align: left;
    margin-top: 20px;
    cursor: pointer;
    display: flex;
    width: 100%;
    background: white;
    color: #4d4d4d;
}

.hix-question-notification::after {
    content: "";
    display: table;
    clear: both;
}

.hix-question-notification.hix-question-notification-column {
    float: left;
    width: 50%;
}

.hix-question-notification .hix-question-notification-icon-wrapper {
    width: 50px;
    text-align: center;
}

.hix-question-notification .hix-question-notification-icon-wrapper .hix-question-notification-icon {
    width: 40px;
    margin: 0;
    position: relative;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.hix-question-notification .hix-question-notification-text {
    width: calc(100% - 50px);
    padding: 13px 10px 10px 10px;
}

.hix-question-notification.success {
    border: 1px solid #6fcf97;
    box-shadow: 0 2px 10px rgba(82,215,0,0.1);
}

.hix-question-notification.error {
    border: 1px solid #eb5757;
    box-shadow: 0 2px 10px rgba(215,0,0,0.1);
}

.hix-question-notification.info {
    border: 1px solid #f2c94c;
    box-shadow: 0 2px 10px rgba(242,201,76,0.1);
}

@keyframes fadeIn {
    from {
        opacity:0;
    }

    to {
        opacity:1;
    }
}