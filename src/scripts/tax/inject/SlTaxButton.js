import style from "!css-loader!./button-style/style.css";

class SlTaxButton {
    constructor() {
        const customButtonContainer = document.createElement('sl-tax-button')
        const shadowRoot = customButtonContainer.attachShadow({mode: 'open'});

        const styleElement = document.createElement('style');
        styleElement.innerText = style;
        styleElement.style.display = "none";
        shadowRoot.appendChild(styleElement)

        const buttonElement = document.createElement('button');
        buttonElement.setAttribute('aria-label', 'Import')
        buttonElement.setAttribute('tabindex', '0')

        const icon = document.createElement('img');

        const loader = document.createElement('span')
        loader.classList.add('loader');

        buttonElement.append(icon)
        buttonElement.append(loader)

        shadowRoot.appendChild(buttonElement)

        this._icon = icon;
        this._button = buttonElement
        this._slButtonContainer = customButtonContainer;
        this._loading = false;
    }

    /**
     *
     * @param {function} callback
     */
    addClickListener(callback) {
        this._button.addEventListener("click", callback)
    }

    get element() {
        return this._slButtonContainer
    }

    set loading(value) {
        this._loading = value;
        if (value) {
            this._button.classList.add("loading")
        } else {
            this._button.classList.remove('loading')
        }
    }

    get loading() {
        return this._loading;
    }
}

export {SlTaxButton}