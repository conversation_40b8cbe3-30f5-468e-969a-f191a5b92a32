import browser from 'webextension-polyfill';
import {Tab<PERSON><PERSON><PERSON>} from "../../../helpers/TabHelper";

class BackgroundNotificationHelper {

    /**
     * @private
     * @param {number} tabId
     * @returns {Promise<void>}
     */
    static async injectNotificationHelper(tabId) {
        await browser.scripting.executeScript(
            {
                target: {tabId: tabId},
                files: ['/dist/utils.js']
            });
    }

    /**
     * @private
     * @param {String} func
     * @param params
     * @returns {Promise<void>}
     */
    static async exec(func, params) {
        const currentTab = await TabHelper.getCurrentTab();
        await this.injectNotificationHelper(currentTab.id);

        await browser.scripting.executeScript(
            {
                args: [func, params],
                target: {tabId: currentTab.id},
                func: (func, params) => {
                    document.dispatchEvent(new CustomEvent('notification_sent', {
                        detail: {func: func, params: params}
                    }));
                }
            }
        );
    }

    /**
     * @param {String} icon
     * @param {String} message
     */
    static async sendToast(icon, message) {
        await this.exec('sendToast', {icon: icon, message: message})
    }

    static async sendSettingsChangedMessage() {
        await this.exec('sendSettingsChangedMessage', null);
    }

    static async sendGenericError() {
        await this.exec('sendGenericError', null);
    }

    static async sendForbiddenError() {
        await this.exec('sendForbiddenError', null);
    }
}

export {
    BackgroundNotificationHelper,
};