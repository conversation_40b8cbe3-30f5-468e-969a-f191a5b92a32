import style from '!css-loader!../style/hix-notifications-style.css'
import browser from "webextension-polyfill";

function getContainerElement() {
    let buttonContainer = document.createElement('div');
    buttonContainer.setAttribute('class', 'hix-question-notification-wrapper');
    let styleElement = document.createElement('style');
    styleElement.innerText = style;
    buttonContainer.appendChild(styleElement);

    return buttonContainer;
}

function getNotificationElement(type) {
    let notification = document.createElement('div');
    notification.classList.add('hix-question-notification', type);
    let textElement = document.createElement('div');
    textElement.innerText = style;
    return notification;
}

function getNotification(type, text) {
    let notificationElement = getNotificationElement(type);

    // handle icon
    let iconWrapperElement = document.createElement('div');
    iconWrapperElement.classList.add('hix-question-notification-icon-wrapper', 'hix-question-notification-column');
    let iconElement = document.createElement('object');
    iconElement.setAttribute('data', browser.runtime.getURL('icons/notification-' + type + '.svg'));
    iconElement.setAttribute('type', 'image/svg+xml');
    iconElement.setAttribute('class', 'hix-question-notification-icon');
    iconWrapperElement.appendChild(iconElement);
    notificationElement.appendChild(iconWrapperElement);

    // handle text
    let textElement = document.createElement('div');
    textElement.classList.add('hix-question-notification-text', 'hix-question-notification-column');
    textElement.innerHTML = text;
    notificationElement.appendChild(textElement);

    return notificationElement;
}

function init() {
    let notificationWrapper = document.querySelector('.hix-question-notification-wrapper');
    if (!notificationWrapper) {
        document.querySelector('html').appendChild(getContainerElement());
    }
}

function addMessage(type, message, timeout) {
    let notificationWrapper = document.querySelector('.hix-question-notification-wrapper');
    let notification = getNotification(type, message);

    // handle deletion
    notification.addEventListener('click', () => {
        notification.parentNode.removeChild(notification);
    });
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, timeout);

    // remove last if length reach and add message
    if (notificationWrapper.children.length > 4) {
        let last = document.querySelector('.hix-question-notification-wrapper > div:last-child');
        if (last) {
            last.remove();
        }
    }
    notificationWrapper.prepend(notification);
}

function toast(type, message, timeout) {
    init();
    addMessage(type, message, timeout)
}

class NotificationHelper {
    /**
     *
     * @param type
     * @param message
     * @param timeout
     */
    static sendToast(type, message, timeout = 6000) {
        toast(type, message, timeout);
    }

    static sendGenericError() {
        toast('error', 'Iets is fout gegaan probeer het later opnieuw.\nSomething went wrong try again later.', 6000);
    }

    static sendForbiddenError() {
        toast('error', 'Log in op je Hix omgeving.\n Log in to your Hix environment.', 6000);
    }

    static sendSettingsChangedMessage() {
        toast('info', 'Instellingen in Hix zijn gewijzigd, herlaad uw pagina.\n Settings in Hix have changes, please reload the page.', 6000);
    }
}

export {
    NotificationHelper
}