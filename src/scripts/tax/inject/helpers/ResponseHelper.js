import browser from 'webextension-polyfill';

class ResponseHelper {
    static async handleHeaders(headers) {
        let updated = false;
        let shouldInform = false;
        if (headers) {
            let headerTimestamp = headers.get('x-sl-settings-last-modified');
            let currentTimestamp = (await browser.storage.local.get('settings_timestamp')).settings_timestamp &&
                JSON.parse((await browser.storage.local.get('settings_timestamp')).settings_timestamp);
            let currentSettings = (await browser.storage.local.get('settings')).settings;
            if (
                headerTimestamp
                && (
                    !currentTimestamp
                    || !currentSettings
                    || currentTimestamp < headerTimestamp
                )
            ) {
                await browser.OpenQuestionsModule.updateSettings(headerTimestamp);
                updated = true;
                // Should inform the user that things changed, for this i checked if it had settings before we updated.
                shouldInform = Boolean(currentSettings);
            }
        }
        return {
            updated: updated,
            shouldInform: shouldInform
        };
    }
}

export {
    ResponseHelper,
};