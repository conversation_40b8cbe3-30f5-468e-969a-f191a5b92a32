import {SlTaxButton} from "../SlTaxButton";
import axios from 'axios';
import {LoketService} from "./LoketService";
import {XML} from "../../shared/XML";
import {TaxCommunicationService} from "../hix/TaxCommunicationService";
import {TaxScrapingData} from "../../shared/TaxScrapingData";
import {NotificationHelper} from "../helpers/NotificationHelper";

export class LoketTaxScraper {
    constructor() {
        this.taxCommunicationService = new TaxCommunicationService();
        this.loketService = new LoketService();
        this.pathId = null;
        this.button = new SlTaxButton();
        this.button.addClickListener(async (event) => {
            if (this.button.loading === true) return;

            this.button.loading = true;
            const xmlData = await this.getXmlData(event);
            if (xmlData !== null) {
                const notificationResponse = await this.taxCommunicationService.sendToBExtension(xmlData);
                if (notificationResponse) {
                    NotificationHelper.sendToast(notificationResponse.icon, notificationResponse.message);
                }
            } else {
                NotificationHelper.sendGenericError();
            }

            this.button.loading = false;
        });
    }

    start() {
        setInterval(() => {
            const header = document.querySelector('vsp-payroll-tax-return-download-list');
            if (!header) return;

            const slButton = header.querySelector('sl-tax-button');

            if (slButton) return;

            header.appendChild(this.button.element);
        }, 1000)
    }

    setPathId(id) {
        console.log(id)
        this.pathId = id;
    }

    /**
     *
     * @param event
     * @returns {Promise<TaxScrapingData>} This will return the scraped elements
     */
    async getXmlData(event) {
        console.log(this.pathId)

        if (this.pathId === null) {
            return null;
        }

        try {
            // TODO: grab from herald the url
            let url = 'https://api.loket.nl/v2/providers/employers/payrolladministrations/payrolltaxreturns/' + this.pathId + '/message';
            const xmlResponse = await axios.get(url, {
                headers: {
                    Authorization: this.loketService.getAuthorizationHeader()
                }
            });

            const taxScrapingData = new TaxScrapingData('loket_tasks');
            taxScrapingData.data.push(new XML(xmlResponse.data));
            return taxScrapingData;
        } catch (e) {
            return null;
        }
    }
}

let loketTaxScraper = new LoketTaxScraper();

document.addEventListener('tax_loket_start', function (e) {
    loketTaxScraper.start();
}, {once: true})

document.addEventListener('tax_loket_set_path_id',  function (e) {
    loketTaxScraper.setPathId(e.detail.id);
});