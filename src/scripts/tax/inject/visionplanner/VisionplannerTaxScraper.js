import {SlTaxButton} from '../SlTaxButton';
import axios from 'axios';
import {VisionplannerService} from './VisionplannerService';
import {XBRL} from '../../shared/XBRL';
import {PDF} from '../../shared/PDF';
import {TaxCommunicationService} from '../hix/TaxCommunicationService';
import {TaxScrapingData} from '../../shared/TaxScrapingData';
import {NotificationHelper} from '../helpers/NotificationHelper';
import {DataUtil} from '../../../utils/DataUtil';

export class VisionplannerTaxScraper {
    constructor() {
        this.button = new SlTaxButton();
        this.taxCommunicationService = new TaxCommunicationService();
        this.visionplannerService = new VisionplannerService();

        this.button.addClickListener(async (event) => {
            if (this.button.loading === true) return;

            this.button.loading = true;
            try {
                const scrapedData = await this.scrape(event);
                const notificationResponse = await this.taxCommunicationService.sendToBExtension(scrapedData);
                if (notificationResponse) {
                    NotificationHelper.sendToast(notificationResponse.icon, notificationResponse.message);
                }
            } catch (e) {
                console.error(e)
            } finally {
                this.button.loading = false;
            }
        });
    }

    start() {
        setInterval(() => {
            const scherm = document.querySelector(
                '.vp-dossier-report[data-ta=approval-and-publication]');
            if (!scherm) return;

            const slButton = scherm.querySelector('sl-tax-button');

            if (slButton) return;

            scherm.insertBefore(this.button.element, scherm.firstChild);
        }, 1000);
    }

    /**
     *
     * @param event
     * @returns {Promise<TaxScrapingData>} This will return the scraped elements
     */
    async scrape(event) {
        this.report("Start scraping Visionplanner")
        const path = window.location.pathname;
        const dossierId = /\/(?<dossier>[0-9]+)\//gm.exec(path).groups.dossier;
        this.report(`Found dossierId: id:"${dossierId}"`)

        const requestHeaders = this.visionplannerService.getRequestHeaders();

        const reportsResponse = await axios.get(
            `https://cloud.visionplanner.nl/vpc-api/SbrReport/GetList?dossierId=${dossierId}`,
            {
                headers: requestHeaders,
            });

        const reports = reportsResponse.data.Data.Reports;

        const taxScrapingData = new TaxScrapingData('visionplanner_tasks');
        taxScrapingData.metaData.name = document.querySelector(
            '.vp-dossier-title input').value;

        this.report(`Found ${reports.length} reports`)

        const financieelVerslagData = reports.find(
            report => report.Name === 'Financieel verslag');

        if (financieelVerslagData) {
            this.report(`Found Financieel verslag: type:"${financieelVerslagData.Type}" id:"${financieelVerslagData.Id}"`)

            const financieelVerslagPDF = await axios.get(
                'https://cloud.visionplanner.nl/vpc-api/SbrReport/DownloadRenderedInstance',
                {
                    headers: requestHeaders,
                    params: {
                        type: financieelVerslagData.Type,
                        reportId: financieelVerslagData.Id,
                    },
                    responseType: 'blob',
                }
            );


            const base64EncodedPdf = await DataUtil.blobToBase64(
                financieelVerslagPDF.data);

            taxScrapingData.data.push(new PDF(base64EncodedPdf.base64));
        }

        const publicatieStukkenRegex = new RegExp(
            '^kvk-rpt-jaarverantwoording-[0-9]{4}-', '');
        const publicatieStukkenData = reports.find(
            report => publicatieStukkenRegex.test(report.Name));

        if (publicatieStukkenData) {
            this.report(`Financieel verslag has publicatie stukken: type:"${publicatieStukkenData.Type}" id:"${publicatieStukkenData.Id}"`)

            const publicatieStukkenResponse = await axios.get(
                'https://cloud.visionplanner.nl/vpc-api/SbrReport/DownloadInstance', {
                    headers: requestHeaders,
                    params: {
                        type: publicatieStukkenData.Type,
                        reportId: publicatieStukkenData.Id,
                    },
                });

            this.report(`Fetched publicatie stukken`)

            taxScrapingData.data.push(new XBRL(publicatieStukkenResponse.data));
        }

        this.report(`Done scraping`)

        return taxScrapingData;
    }

    /**
     * @private
     * @param message
     */
    report(message) {
        console.error(message)
    }
}

let visionplannerTaxScraper = new VisionplannerTaxScraper();

document.addEventListener('tax_visionplanner_start', function (e) {
    visionplannerTaxScraper.start();
}, {once: true})
