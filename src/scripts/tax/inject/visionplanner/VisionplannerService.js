class VisionplannerService {
    getAuth() {
        const sessionKeys = Object.keys(sessionStorage);

        for (let sessionKey of sessionKeys) {
            if (sessionKey.startsWith('oidc.user')) {
                return JSON.parse(sessionStorage.getItem(sessionKey))?.access_token;
            }
        }

        return null
    }

    getAuthorizationHeader() {
        const auth = this.getAuth();

        return `Bear<PERSON> ${auth}`
    }

    getCompanyId() {
        let companyId = localStorage.getItem('vp.company-id');

        if (!companyId) {
            companyId = sessionStorage.getItem('vp.company-id');
        }

        return companyId;
    }

    getCsrfToken() {
        return localStorage.getItem('vp.csrf-token')
    }

    getRequestHeaders() {
        return {
            Authorization: this.getAuthorizationHeader(),
            'vp-companyid': this.getCompanyId(),
            'vp-csrftoken': this.getCsrfToken()
        }
    }
}

export {VisionplannerService}