import browser from 'webextension-polyfill';

class LoketModule {
    async start(details) {
        await browser.scripting.executeScript({
            target: {tabId: details.tabId},
            files: ['/dist/tax-loket.js']
        });

        await browser.scripting.executeScript({
            target: {tabId: details.tabId},
            func: dispatchTaxLoketStart,
            args: [],
        });

        this.setListener();
    }

    setListener() {
        if (this.listenerSet) {
            return;
        }

        this.listenerSet = true;

        browser.webRequest.onCompleted.addListener(
            async (details) => {
                if (details.method === 'GET') {
                    let url = details.url.split('/');
                    console.log(url)
                    await browser.scripting.executeScript(
                        {
                            target: {tabId: details.tabId},
                            func: dispatchSetPathId,
                            args: [url[url.length - 2]]
                        });
                }
            },
            {
                urls: [ // TODO: grab this from herald
                    "https://api.loket.nl/v2/providers/employers/payrolladministrations/payrolltaxreturns/*/idealPaymentStatus"
                ],
                types: ['xmlhttprequest', 'main_frame'],
            },
        );
    }
}

export {LoketModule}

function dispatchTaxLoketStart() {
    document.dispatchEvent(new CustomEvent('tax_loket_start', {}));
}

function dispatchSetPathId(id) {
    let setDataEvent = new CustomEvent("tax_loket_set_path_id", {
        "detail": {
            id: id
        }
    });
    document.dispatchEvent(setDataEvent);
}