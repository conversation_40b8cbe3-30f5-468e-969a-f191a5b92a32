import browser from 'webextension-polyfill';

class VisionPlannerModule {
    async start(details) {
        await browser.scripting.executeScript({
            target: {tabId: details.tabId},
            files: ['/dist/tax-visionplanner.js']
        });

        await browser.scripting.executeScript({
            target: {tabId: details.tabId},
            func: dispatchTaxVisionplannerStart,
            args: [],
        });
    }
}

export {VisionPlannerModule}

function dispatchTaxVisionplannerStart() {
    document.dispatchEvent(new CustomEvent('tax_visionplanner_start', {}));
}