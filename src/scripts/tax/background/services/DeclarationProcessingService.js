import {TaxScrapingData} from "../../shared/TaxScrapingData";
import {Hix<PERSON><PERSON>} from "../../../hix/HixApi";

class DeclarationProcessingService {
    /**
     * @param {HixApi} hixApi
     */
    constructor(hixApi) {
        this.hixApi = hixApi;
    }

    /**
     * @param {TaxScrapingData} declaration
     * @returns {Promise<*>}
     */
    async processDeclaration(declaration) {
        if (declaration.source === 'loket_tasks') {
            return await this.hixApi.generateLoketTask({
                xml: declaration.data[0].value
            })
        }

        if (declaration.source === 'visionplanner_tasks') {
            const data = {
                name: declaration.metaData.name,
            };
            _.forEach(declaration.data, (document) => {
                data[document.__type__.toLowerCase()] = document.value;
            });

            return await this.hixApi.generateVisionplannerTask(data)
        }

        throw new Error(`Unknown declaration type ${declaration.source}`)
    }
}

export {
    DeclarationProcessingService
}