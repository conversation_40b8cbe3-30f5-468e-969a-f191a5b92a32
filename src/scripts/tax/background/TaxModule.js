import browser from "webextension-polyfill";
import KnownHosts from "../../knownHosts";
import {XML} from "../shared/XML";
import {XBRL} from "../shared/XBRL";
import {PDF} from "../shared/PDF";
import {TaxScrapingData} from "../shared/TaxScrapingData";
import {HixApi} from "../../hix/HixApi";
import {DeclarationProcessingService} from "./services/DeclarationProcessingService";
import globalCommunicationListener from '../../GlobalCommunicationListener';
import {LoketModule} from "./services/LoketModule";
import {VisionPlannerModule} from "./services/VisionPlannerModule";

class TaxModule {
    constructor() {
        this.knownHosts = new KnownHosts();
        this.hixApi = new HixApi();
        this.declarationProcessingService = new DeclarationProcessingService(this.hixApi)

        this.registerNavigatorListeners({
            url: [
                {hostSuffix: "loket.nl"},
                {hostSuffix: ".loket.nl"},
                {hostEquals: "cloud.visionplanner.nl"},
            ]
        });

        this.mappingClasses = new Map([
            ["XML", XML.prototype],
            ["XBRL", XBRL.prototype],
            ["PDF", PDF.prototype],
        ])

        this.availableModules = {
            loket_tasks: LoketModule,
            visionplanner_tasks: VisionPlannerModule,
        };

        globalCommunicationListener.addListener('process_tax_data', this.messageListener.bind(this))
    }

    /**
     * Logic
     */
    async messageListener(message) {
        try {
            const declaration = this.parseMessage(message)

            // handling

            const response = await this.declarationProcessingService.processDeclaration(declaration)

            if (response.data.notification) {
                return response.data.notification;
            }
        } catch (e) {
            throw e
        }
    }


    parseMessage(message) {
        const parsedJson = JSON.parse(message.data, (key, value) => {
            if (key === 'data') {
                value.map(dataValue => {
                    const mappingClass = this.mappingClasses.get(dataValue['__type__'])

                    return Object.setPrototypeOf(dataValue, mappingClass);
                })
            }

            return value;
        })

        Object.setPrototypeOf(parsedJson, TaxScrapingData.prototype)

        return parsedJson
    }

    registerNavigatorListeners(urlFilter) {
        browser.webNavigation.onDOMContentLoaded.addListener(this.navigationListener.bind(this), urlFilter);
    }

    async navigationListener(details) {
        const hostname = this.knownHosts.getHostnameOfUrl(details.url);
        const platformType = this.findPlatformType(hostname);

        if (!await this.checkUserIsAbleToUse(platformType)) return;

        let module = new this.availableModules[platformType]();
        module.start(details);

        console.debug(`TaxModule:: Injected TaxModule into ${platformType}`)
    }

    async checkUserIsAbleToUse(platformType) {
        const servicesResponse = await this.hixApi.findAvailableServicesForUser();

        const services = servicesResponse.data.data;

        for (const service of services) {
            if (service.reference_name === platformType && service.enabled) {
                return true;
            }
        }

        return false;
    }

    findPlatformType(platform) {
        if (platform.endsWith('loket.nl')) {
            return 'loket_tasks'
        }
        if (platform.endsWith('visionplanner.nl')) {
            return 'visionplanner_tasks'
        }
    }
}

export default new TaxModule() // Singleton

function dispatchTaxDetectorStart(platform) {
    document.dispatchEvent(new CustomEvent('tax_detector_start',  {
        detail: {platform: platform}
    }));
}