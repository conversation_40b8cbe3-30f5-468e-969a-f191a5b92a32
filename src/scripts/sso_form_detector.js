import browser from 'webextension-polyfill';

class SSOFormDetector {

  constructor() {

    console.log('Init form detector');

    this.results = {};

    this.instructionsTemplate = `                    'instructions' => [
                        'step1' => ['selector' => '{{usernameSelector}}', 'func' => 'setvalue', 'value' => '{{$username}}'],\n\
                        'step2' => ['selector' => '{{passwordSelector}}', 'func' => 'setvalue', 'value' => '{{$password}}'],\n\
                        'step3' => ['selector' => '{{passwordSelector}}', 'func' => 'attr', 'value' => ['type', 'hidden']],\n\
                        'step4' => ['selector' => '{{submitSelector}}', 'func' => 'click'],
                    ],
`;

    String.prototype.replaceAll = function (search, replacement) {
      return this.split(search).join(replacement);
    };

  }

  initFormDetector() {
    return 'SSOFormDetector successfully inserted.';
  }

  isHidden(el) {
    return (el.offsetParent === null)
  }

  /**
   * Add an entry to the log.
   */
  writeLog(entry) {
    console.log(entry);
    this.results.log.push(entry);
  }

  /**
   * Used dynamically in SSO Invisible Hand
   */
  extractInformation() {
    console.log('Extracting information...');
    let detector = new SSOFormDetector();
    detector.findForms();
    detector.createSelectors();
    detector.testSelectors();
    detector.results.template = detector.createInstructionsTemplate();
    detector.sendResults();
  }

  /**
   * Create small code snippet that can be used to generate a widget.
   */
  createInstructionsTemplate() {
    let template = this.instructionsTemplate;
    template = template.replaceAll('{{usernameSelector}}', this.results.selectors.username);
    template = template.replaceAll('{{passwordSelector}}', this.results.selectors.password);
    template = template.replaceAll('{{submitSelector}}', this.results.selectors.submit);
    return template;
  }

  findForms() {
    console.log('findForms() called');

    this.results.formIndex = null;
    this.results.log = [];
    this.results.slots = {
      username: null,
      password: null,
      submit: null
    };

    this.results.elements = [];

    let forms = document.forms;
    this.results.forms = [];

    for (let i in forms) {
      let form = forms[i];

      if (typeof form != 'object') {
        continue;
      }

      this.results.forms.push(form.outerHTML);

      if (this.isHidden(form)) {
        this.writeLog('Skipping form. Is not visible.');
        continue;
      }

      if (this.results.formIndex !== null) {
        this.writeLog('Skipping form. Already found fields.');
        continue;
      }

      for (let j in form.elements) {
        let el = form.elements[j];
        console.log(el);
        //elements array contains objects and functions, we only want objects.
        if (typeof el != 'object') {
          continue;
        }

        this.results.elements.push(el.outerHTML);

        if (this.seemsUsernameField(el) && this.results.slots.password === null) //before password field
        {
          this.results.slots.username = el;
          this.results.formIndex = i;
          this.writeLog('Found potential username field.');
        } else if (this.seemsPasswordField(el)) {
          this.results.slots.password = el;
          this.results.formIndex = i;
          this.writeLog('Found potential password field.');
        } else if (this.seemsSubmitField(el) && this.results.slots.password !== null) //after password field
        {
          this.results.slots.submit = el;
          this.results.formIndex = i;
          this.writeLog('Found potential submit field.');
        }
      }
    }
  }

  seemsUsernameField(el) {

    if (!el.hasAttribute('type')) {
      return 0;
    }

    let tag = el.tagName.toLowerCase();
    let type = el.getAttribute('type').toLowerCase();

    if (tag === 'input' && (type === 'text' || type === 'email')) {
      return 1;
    }
    return 0;
  }

  seemsPasswordField(el) {
    if (!el.hasAttribute('type')) {
      return 0;
    }

    let tag = el.tagName.toLowerCase();
    let type = el.getAttribute('type').toLowerCase();

    if (tag === 'input' && type === 'password') {
      return 1;
    }

    return 0;
  }

  seemsSubmitField(el) {
    let tag = el.tagName.toLowerCase();
    if (tag === 'input' && el.hasAttribute('type')) {
      let type = el.getAttribute('type').toLowerCase();
      if (type === 'submit' || type === 'button' || type === 'image') {
        return 1;
      }
    } else if (tag === 'button') {
      return 1;
    }

    return 0;
  }

  /**
   * Attempt to create a string that is a unique selector for the element.
   *
   * @todo Maybe integrate a library like https://github.com/antonmedv/finder/
   * @return string CSS/JS compatible element selector
   */
  createSelector(el) {
    let selector = '';
    let parent = el.parentNode;
    while (selector === '') {
      let parentTag = parent.tagName.toLowerCase();
      if (parentTag === 'body' || parentTag === 'html') {
        this.writeLog('Unable to find form element.');
        break;
      }

      if (parentTag === 'form') {
        if (parent.hasAttribute('id')) {
          selector = 'form#' + parent.getAttribute('id').replaceAll('.', '\\.').replaceAll(':', '\\:');
        } else if (this.results.forms.length > 1) {
          let matches = 0;
          selector = 'form';
          if (parent.hasAttribute('class')) {

            let classes = parent.getAttribute('class').split(' ');
            for (let i in classes) {
              selector += '.' + classes[i];
              matches = document.querySelectorAll(selector).length;
              if (matches === 1) {
                break;
              }
            }
          }

          if (this.results.forms.length === matches) {
            this.writeLog('Form selector "' + selector + '" matches ' + matches + ' forms. Using "form" instead.');
            selector = 'form';
          }
        } else {
          selector = 'form';
        }
      }
      parent = parent.parentNode;
    }

    let tag = el.tagName.toLowerCase();

    if (el.hasAttribute('id')) {
      //escape special characters . and : with double backslash
      selector += ' ' + tag + '#' + el.getAttribute('id').replaceAll('.', '\\.').replaceAll(':', '\\:');
    } else if (el.hasAttribute('name')) {
      selector += ' ' + tag + '[name="' + el.getAttribute('name') + '"]';
    } else if (el.hasAttribute('type')) {
      selector += ' ' + tag + '[type="' + el.getAttribute('type') + '"]';
    } else {
      selector += ' ' + tag;
    }

    return selector;
  }

  createSelectors() {
    this.results.selectors = {};
    for (let i in this.results.slots) {
      if (this.results.slots[i] !== null && typeof this.results.slots[i] == 'object') {
        this.results.selectors[i] = this.createSelector(this.results.slots[i]);
      } else {
        this.writeLog('Cannot create selector for ' + i);
      }
    }
  }

  /**
   * Test if the generated selectors match exactly one element for every slot and highlight them.
   */
  testSelectors() {
    if (typeof this.results.selectors.username == 'string') {
      let usernameElement = this.testSelector(this.results.selectors.username);
      if (usernameElement !== null && typeof usernameElement == 'object') {
        usernameElement.style.border = 'dotted 2px #f00';
      } else {
        this.writeLog('Selector for username does not match.');
      }
    }

    if (typeof this.results.selectors.password == 'string') {
      let passwordElement = this.testSelector(this.results.selectors.password);
      if (passwordElement !== null && typeof passwordElement == 'object') {
        passwordElement.style.border = 'dotted 2px #0f0';
      } else {
        this.writeLog('Selector for password does not match.');
      }
    }

    if (typeof this.results.selectors.submit == 'string') {
      let submitElement = this.testSelector(this.results.selectors.submit);
      if (submitElement !== null && typeof submitElement == 'object') {
        submitElement.style.border = 'dotted 2px #00f';
      } else {
        this.writeLog('Selector for submit does not match.');
      }
    }
  }

  /**
   * Test if a selector matches exactly one element.
   */
  testSelector(selector) {

    if (typeof selector != 'string') {
      this.writeLog('Invalid selector type.');
      return null;
    }

    if (selector === '') {
      this.writeLog('Empty selector.');
      return null;
    }

    let matches = document.querySelectorAll(selector);

    if (matches.length === 0) {
      this.writeLog('Zero matching elements.');
    } else if (matches.length === 1) {
      return matches[0];
    } else {
      //color ambiguous matches orange.
      for (let i in matches) {
        if (typeof matches[i] == 'object') {
          matches[i].style.border = 'dotted 2px #fd2';
        }
      }
      this.writeLog('Too many matches: ' + matches.length);
    }

    return null;
  }

  /**
   * Convert DOM node values to string so that they can be sent as a message.
   */
  stringifyResults(results) {
    if (results.slots) {
      for (let i in results.slots) {
        if (results.slots[i] !== null && typeof results.slots[i] == 'object') {
          results.slots[i] = results.slots[i].outerHTML;
        }
      }
    }

    return results;
  }

  /**
   * Get three elements that seem the most likely form elements.
   */
  getSlotElements() {
    this.findForms();
    return this.results.slots;
  }

  sendResults() {

    this.writeLog('Attempting to send form detector results...');
    console.log(this.results);

    return browser.runtime.sendMessage({
      status: 'done',
      results: this.stringifyResults(this.results)
    }).catch((error) => {
      console.log('Error sending results message to extension:');
      console.log(error)
    });
  }
}

export default SSOFormDetector;
