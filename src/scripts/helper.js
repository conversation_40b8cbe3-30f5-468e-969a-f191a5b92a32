import {Log<PERSON>elper} from "./helpers/LogHelper";
class Helper {

    toMatchPattern(name) {
        return RegExp('^' + name, "i");
    }

    headerPresent(headers, name) {
        let pattern = this.toMatchPattern(name);
        for (let header of headers) {
            if (header.name.match(pattern)) {
                return true;
            }
        }
        return false;
    }

    headerGet(headers, name) {
        let pattern = this.toMatchPattern(name);
        for (let header of headers) {
            if (header.name !== undefined && header.name.match(pattern)) {
                return header.value;
            }
        }
        return "";
    }

    headerAdd(headers, name, value) {
        LogHelper.debug("HeaderHelper headerAdd: " + name);
        headers.push({
            "name": name,
            "value": value
        });
    }

    headerChange(headers, name, value) {
        LogHelper.debug("HeaderHelper headerChange: " + name);
        let pattern = this.toMatchPattern(name);
        for (let header of headers) {
            if (header.name.match(pattern)) {
                header.value = value;
            }
        }
    }

    headerChangeOrAdd(headers, name, value) {
        if (this.headerPresent(headers, name)) {
            this.headerChange(headers, name, value);
        } else {
            this.headerAdd(headers, name, value);
        }
    }

    headerChangeOrAddIfNotEmpty(headers, name, value) {
        if (value.length > 0) {
            return this.headerChangeOrAdd(headers, name, value);
        }
    }

    headerDowngradeSameSite(headers) {
        let pattern = this.toMatchPattern("Set-Cookie");
        for (let header of headers) {
            if (header.name.match(pattern)) {
                LogHelper.debug("HeaderHelper headerDowngradeSameSite: " + header.name);
                header.value = header.value.replace("SameSite=strict", "");
                header.value = header.value.replace("SameSite=lax", "");
            }
        }
    }

    /**
     * Removes headers from the Array headers where they match RegExp(name,"i")
     * @param {Array} headers
     * @param {String} name
     * @returns {Array}
     */
    headerRemove(headers, name) {
        let pattern = this.toMatchPattern(name), filteredHeaders = [];
        for (let header of headers) {
            if (header.name.match(pattern) == null) {
                filteredHeaders.push(header);
            } else {
                LogHelper.debug("HeaderHelper headerRemove: " + header.name);
            }
        }
        return filteredHeaders;
    }

    /**
     * Finds in headers where name starts with filter (converted to RegExp)
     * and replaces the header where the name equals the end part of the name to with
     * the new value of the current filtered header
     *
     * eg.: filter = 'X-SL-Cookie', will find the Cookie header in headers and replaces
     * (or adds in case not existing)  with the value of the X-SL-Cookie header
     *
     * @param {Array} headers
     * @param {String} filter
     * @returns {Array}
     */
    replaceSLHeaders(headers, filter) {
        let pattern = this.toMatchPattern(filter + '-(.*)'), filteredHeaders = headers;
        for (let header of headers) {
            let headerParts = header.name.match(pattern);
            if (headerParts) {
                LogHelper.debug("replaceSLHeaders: " + header.name);
                this.headerChangeOrAdd(filteredHeaders, headerParts[1], header.value);
            }
        }
        return filteredHeaders;
    }

    /**
     * Gets the current unixtime stamp in seconds
     * @returns {number}
     */
    getCurrentTimeInSeconds() {
        return Math.round((new Date()).getTime() / 1000);
    }

    /**
     *  Retuns if url is from hix domain and is SSO url
     *  @param {String} url
     *  @return RegExpMatchArray | null
     */
    isSSOUrl(url) {
        return url && url.match(/https:\/\/.*\/(access\/[0-9]+|user_widget\/[0-9]+\/start).*/);
    }

    /**
     * Wraps a promise returning function in a try/catch block
     * returning a rejected promise when an exception occurs
     * @param fn
     * @returns {Promise<void>|*}
     */
    tryCatchPromise(fn) {
        try {
            return fn();
        } catch (e) {
            LogHelper.error(e);
            console.error(e);
            return Promise.reject(e);
        }
    }
}

export default new Helper(); // singleton
