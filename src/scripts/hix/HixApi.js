import {BaseHixApi} from './BaseHixApi';

class Hix<PERSON><PERSON> extends BaseHixApi {
    async generateLoketTask(data) {
        return (await this.axios()).post('/api/task/1.0/generate/loket', data)
    }

    async generateVisionplannerTask(data) {
        return (await this.axios()).post('/api/task/1.0/generate/visionplanner', data)
    }

    async findAvailableServicesForUser() {
        return (await this.axios()).get('/api/task/1.0/services')
    }
}

export {
    HixApi
};