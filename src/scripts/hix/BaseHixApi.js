import Axios, {AxiosError, AxiosResponse} from 'axios';
import {BackgroundNotificationHelper} from '../tax/inject/helpers/BackgroundNotificationHelper';
import {ResponseHelper} from "../tax/inject/helpers/ResponseHelper";
import browser from "webextension-polyfill";
import {IconHelper} from "../helpers/IconHelper";

class BaseHixApi {

    /**
     * @returns AxiosInstance
     * @protected
     */
     async axios() {
        const hostname =  (await browser.storage.local.get('host/hostname/domain'))['host/hostname/domain'];
        if (!hostname) {
            throw new Error("Hostname was not defined")
        }

        const axiosInstance = Axios.create({
            baseURL: `https://${hostname}`,
            withCredentials: true
        });

        axiosInstance.interceptors.response.use(this.onAxiosSuccess, this.onAxiosReject)

        return axiosInstance
    }

    /**
     * @private
     * @param {AxiosError} error
     * @returns {Promise<never>}
     */
    async onAxiosReject(error) {
        ResponseHelper.handleHeaders(error?.response?.headers).then((updateInfo) => {
            if (updateInfo.updated) {
                if (updateInfo.shouldInform) {
                    BackgroundNotificationHelper.sendSettingsChangedMessage()
                }
            } else {
                if (error?.response?.status === 401) {
                    IconHelper.setInactive();
                    // Call the HixExtension singleton that has already been initialized.
                    if (browser.HixExtension) {
                        browser.HixExtension.checkUserAndReloadModules();
                    }
                } else {
                    if (error.response.data.name === 'not_enabled_exception') {
                        // Don't show error message when service is not enabled
                        browser.OpenQuestionsModule.updateSettings();
                    } else if (error?.response?.data.message) {
                        BackgroundNotificationHelper.sendToast('error', error?.response?.data.message);
                    } else if (error?.response?.data.original && error?.response?.data.original.message) {
                        BackgroundNotificationHelper.sendToast('error', error?.response?.data.original.message);
                    } else if (error?.response?.data.summary) {
                        BackgroundNotificationHelper.sendToast('error', error?.response?.data.summary);
                    } else {
                        BackgroundNotificationHelper.sendGenericError();
                    }
                }
            }
        })
        return Promise.reject(error);
    }

    /**
     *
     * @param {AxiosResponse} response
     * @returns {*}
     */
    async onAxiosSuccess(response) {
        ResponseHelper.handleHeaders(response.headers).then((updateInfo) => {
            if (updateInfo.updated) {
                if (updateInfo.shouldInform) {
                    BackgroundNotificationHelper.sendSettingsChangedMessage()
                }
            }
        })
        return response;
    }
}

export {
    BaseHixApi
}