import {BaseHixApi} from "./BaseHixApi";
import browser from "webextension-polyfill";
import {LogHelper} from "../helpers/LogHelper";

class BrowserExtensionApi extends BaseHixApi {

    async getUserInfo(){
        let url = '/api/v1/browser_extension/user_info.json';

        return (await this.axios()).get(url).then( async (response) => {
            return response.data;
        }).catch((e) => {
            LogHelper.error('BrowserExtensionApi failed to load user info from ' + url, e);
            return null;
        });
    }

    async updateSettings(timestamp) {
        let url = '/api/v1/browser_extension/settings.json';

        return (await this.axios()).get(url).then( async (response) => {
            if (!response.data.settings) {
                LogHelper.warn('BrowserExtensionApi unable to retrieve settings from ' + url);
                return null;
            }

            let settings = response.data.settings;

            await browser.storage.local.set({'settings': JSON.stringify(settings)});
            await browser.storage.local.set({'settings_timestamp': timestamp});

            return {
                timestamp: timestamp,
                settings: settings
            };
        }).catch((e) => {
            LogHelper.error('BrowserExtensionApi failed to load settings from ' + url, e);
            return null;
        });
    }
}

export {BrowserExtensionApi};