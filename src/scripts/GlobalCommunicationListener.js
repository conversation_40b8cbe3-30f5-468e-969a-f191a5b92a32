import browser from 'webextension-polyfill';
import {LogHelper} from './helpers/LogHelper.js'

class GlobalCommunicationListener {
  constructor() {
    this.mappedListeners = new Map();

    browser.runtime.onMessage.addListener(async function (message) {
      const eventCallback = this.mappedListeners.get(message.action);
      if (eventCallback) {
        return await eventCallback(message);
      }
    }.bind(this))
  }

  addListener(connectionTag, callback) {
    this.mappedListeners.set(connectionTag, callback);
    LogHelper.debug("GlobalCommunicationListener added listener " + connectionTag);
  }
}

export default new GlobalCommunicationListener(); //singleton