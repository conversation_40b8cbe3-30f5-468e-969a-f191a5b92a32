import {LogHelper} from "./helpers/LogHelper";

/**
 * Holds a set of known host names in memory
 * @param debug
 * @constructor
 */
class KnownHosts {
    constructor() {
        this.cache = new Set();
    }

    contains(url) {
        let hostname = this.getHostnameOfUrl(url);
        return this.containsHostname(hostname);
    }

    containsHostname(hostname) {
        if (!hostname) {
            return false;
        }
        let result = this.cache.has(hostname);
        LogHelper.debug("KnownHosts contains " + hostname + ": " + result);
        return result;
    }

    add(url) {
        let hostname = this.getHostnameOfUrl(url);
        if (!hostname || this.containsHostname(hostname)) {
            return;
        }
        LogHelper.debug("KnownHosts adding " + hostname);
        this.cache.add(hostname)
    }

    getHostnameOfUrl(url) {
        try {
            let urlObj = (url && new URL(url));
            let hostname = ((urlObj && urlObj.hostname) || '').toLowerCase();
            return hostname || '';
        } catch (e) {
            LogHelper.error('KnownHosts error while getting hostname from URL ' + url);
            console.warn(e);
            return '';
        }
    }
}

export default KnownHosts;