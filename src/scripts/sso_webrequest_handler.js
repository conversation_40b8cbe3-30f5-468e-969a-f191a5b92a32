import helper from './helper';
import browser from 'webextension-polyfill';
import {LogHelper} from "./helpers/LogHelper";

class SSOWebRequestHandler {

  /**
   * SSOWebRequestHandler Class. Deals with webrequest and is intended to run
   * per tabId
   * @param {HixExtension} HixExtension
   * @param {int} tabId
   */
  constructor(HixExtension, tabId) {
    this.HixExtension = HixExtension;
    this.tabId = tabId;

    // for valid referer matching, see https://developer.mozilla.org/en-US/Add-ons/WebExtensions/Match_patterns
    this.requestStack = [];                 //current valid response listeners, array
    this.maxAuthExecutionTime = 30;         // seconds!
    this.requestStartTime = 0;              // start time of the whole SSO request handling

    LogHelper.debug('SsoWebRequestHandler Creating listeners....');
    this.createMyListeners();
  }

  /**
   * This function is necessary to create new unique functions as the
   * browser.webRequest expects unique function identifiers.
   * This also helps to update or delete listeners easier
   */
  createMyListeners() {
    this.myCORSRequestListener = (e) => {
      LogHelper.debug('SsoWebRequestHandler CORS Request made!');
      return this.getInstance(e.tabId).onCORSRequest(e);
    };

    this.myCORSResponseListener = (e) => {
      LogHelper.debug('SsoWebRequestHandler CORS Response!');
      return this.getInstance(e.tabId).onCORSResponse(e);
    };

    this.myOnNavigationCompletedListener = (e) => {
      LogHelper.debug('SsoWebRequestHandler CORS nav completed!');
      return this.getInstance(e.tabId).onNavigationCompleted(e);
    };
  }

  /**
   * Function called on Initial Request. Main function
   * @param {Request} e
   */
  onInitialRequest(e) {
    this.addBeforeSendHeadersListener(this.myCORSRequestListener, '<all_urls>');
    this.addHeadersReceivedListener(this.myCORSResponseListener, '<all_urls>');
    this.requestStartTime = helper.getCurrentTimeInSeconds();
  }

  /**
   * Process a CORS request. check if from correct SSO Url
   *
   * Check the timestamp (max 30 seconds) of the listener
   * @param {Request} e request object
   * @returns {object}
   */
  onCORSRequest(e) {

    LogHelper.debug('SsoWebRequestHandler onCORSRequest in tabId #' + e.tabId);

    this.pushRequest(e);

    let headers = helper.replaceSLHeaders(e.requestHeaders, 'X-SL-Request');

    // remove the X-SL-* headers
    headers = helper.headerRemove(headers, 'X-SL-');

    return {requestHeaders: headers};
  }

  isWithinValidExecutionTimeFrame() {
    return (helper.getCurrentTimeInSeconds() - this.getRequestTime()) <= this.maxAuthExecutionTime;
  }

  /**
   * CORS request Response handler. Sets AJAX request parameter and modifies CORS headers
   * @param {Request} e
   * @returns {Object}
   */
  onCORSResponse(e) {
      LogHelper.debug('SsoWebRequestHandler onCORSResponse ' + e.method.toString() + ' ' + e.url.toString());

    if (!this.isWithinValidExecutionTimeFrame()) {
      // we are done!
      LogHelper.debug('SsoWebRequestHandler Execution time expired. Destroying...');
      this.destruct();
    }

    // add listener to initiate destruction
    this.addNavigationCompletedListener(this.myOnNavigationCompletedListener);

    // ..but also set the Set-Cookie header according to the special X-SL-Response-Set-Cookie header
    let headers = e.responseHeaders;
    helper.headerChangeOrAddIfNotEmpty(headers, 'Set-Cookie', this.getRequestHeader(e.requestId, 'X-SL-Response-Set-Cookie'));
    helper.headerChangeOrAddIfNotEmpty(headers, 'Access-Control-Allow-Credentials', 'true');
    helper.headerChangeOrAddIfNotEmpty(headers, 'Access-Control-Allow-Origin', this.getRequestHeader(e.requestId, 'origin'));
    helper.headerChangeOrAddIfNotEmpty(headers, 'Access-Control-Allow-Headers', this.getRequestHeader(e.requestId, 'Access-Control-Request-Headers'));
    helper.headerChangeOrAddIfNotEmpty(headers, 'Access-Control-Allow-Methods', this.getRequestHeader(e.requestId, 'Access-Control-Request-Method'));
    helper.headerDowngradeSameSite(headers);

    return {responseHeaders: headers};
  }

  /**
   * When navigation completed, to another url than the main hix url..
   * @param {Event} e
   */
  onNavigationCompleted(e) {
    let url = e.url.toString();
    if (!helper.isSSOUrl(url) && url.match(/^https?:.*/)) {
      LogHelper.debug('SsoWebRequestHandler Navigation Completed. Removing SSOWebRequest ' + url);
      this.destruct();
    }
  }

  /**
   * Short function to remove all the listeners. Should be called on removal of
   * this class, or when the maximum execution time has passed.
   */
  removeAllWebRequestListeners() {
    LogHelper.debug('SSOWebRequestHandler Removing all listeners...');
    this.removeHeadersReceivedListener(this.myCORSResponseListener);
    this.removeBeforeSendHeaderListener(this.myCORSRequestListener);
    this.removeNavigationCompletedListener(this.myOnNavigationCompletedListener);
  }

  /**
   * Destructor function (not understand by Javascript)
   * @returns {void}
   */
  destruct() {
    this.removeAllWebRequestListeners();
    this.HixExtension.moveSSOWebRequestToBinByTabId(this.tabId);
  }

  /**
   * Get the current requestStartTime
   * @returns {undefined}
   */
  getRequestTime() {
    return this.requestStartTime;
  }

  /**
   * Add listener to onHeaderReceived response listeners
   * @param {Function} listener
   * @param {string} url
   */
  addHeadersReceivedListener(listener, url) {
    if (browser.webRequest.onHeadersReceived.hasListener(listener)) {
      LogHelper.debug('SsoWebRequestHandler HeaderReceived Listener already set, ignoring... (name:' + listener.name + ')');
      return;
    }

    LogHelper.debug('SsoWebRequestHandler Add listener for ' + url);

    let extraInfoSpec = ['responseHeaders'];
    if (browser.webRequest.OnBeforeSendHeadersOptions.hasOwnProperty('EXTRA_HEADERS')) {
      extraInfoSpec.push('extraHeaders');
    }

    browser.webRequest.onHeadersReceived.addListener(
      listener,
      {
        urls: [url],
        types: ['xmlhttprequest']
      },
      extraInfoSpec
    );
  }

  /**
   * Set a wide open listener, checks the origin of the request.
   * This request should only be enabled within a certain time frame.
   * This function should check the headers of the request, if they relate to the
   * allowed domain
   * @param {Function} listener
   * @param {string} url conform: https://developer.mozilla.org/en-US/Add-ons/WebExtensions/Match_patterns
   */
  addBeforeSendHeadersListener(listener, url) {
    if (browser.webRequest.onBeforeSendHeaders.hasListener(listener)) {
      LogHelper.debug('SsoWebRequestHandler BeforeSendHeader Listener already set, ignoring... (name:' + listener.name + ')');
      return;
    }

    let extraInfoSpec = ['requestHeaders'];
    if (browser.webRequest.OnBeforeSendHeadersOptions.hasOwnProperty('EXTRA_HEADERS')) {
      extraInfoSpec.push('extraHeaders');
    }

    browser.webRequest.onBeforeSendHeaders.addListener(
      listener,
      {
        urls: [url], // filtering in the Generic Request listener
        types: ['xmlhttprequest']
      },
      extraInfoSpec
    );
  }

  /**
   * Adds a Navigation Completed listener to the webNavigation
   * @param {Function} listener
   */
  addNavigationCompletedListener(listener) {
    if (browser.webNavigation.onCompleted.hasListener(listener)) {
      LogHelper.debug('SsoWebRequestHandler Navigation onCompleted Listener already set, ignoring... (name:' + listener.name + ')');
      return;
    }
    browser.webNavigation.onCompleted.addListener(listener);
  }

  /**
   * On the fly removing the listener for onHeadersReceived
   * @param {Function} listener
   */
  removeHeadersReceivedListener(listener) {
    LogHelper.debug('SsoWebRequestHandler Remove Response Listener: ' + listener.name);
    browser.webRequest.onHeadersReceived.removeListener(listener);
  }

  /**
   * On the fly removing the listener for onBeforeSendHeaders
   * @param {Function} listener
   */
  removeBeforeSendHeaderListener(listener) {
    LogHelper.debug('SsoWebRequestHandler Remove BeforeSendHeader Listener: ' + listener.name);
    browser.webRequest.onBeforeSendHeaders.removeListener(listener);
  }

  /**
   * On the fly removing the listener for onCompleted
   * @param {Function} listener
   */
  removeNavigationCompletedListener(listener) {
    LogHelper.debug('SsoWebRequestHandler Remove Navigation Completed Listener: ' + listener.name);
    browser.webNavigation.onCompleted.removeListener(listener);
  }

  /**
   * Push the info of this request to the stack
   *
   * Requestinfo is an object, holding following keys;
   * "requestId", "url", "originUrl", "method", "tabId", "type", "timeStamp", "frameId", "parentFrameId", "requestHeaders"
   *
   * however, originUrl is not available in chrome
   * @param request
   */
  pushRequest(request) {
    LogHelper.debug('SsoWebRequestHandler Saving request ' + request.requestId);
    this.requestStack[request.requestId] = JSON.parse(JSON.stringify(request));
  }

  /**
   * Get's the request by the request ID
   * @param {int} requestId
   * @returns {Array|Boolean}
   */
  getRequestById(requestId) {
    if (typeof this.requestStack[requestId] !== 'undefined') {
      return this.requestStack[requestId];
    }
    return false;
  }

  /**
   * Get the request header from the stack, identified by the request Id and
   * header name
   * @param {int} requestId
   * @param {string} headerName
   * @returns {string}
   */
  getRequestHeader(requestId, headerName) {
    let request = this.getRequestById(requestId);
    if (request) {
      return helper.headerGet(request.requestHeaders, headerName);
    }
    LogHelper.debug('SsoWebRequestHandler Could not find request: ID#' + requestId);
    return '';
  }

  /**
   * Return the Instance of the App
   * @returns {SSOWebRequestHandler}
   */
  getInstance(tabId) {
    if (this.HixExtension.getSSOWebRequestByTabId(tabId)) {
      return this.HixExtension.getSSOWebRequestByTabId(tabId);
    }
    // should never reach this point!
    LogHelper.error('SsoWebRequestHandler could not find nor create SsoOWebRequestHandler!');
  }
}

export default SSOWebRequestHandler;