import browser from 'webextension-polyfill';
import SSOFormDetector from './sso_form_detector'
import {LogSender} from "./helpers/LogSender";

/**
 * The DOM Fondler is injected into target login page(s) by the invisible hand as a socalled content script.
 * After receiving instructions the D<PERSON> Fondler manipulates the DOM and reports back the result to the invisible hand.
 */

class SSODomFondler {

    constructor() {
        this.stepLog = [];
        this.pageId = null;
        this.stepCount = 0;
        this.startTime = null;
        this.debugEnabled = false;
        this.selectors = {
            selector: (element, selector) => {
                return element.querySelectorAll(selector);
            },
            iframe: (element, selector) => {
                return element.querySelector(selector).contentWindow.document;
            },
            shadowRoot: (element, selector) => {
                return element.querySelector(selector).shadowRoot;
            }
        };

        try {
            this.initBrowserConsoleListener();
        } catch (e) {
            console.error(e);
            LogSender.error('SSODomFondler constructor error');
        }
    }

    initFondler() {
        LogSender.debug('SSODomFondler::initFondler');
        browser.runtime.onMessage.addListener((message) => {
            if (message.action === 'init') {
     //           this.debugEnabled = this.debugEnabled || message.meta.debug;
                LogSender.info(JSON.stringify(message));
                this.startTime = this.getMilliTime();
                this.showLoaderOverlay(message.meta);
                this.pageId = message.page;
                if (message.method === 'autologin') {
                    this.autologin(message.settings);
                } else {
                    let instructionArray = Object.values(message.instructions); //convert to array
                    this.stepCount = instructionArray.length;
                    this.followInstructions(instructionArray);
                }
            }
        });
        return 'SSODomFondler successfully inserted.';
    }

    /**
     * Get amount of milliseconds since midnight (UTC)
     * Used for measuring speed/performance.
     * @return int
     */
    getMilliTime() {
        return (new Date()).getTime();
    }

    /**
     * Add an entry to the step log.
     */
    addStepLog(entry) {
        LogSender.debug('SSODomFondler::addStepLog');
        LogSender.debug(JSON.stringify(entry));
        entry.elapsed = this.getMilliTime() - this.startTime;
        this.stepLog.push(entry);

        switch (entry.status.toLowerCase()) {
            case 'ok':
            case 'success':
                console.log({elapsed: entry.elapsed, message: entry.message});
                break;
            case 'error':
                console.error({elapsed: entry.elapsed, message: entry.message});
                break;
        }
    }

    initBrowserConsoleListener() {

        LogSender.debug('SSODomFondler::initBrowserConsoleListener');

        let _log = console.log,
            _warn = console.warn,
            _error = console.error;

        let sendLogToExtension = (method, params) => {

            let cleanedParams = [];
            for (let i = 0; i < params.length; i++) {
                cleanedParams.push(params[i]);
            }

            return browser.runtime.sendMessage({
                action: 'log',
                method: method,
                params: cleanedParams
            });
        };

        console.log = function () {
            try {
                sendLogToExtension('log', arguments);
                if (this.debugEnabled) {
                    _log.apply(console, arguments);
                }
            } catch (e) {
                _warn(e);
            }
        };

        console.warn = function () {
            try {
                sendLogToExtension('warn', arguments);
                if (this.debugEnabled) {
                    _warn.apply(console, arguments);
                }
            } catch (e) {
                _error(e);
            }
        };

        console.error = function () {
            try {
                sendLogToExtension('error', arguments);
                if (this.debugEnabled) {
                    _error.apply(console, arguments);
                }
            } catch (e) {
                _error(e);
            }
        };
    }

    /**
     * Add loader overlay over page while the invisible hand does its work.
     * Extra strong styles are needed to make sure it overwrites everything else on the page.
     */
    showLoaderOverlay(meta) {
        if (!meta.showloader) {
            //do not show loader
            return;
        }

        if (meta.loading === undefined) {
            meta.loading = 'Loading...';
        }

        if (meta.color === undefined) {
            meta.color = '#ff0000';
        }

        let pageLoader = document.getElementById('sl-pageloader');
        if (pageLoader !== null) {
            pageLoader.style.display = 'block';
        } else {
            document.head.innerHTML += '<link rel="stylesheet" href="https://securelogin.securelogin.nu/css/pageloader.css" type="text/css">';
            document.body.innerHTML += ` <div id="sl-pageloader" class="page-loader" style="z-index: 99999;">
      <div class="preloader pls-blue pl-xl" style="top: 38%;">
      <svg class="pl-circular" viewBox="25 25 50 50" width="100" height="100">
        <circle class="plc-path" cx="50" cy="50" r="20" style="stroke: ${meta.color} !important;" />
      </svg>
      <p style="font-family: Roboto, Arial, sans-serif; text-align: center; top: 80px; margin-left: -110px; min-width: 320px;">${meta.loading}<br><b>${meta.displayName}</b><br><span class="progress-percentage">0%</span></p>
      </div>
    </div>`;
        }
    }

    /**
     * Hide loader, if it exists.
     */
    hideLoaderOverlay() {
        let pageLoader = document.getElementById('sl-pageloader');
        if (pageLoader) {
            LogSender.info('SSODomFondler Loader timeout.');
            pageLoader.style.display = 'none';
        }
    }

    /**
     * Follow list of instructions contained in JSON data structure.
     */
    followInstructions(instructions) {

        LogSender.info('Following instructions!');
        LogSender.info(JSON.stringify(instructions));

        for (let i = 0; i < instructions.length; i++) {
            let instruction = instructions[i];
            /*
             * Wait is different from other instructions.
             * The current loop is aborted and the method followInstructions() is called recursively with the remaining instructions.
             */
            if (instruction.func === 'wait') {
                setTimeout((remaining) => {
                    //this.log('Waiting... ' + remaining.length + ' instructions remaining');
                    if (remaining.length > 0) {
                        this.followInstructions(remaining);
                    }
                }, instruction.value, instructions.slice(i + 1)); //pass remaining instructions as third parameter
                break;
            }

            if (instruction.func === 'redirect') {
                if (typeof instruction.value !== 'string') {
                    this.addStepLog({status: 'error', message: 'Unable to redirect because the url is not a string.'});
                    break;
                }

                window.location = instruction.value;
                this.addStepLog({status: 'OK', message: 'Successfully redirected'});
                break;
            }


            if (this.isCorrectInstruction(instruction)) {
                let elements = this.findElements(instruction.selector, instruction.iframe, instruction.shadowRoot);

                if (instruction.func === 'hcaptcha') {
                    let max_time = 60000;
                    let interval = 100;
                    instructions[i].value = instructions[i].value || 0;
                    if (instructions[i].value < max_time) {

                        if (elements.length === 0) {
                            this.followInstructions(instructions.slice(i + 1));
                        } else {
                            let response = elements[0].getAttribute(instructions[i].attribute);
                            if (response === '') {
                                setTimeout((remaining) => {
                                    if (remaining.length > 0) {
                                        this.followInstructions(remaining);
                                    }
                                }, interval, instructions.slice(i));
                            } else {
                                LogSender.info('data-hcaptcha-response ' + JSON.stringify(response));
                                this.followInstructions(instructions.slice(i + 1));
                            }
                        }
                    } else {
                        LogSender.error('hCaptcha failed. Selector: ' + instruction.selector);
                    }
                    break;
                }

                if (instruction.func === 'wait_for_element') {
                    let max_time = 60000;
                    let interval = 100;
                    instructions[i].value = instructions[i].value || 0;
                    if (instructions[i].value < max_time) {
                        if (elements.length > 0) {
                            this.followInstructions(instructions.slice(i + 1));
                        } else {
                            instructions[i].value += interval;
                            setTimeout((remaining) => {
                                if (remaining.length > 0) {
                                    this.followInstructions(remaining);
                                }
                            }, interval, instructions.slice(i));
                        }
                    } else {
                        console.error('Wait for element failed. Selector:', instruction.selector);
                    }
                    break;
                }

                if (this.isFunction(instruction.func)) {
                    if (elements.length === 0) {
                        let message = `The selector '${instruction.selector}' does not match any elements function: ${instruction.func}`;
                        this.addStepLog({status: 'error', message: message});
                    } else {
                        this.interact(elements, instruction.func, instruction.value);
                    }
                }
            }

            this.updateLoaderProgress(this.stepCount - (instructions.length - i));

            if ((instructions.length - 1) === i) {
                this.addStepLog({status: 'done', message: 'All steps executed.'});
                this.sendWorkDoneMessage();
                //hide loader after 5 seconds to display any potential error messages.
                setTimeout(() => this.hideLoaderOverlay(), 5000);
                break;
            }
        }
    }

    /**
     * Update progress in loader overlay.
     */
    updateLoaderProgress(step) {
        let percentage = Math.ceil((100 / this.stepCount) * step);
        let el = document.querySelector('.page-loader .progress-percentage');
        if (el) {
            el.textContent = percentage + '%';
        }
    }

    /**
     * Once all the work has been done, send a message to the extension.
     * The extension can then remove the instructions to prevent them from being executed infinitely
     * when login does not work and user is redirected back to the login page.
     * The message includes a log of the executed instructions and any errors that occured.
     */
    sendWorkDoneMessage() {

        LogSender.info('Attempting to send work done message...');

        return browser.runtime.sendMessage({
            action: 'work_done',
            status: 'done',
            started: this.startTime,
            sent: this.getMilliTime(),
            page: this.pageId,
            log: this.stepLog
        }).catch((error) => {
            LogSender.error('Error sending work done message to extension');
            // also put message in console
            console.error('Error sending work done message to extension:');
            console.error(error)
        });
    }

    /**
     * Automatically find and fill login form elements.
     */
    autologin(settings) {

        this.addStepLog({status: 'OK', message: 'Called autologin() method'});

        if (typeof (settings.username) !== 'string' || settings.username == '') {
            this.addStepLog({status: 'error', message: 'Username value is empty.'});
            return;
        }

        if (typeof (settings.password) !== 'string' || settings.password == '') {
            this.addStepLog({status: 'error', message: 'Password value is empty.'});
            return;
        }

        let detector = new SSOFormDetector();
        let slots = detector.getSlotElements();

        if (typeof slots.username !== 'object') {
            this.addStepLog({status: 'error', message: 'Invalid username element.'});
            return;
        }

        if (typeof slots.password !== 'object') {
            this.addStepLog({status: 'error', message: 'Invalid password element.'});
            return;
        }

        if (typeof slots.submit !== 'object') {
            this.addStepLog({status: 'error', message: 'Invalid submit element.'});
            return;
        }

        this.stepCount = 4;

        this.setvalue(slots.username, settings.username);
        this.updateLoaderProgress(1);
        this.setvalue(slots.password, settings.password);
        this.updateLoaderProgress(2);
        this.attr(slots.password, ['type', 'hidden']);
        this.updateLoaderProgress(3);
        this.click(slots.submit);

        this.sendWorkDoneMessage();

        //hide loader after 5 seconds to display any potential error messages.
        setTimeout(() => this.hideLoaderOverlay(), 5000);
    }

    /**
     *
     * @param selector
     * @param [iframeSelector]
     * @param [shadowSelector]
     * @returns {*}
     */
    findElements(selector, iframeSelector = null, shadowSelector = null) {
        try {
            let element = document;
            if (iframeSelector) {
                element = this.selectors.iframe(element, iframeSelector);
            }

            if (shadowSelector) {
                if (Array.isArray(shadowSelector)) {
                    for (let i = 0; i < shadowSelector.length; i++) {
                        element = this.selectors.shadowRoot(element, shadowSelector[i]);
                    }
                } else if (typeof shadowSelector === 'string') {
                    element = this.selectors.shadowRoot(element, shadowSelector);
                }
            }

            return this.selectors.selector(element, selector);
        } catch (e) {
            LogSender.warn('SSODomFondler findElements error');
            console.warn(e);
            return [];
        }
    }

    /**
     * Checks if the instruction contains everything and if the function exists
     * @param instruction
     * @returns {boolean}
     */
    isCorrectInstruction(instruction) {
        if (typeof instruction.func === 'undefined' || instruction.func == '') {
            this.addStepLog({
                status: 'error',
                message: 'Missing "func" attribute. Maybe a stray setting in the database?'
            });
            return false;
        }

        if (typeof instruction.selector === 'undefined' || instruction.selector == '') {
            this.addStepLog({status: 'error', message: 'Missing "selector" attribute.'});
            return false;
        }
        return true;
    }

    /**
     * Check if function exists
     * @param func function name
     * @returns {boolean}
     */
    isFunction(func) {
        if (typeof this[func] !== 'function') {
            this.addStepLog({status: 'error', message: 'Unknown function "' + func + '"'});
            return false;
        }
        return true;
    }

    /**
     * Execute an instruction on the page.
     * @param elements Array with elements selected by the selector
     * @param func String of function name which matches with one of the methods in this class.
     * @param value String/array with values to pass to the function.
     */
    interact(elements, func, value) {
        //The selector can match multiple elements, if so, apply to all elements.
        for (let i = 0; i < elements.length; i++) {
            this.addStepLog(this[func](elements[i], value));
        }
    }

    /**
     * Set the value of an <input> or <textarea> element.
     * @param element Reference to DOM element.
     * @param value String value for form field.
     */
    setvalue(element, value) {

        if (typeof value !== 'string') {
            let message = 'Value for the following element has to be of the type string:';
            return {status: 'error', message: message, element: element.outerHTML, type: (typeof value)};
        }
        let tag = element.tagName.toLowerCase();
        element.focus();

        if (tag === 'input' || tag === 'select') {
            element.value = value;
        } else if (tag === 'textarea') {
            element.textContent = value;
        } else {
            let message = 'The following element is not an input or textarea of which the value can be set:';
            return {status: 'error', message: message, element: element.outerHTML};
        }

        element.dispatchEvent(new KeyboardEvent('change')); //some JS validations like it when you trigger a change event
        element.dispatchEvent(new Event('input', {bubbles: true})); //for sites like angular the fields require an input event in order to detect if the field is filled
        element.blur();

        return {status: 'OK', 'message': 'Set field value'};
    }

    /**
     * Set value of attribute of element. Set attribute value to NULL to remove it.
     * @param element Reference to DOM element.
     * @param values array Two string values, first one with name of attribute, second one with desired value.
     */
    attr(element, values) {

        if (typeof values === 'string' || values.length !== 2 || typeof values[0] !== 'string') {
            let message = 'Values for the following element has to be two values to be a valid attribute';
            return {status: 'error', message: message, element: element.outerHTML};
        }

        if (typeof values[1] === 'string') {
            element.setAttribute(values[0], values[1]);
        } else if (values[1] === null) {
            element.removeAttribute(values[0]);
        }

        return {status: 'OK', message: 'Set attribute value'};
    }

    triggerEvent(element, value) {
        let evt = new Event(value);
        element.dispatchEvent(evt);
        return {status: 'OK', message: 'triggered event ' + value};
    }

    /**
     * Remove event handlers from origin if they interfere with us.
     * @param element Reference to DOM element.
     */
    clearEventHandlers(element) {
        element.outerHTML = element.outerHTML;
        return {status: 'OK', message: 'replaced outerHTML'};
    }

    /**
     * Remove event handlers from origin if they interfere with us.
     * @param element Reference to DOM element.
     */
    clearInnerEventHandlers(element) {
        element.innerHTML = element.innerHTML;
        return {status: 'OK', message: 'replaced innerHTML'};
    }

    /**
     * Trigger click event on an element.
     * @param element Reference to DOM element.
     */
    click(element) {
        element.click();
        return {status: 'OK', message: 'Clicked element'};
    }

    /**
     * Trigger keypress event on an element with the Enter key.
     * @param element Reference to DOM element.
     */
    pressEnter(element) {
        this.pressKey(element, 13);
        return {status: 'OK', message: 'Pressed Enter on element'};
    }

    /**
     * Trigger keypress event on an element.
     * @param element Reference to DOM element.
     * @param keyCode Integer,
     */
    pressKey(element, keyCode) {
        const event = new KeyboardEvent('keypress', {
            view: window,
            keyCode: keyCode,
            bubbles: true,
            cancelable: false
        });
        element.dispatchEvent(event);
        return {status: 'OK', message: 'Pressed key on element'};
    }

    /**
     * Insert text or HTML directly before an element.
     * @param element Reference to DOM element.
     * @param value Text content, can also contain HTML markup.
     */
    insertBefore(element, value) {
        element.insertAdjacentHTML('beforebegin', value);
        return {status: 'OK', message: 'Inserted text before element.'};
    }

    /**
     * Insert text or HTML directly after an element.
     * @param element Reference to DOM element.
     * @param value Text content, can also contain HTML markup.
     */
    insertAfter(element, value) {
        element.insertAdjacentHTML('afterend', value);
        return {status: 'OK', message: 'Inserted text after element.'};
    }

    addClass(element, value) {
        element.classList.add(value);
        return {status: 'OK', message: 'Added class to element.'};
    }

    removeClass(element, value) {
        element.classList.remove(value);
        return {status: 'OK', message: 'Removed class from element.'};
    }

    /**
     * Trigger submit event on a form.
     * @param element Reference to DOM element.
     */
    submit(element) {
        let tag = element.tagName.toLowerCase();
        if (tag === 'form') {
            element.submit();
        } else {
            let message = 'The following element is not a form which can be submitted:';
            return {status: 'error', message: message, element: element.outerHTML};
        }
        return {status: 'OK', message: 'Triggered form submit'};
    }
}

export default SSODomFondler;