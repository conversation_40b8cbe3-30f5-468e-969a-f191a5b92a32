import browser from "webextension-polyfill";

/**
 * This helper class sends console messages from injected scripts to the LoghHelper in the extension's service worker
 */
class LogSender {

    static debug(message) {
        console.debug('HIX: ' + message);
        LogSender.sendLogMessage('debug', message);
    }
    static info(message) {
        console.info('HIX: ' + message);
        LogSender.sendLogMessage('info', message);
    }

    static warn(message) {
        console.warn('HIX: ' + message);
        LogSender.sendLogMessage('warn', message);
    }

    static error(message) {
        console.error('HIX: ' + message);
        LogSender.sendLogMessage('error', message);
    }

    /**
     * Send log message to service worker so it can be combined with other log messages there.
     * @param string level
     * @param string message
     */
    static sendLogMessage(level, message) {
        browser.runtime.sendMessage({
            action: 'log',
            level: level,
            message: message
        });
    }
}

export {LogSender}