import browser from 'webextension-polyfill';

class IconHelper {
    /**
     * Change icon to active state
     */
    static setActive() {
        browser.action.setIcon({
            path: {
                "16": "/icons/color-16.png",
                "32": "/icons/color-32.png"
            }
        });
    }

    /**
     * Change icon to inactive state
     */
    static setInactive() {
        browser.action.setIcon({
            path: {
                "16": "/icons/grey-16.png",
                "32": "/icons/grey-32.png"
            }
        });
    }
}

export {IconHelper};