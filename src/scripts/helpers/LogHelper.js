import browser from "webextension-polyfill";

class LogHelper {

    static currentLevel= 4;

    static levels = ['debug', 'info', 'warn', 'error', 'none'];

    static setLevel(level) {
        this.currentLevel = this.levels.indexOf(level);
    }

    static initListener() {
        LogHelper.debug('LogHelper initListener called');
        browser.runtime.onMessage.addListener(
            function(request, sender) {
                if (request.action === "log") {
                    switch (request.level) {
                        case "debug":
                            LogHelper.debug(request.message, {tab: sender});
                            break;
                        case "info":
                            LogHelper.info(request.message, {tab: sender});
                            break;
                        case "warn":
                            LogHelper.warn(request.message, {tab: sender});
                            break;
                        case "error":
                            LogHelper.error(request.message, {tab: sender});
                            break;
                        default:
                            LogHelper.debug(request.message, {tab: sender});
                    }
                }
            }
        );
    }

    static debug(...args) {
        if (this.currentLevel <= 0) {
            args[0] = LogHelper.getTime() + ' ' + args[0];
            console.debug(...args);
        }
    }
    static info(...args) {
        if (this.currentLevel <= 1) {
            args[0] = LogHelper.getTime() + ' ' + args[0];
            console.log(...args);
        }
    }

    static warn(...args) {
        if (this.currentLevel <= 2) {
            args[0] = LogHelper.getTime() + ' ' + args[0];
            console.warn(...args);
        }
    }

    static error(...args) {
        if (this.currentLevel <= 3) {
            args[0] = LogHelper.getTime() + ' ' + args[0];
            console.error(...args);
        }
    }

    static getTime() {
        let d = new Date();
        return d.toLocaleTimeString();
    }
}

export {LogHelper}