import GlobalCommunicationListener from "../../GlobalCommunicationListener";
import browser from "webextension-polyfill";
import {CustomWidgetApi} from "../api/CustomWidgetApi";
import {TabHelper} from "../../helpers/TabHelper";

class CustomSsoModule {
    constructor() {
        this.customWidgetApi = new CustomWidgetApi();
        this.isRecording = false;
        this.selectors = [];
        GlobalCommunicationListener.addListener('is_recording', this.checkRecording.bind(this))
        GlobalCommunicationListener.addListener('start_recording', this.startRecording.bind(this))
        GlobalCommunicationListener.addListener('stop_recording', this.stopRecording.bind(this))
        GlobalCommunicationListener.addListener('focusin', this.onFocusin.bind(this))
        GlobalCommunicationListener.addListener('store_custom_widget', this.storeCustomWidget.bind(this))
    }

    async checkRecording() {
        return this.isRecording;
    }

    async startRecording() {
        this.selectors = [];
        this.isRecording = true;

        const currentTab = await TabHelper.getCurrentTab();

        await browser.scripting.executeScript(
            {
                target: {tabId: currentTab.id},
                files: ['/dist/custom-sso.js'],
            }
        );

        await browser.scripting.executeScript(
            {
                target: {tabId: currentTab.id},
                func: dispatchStartRecording
            }
        );
    }

    async stopRecording() {
        this.isRecording = false;
        const categories = (await this.customWidgetApi.widgetCategories()).data.data;

        const currentTab = await TabHelper.getCurrentTab();

        await browser.scripting.executeScript(
            {
                args: [this.selectors, categories],
                target: {tabId: currentTab.id},
                func: dispatchStopRecording
            }
        );
    }

    selectorExists(selector) {
        return this.selectors.some(function (el) {
            return el.value === selector;
        });
    }

    async onFocusin(message) {
        let exists = this.selectorExists(message.data);

        if (!exists) {
            let text = '';

            switch (this.selectors.length) {
                case 0:
                    text = '2. Klik op het veld voor het wachtwoord';
                    this.selectors.push({
                        type: 'username',
                        value: message.data
                    })
                    break;
                case 1:
                    text = '3. Klik op de login knop';
                    this.selectors.push({
                        type: 'password',
                        value: message.data
                    })
                    break;
                case 2:
                    text = 'Om de widget op te slaan, klik op de stop knop in de browser extensie popup';
                    this.selectors.push({
                        type: 'button',
                        value: message.data
                    })
                    break;
                default:
                    text = 'Om de widget op te slaan, klik op de stop knop in de browser extensie popup';
                    break;
            }
            const currentTab = await TabHelper.getCurrentTab();

            await browser.scripting.executeScript(
                {
                    args: [text],
                    target: {tabId: currentTab.id},
                    func: dispatchUpdateInstructionsPopupRecording
                }
            );
        }
    }

    async storeCustomWidget(message) {
        let data = message.data;
        data['selectors'] = this.selectors;
        data['be_version'] = browser.runtime.getManifest().version;
        const response = await this.customWidgetApi.saveCustomWidget(data);
        let result = response.data;
        result.hostname = (await browser.storage.local.get('host/hostname/domain'))['host/hostname/domain'];

        return result;
    }
}

function dispatchStartRecording() {
    document.dispatchEvent(new CustomEvent('start_recording'));
}

function dispatchUpdateInstructionsPopupRecording(text) {
    document.dispatchEvent(new CustomEvent('update_instruction_popup_recording', {
        detail: {text: text}
    }));
}

function dispatchStopRecording(selectors, categories) {
    document.dispatchEvent(new CustomEvent('stop_recording', {
        detail: {selectors: selectors, categories: categories}
    }));
}

export default new CustomSsoModule();