import {sendToBExtension} from "../CommunicationService";

class RecordButton {
    constructor(isRecording) {
        const recordButton = document.createElement('button');
        recordButton.classList.add('record-button');
        if (isRecording === true) {
            recordButton.textContent = 'Widget opslaan';
            recordButton.addEventListener('click', () => this.stopRecording());
        } else {
            recordButton.textContent = 'Widget maken';
            recordButton.addEventListener('click', () => this.startRecording());
        }

        this._injectableElement = recordButton;
    }

    startRecording() {
        sendToBExtension('start_recording');
        this._injectableElement.textContent = 'Widget opslaan';
        this._injectableElement.addEventListener('click', () => this.stopRecording());
    }

    stopRecording() {
        sendToBExtension('stop_recording');
        this._injectableElement.textContent = 'Widget maken';
        this._injectableElement.addEventListener('click', () => this.startRecording());
    }

    get injectableElement() {
        return this._injectableElement;
    }
}

export {RecordButton};