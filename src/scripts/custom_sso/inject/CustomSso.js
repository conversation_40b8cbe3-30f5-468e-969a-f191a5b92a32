import {sendToBExtension} from "./CommunicationService";
import {ConfirmationPopup} from "./ConfirmationPopup/ConfirmationPopup";
import {InstructionsPopup} from "./InstructionsPopup/InstructionsPopup";

function elemToSelector(elem) {
    const {
        tagName,
        id,
        className,
        parentNode
    } = elem;

    if (tagName === 'HTML') return 'HTML';

    let str = tagName;

    str += (id !== '') ? `#${id}` : '';

    if (className) {
        const classes = className.split(/\s/);
        for (let i = 0; i < classes.length; i++) {
            str += `.${classes[i]}`;
        }
    }

    let childIndex = 1;

    for (let e = elem; e.previousElementSibling; e = e.previousElementSibling) {
        childIndex += 1;
    }

    str += `:nth-child(${childIndex})`;

    return `${elemToSelector(parentNode)} > ${str}`;
}

function cssSelector(el) {
    if (!(el instanceof Element))
        return;

    let path = [];
    while (el.nodeType === Node.ELEMENT_NODE) {
        let selector = el.nodeName.toLowerCase();
        if (el.id) {
            selector += '#' + el.id;
            path.unshift(selector);
            break;
        } else {
            let type = el.getAttribute('type');
            if (type) {
                selector += '[type="' + type + '"]';
            }

            let sib = el, nth = 1;
            while (sib === sib.previousElementSibling) {
                if (sib.nodeName.toLowerCase() === selector)
                    nth++;
            }
            if (nth !== 1)
                selector += ":nth-of-type(" + nth + ")";
        }
        path.unshift(selector);
        el = el.parentNode;
    }
    return path.join(" > ");
}

function disablePageHandler(e) {
    e.stopPropagation();
    e.preventDefault();
}

function grabSelectorHandler(e) {
    sendToBExtension('focusin', cssSelector(e.target));
}

document.addEventListener('start_recording', function (e) {
    // disable page events and grab selectors with focusin event
    document.addEventListener('click', disablePageHandler, true);
    document.addEventListener('focusin', grabSelectorHandler);

    // update instructions popup
    const body = document.querySelector('body');
    const text = '1. Klik op het veld voor de gebruikersnaam';
    let instructionsPopup = new InstructionsPopup(text);
    instructionsPopup.setText(text);
    body.appendChild(instructionsPopup.injectableElement);
});

// stop recording
document.addEventListener('stop_recording', function (e) {
    document.removeEventListener('click', disablePageHandler, true);
    document.removeEventListener('focusin', grabSelectorHandler);

    const body = document.querySelector('body');
    const instructionsPopup = document.querySelector('custom-sso-confirmation-popup');
    instructionsPopup.remove();

    if (e.detail.selectors.length >= 3) {
        body.appendChild(new ConfirmationPopup(e.detail.categories).injectableElement);
    }
})

document.addEventListener('update_instruction_popup_recording', function (e) {
    const instructionsPopup = document.querySelector('custom-sso-confirmation-popup');
    const text = instructionsPopup.shadowRoot.querySelector('.instructions-popup');

    text.textContent = e.detail.text;
})