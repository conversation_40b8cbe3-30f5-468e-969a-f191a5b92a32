import style from '!css-loader!./style.css';

class InstructionsPopup {
    constructor() {
        const rootContainer = document.createElement('custom-sso-confirmation-popup');
        const shadowRoot = rootContainer.attachShadow({mode: 'open'});

        const styleElement = document.createElement('style');
        styleElement.innerText = style;
        styleElement.style.display = "none";

        shadowRoot.appendChild(styleElement);

        // region Main container
        this.rootDiv = document.createElement('div');
        this.rootDiv.classList.add('instructions-popup');
        shadowRoot.appendChild(this.rootDiv);
        //endregion

        this._injectableElement = rootContainer;
    }

    setText(text) {
        this.rootDiv.textContent = text;
    }

    get injectableElement() {
        return this._injectableElement;
    }
}

export {InstructionsPopup}