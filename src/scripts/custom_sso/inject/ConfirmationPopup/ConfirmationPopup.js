import style from '!css-loader!./style.css';
import {ImageInput} from "../ImageInput/ImageInput";
import styleImageInput from "!css-loader!./../ImageInput/style.css";
import {sendToBExtension} from "../CommunicationService";
import {NotificationHelper} from "../../../tax/inject/helpers/NotificationHelper";

class ConfirmationPopup {
    constructor(categories) {
        const rootContainer = document.createElement('custom-sso-confirmation-popup');
        const shadowRoot = rootContainer.attachShadow({mode: 'open'});

        const styleElement = document.createElement('style');
        styleElement.innerText = style + styleImageInput;
        styleElement.style.display = "none";

        shadowRoot.appendChild(styleElement);

        // region Main container
        const rootDiv = document.createElement('div');
        rootDiv.classList.add('full-screen');
        shadowRoot.appendChild(rootDiv);
        //endregion

        // region Popup
        const popupContainer = document.createElement('div');
        popupContainer.classList.add('popup-container');
        rootDiv.appendChild(popupContainer);
        // endregion

        // region Header
        const header = document.createElement('div');
        header.classList.add('header');
        header.innerText = 'Widget opslaan';
        popupContainer.appendChild(header);
        // endregion

        // region Form
        const formElement = document.createElement('form');
        popupContainer.appendChild(formElement);
        // endregion

        // region widget name
        const widgetNameContainer = document.createElement('div')
        widgetNameContainer.classList.add('input-container');
        formElement.appendChild(widgetNameContainer)

        const nameLabel = document.createElement('label');
        nameLabel.classList.add('input-label');
        nameLabel.textContent = 'Naam*';
        nameLabel.setAttribute('for', 'widget_name');
        widgetNameContainer.appendChild(nameLabel);

        const nameInput = document.createElement('input');
        nameInput.required = true;
        nameInput.setAttribute('name', 'widget_name');
        nameInput.setAttribute('maxlength', '50');
        widgetNameContainer.appendChild(nameInput);
        // endregion

        // region widget category
        const categoryContainer = document.createElement('div')
        categoryContainer.classList.add('input-container');
        formElement.appendChild(categoryContainer)

        const categoryLabel = document.createElement('label');
        categoryLabel.classList.add('input-label');
        categoryLabel.textContent = 'Categorie';
        categoryLabel.setAttribute('for', 'widget_category');
        categoryContainer.appendChild(categoryLabel);

        const categoriesDropdown = document.createElement('select');
        categoriesDropdown.classList.add('company-dropdown');
        categoriesDropdown.classList.add('dropdown');
        categoriesDropdown.setAttribute('name', 'widget_category');

        for (let i = 0; i < categories.length; i++) {
            const categoryOption = document.createElement('option');
            categoryOption.setAttribute('value', categories[i].id);
            if (i === categories.length - 1) {
                categoryOption.selected = true;
            }
            categoryOption.textContent = categories[i].display_name;
            categoriesDropdown.appendChild(categoryOption);
        }

        categoryContainer.appendChild(categoriesDropdown);
        // endregion

        // TODO: Uncomment when we find a way to send files
        /* region logo upload
        const imageUpload = new ImageInput(
            'Logo',
            'widget_logo',
            'https://local.securelogin.nu/ext/img/custom-sso/default-logo.png'
        );
        formElement.appendChild(imageUpload.injectableElement);
        endregion */

        // region Bottom buttons
        const bottomButtonsContainer = document.createElement('div');
        bottomButtonsContainer.classList.add('bottom-buttons-container');
        formElement.appendChild(bottomButtonsContainer);

        const saveButton = document.createElement('button');
        saveButton.setAttribute('role', 'submit');
        saveButton.classList.add('save-button');
        saveButton.type = 'button';
        saveButton.textContent = 'Opslaan';
        saveButton.onclick = function () {
            const formData = new FormData(formElement);
            let formDataValues = Object.fromEntries(formData);
            formDataValues['widget_url'] = window.location.origin + window.location.pathname;
            formDataValues['widget_hostname'] = window.location.hostname;
            sendToBExtension('store_custom_widget', formDataValues).then((response) => {
                let link = 'https://' + response.hostname + '/new/manage/widgets/' + response.id + '/edit';
                let linkElement = '<a target="_blank" href="' + link + '">hier</a>';
                let text = 'Widget gemaakt. U kunt de instellingen ' + linkElement + ' bijwerken.';
                NotificationHelper.sendToast('success', text);
                rootContainer.remove();
            })
        }
        bottomButtonsContainer.appendChild(saveButton);

        const cancelButton = document.createElement('button');
        cancelButton.classList.add('cancel-button');
        cancelButton.textContent = 'Annuleer';
        cancelButton.type = 'button';
        cancelButton.onclick = function () {
            rootContainer.remove();
        }
        bottomButtonsContainer.appendChild(cancelButton);
        // endregion

        this._injectableElement = rootContainer;
    }

    get injectableElement() {
        return this._injectableElement;
    }
}

export {ConfirmationPopup}