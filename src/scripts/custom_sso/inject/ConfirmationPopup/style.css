* {
    all: unset;
}

.full-screen {
    --border-color: rgba(0, 0, 0, 0.15);
    --sl-color: #0AAAEE;

    position: fixed;
    background-color: rgba(0, 0, 0, 0.3);
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2147483647;

    font-family: Proxima Nova, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #353535;
}

.full-screen .popup-container {
    width: 30%;
    background-color: white;
    padding: 1.5em;

    border: var(--border-color) 2px solid;
    border-radius: 8px;
}

.header {
    text-align: center;
    display: block;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
}

.input-label {
    font-weight: bold;
}

.input-container {
    display: flex;
    gap: 10px;
    flex-direction: column;
    margin-bottom: 20px;
}

input {
    height: 30px;
    padding: 5px;
    border: 1px solid #BABCBF;
    border-radius: 4px;
    font-size: 14px;
}

.bottom-buttons-container {
    display: flex;
    justify-content: space-between;
    text-align: center;
}

.bottom-buttons-container button {
    min-width: 100px;
    padding: 1em;
    border-radius: 0.3em;
    cursor: pointer;
}

.save-button {
    background-color: var(--sl-color);
    color: white;
}

.cancel-button {
    color: var(--sl-color);
}

.cancel-button:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.dropdown {
    border: var(--border-color) solid 1px;
    border-radius: 0.5em;
    padding: 0.6em 1.5em 0.6em 1em;
    appearance: revert;
    cursor: pointer;
}