.image-input {
    display: block;
    margin-bottom: 32px;
}

.checkerboard {
    display: flex;
    margin: auto;
    padding: 8px;
    background-image: /* tint image */ linear-gradient(to right, rgba(255, 255, 255, 0.90), rgba(255, 255, 255, 0.90)),
        /* checkered effect */ linear-gradient(to right, black 50%, white 50%),
    linear-gradient(to bottom, black 50%, white 50%);
    background-blend-mode: normal, difference, normal;
    background-size: 2em 2em;
    max-width: 250px;
    max-height: 250px;
    min-height: 70px;
    text-align: center;
    line-height: 0;
    margin-bottom: 16px;
    cursor: pointer;
}

.checkerboard img {
    height: auto;
    max-width: 100%;
}

.file-selector {
    position: relative;
    height: 46px;
    text-align: center;
}

input[type="file"] {
    display: none;
}
