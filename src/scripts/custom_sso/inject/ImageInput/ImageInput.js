import style from '!css-loader!./style.css';

class ImageInput {
    constructor(label, name, source = null) {
        const styleElement = document.createElement('style');
        styleElement.innerText = style;

        // Image Input Container
        const imageInput = document.createElement('div');
        imageInput.classList.add('image-input');

        // Label
        const nameLabel = document.createElement('div');
        nameLabel.classList.add('input-label');
        nameLabel.textContent = label;
        imageInput.appendChild(nameLabel);

        // Checkerboard
        const checkerboard = document.createElement('div');
        checkerboard.classList.add('checkerboard');
        imageInput.appendChild(checkerboard);
        const image = document.createElement('img');
        image.src = source;
        image.onclick = function () {
            input.click();
        }
        checkerboard.appendChild(image);

        // File selector
        const fileSelector = document.createElement('div');
        fileSelector.classList.add('file-selector');
        imageInput.appendChild(fileSelector);

        // Input
        const input = document.createElement('input');
        input.name = name;
        input.type = 'file';
        input.accept = 'image/*';
        input.onchange = (event) => this.updateImageValue(event, image);
        fileSelector.appendChild(input);

        this._injectableElement = imageInput;
    }

    updateImageValue(event, image) {
        if (typeof event == 'object' && typeof event.target.files[0] == 'object') {
            image.src = URL.createObjectURL(event.target.files[0]);
        }
    }

    get injectableElement() {
        return this._injectableElement;
    }
}

export {ImageInput}