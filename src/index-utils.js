import {NotificationHelper} from './scripts/tax/inject/helpers/NotificationHelper';

global.NotificationHelper = NotificationHelper;

document.addEventListener('notification_sent', function (e) {
    let func = e.detail.func;

    if (func === 'sendToast') {
        NotificationHelper[func](e.detail.params.icon, e.detail.params.message);
    } else {
        NotificationHelper[func]();
    }
}, {once: true})