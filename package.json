{"name": "@securelogin/browser-extension", "version": "2.3.2", "private": true, "engines": {"node": ">=14.0.0"}, "dependencies": {"axios": "^1.7.2", "blueimp-md5": "^2.12.0", "cookie": "^0.4.1", "dayjs": "^1.10.6", "jquery": "^3.4.1", "serialize-error": "^8.0.1", "webextension-polyfill": "^0.10.0"}, "scripts": {"start:chrome": "npm run dist:chrome && webpack --config config/webpack-dev.js --watch --output-path=dist/chrome/dist", "start:firefox": "npm run dist:firefox && webpack --config config/webpack-dev.js --watch --output-path=dist/firefox/dist", "start": "npm run start:chrome", "dist:chrome": "node ./create-dist.js --target=chrome", "dist:firefox": "node ./create-dist.js --target=firefox", "build": "run-s build:*", "build:chrome": "npm run dist:chrome && webpack --config config/webpack-prod.js --output-path=dist/chrome/dist", "build:firefox": "npm run dist:firefox && webpack --config config/webpack-prod.js --output-path=dist/firefox/dist", "archive": "run-s archive:*", "archive:chrome": "npm run build:chrome && node ./create-archive.js --target=chrome", "archive:firefox": "npm run build:firefox && node ./create-archive.js --target=firefox"}, "devDependencies": {"case-sensitive-paths-webpack-plugin": "^2.2.0", "css-loader": "^3.2.0", "file-loader": "^6.2.0", "fs-extra": "^8.1.0", "jszip": "^3.2.2", "lodash": "^4.17.15", "npm-run-all": "^4.1.5", "postcss-import-webpack-resolver": "^1.0.1", "postcss-loader": "^3.0.0", "raw-loader": "^4.0.2", "style-loader": "^1.0.0", "stylelint": "^10.1.0", "webpack": "^5.74.0", "webpack-cli": "^4.9.1", "webpack-ext-reloader": "^1.1.9", "yargs": "^14.0.0"}, "browserslist": ["defaults"]}