#!/usr/bin/env node
const fs = require('fs-extra');
const path = require('path');
const template = require('lodash/template');
const pkg = require('./package.json');
const argv = require('yargs')
  .alias('h', 'help')
  .usage('Usage: $0 <distDir> [options]')
  .example('$0 --target=chrome')
  .showHelpOnFail(false, 'Specify --help for available options')
  .options({
    t: {
      alias: 'target',
      describe: 'defines target browser',
      type: 'string',
      nargs: 1,
      demand: 'target is required',
    },
  })
  .argv;

const options = {
  target: argv.target,
  version: pkg.version.replace('-', '.')
};

const templateDir = 'template/';
const distDir = 'dist/' + options.target;

// Clean distDir by removing it
fs.removeSync(distDir);

// Copy content from 'template/' folder to distDir
fs.copySync(templateDir, distDir);

// Interpolate manifest template
const manifestTemplate = fs.readFileSync(path.join(__dirname, `${distDir}/manifest.json`));
const finalManifest = template(manifestTemplate)({ options: options }).replace(/^\s*\r?\n/gm, '');

fs.writeFileSync(path.join(__dirname, `${distDir}/manifest.json`), finalManifest);