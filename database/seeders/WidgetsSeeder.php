<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Symfony\Component\Console\Output\ConsoleOutput;

class WidgetsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->call(LicenseSeeder::class);
        $this->call(WidgetTemplateSeeder::class);
        $this->call(GenericWidgetCategorySeeder::class);
        $this->call(GenericWidgetSeeder::class);

        $output = new ConsoleOutput();
        $output->writeln("<comment>Done ✔</comment>");
    }
}
