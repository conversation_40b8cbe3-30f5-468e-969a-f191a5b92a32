<?php

namespace Database\Seeders;

use App\Models\OpenQuestions\Questions\Templates\TemplateFieldType;

class TemplateFieldTypeSeeder extends TableSeeder
{
    protected $may_run_while_not_empty = true;
    protected $update_reference = 'name';

    var $model = TemplateFieldType::class;

    public function seed(): array
    {
        return [
            ['name' => TemplateFieldType::TEXT],
            ['name' => TemplateFieldType::MULTITEXT],
            ['name' => TemplateFieldType::UPLOAD_FILE],
            ['name' => TemplateFieldType::UPLOAD_MULTIFILE],
            ['name' => TemplateFieldType::CHECKBOX],
            ['name' => TemplateFieldType::MULTIPLE_CHECKBOXES],
            ['name' => TemplateFieldType::RADIO_BUTTONS],
            ['name' => TemplateFieldType::HEADER],
            ['name' => TemplateFieldType::SECTION],
            ['name' => TemplateFieldType::STATIC],
        ];
    }
}
