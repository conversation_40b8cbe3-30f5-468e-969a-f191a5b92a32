<?php

$widgets[] = [
    'reference_name' => 'unitouch_online',
    'display_name' => 'Unitouch Online',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2025-05-13',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => ['https://online2.unitouch.eu'],
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://online2.unitouch.eu:2112/',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/online2\.unitouch\.eu:2112\/',
                    'instructions' => [
                        ['selector' => 'input[name="Login"]', 'func' => 'wait_for_element'],
                        ['value' =>  500, 'func' => 'wait'],
                        ['selector' => 'input[name="Login"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input[name="Password"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'input[name="Password"]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'input#Editbox3', 'func' => 'setvalue', 'value' => '{{$domain}}'],
                        ['selector' => 'input[type="button"]' , 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[domain]' => [
            'type' => 'domain',
        ],
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];