<?php

$widgets[] = [
    'reference_name' => 'robotx',
    'display_name' => 'RobotX',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2025-02-20',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => ['https://portal.robotx.eu'],
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://portal.robotx.eu/Identity/Account/Login',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/portal\.robotx\.eu\/Identity\/Account\/Login',
                    'instructions' => [
                        ['selector' => 'input#Input_Email', 'func' => 'wait_for_element'],
                        ['selector' => 'input#Input_Email', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input#Input_Password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'input#Input_Password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'button[type="submit"]', 'func' => 'wait_for_element'],
                        ['selector' => 'button[type="submit"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];

