<?php

$widgets[] = [
    'reference_name' => 'de_goudse',
    'display_name' => 'de Goudse Adviseur',
    'description' => 'Inloggen adviseur',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2024-10-11',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => [
            'https://beherenmijnpersoneel.apps.goudse.nl',
            'https://www.goudse.nl',
            'https://goudse.nl',
        ],
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.goudse.nl/mijn-goudse-adviseurs',
            'pages' => [
                'page' => [
                    'pattern' => '^https:\/\/www\.goudse\.nl\/ondernemer\/inloggenondernemer',
                    'instructions' => [
                        ['selector' => 'a[id="content-simple-button"]', 'func' => 'wait_for_element'],
                        ['selector' => 'a[id="content-simple-button"]', 'func' => 'click'],
                    ],
                ],
                'redirectpage1' => [
                    'pattern' => '^https:\/\/gids\.goudse\.nl\/aselectserver\/server',
                    'instructions' => [
                        ['selector' => 'button[data-gtm="loginEmail"]', 'func' => 'wait_for_element'],
                        ['selector' => 'button[data-gtm="loginEmail"]', 'func' => 'click'],
                    ],
                ],
                'loginpage' => [
                    'pattern' => '^https:\/\/inloggen\.goudse\.nl\/goudse\/login\/',
                    'instructions' => [
                        ['selector' => 'form#UsernameAndPassword input#username', 'func' => 'wait_for_element'],
                        ['selector' => 'form#UsernameAndPassword input#username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form#UsernameAndPassword input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form#UsernameAndPassword input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form#UsernameAndPassword button[type=submit]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
