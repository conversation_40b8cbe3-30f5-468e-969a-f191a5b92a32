<?php

$widgets[] = [
    'reference_name' => 'datadear',
    'display_name' => 'Datadear',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2020-06-16',
    'order' => 100,
    'licenses' => 'accounting_nl|accounting_uk',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://apps.datadear.com/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/apps\.datadear\.com\/login',
                    'instructions' => [
                        ['selector' => 'form input#exampleInputEmail1', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#exampleInputEmail1', 'func' => 'setvalue', 'value' => '{{$email}}'],
                        ['selector' => 'form input[type=password]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input[type=password]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form button[type=submit]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[email]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
