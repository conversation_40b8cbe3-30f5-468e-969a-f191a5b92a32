<?php

$widgets[] = [
    'reference_name' => 'ringcentral',
    'display_name' => 'RingCentral',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-09-13',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://app.ringcentral.com',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://login.ringcentral.com/sw.html',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/login\.ringcentral\.com\/sw\.html\?responseType=code&clientId=',
                    'instructions' => [
                        ['selector' => 'form input#credential', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#credential', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                        ['selector' => 'form input[type=password]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[type=password]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input[type=password]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
