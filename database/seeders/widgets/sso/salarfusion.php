<?php

$widgets[] = [
    'reference_name' => 'salarfusion',
    'display_name' => 'SalarFusion',
    'description' => 'online salaris - en personeelsplatform',
    'category' => ['wages'],
    'status' => 'beta',
    'version_date' => '2024-09-10',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.salarfusion.nl/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/www\.salarfusion\.nl\/login',
                    'instructions' => [
                        'step0' => ['selector' => 'form input#login-username', 'func' => 'wait_for_element'],
                        'step1' => ['selector' => 'form input#login-username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        'step2' => ['selector' => 'form input#login-password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        'step3' => ['selector' => 'form input#login-password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        'step4' => ['selector' => 'form button#login-cmdlogin', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[post][data][userName]' => [
            'type' => 'username',
        ],
        'properties[post][data][password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'instructions' => [
                'title1' => 'widget.salarfusion.totp_instructions.title',
                'step1' => 'widget.salarfusion.totp_instructions.step1',
                'step2' => 'widget.salarfusion.totp_instructions.step2',
                'step3' => 'widget.salarfusion.totp_instructions.step3',
                'step4' => 'widget.salarfusion.totp_instructions.step4',
                'step5' => 'widget.salarfusion.totp_instructions.step5',
                'step6' => 'widget.salarfusion.totp_instructions.step6',
            ],
        ],
    ],
    'onstart_operations' => [
        'properties[username]' => [
            'property' => 'properties[post][data][userName]',
        ],
        'properties[password]' => [
            'property' => 'properties[post][data][password]',
        ],
        'properties[invisiblehand][pages][2fa_page]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'array_merge' => [
                    'property' => 'properties[pages][loginpage][instructions]',[
                        ['selector' => 'input#txt-code', 'func' => 'wait_for_element'],
                        ['selector' => 'input#txt-code', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                    ],
                ],
            ]
        ]
    ],
    'onstart_checks' => null
];
