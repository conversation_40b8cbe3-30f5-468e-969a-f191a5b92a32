<?php

$widgets[] = [
    'reference_name' => 'bouw7',
    'display_name' => 'Bouw7',
    'description' => 'Bouwmanagement software',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2025-05-28',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://login.bouw7.nl/site/login',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://login.bouw7.nl/site/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/login\.bouw7\.nl\/\_a\/[a-z]+\/login',
                    'instructions' => [
                        ['selector' => 'form input#loginform-username', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#loginform-username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form button[type=submit]', 'func' => 'click'],
                    ],
                ],
                'password_page' => [
                    'pattern' => '^https:\/\/login\.bouw7\.nl\/\_a\/[a-z]+\/login\/pass',
                    'instructions' => [
                        ['selector' => 'form input#loginform-password', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#loginform-password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#loginform-password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form button[type=submit]:not([disabled])', 'func' => 'wait_for_element'],
                        ['selector' => 'form button[type=submit]', 'func' => 'click'],

                    ],
                ],
                'b2clogin_page' => [
                    'pattern' => '^https:\/\/exactidentity\.b2clogin\.com\/[a-zA-Z-0-9]+\/[a-zA-Z0-9_]+\/oauth2\/v2\.0\/authorize',
                    'instructions' => [
                        ['selector' => 'form input#password', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form button#next', 'func' => 'click'],

                    ],
                ],
                'exact_page' => [
                    'pattern' => '^https:\/\/login\.exact\.com\/[a-zA-Z-0-9]+\/b2c_1a_unifiedsignin\/oauth2\/v2.0\/authorize',
                    'instructions' => [
                        ['selector' => 'form input#password', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form button#next', 'func' => 'click'],

                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'title' => 'totp_secret_optional'
        ],
    ],
    'onstart_operations' => [
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ],
        ],
        'properties[invisiblehand][pages]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]',
                ],
                'array_merge' => [
                    'property' => 'properties[invisiblehand][pages]',
                    [
                        'old_totp_page' => [
                            'pattern' => '^https:\/\/start\.exactonline\.[a-z]{2}\/docs\/TotpLogin.aspx\?.*$',
                            'instructions' => [
                                ['selector' => 'form#login input#LoginForm_Input_Key', 'func' => 'wait_for_element'],
                                ['selector' => 'form#login input#LoginForm_Input_Key', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                                ['selector' => 'form#login button#LoginForm_btnSave', 'func' => 'removeClass', 'value' => 'disabled'],
                                ['selector' => 'form#login #LoginForm_btnSave', 'func' => 'click'],
                            ],
                        ],
                        'new_totp_page' => [
                            'pattern' => '^https://exactidentity.b2clogin.com\/[a-z0-9A-Z-]+\/B2C_[a-z0-9A-Z]+_UnifiedSignin\/api\/CombinedSigninAndSignup\/confirmed',
                            'instructions' => [
                                ['selector' => 'form#attributeVerification input#totpVerificationCode', 'func' => 'wait_for_element'],
                                ['selector' => 'form#attributeVerification input#totpVerificationCode', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                                ['selector' => 'form#attributeVerification button#continue', 'func' => 'click'],
                            ],
                        ]
                    ]
                ],
            ],
        ],
    ],
];
