<?php

$widgets[] = [
    'reference_name' => 'strato',
    'display_name' => 'STRATO',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2021-12-20',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://www.strato.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.strato.nl/apps/CustomerService#/skl',
            'pages' => [
                'login_cookie' => [
                    'pattern' => '^https:\/\/www\.strato\.nl\/apps\/CustomerService',
                    'instructions' => [
                        ['selector' => 'form#cookieform1 button[type="submit"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form#cookieform1 input#redirect_consent', 'func' => 'setvalue', 'value' => 'https://www.strato.nl/apps/CustomerService?step=2#/skl'],
                        ['selector' => 'form#cookieform1 button[type="submit"]', 'func' => 'click'],
                    ],
                ],
                'login_ip' => [
                    'pattern' => '^https:\/\/www.strato.nl\/apps\/CustomerService\?step\=2',
                    'instructions' => [
                        ['selector' => 'form#ksbLogin input[name="identifier"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form#ksbLogin input[name="identifier"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form#ksbLogin input[name="passwd"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form#ksbLogin input[name="passwd"]', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form#ksbLogin input[name="action_customer_login.x"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
