<?php

$widgets[] = [
    'reference_name' => 'accountants_academy',
    'display_name' => 'Accountants Academy',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-03-09',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://{{$subdomain}}.accountants.academy',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://{{$subdomain}}.accountants.academy/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/{{$subdomain}}\.accountants\.academy\/login',
                    'instructions' => [
                        ['selector' => 'input#basic_email', 'func' => 'wait_for_element'],
                        ['selector' => 'input#basic_email', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input#basic_password', 'func' => 'wait_for_element'],
                        ['selector' => 'input#basic_password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'button[type="submit"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[subdomain]' => [
            'type' => 'subdomain',
            'sanitize' => 'subdomain'
        ],
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
