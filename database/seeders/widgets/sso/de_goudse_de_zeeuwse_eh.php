<?php

$widgets[] = [
    'reference_name' => 'de_goudse_de_zeeuwse_eh',
    'display_name' => 'De Goudse/De Zeeuwse EH',
    'description' => 'Inloggen met eHerkenning',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2021-03-17',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://www.goudse.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.goudse.nl/adviseur',
            'pages' => [
                'include' => 'eherkenning.brokers',
                'preload' => [
                    'pattern' => '^https:\/\/www\.goudse\.nl\/adviseur',
                    'instructions' => [
                        ['selector' => 'a[class="button button--primary button--icon"]', 'func' => 'wait_for_element'],
                        ['selector' => 'a[class="button button--primary button--icon"]', 'func' => 'click'],
                    ],
                ],
                'prelogin' => [
                    'pattern' => '^https:\/\/gids\.goudse\.nl\/aselectserver\/server.*$',
                    'instructions' => [
                        ['selector' => 'button[data-gtm="logineHerkenning"]', 'func' => 'wait_for_element'],
                        ['selector' => 'button[data-gtm="logineHerkenning"]', 'func' => 'click'],
                    ],
                ],
                'ehpage' => [
                    'pattern' => '^https:\/\/eid\.digidentity\.eu\/hm\/eh113\/select_ad',
                    'instructions' => [
                        ['selector' => 'form select#identity_provider', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => '300'],
                        ['selector' => 'form select#identity_provider', 'func' => 'setvalue', 'value' => '{{$brokerid}}'],
                        ['selector' => 'form input[type=submit]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'include' => 'eherkenning.settings',
    ],
    'onstart_operations' => [
        'include' => 'eherkenning.onstart_operations_digidentity',
    ],
];
