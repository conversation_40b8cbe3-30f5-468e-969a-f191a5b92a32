<?php
$widgets[] = [
    'reference_name' => 'fscd',
    'migrate_from' => '66bdccaec3eb5_fscd',
    'display_name' => 'FSCD',
    'description' => NULL,
    'category' => [
        0 => 'other',
    ],
    'status' => 'beta',
    'version_date' => '2024-08-20',
    'order' => NULL,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '2.1.19',
        'clear_cookies' => 'fsdc.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://fsdc.nl/handleiding-belastingrecht/',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\\/\\/fsdc\\.nl\\/handleiding-belastingrecht\\/',
                    'instructions' => [
                        0 => [
                            'selector' => 'input#rcp_user_login',
                            'func' => 'wait_for_element',
                        ],
                        1 => [
                            'selector' => 'input#rcp_user_login',
                            'func' => 'setvalue',
                            'value' => '{{$username}}',
                        ],
                        2 => [
                            'selector' => 'input#rcp_user_pass',
                            'func' => 'wait_for_element',
                        ],
                        3 => [
                            'selector' => 'input#rcp_user_pass',
                            'func' => 'setvalue',
                            'value' => '{{$password}}',
                        ],
                        4 => [
                            'selector' => 'input#rcp_login_submit',
                            'func' => 'wait_for_element',
                        ],
                        5 => [
                            'selector' => 'input#rcp_login_submit',
                            'func' => 'click',
                        ],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
