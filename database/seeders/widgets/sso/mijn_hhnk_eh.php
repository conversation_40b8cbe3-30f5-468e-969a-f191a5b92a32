<?php

$widgets[] = [
    'reference_name' => 'mijn_hhnk_eh',
    'display_name' => 'Mijn HHNK EH',
    'description' => 'Hoogheemraadschap Hollands Noorderkwartier ',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2024-11-26',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://mijn.hhnk.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://mijn.hhnk.nl/eidas_eh/login/hhn-prd-n2',
            'pages' => [
                'include' => 'eherkenning.brokers',
                'ehpage' => [
                    'pattern' => '^https:\/\/brk\.eid\.kpn\.com\/brk\/EID1BBroker',
                    'instructions' => [
                        ['selector' => 'form#idpform select#leverancier', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => '300'],
                        ['selector' => 'form#idpform select#leverancier', 'func' => 'setvalue', 'value' => '{{$brokerid}}'],
                        ['selector' => 'form#idpform input[type=submit]', 'func' => 'click'],
                    ],
                ]
            ],
        ],
    ],
    'settings' => [
        'include' => 'eherkenning.settings',
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
    'onstart_operations' => [
        'include' => 'eherkenning.onstart_operations',
    ],
];
