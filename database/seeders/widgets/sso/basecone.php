<?php

$widgets[] = [
    'reference_name' => 'basecone',
    'display_name' => 'Basecone',
    'description' => 'Aanleveren en autoriseren van bestanden',
    'category' => ['accountancy'],
    'status' => 'beta',
    'version_date' => '2021-10-02',
    'order' => 50,
    'licenses' => 'accounting_nl|accounting_be|accounting_uk',
    'contact_detail' => [
        'name' => '<PERSON>',
        'email' => '<EMAIL>',
        'remark' => 'For users <NAME_EMAIL>',
    ],
    'uptime_monitor_url' => 'https://identity.basecone.com/authentication/logout',
    'properties' => [
        'initial_get' => [
            'url' => 'https://identity.basecone.com/authentication/logout',
        ],
        'post' => [
            'url' => "https://identity.basecone.com/authentication/login/formPost",
            'data' => [
                'redirectUri' => 'https://secure.basecone.com',
                'clientIdentifier' => '********-a970-4a9e-b3ea-8ffd4f5741bb',
            ],
        ],
    ],
    'settings' => [
        'properties[post][data][officecode]' => [
            'type' => 'office',
            'title' => 'officecode',
            'scope' => ['account', 'context', 'user'],
        ],
        'properties[post][data][username]' => [
            'type' => 'username',
        ],
        'properties[post][data][password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'instructions' => [
                'title1' => 'widget.basecone.field_totp_secret_instruction_title',
                'step1' => 'widget.basecone.field_totp_secret_instruction_step1',
                'step2' => 'widget.basecone.field_totp_secret_instruction_step2',
                'step3' => 'widget.basecone.field_totp_secret_instruction_step3',
                'step4' => 'widget.basecone.field_totp_secret_instruction_step4',
            ],
        ],

    ],
    'onstart_operations' => [
        'properties[username]' => [
            'property' => 'properties[post][data][username]',
        ],
        'properties[password]' => [
            'property' => 'properties[post][data][password]',
        ],
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ],
        ],
    ],
];
