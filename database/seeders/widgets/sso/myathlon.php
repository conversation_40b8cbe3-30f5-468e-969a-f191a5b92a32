<?php

$widgets[] = [
    'reference_name' => 'myathlon',
    'display_name' => 'MyAthlon',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2020-10-23',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://login.athlon.com',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://login.athlon.com/auth/realms/athlon/protocol/openid-connect/auth?response_type=code&client_id=MyAthlon&redirect_uri=http%3A%2F%2Fmy.athlon.com',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/login\.athlon\.com\/auth\/realms\/athlon\/protocol\/openid-connect\/auth.*',
                    'instructions' => [
                        ['selector' => 'form input#username', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form input#kc-login', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
