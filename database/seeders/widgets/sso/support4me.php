<?php

$widgets[] = [
    'reference_name' => 'support4me',
    'display_name' => 'Support 4Me',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-09-19',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://{{$domain}}',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://{{$domain}}',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/login\.microsoftonline\.com\/[a-z0-9-]+\/saml2',
                    'instructions' => [
                        ['selector' => 'input[type="email"]', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => 300],
                        ['selector' => 'input[type="email"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['func' => 'wait', 'value' => 300],
                        ['selector' => 'input[type="submit"]', 'func' => 'click'],
                        ['func' => 'wait', 'value' => 300],
                        ['selector' => 'input[type="password"]', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => 300],
                        ['selector' => 'input[type="password"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['func' => 'wait', 'value' => 300],
                        ['selector' => 'input[type="submit"]', 'func' => 'click']
                    ]
                ]
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
        'properties[domain]' => [
            'type' => 'domain',
            'sanitize' => 'domain',
        ],
    ],
    'onstart_operations' => null
];
