<?php

$widgets[] = [
    'reference_name' => 'svc_audittool',
    'migrate_from' => '64d3920903d4a_s_v_c_audittool',
    'display_name' => 'SVC Audittool',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-08-09',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '2.1.7',
        'clear_cookies' => 'www.svcaudittool.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.svcaudittool.nl/account/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/www\.svcaudittool\.nl\/account\/login',
                    'instructions' => [
                        [
                            'selector' => 'input#username',
                            'func' => 'wait_for_element'
                        ],
                        [
                            'selector' => 'input#username',
                            'func' => 'setvalue',
                            'value' => '{{$username}}'
                        ],
                        [
                            'selector' => 'input#password',
                            'func' => 'wait_for_element'
                        ],
                        [
                            'selector' => 'input#password',
                            'func' => 'setvalue',
                            'value' => '{{$password}}'
                        ],
                        [
                            'selector' => 'div#logincontent > form > input[type="submit"]',
                            'func' => 'wait_for_element'
                        ],
                        [
                            'selector' => 'div#logincontent > form > input[type="submit"]',
                            'func' => 'click'
                        ]
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username'
        ],
        'properties[password]' => [
            'type' => 'password'
        ]
    ],
];
