<?php

$widgets[] = [
    'reference_name' => 'mijnkantoorapp',
    'reference_name_alt' => 'bettyblocks',
    'description' => 'Portal',
    'category' => ['general'],
    'display_name' => 'Mijn Kantoorapp',
    'status' => 'beta',
    'version_date' => '2023-11-03',
    'order' => 190,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://web.mijnkantoorapp.nl',
            'pages' => [
                'loginPage' => [
                    'pattern' => '^https:\/\/web\.mijnkantoorapp\.nl',
                    'instructions' => [
                        ['selector' => 'input#login_username', 'func' => 'wait_for_element'],
                        ['selector' => 'input#login_username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input#login_password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'input#login_password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'button.loginButton', 'func' => 'click'],
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[post][data][email_address]' => [
            'type' => 'email_as_username',
            'title' => 'email',
        ],
        'properties[post][data][password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'title' => 'totp_secret_optional'
        ],
    ],
    'onstart_operations' => [
        'properties[username]' => [
            'property' => 'properties[post][data][email_address]'
        ],
        'properties[password]' => [
            'property' => 'properties[post][data][password]'
        ],
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ],
        ],
        'properties[pin1]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    0,
                    1
                ]
            ],
        ],
        'properties[pin2]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    1,
                    1
                ]
            ],
        ],
        'properties[pin3]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    2,
                    1
                ]
            ],
        ],
        'properties[pin4]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    3,
                    1
                ]
            ],
        ],
        'properties[pin5]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    4,
                    1
                ]
            ],
        ],
        'properties[pin6]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'substr' => [
                    'property' => 'properties[totp]',
                    5,
                    1
                ]
            ],
        ],
        'properties[invisiblehand][pages][loginPage][instructions]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                'array_merge' => [
                    'property' => 'properties[invisiblehand][pages][loginPage][instructions]',
                    [
                        ['selector' => 'div.pincode-input-container input[type="tel"]:nth-of-type(1)', 'func' => 'wait_for_element'],
                        ['selector' => 'div.pincode-input-container input[type="tel"]:nth-of-type(1)', 'func' => 'setvalue', 'value' => '{{$pin1}}'],
                        ['selector' => 'div.pincode-input-container input[type="tel"]:nth-of-type(2)', 'func' => 'setvalue', 'value' => '{{$pin2}}'],
                        ['selector' => 'div.pincode-input-container input[type="tel"]:nth-of-type(3)', 'func' => 'setvalue', 'value' => '{{$pin3}}'],
                        ['selector' => 'div.pincode-input-container input[type="tel"]:nth-of-type(4)', 'func' => 'setvalue', 'value' => '{{$pin4}}'],
                        ['selector' => 'div.pincode-input-container input[type="tel"]:nth-of-type(5)', 'func' => 'setvalue', 'value' => '{{$pin5}}'],
                        ['selector' => 'div.pincode-input-container input[type="tel"]:nth-of-type(6)', 'func' => 'setvalue', 'value' => '{{$pin6}}'],
                        ['selector' => 'form button.loginButton', 'func' => 'click'],
                    ]
                ]
            ]
        ]
    ]
];
