<?php

$widgets[] = [
    'reference_name' => 'joiin',
    'display_name' => 'Joiin',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2025-02-13',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => [
            'https://app.joiin.co'
        ],
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://app.joiin.co/auth/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/app\.joiin\.co\/auth\/login',
                    'instructions' => [
                        ['selector' => 'input#email', 'func' => 'wait_for_element'],
                        ['selector' => 'input#email', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'button[type="submit"]', 'func' => 'wait_for_element'],
                        ['selector' => 'button[type="submit"]', 'func' => 'click'],
                        ],
                    ],
                ],
            ],
        ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];