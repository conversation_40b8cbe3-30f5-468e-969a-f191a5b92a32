<?php

$widgets[] = [
    'reference_name' => 'allianz_schade_particulier',
    'display_name' => 'Allianz Schade Particulier ',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2024-04-03',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => ['https://idallianz.idselect.nl','https://id.allianzretailportal.com'],
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://id.allianzretailportal.com/login/?realm=/alpha#',
            'pages' => [
                'include' => 'eherkenning.brokers',
                'homepage' => [
                    'pattern' => '^https:\/\/id\.allianzretailportal\.com\/login',
                    'instructions' => [
                        ['selector' => 'a.btn', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => 1000],
                        ['selector' => 'a.btn', 'func' => 'click'],
                    ],
                ],
                'loginpage' => [
                    'pattern' => '^https:\/\/idallianz\.idselect\.nl\/aselectserver\/server',
                    'instructions' => [
                        ['selector' => 'form button[type="submit"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                    ],
                ],
                'ehpage' => [
                    'pattern' => '^https:\/\/eid\.digidentity\.eu\/hm\/eh113\/select_ad',
                    'instructions' => [
                        ['selector' => 'form select#identity_provider', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => '300'],
                        ['selector' => 'form select#identity_provider', 'func' => 'setvalue', 'value' => '{{$brokerid}}'],
                        ['selector' => 'form input[type=submit]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
        'include' => 'eherkenning.settings',
    ],
    'onstart_operations' => [
        'include' => 'eherkenning.onstart_operations_allianz_schade_particulier',
    ],
];
