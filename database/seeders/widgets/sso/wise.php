<?php

$widgets[] = [
    'reference_name' => 'wise',
    'migrate_from' => '63a2f72cef512_wise',
    'display_name' => 'Wise',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2025-01-27',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.6.17',
        'clear_cookies' => 'wise.com',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://wise.com/login/',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/wise\.com\/login',
                    'instructions' => [
                        ['selector' => 'input#email', 'func' => 'wait_for_element'],
                        ['selector' => 'input#email', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input#password', 'func' => 'wait_for_element'],
                        ['selector' => 'input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'button[type="submit"]:not([disabled])', 'func' => 'wait_for_element'],
                        ['selector' => 'button[type="submit"]:not([disabled])', 'func' => 'click']
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username'
        ],
        'properties[password]' => [
            'type' => 'password'
        ]
    ],
];