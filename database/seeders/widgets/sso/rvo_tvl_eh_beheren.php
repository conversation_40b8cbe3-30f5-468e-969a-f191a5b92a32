<?php

$widgets[] = [
    'reference_name' => 'rvo_tvl_eh_beheren',
    'display_name' => 'RVO TVL - eHerkenning (beheren)',
    'description' => 'Tegemoetkoming Vaste Lasten',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2025-01-22',
    'order' => 100,
    'licenses' => 'eherkenning',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://mijn.rvo.nl/upnl/login-start3.html?ProductDirectOpenen=TVL-{{$quarter}}-{{$year}}',
            'pages' => [
                'include' => 'eherkenning.brokers',
                'prelogin' => [
                    'pattern' => '^https:\/\/mijn\.rvo\.nl\/upnl/login-start3\.html',
                    'instructions' => [
                        'step0' => ['selector' => 'a#login-eh3', 'func' => 'wait_for_element'],
                        'step1' => ['func' => 'wait', 'value' => '300'],
                        'step2' => ['selector' => 'a#login-eh3', 'func' => 'click'],
                    ],
                ],
                'ehpage' => [
                    'pattern' => '^https:\/\/eh\.onewelcome\.nl\/broker\/authn\/eh-113-broker\/select-idp',
                    'instructions' => [
                        ['selector' => 'form select#selectedIdp', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => '300'],
                        ['selector' => 'form select#selectedIdp', 'func' => 'setvalue', 'value' => '{{$brokerid}}'],
                        ['selector' => 'form input[type=submit]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'include' => 'eherkenning.settings',
        'properties[year]' => [
            'type' => 'combo',
            'title' => 'year',
            'deselectable' => true,
            'values' => [
                'year' => 'Huidig jaar',
                'lastYear' => 'Vorig jaar',
            ],
            'validation' => 'in:year,lastYear',
            'scope' => ['account', 'context', 'user'],
        ],
        'properties[quarter]' => [
            'type' => 'combo',
            'title' => 'quarter',
            'deselectable' => true,
            'values' => [
                'Q1' => '1',
                'Q2' => '2',
                'Q3' => '3',
                'Q4' => '4',
            ],
            'validation' => 'in:Q1,Q2,Q3,Q4',
            'scope' => ['account', 'context', 'user'],
        ],
    ],
    'onstart_operations' => [
        'include' => 'eherkenning.onstart_operations',
        'properties[year]' => [
            'case0' => [
                'equal' => [
                    'property' => 'properties[year]',
                    'lastYear',
                ],
                'getCurrentYear' => [-1],
            ],
            'case1' => [
                'equal' => [
                    'property' => 'properties[year]',
                    'year',
                ],
                'getCurrentYear' => [],
            ],
        ]
    ],
];
