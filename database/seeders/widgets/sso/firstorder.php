<?php

$widgets[] = [
    'reference_name' => 'firstorder',
    'display_name' => 'FirstOrder',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2022-04-01',
    'order' => 100,
    'licenses' => 'accounting_uk',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.3.4',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://app.thecosechouse.co.uk/firstorder.php',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/app\.thecosechouse\.co\.uk\/firstorder\.php',
                    'instructions' => [
                        'step0' => ['selector' => 'form input#email', 'func' => 'wait_for_element'],
                        'step1' => ['selector' => 'form input#email', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        'step2' => ['selector' => 'form input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        'step3' => ['selector' => 'form input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        'step4' => ['selector' => 'form input#login', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
