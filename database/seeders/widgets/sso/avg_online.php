<?php

$widgets[] = [
    'reference_name' => 'avg_online',
    'migrate_from' => '628506cfc83b0_a_v_g_online',
    'display_name' => 'AVG Online',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2022-05-18',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.6.9',
        'clear_cookies' => 'mijn.avgonline.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://mijn.avgonline.nl/',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/mijn\.avgonline\.nl\/',
                    'instructions' => [
                        ['selector' => 'input[type="email"]', 'func' => 'wait_for_element'],
                        ['selector' => 'input[type="email"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input[type="password"]', 'func' => 'wait_for_element'],
                        ['selector' => 'input[type="password"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'button[type="submit"]', 'func' => 'wait_for_element'],
                        ['selector' => 'button[type="submit"]', 'func' => 'click']
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
