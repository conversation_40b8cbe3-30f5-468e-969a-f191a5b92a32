<?php

$widgets[] = [
    'reference_name' => 'mks_tools',
    'display_name' => 'MKS Tools',
    'description' => 'Platform',
    'category' => ['general'],
    'status' => 'beta',
    'version_date' => '2019-09-06',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.3.4',
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://applicatie.mks.tools/Applicatie/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/applicatie\.mks\.tools\/Applicatie\/login',
                    'instructions' => [
                        'step0' => ['selector' => 'form input#Email', 'func' => 'wait_for_element'],
                        'step1' => ['selector' => 'form input#Email', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        'step2' => ['selector' => 'form input#Password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        'step3' => ['selector' => 'form input#Password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        'step4' => ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[fe_steps][step2][data][Email]' => [
            'type' => 'email',
        ],
        'properties[fe_steps][step2][data][Password]' => [
            'type' => 'password',
        ],
    ],
    'onstart_operations' => [
        'properties[username]' => [
            'property' => 'properties[fe_steps][step2][data][Email]',
        ],
        'properties[password]' => [
            'property' => 'properties[fe_steps][step2][data][Password]',
        ],
        'properties[fe_steps]' => [
            null
        ],
    ],
];
