<?php

$widgets[] = [
    'reference_name' => 'quovadis_up_eh',
    'display_name' => 'QuoVadis - eHerkenning',
    'description' => 'eHerkenning login',
    'category' => ['general'],
    'status' => 'beta',
    'version_date' => '2021-07-22',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.3.4',
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://beheer.qv-eherkenning.nl/beheer',
            'pages' => [
                'loginquovadis' => [
                    'pattern' => '^https:\/\/login\.qv-eherkenning\.nl\/QVLogin\/nl\/Login\/UsernamePassword',
                    'instructions' => [
                        'step0' => ['selector' => 'form input#Name', 'func' => 'wait_for_element'],
                        'step1' => ['selector' => 'form input#Name', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        'step2' => ['selector' => 'form input#Password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        'step3' => ['selector' => 'form input#Password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        'step4' => ['selector' => 'form button[name="login"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
