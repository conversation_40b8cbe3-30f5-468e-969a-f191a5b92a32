<?php

$widgets[] = [
    'reference_name' => 'scribe',
    'display_name' => 'Scribe',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2025-02-19',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => [
            'https://scribehow.com'
        ],
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://scribehow.com/signin',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/scribehow\.com\/signin',
                    'instructions' => [
                        ['selector' => 'input#email', 'func' => 'wait_for_element'],
                        ['selector' => 'input#email', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['value' => 700, 'func' => 'wait'],
                        ['selector' => '[data-testid="submitButton"]', 'func' => 'wait_for_element'],
                        ['selector' => '[data-testid="submitButton"]', 'func' => 'click'],
                        ['value' => 700, 'func' => 'wait'],
                        ['selector' => 'input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['value' => 700, 'func' => 'wait'],
                        ['selector' => '[data-testid="submitButton"]', 'func' => 'wait_for_element'],
                        ['selector' => '[data-testid="submitButton"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
