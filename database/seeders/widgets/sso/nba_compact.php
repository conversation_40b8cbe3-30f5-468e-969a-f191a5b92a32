<?php

$widgets[] = [
    'reference_name' => 'nba_compact',
    'migrate_from' => '65cf67bab7337_n_b_a_c_o_mpact',
    'display_name' => 'NBA COMpact',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2024-08-23',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '2.1.15',
        'clear_cookies' => 'compact.nba.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://compact.nba.nl/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/compact\.nba\.nl\/login',
                    'instructions' => [
                        ['func' => 'wait', 'value' => 2000],
                        ['selector' => '#app form input[type="email"]', 'func' => 'wait_for_element'],
                        ['selector' => '#app form input[type="email"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => '#app form input[type="password"]', 'func' => 'wait_for_element'],
                        ['selector' => '#app form input[type="password"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => '#app form button[type="submit"]', 'func' => 'wait_for_element'],
                        ['selector' => '#app form button[type="submit"]', 'func' => 'click']
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username'
        ],
        'properties[password]' => [
            'type' => 'password'
        ]
    ],
];
