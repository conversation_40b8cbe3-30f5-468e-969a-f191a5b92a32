<?php

$widgets[] = [
    'reference_name' => 'jongeneel',
    'migrate_from' => '6368fe1f511df_jongeneel',
    'display_name' => 'Jongeneel',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2022-11-07',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.6.14',
        'clear_cookies' => 'www.jongeneel.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.jongeneel.nl/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/www\.jongeneel\.nl\/login',
                    'instructions' => [
                        ['selector' => 'input#j_username', 'func' => 'wait_for_element'],
                        ['selector' => 'input#j_username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input#j_password', 'func' => 'wait_for_element'],
                        ['selector' => 'input#j_password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form#loginForm > button[type="submit"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form#loginForm > button[type="submit"]', 'func' => 'click']
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
