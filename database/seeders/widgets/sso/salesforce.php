<?php

$widgets[] = [
    'reference_name' => 'salesforce',
    'display_name' => 'Salesforce',
    'description' => 'CRM',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-03-01',
    'order' => 0,
    'licenses' => 'accounting_nl|accounting_uk',
    'properties' => [
        'post' => [
            'url' => 'https://{{$domain}}/',
            'data' => [
                'login' => 'Inloggen',
            ],
        ],
    ],
    'settings' => [
        'properties[post][data][un]' => [
            'type' => 'username',
        ],
        'properties[post][data][pw]' => [
            'type' => 'password',
        ],
        'properties[domain]' => [
            'type' => 'domain',
            'sanitize' => 'domain',
            'obligatory' => false,
            'note' => 'access_widget.field_domain_optional_note',
            'scope' => ['account', 'context', 'user'],
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'instructions' => [
                'title1' => 'widget.salesforce.totp_instructions.title',
                'step1' => 'widget.salesforce.totp_instructions.step1',
                'step2' => 'widget.salesforce.totp_instructions.step2',
                'step3' => 'widget.salesforce.totp_instructions.step3',
                'step4' => 'widget.salesforce.totp_instructions.step4',
                'step5' => 'widget.salesforce.totp_instructions.step5',
                'step6' => 'widget.salesforce.totp_instructions.step6',
            ]
        ],
    ],
    'onstart_operations' => [
        'properties[domain]' => [
            'if' => [
                'empty' => [
                    'property' => 'properties[domain]',
                ],
                'native' => 'login.salesforce.com'
            ],
        ],
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ],
        ],
        'properties[invisiblehand][pages][2fa_page]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                [
                    'pattern' => '^https:\/\/[a-zA-Z0-9._-]+\.com\/_ui\/identity\/verification\/method\/TotpVerificationUi\/e',
                    'instructions' => [
                        ['selector' => 'input#tc', 'func' => 'wait_for_element'],
                        ['selector' => 'input#tc', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                        ['selector' => 'input#save', 'func' => 'click'],
                    ],
                ]
            ]
        ]
    ]
];
