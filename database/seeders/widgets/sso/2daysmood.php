<?php

$widgets[] = [
    'reference_name' => '2daysmood',
    'display_name' => '2DaysMood',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2024-11-06',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '2.0.0',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://2daysmood.us/app/vue/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/2daysmood\.us\/app\/vue\/login',
                    'instructions' => [
                        ['func' => 'wait', 'value' => 6000],
                        ['selector' => 'form input#login_input_step1', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#login_input_step1', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form:has(#login_input_step1) button[type="submit"][name="Login"]', 'func' => 'click'],
                        ['selector' => 'form input#login_password_input', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form:has(#login_password_input) button[type="submit"][name="Login"]', 'func' => 'attr', 'value' => ['disabled', null]],
                        ['selector' => 'form:has(#login_password_input) button[type="submit"][name="Login"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
