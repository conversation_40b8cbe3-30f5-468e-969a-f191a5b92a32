<?php

$widgets[] = [
    'reference_name' => 'atlassian',
    'display_name' => 'Atlassian',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2020-07-06',
    'order' => 100,
    'licenses' => 'accounting_nl|accounting_uk',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.3',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://id.atlassian.com/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/id\.atlassian\.com\/login',
                    'instructions' => [
                        ['selector' => 'form#form-login input#username', 'func' => 'wait_for_element'],
                        [
                            'selector' => 'form#form-login input#username',
                            'func' => 'setvalue',
                            'value' => '{{$username}}'
                        ],
                        ['selector' => 'form#form-login button#login-submit', 'func' => 'click'],
                        ['selector' => 'form#form-login input#password', 'func' => 'wait_for_element'],
                        [
                            'selector' => 'form#form-login input#password',
                            'func' => 'setvalue',
                            'value' => '{{$password}}'
                        ],
                        [
                            'selector' => 'form#form-login input#password',
                            'func' => 'attr',
                            'value' => ['type', 'hidden']
                        ],
                        ['value' => 5000, 'func' => 'wait'],
                        ['selector' => 'form#form-login button[type="submit"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'instructions' => [
                'title1' => 'widget.atlassian.totp_instructions.title',
                'step1' => 'widget.atlassian.totp_instructions.step1',
                'step2' => 'widget.atlassian.totp_instructions.step2',
                'step3' => 'widget.atlassian.totp_instructions.step3',
                'step4' => 'widget.atlassian.totp_instructions.step4',
                'step5' => 'widget.atlassian.totp_instructions.step5',
            ],
        ],
    ],
    'onstart_operations' => [
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]'
                    ]
                ]
            ]
        ],
        'properties[invisiblehand][pages][mfpage]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]',
                ],
                [
                    'pattern' => '^https:\/\/auth\.atlassian\.com\/mf',
                    'instructions' => [
                        [
                            'selector' => 'div#two-step-verification-form form input[name="otpCode"]',
                            'func' => 'wait_for_element'
                        ],
                        [
                            'selector' => 'div#two-step-verification-form form input[name="otpCode"]',
                            'func' => 'setvalue',
                            'value' => '{{$totp}}'
                        ],
                        [
                            'selector' => 'div#two-step-verification-form form div#two-step-verification-submit button[type="submit"]',
                            'func' => 'click'
                        ],
                    ],
                ]
            ]
        ]
    ],
];
