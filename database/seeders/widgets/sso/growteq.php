<?php

$widgets[] = [
    'reference_name' => 'growteq',
    'display_name' => 'Growteq',
    'description' => 'Business Intelligence, CRM en procesoptimalisatie',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-02-20',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => true,
            'url' => 'https://youprovide.smartinsight.nu',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https://youprovideb2c\.b2clogin\.com/youprovideb2c\.onmicrosoft\.com\/b2c_1a_signup_signin\/oauth2\/v2.0\/authorize',
                    'instructions' => [
                    ],
                ],
                // 2FA not updated on 2021-08-04 because no access to any real account, probably not working anymore.
                '2fapage' => [
                    'pattern' => '^https:\/\/account\.smartinsight\.nu\/Identity\/Account\/LoginWith2fa',
                    'instructions' => [
                        ['selector' => 'form input#Input_TwoFactorCode', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#Input_TwoFactorCode', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                        ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                    ]
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[use_microsoft]' => [
            'title' => 'access_widget.use_microsoft',
            'type' => 'switch',
            'default_value' => false,
        ],
        'properties[username]' => [
            'type' => 'email_as_username',
            'obligatory' => false,
        ],
        'properties[password]' => [
            'type' => 'password',
            'obligatory' => false,
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'instructions' => [
                'title1' => 'widget.growteq.instructions.title',
                'step1' => 'widget.growteq.instructions.step1',
                'step2' => 'widget.growteq.instructions.step2',
                'step3' => 'widget.growteq.instructions.step3',
                'step4' => 'widget.growteq.instructions.step4',
            ],
        ],
    ],
    'onstart_operations' => [
        'properties[invisiblehand][pages][loginpage][instructions]' => [
            'if' => [
                'equal' => [
                    'property' => 'properties[use_microsoft]',
                    true,
                ],
                [
                    ['selector' => 'button#AzureADCommonExchange', 'func' => 'wait_for_element'],
                    ['selector' => 'button#AzureADCommonExchange', 'func' => 'click'],
                ]
            ],
            'else' => [
                [
                    ['selector' => 'form input#signInName', 'func' => 'wait_for_element'],
                    ['selector' => 'form input#signInName', 'func' => 'setvalue', 'value' => '{{$username}}'],
                    ['selector' => 'form input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                    ['selector' => 'form input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                    ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                ]
            ]
        ],
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]'
                    ]
                ]
            ]
        ],
    ],
    'onstart_checks' => [
        'settings_complete_user' => [
            'disjunction' => [
                'equal' => [
                    'property' => 'properties[use_microsoft]',
                    true
                ],
                'conjunction' => [
                    'property0' => 'properties[username]',
                    'property1' => 'properties[password]',
                ]
            ]
        ],
    ]
];
