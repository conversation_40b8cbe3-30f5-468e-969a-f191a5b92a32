<?php

$widgets[] = [
    'reference_name' => 'hyarchis_support',
    'display_name' => 'Hyarchis support',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-01-09',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://helpdesk-hyarchis.atlassian.net',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://helpdesk-hyarchis.atlassian.net/servicedesk/customer/user/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/helpdesk-hyarchis\.atlassian\.net\/servicedesk\/customer\/user\/login',
                    'instructions' => [
                        ['selector' => 'form input#user-email', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#user-email', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form button#login-button', 'func' => 'click'],
                        ['selector' => 'form input#user-password', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#user-password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#user-password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form button#login-button', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
