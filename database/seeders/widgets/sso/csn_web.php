<?php

$widgets[] = [
    'reference_name' => 'csn_web',
    'display_name' => 'CSN Web',
    'description' => 'Salarisverwerking',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2022-11-02',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.6.13',
        'clear_cookies' => 'https://www.csnweb.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.csnweb.nl/login.aspx',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/www\.csnweb\.nl\/login\.aspx',
                    'instructions' => [
                        ['selector' => 'form input#input_Gebruikernaam', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#input_Gebruikernaam', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input#input_Wachtwoord', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#input_Wachtwoord', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form input#button_Login', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[post][data][username]' => [
            'type' => 'username',
        ],
        'properties[post][data][password]' => [
            'type' => 'password',
        ],
    ],
    'onstart_operations' => [
        'properties[username]' => [
            'property' => 'properties[post][data][username]',
        ],
        'properties[password]' => [
            'property' => 'properties[post][data][password]',
        ],
    ],
];
