<?php

$widgets[] = [
    'reference_name' => 'afas_outsite_openid',
    'display_name' => 'AFAS Outsite (OpenID)',
    'description' => 'Medewerkersportaal',
    'category' => ['general'],
    'status' => 'beta',
    'version_date' => '2019-10-08',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [],
    'settings' => [
        'properties[consumer_id]' => [
            'type' => 'combo',
            'title' => 'access_widget.realm',
            'overwritable' => true,
            'icon' => 'square-down',
            'default_value' => null,
            'values' => null,
            'values_operation' => [
                'get_generic_widget_consumers' => ['get_widget' => []]],
            'validation' => null,
            'scope' => ['account'],
        ],
        'properties[username]' => [
            'type' => 'email_as_username',
            'scope' => ['user'],
        ],
    ],
    'onstart_operations' => [
        'afas_outsite_connect' => [
            'afas_outsite_connect' => [
                'get_consumer' => [
                    'get_widget' => [],
                    'property' => 'properties[consumer_id]',
                ],
                'property' => 'properties[username]',
            ]
        ],
        'properties[redirect_url]' => [
            'get_consumer_login_url' => [
                'get_consumer' => [
                    'get_widget' => [],
                    'property' => 'properties[consumer_id]',
                ],
            ],
        ],
    ],
    'onstart_checks' => [
        'environment_incomplete_settings' => [
            'not_empty' => [
                'property' => 'properties[redirect_url]',
            ],
        ],
    ],
    'oninstall_operations' => null,
];
