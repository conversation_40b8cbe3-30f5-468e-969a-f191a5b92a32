<?php

$widgets[] = [
    'reference_name' => 'amando',
    'display_name' => 'Amando',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2021-11-10',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://amando.cloud',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://amando.cloud/account/login/?step=1',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/amando\.cloud\/account\/login\/\?step\=1',
                    'instructions' => [
                        ['selector' => 'form input#id_auth-username', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#id_auth-username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input#id_auth-password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#id_auth-password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form', 'func' => 'attr', 'value' => ['action', './?step=2']],
                        ['selector' => 'form button[type="submit"]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'instructions' => [
                'title1' => 'widget.amando.totp_instructions.title',
                'step1' => 'widget.amando.totp_instructions.step1',
                'step2' => 'widget.amando.totp_instructions.step2',
                'step3' => 'widget.amando.totp_instructions.step3',
                'step4' => 'widget.amando.totp_instructions.step4',
                'step5' => 'widget.amando.totp_instructions.step5',
                'step6' => 'widget.amando.totp_instructions.step6',
                'step7' => 'widget.amando.totp_instructions.step7',
            ],
        ],
    ],
    'onstart_operations' => [
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ],
        ],
        'properties[invisiblehand][pages]' => [
            'case' => [
                'not_null' => [
                    'property' => 'properties[totp_secret]',
                ],
                'array_merge' => [
                    'property' => 'properties[invisiblehand][pages]',
                    [
                        '2fa_page' => [
                            'pattern' => '^https:\/\/amando\.cloud\/account\/login\/\?step\=2',
                            'instructions' => [
                                ['selector' => 'form input#id_token-otp_token', 'func' => 'wait_for_element'],
                                ['selector' => 'form input#id_token-otp_token', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                                ['selector' => 'form button[type=submit].btn-primary', 'func' => 'click'],
                            ],
                        ]
                    ]
                ],
            ]
        ]
    ]
];
