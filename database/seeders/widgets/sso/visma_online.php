<?php

$widgets[] = [
    'reference_name' => 'visma_online',
    'display_name' => 'Visma Online',
    'description' => 'Online ERP, eAccounting, AccountView',
    'category' => ['accountancy'],
    'status' => 'beta',
    'version_date' => '2022-11-09',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.2.0',
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://connect.visma.com/',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/connect\.visma\.com',
                    'instructions' => [
                        ['selector' => 'form input#Username', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#Username', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input#LoginButton', 'func' => 'click'],
                    ],
                ],
                'passwordpage' => [
                    'pattern' => '^https:\/\/connect\.visma\.com\/password',
                    'instructions' => [
                        ['selector' => 'form input#Password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#Password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => '.recaptcha-checkbox-border', 'func' => 'click'],
                        ['selector' => 'form input#LoginButton', 'func' => 'click'],
                    ]
                ]
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
        'properties[totp_secret]' => [
            'type' => 'totp_secret',
            'obligatory' => false,
            'instructions' => [
                'title1' => 'widget.visma_online.totp_instructions.title',
                'step1' => 'widget.visma_online.totp_instructions.step1',
                'step2' => 'widget.visma_online.totp_instructions.step2',
                'step3' => 'widget.visma_online.totp_instructions.step3',
                'step4' => 'widget.visma_online.totp_instructions.step4',
                'step5' => 'widget.visma_online.totp_instructions.step5',
                'step6' => 'widget.visma_online.totp_instructions.step6',
            ]
        ],
    ],
    'onstart_operations' => [
        'properties[totp]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp_secret]',
                ],
                'current' => [
                    'generate_totp' => [
                        'property' => 'properties[totp_secret]',
                    ],
                ],
            ],
        ],
        'properties[invisiblehand][pages][2fa_page]' => [
            'case' => [
                'not_empty' => [
                    'property' => 'properties[totp]'
                ],
                [
                    'pattern' => '^https:\/\/connect\.visma\.com\/totp',
                    'instructions' => [
                        ['selector' => 'input#AuthCode', 'func' => 'wait_for_element'],
                        ['selector' => 'input#AuthCode', 'func' => 'setvalue', 'value' => '{{$totp}}'],
                        ['selector' => 'input#ButtonSubmit', 'func' => 'click'],
                    ],
                ]
            ]
        ]
    ],
];
