<?php

$widgets[] = [
    'reference_name' => 'farefinder',
    'display_name' => 'FareFinder',
    'description' => 'Airtrade',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2019-06-06',
    'order' => 100,
    'licenses' => 'custom_tioga_tours',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.3.4',
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.farefinder.nl/login',
            'pages' => [
                'homepage' => [
                    'pattern' => '^https:\/\/www\.farefinder\.nl\/login',
                    'instructions' => [
                        'step0' => ['selector' => 'button[ng-click="$ctrl.login()"]', 'func' => 'wait_for_element'],
                        'step1' => ['selector' => 'button[ng-click="$ctrl.login()"]', 'func' => 'click'],
                    ],
                ],
                'loginpage' => [
                    'pattern' => '^https:\/\/auth\.airtrade\.com\/login',
                    'instructions' => [
                        'step0' => ['selector' => 'form input#UserName', 'func' => 'wait_for_element'],
                        'step1' => ['selector' => 'form input#UserName', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        'step2' => ['selector' => 'form input#Password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        'step3' => ['selector' => 'form input#Password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        'step4' => ['selector' => 'form button[type=submit]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
