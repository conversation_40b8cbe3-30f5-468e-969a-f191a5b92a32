<?php

$widgets[] = [
    'reference_name' => 'multit_it_client_portal',
    'display_name' => 'Multit IT Client portal',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-03-24',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://multit.itclientportal.com',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://multit.itclientportal.com/ClientPortal/Login.aspx',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/multit\.itclientportal\.com\/ClientPortal\/Login\.aspx',
                    'instructions' => [
                        ['selector' => 'input#clientAccessUserLogin_UserName', 'func' => 'wait_for_element'],
                        ['selector' => 'input#clientAccessUserLogin_UserName', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input#clientAccessUserLogin_Password', 'func' => 'wait_for_element'],
                        ['selector' => 'input#clientAccessUserLogin_Password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'input#clientAccessUserLogin_LoginButton', 'func' => 'wait_for_element'],
                        ['selector' => 'input#clientAccessUserLogin_LoginButton', 'func' => 'click']
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username'
        ],
        'properties[password]' => [
            'type' => 'password'
        ]
    ],
];
