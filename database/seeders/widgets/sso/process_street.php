<?php

$widgets[] = [
    'reference_name' => 'process_street',
    'display_name' => 'Process Street',
    'description' => 'Process and workflow management',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2020-10-23',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => 'https://app.process.st',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://app.process.st/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/app\.process\.st\/login',
                    'instructions' => [
                        ['selector' => 'form input#email', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#email', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
                        ['selector' => 'form button[type=submit]', 'func' => 'click'],
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
