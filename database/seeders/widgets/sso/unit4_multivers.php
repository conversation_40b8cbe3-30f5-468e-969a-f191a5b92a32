<?php

$widgets[] = [
    'reference_name' => 'unit4_multivers',
    'display_name' => 'Multivers/Accounting Online',
    'description' => 'Online boekhouden',
    'category' => ['accountancy'],
    'status' => 'beta',
    'version_date' => '2021-04-19',
    'order' => 60,
    'licenses' => 'accounting_nl|accounting_be',
    'uptime_monitor_url' => 'https://u4bsw.cloud.com',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.4.2',
        'clear_cookies' => [
            'https://login.microsoftonline.com/'
        ],
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://u4bsw.cloud.com/Citrix/StoreWeb/#/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => '^https:\/\/login\.microsoftonline\.com\/[a-zA-Z-0-9]+\/oauth2\/authorize?.*$',
                    'instructions' => [
                        ['selector' => 'form input#i0116', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => 1500],
                        ['selector' => 'form input#i0116', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['func' => 'wait', 'value' => 500],
                        ['selector' => 'form input#idSIButton9', 'func' => 'click'],
                        ['func' => 'wait', 'value' => 1000],
                        ['selector' => 'form input#i0118', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => 1000],
                        ['selector' => 'form input#i0118', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['func' => 'wait', 'value' => 500],
                        ['selector' => 'form input#idSIButton9', 'func' => 'click'],
                    ]
                ],
                'loginpage_sso_reload' => [
                    'pattern' => '^https:\/\/login\.microsoftonline\.com\/[a-zA-Z-0-9]+\/oauth2\/authorize.*sso_reload=true',
                    'instructions' => [
                        ['selector' => 'form input#i0116', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => 1500],
                        ['selector' => 'form input#i0116', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['func' => 'wait', 'value' => 500],
                        ['selector' => 'form input#idSIButton9', 'func' => 'click'],
                        ['func' => 'wait', 'value' => 1000],
                        ['selector' => 'form input#i0118', 'func' => 'wait_for_element'],
                        ['func' => 'wait', 'value' => 1000],
                        ['selector' => 'form input#i0118', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['func' => 'wait', 'value' => 500],
                        ['selector' => 'form input#idSIButton9', 'func' => 'click'],
                    ]
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[post][data][UsernameTextBox]' => [
            'type' => 'username',
        ],
        'properties[post][data][PasswordTextBox]' => [
            'type' => 'password',
        ],
    ],
    'onstart_operations' => [
        'properties[password]' => [
            'property' => 'properties[post][data][PasswordTextBox]',
        ],
        'properties[post][data][UsernameTextBox]' => [
            'if' => [
                'itcontains' => "@",
                'property' => 'properties[post][data][UsernameTextBox]',
            ],
            'else' => [
                'concat' => [
                    'property' => 'properties[post][data][UsernameTextBox]',
                    '@u4bsw.nl',
                ],
            ]
        ],
        'properties[username]' => [
            'property' => 'properties[post][data][UsernameTextBox]',
        ],
    ],
];
