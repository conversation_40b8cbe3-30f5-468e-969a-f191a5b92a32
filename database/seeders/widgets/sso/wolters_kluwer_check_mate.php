<?php

$widgets[] = [
    'reference_name' => 'wolters_kluwer_check_mate',
    'migrate_from' => '6262a67d97d44_wolters_kluwer_check_mate',
    'display_name' => 'Wolters Kluwer CheckMate',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2022-04-22',
    'order' => 100,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.6.9',
        'clear_cookies' => 'checkmate.kluwer.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://checkmate.kluwer.nl/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/checkmate\.kluwer\.nl\/login',
                    'instructions' => [
                        ['selector' => 'form input[type="text"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[type="text"]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input[type="password"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[type="password"]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form input[type="submit"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form input[type="submit"]', 'func' => 'click']
                    ],
                ],
            ],
        ],
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username',
        ],
        'properties[password]' => [
            'type' => 'password',
        ],
    ],
];
