<?php

$widgets[] = [
    'reference_name' => 'cube_charging',
    'migrate_from' => '64feba406922a_cube_charging',
    'display_name' => 'CubeCharging',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-09-11',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '2.1.9',
        'clear_cookies' => 'https://www.cubecharging.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://www.cubecharging.nl/web/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/www\.cubecharging\.nl\/([a-z]+\/|)web\/login',
                    'instructions' => [
                        ['selector' => 'form input#login', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#login', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'form input#password', 'func' => 'wait_for_element'],
                        ['selector' => 'form input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'form button[type="submit"]', 'func' => 'wait_for_element'],
                        ['selector' => 'form button[type="submit"]', 'func' => 'click']
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username'
        ],
        'properties[password]' => [
            'type' => 'password'
        ]
    ],
];
