<?php

$widgets[] = [
    'reference_name' => 'sum_up',
    'display_name' => 'SumUp',
    'description' => '',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2025-02-18',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '1.6.10',
        'clear_cookies' => 'https://auth.sumup.com',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://auth.sumup.com/flows/login',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/auth\.sumup\.com\/flows\/login',
                    'instructions' => [
                        ['selector' => 'input[type=email]', 'func' => 'wait_for_element'],
                        ['selector' => 'input[type=email]', 'func' => 'setvalue', 'value' => '{{$username}}'],
                        ['selector' => 'input[type=password]', 'func' => 'wait_for_element'],
                        ['selector' => 'input[type=password]', 'func' => 'setvalue', 'value' => '{{$password}}'],
                        ['selector' => 'button[type="submit"]', 'func' => 'wait_for_element'],
                        ['selector' => 'button[type="submit"]', 'func' => 'click']
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'email_as_username'
        ],
        'properties[password]' => [
            'type' => 'password'
        ]
    ],
];
