<?php

$widgets[] = [
    'reference_name' => 'wagenparkmodule',
    'migrate_from' => '64d39a5b2476a_wagenparkmodule',
    'display_name' => 'Wagenparkmodule',
    'description' => 'Verzekeringen',
    'category' => ['other'],
    'status' => 'beta',
    'version_date' => '2023-08-09',
    'order' => 0,
    'licenses' => 'accounting_nl',
    'properties' => [
        'cors_ext' => true,
        'cors_ext_version' => '2.1.7',
        'clear_cookies' => 'mijn-verzekeringen.nl',
        'hybrid' => false,
        'invisiblehand' => [
            'showloader' => false,
            'url' => 'https://mijn-verzekeringen.nl/cvb_beheer/Login.aspx',
            'pages' => [
                'loginpage' => [
                    'pattern' => 'https:\/\/mijn-verzekeringen\.nl\/cvb_beheer\/Login\.aspx',
                    'instructions' => [
                        [
                            'selector' => 'input#BeheerMaster_MC_txtUser',
                            'func' => 'wait_for_element'
                        ],
                        [
                            'selector' => 'input#BeheerMaster_MC_txtUser',
                            'func' => 'setvalue',
                            'value' => '{{$username}}'
                        ],
                        [
                            'selector' => 'input#BeheerMaster_MC_txtPassword',
                            'func' => 'wait_for_element'
                        ],
                        [
                            'selector' => 'input#BeheerMaster_MC_txtPassword',
                            'func' => 'setvalue',
                            'value' => '{{$password}}'
                        ],
                        [
                            'selector' => 'input#BeheerMaster_MC_btnSubmit',
                            'func' => 'wait_for_element'
                        ],
                        [
                            'selector' => 'input#BeheerMaster_MC_btnSubmit',
                            'func' => 'click'
                        ]
                    ]
                ]
            ]
        ]
    ],
    'settings' => [
        'properties[username]' => [
            'type' => 'username'
        ],
        'properties[password]' => [
            'type' => 'password'
        ]
    ],
];
