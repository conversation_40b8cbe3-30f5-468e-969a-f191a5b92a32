<?php

$includes['sdu_pages'] = [
    'loginpage' => [
        'pattern' => '^https:\/\/login\.sdu\.nl\/as\/[a-zA-Z0-9-]*\/resume\/as\/authorization\.ping?.*$',
        'instructions' => [
            ['selector' => 'form input#username', 'func' => 'wait_for_element'],
            ['selector' => 'form input#username', 'func' => 'setvalue', 'value' => '{{$username}}'],
            ['selector' => 'form input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
            ['selector' => 'form input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
            ['selector' => 'form button[type=submit]', 'func' => 'click'],
        ],
    ],
    'account_page' => [
        'pattern' => '^https:\/\/federate\.prod\.ping\.awssdu\.nl\/as\/[a-zA-Z0-9-]*\/resume\/as\/authorization\.ping?.*$',
        'instructions' => [
            ['selector' => 'form input#username', 'func' => 'wait_for_element'],
            ['selector' => 'form input#username', 'func' => 'setvalue', 'value' => '{{$username}}'],
            ['selector' => 'form input#password', 'func' => 'setvalue', 'value' => '{{$password}}'],
            ['selector' => 'form input#password', 'func' => 'attr', 'value' => ['type', 'hidden']],
            ['selector' => 'form button[type=submit]', 'func' => 'click'],
        ],
    ],
];