<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('account_billing_products', function (Blueprint $table) {
            $table->decimal('monthly_contract_price')->default(0);
            $table->decimal('yearly_contract_price')->default(0);
            $table->decimal('yearly_contract_combo_price')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('account_billing_products', function (Blueprint $table) {
            $table->dropColumn(['monthly_contract_price', 'yearly_contract_price', 'yearly_contract_combo_price']);
        });
    }
};
