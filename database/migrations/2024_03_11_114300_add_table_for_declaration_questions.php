<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('open_question_declarations');
        Schema::create('open_question_declarations', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('open_question_id');
            $table->foreign('open_question_id')
                ->references('id')
                ->on('open_questions')
                ->cascadeOnUpdate()
                ->cascadeOnDelete();
            $table->string('description')->nullable();
            $table->string('external_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('open_question_declarations');
    }
};
