<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('service_tasks', function (Blueprint $table) {
            $table->unsignedInteger('account_service_upload_type_id')->nullable()->after('service_task_group_id');
            $table->foreign('account_service_upload_type_id')
                ->references('id')
                ->on('account_service_upload_types')
                ->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('service_tasks', function (Blueprint $table) {
            $table->dropForeign('service_tasks_account_service_upload_type_id_foreign');
            $table->dropColumn('account_service_upload_type_id');
        });
    }
};
