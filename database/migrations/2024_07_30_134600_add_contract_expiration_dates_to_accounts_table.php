<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->date('contract_end_date')
                ->nullable()
                ->after('trial_end_date')
                ->index('contract_end_date_index');
            $table->date('delete_after')
                ->nullable()
                ->after('contract_end_date')
                ->index('delete_after_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->dropIndex('contract_end_date_index');
            $table->dropColumn('contract_end_date');
            $table->dropIndex('delete_after_index');
            $table->dropColumn('delete_after');
        });
    }
};
