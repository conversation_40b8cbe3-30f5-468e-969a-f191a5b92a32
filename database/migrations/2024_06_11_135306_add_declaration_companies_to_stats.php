<?php

use App\AccountUsageStatistics;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table(AccountUsageStatistics::TABLE, function (Blueprint $table) {
            $table->unsignedInteger(AccountUsageStatistics::DECLARATION_COMPANIES)
                ->nullable();
        });

    }

    public function down(): void
    {
        Schema::table(AccountUsageStatistics::TABLE, function (Blueprint $table) {
            $table->dropColumn(AccountUsageStatistics::DECLARATION_COMPANIES);
        });
    }
};
