<?php

namespace Database\Factories\Company;

use App\CompanyIdentifier;
use Illuminate\Database\Eloquent\Factories\Factory;

class CompanyIdentifierFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = CompanyIdentifier::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [];
    }
}
