<?php

namespace Database\Factories\OpenQuestions\Templates;

use App\Models\OpenQuestions\Questions\Templates\TemplateAnswerAttachment;
use Illuminate\Database\Eloquent\Factories\Factory;

class TemplateAnswerAttachmentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = TemplateAnswerAttachment::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [];
    }
}
