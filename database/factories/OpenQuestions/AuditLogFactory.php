<?php

namespace Database\Factories\OpenQuestions;

use App\Models\OpenQuestions\OpenQuestionAuditLog;
use Illuminate\Database\Eloquent\Factories\Factory;

class AuditLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = OpenQuestionAuditLog::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [];
    }
}
