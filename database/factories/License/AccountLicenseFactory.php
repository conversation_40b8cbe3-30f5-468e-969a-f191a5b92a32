<?php

namespace Database\Factories\License;

use App\AccountLicense;
use Illuminate\Database\Eloquent\Factories\Factory;

class AccountLicenseFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = AccountLicense::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [];
    }
}
