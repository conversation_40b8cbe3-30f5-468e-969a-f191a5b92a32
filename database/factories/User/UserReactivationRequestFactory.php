<?php

namespace Database\Factories\User;

use App\Models\UserReactivationRequest;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserReactivationRequestFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = UserReactivationRequest::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [];
    }
}
