<?php

namespace Database\Factories\ServiceTask;

use App\Models\ServiceTask\ServiceTaskAssociation;
use Illuminate\Database\Eloquent\Factories\Factory;

class ServiceTaskAssociationFactory extends Factory
{
    protected $model = ServiceTaskAssociation::class;

    public function definition(): array
    {
        return [
            ServiceTaskAssociation::UUID => $this->faker->uuid
        ];
    }
}
