<?php

namespace Database\Factories\ServiceTask\AuditLog;

use App\TaskAuditLog;
use Illuminate\Database\Eloquent\Factories\Factory;

class TaskAuditLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = TaskAuditLog::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [];
    }
}
