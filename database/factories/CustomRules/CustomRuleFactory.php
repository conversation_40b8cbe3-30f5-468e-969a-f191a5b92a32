<?php

namespace Database\Factories\CustomRules;

use App\Models\CustomRules\CustomRule;
use Illuminate\Database\Eloquent\Factories\Factory;

class CustomRuleFactory extends Factory
{
    protected $model = CustomRule::class;

    public function definition(): array
    {
        return [
            'active' => 1,
            'action' => ucfirst($this->faker->word()) . ucfirst($this->faker->word()),
            'parameters' => [],
            'followed_at' => null
        ];
    }
}
