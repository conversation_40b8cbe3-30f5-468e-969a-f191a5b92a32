{
  "manifest_version": 3,
  "name": "Hix Browser Extension",
  "author": "Hix",
  "version": "<%= options.version %>",
  "action": {
    "default_title": "Hix",
    "default_popup": "popup.html",
    "default_icon": {
      "16": "/icons/grey-16.png",
      "32": "/icons/grey-32.png"
    }
  },
    "web_accessible_resources": [
        {
            "resources": [
                "icons/belastingtool.svg",
                "icons/checkmark.svg",
                "icons/cross.svg",
                "icons/attachment.svg",
                "icons/notification-success.svg",
                "icons/notification-error.svg",
                "icons/notification-info.svg"
            ],
            "matches": ["<all_urls>"]
        }
    ],
  "description": "Hix Browser Extension",
  "homepage_url": "https://hellohix.com/",
  "icons": {
    "48": "icons/color-48.png",
    "96": "icons/color-96.png"
  },

    <% if (options.target === 'chrome' || options.target === 'anaheim') { %>
    "incognito": "split",
    <% } %>

    "host_permissions": [
        "http://*/*",
        "https://*/*"
    ],
  <% if (options.target === 'firefox' || options.target === 'anaheim') { %>
  "browser_specific_settings": {
    "gecko": {
      "id": "{1832e615-7186-4e0d-a32f-0c91ea9d7cd7}",
      "strict_min_version": "122.0",
      "update_url": "https://securelogin.securelogin.nu/ext/updates.json"
    }
  },
    "permissions": [
        "webRequest",
        "webRequestBlocking",
        "webNavigation",
        "activeTab",
        "tabs",
        "cookies",
        "scripting",
        "storage"
    ],
  "background": {
    "scripts": ["dist/background.js"]
  }
  <% } %>
<% if (options.target === 'chrome') { %>
    "declarative_net_request": {
        "rule_resources": [{
        "id": "addExtensionVersion",
        "enabled": true,
        "path": "rules.json"
        }]
    },
    "permissions": [
        "webRequest",
        "webNavigation",
        "activeTab",
        "tabs",
        "cookies",
        "scripting",
        "storage",
        "declarativeNetRequest",
        "declarativeNetRequestFeedback",
        "alarms"
    ],
    "background": {
        "service_worker": "dist/background.js",
        "type": "module"
    }
<% } %>
}
