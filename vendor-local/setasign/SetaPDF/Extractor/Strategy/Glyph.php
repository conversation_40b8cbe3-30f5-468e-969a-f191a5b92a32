<?php
/**
 * This file is part of the SetaPDF-Extractor Component
 *
 * @copyright  Copyright (c) 2024 Setasign GmbH & Co. KG (https://www.setasign.com)
 * @category   SetaPDF
 * @package    SetaPDF_Extractor
 * @license    https://www.setasign.com/ Commercial
 * @version    $Id: Glyph.php 1926 2024-03-13 15:33:09Z jan.slabon $
 */

/**
 * Extraction strategy for single glyphs.
 *
 * The result of this strategy is not sorted.
 *
 * @copyright  Copyright (c) 2024 Setasign GmbH & Co. KG (https://www.setasign.com)
 * @category   SetaPDF
 * @package    SetaPDF_Extractor
 * @subpackage SetaPDF_Extractor_Strategy
 * @license    https://www.setasign.com/ Commercial
 */
class SetaPDF_Extractor_Strategy_Glyph extends SetaPDF_Extractor_Strategy_Plain
{
    /**
     * Defines whether space characters should be ignored or not.
     *
     * @var bool
     */
    protected $_ignoreSpaceCharacter = false;

    /**
     * Get all resoved glyphs.
     *
     * @param string $stream
     * @param SetaPDF_Core_Type_Dictionary $resources
     * @return SetaPDF_Extractor_Result_Collection
     */
    public function getResult($stream, SetaPDF_Core_Type_Dictionary $resources)
    {
        $items = $this->process($stream, $resources);

        $glyphs = new SetaPDF_Extractor_Result_Collection();
        foreach ($items as $item) {
            $glyphs[] = new SetaPDF_Extractor_Result_Glyph($item);
        }

        return $glyphs;
    }

    /**
     * @internal
     */
    protected function _init()
    {
        // do nothing
    }

    /**
     * Callback that is called if a text should be shown.
     *
     * @param array $arguments
     * @param mixed $operator
     * @throws SetaPDF_Core_Exception
     */
    public function _onTextShow($arguments, $operator)
    {
        $argumentsCount = \count($arguments);
        if ($argumentsCount < 1) {
            return;
        }

        $text = $this->getGraphicState()->text();

        switch ($operator) {
            case "'":
                $text->moveToStartOfNextLine();
                $this->_showText($arguments[0]->getValue());
                break;
            case '"':
                if ($argumentsCount < 3) {
                    return;
                }
                $text->setWordSpacing($arguments[1]->getValue());
                $text->setCharacterSpacing($arguments[2]->getValue());
                $this->_showText($arguments[0]->getValue());
                break;
            case 'Tj':
                $this->_showText($arguments[0]->getValue());
                break;
            case 'TJ':
                $this->_showTextStrings($arguments[0]->toPhp());
                break;
        }
    }

    /**
     * Method that shows text.
     *
     * @param string $string
     * @throws SetaPDF_Core_Exception
     */
    protected function _showText($string)
    {
        $text = $this->getGraphicState()->text();
        $font = $text->getFont();
        if ($string === '' || $font === false) {
            return;
        }

        $fontSize = $text->getFontSize();
        $scaling = $text->getScaling();
        $wordSpacing = $text->getWordSpacing();
        $characterSpacing = $text->getCharacterSpacing();

        /**
         * @var SetaPDF_Core_Font $font
         */
        $start = $end = $text->getTextMatrix();
        $ctm = $this->getGraphicState()->getCurrentTransformationMatrix();
        $prevString = null;
        foreach ($font->splitCharCodes($string) as $no => $charCode) {
            $w0 = $font->getGlyphWidthByCharCode($charCode);

            //$w1 = 0;
            if ($font instanceof SetaPDF_Core_Font_Type3) {
                $fontMatrix = $font->getFontMatrix();
                $v          = new SetaPDF_Core_Geometry_Vector($w0);
                $v          = $v->multiply($fontMatrix);
                $w0         = $v->getX();
            } else {
                $w0 /= 1000;
            }

            // [...]Word spacing [...] shall apply only to the ASCII SPACE character(20h).
            if ($charCode === "\x20") {
                $tx = ($w0 * $fontSize + $wordSpacing) * $scaling / 100;
            } else {
                $tx = ($w0 * $fontSize) * $scaling / 100;
            }
            $ty = 0;//$w1 * $this->getFontSize();

            $m = new SetaPDF_Core_Geometry_Matrix(1, 0, 0, 1, $tx, $ty);
            $end = $m->multiply($start);

            $item = new SetaPDF_Extractor_TextItem(
                $charCode,
                $font,
                $fontSize,
                $characterSpacing,
                $wordSpacing,
                $scaling,
                $start->multiply($ctm),
                $end->multiply($ctm),
                $this->_textCount . '.' . $no
            );

            $string = $item->getString();

            if (
                $prevString === null ||
                $string !== "\x20" ||
                ($string === "\x20" && $prevString !== "\x20")
            ) {

                if ($filterId = $this->_accept($item)) {
                    if ($filterId !== true) {
                        $item->setFilterId($filterId);
                    }

                    $this->_items[] = $item;
                }
            }

            if (SetaPDF_Core::isNotZero($characterSpacing)) {
                $tx = $characterSpacing * $scaling / 100;
                $m = new SetaPDF_Core_Geometry_Matrix(1, 0, 0, 1, $tx, 0);
                $end = $m->multiply($end);
            }

            $start = $end;
            $prevString = $string;
        }

        $text->setTextMatrix($end);
    }

    /**
     * Callback that is called if text strings should be shown.
     *
     * @param array $textStrings
     * @throws SetaPDF_Core_Exception
     */
    public function _showTextStrings($textStrings)
    {
        $text = $this->getGraphicState()->text();

        if (!is_array($textStrings)) {
            $textStrings = [$textStrings];
        }

        foreach ($textStrings as $textString) {
            if (is_float($textString) || is_int($textString)) {
                if (SetaPDF_Core::isZero($textString)) {
                    continue;
                }

                $tx = ((-$textString / 1000) * $text->getFontSize()) * $text->getScaling() / 100;
                $ty = 0;

                $m = new SetaPDF_Core_Geometry_Matrix(1, 0, 0, 1, $tx, $ty);

                $text->setTextMatrix($m->multiply($text->getTextMatrix()));

            } else {
                $this->_showText($textString);
            }
        }
    }

    /**
     * Defines whether a space character should be fetched or not.
     *
     * If this is set to true, the strategy will use the found space character as a delemitter.
     * If this is set to false (default), the strategy will calculate a delemitter by the distance of 2 charachters/glyphs.
     *
     * @param bool $ignoreSpaceCharacter
     */
    public function setIgnoreSpaceCharacter($ignoreSpaceCharacter = true)
    {
        $this->_ignoreSpaceCharacter = (boolean) $ignoreSpaceCharacter;
    }

    /**
     * Gets whether a space character should be fetched or not.
     *
     * @return bool
     */
    public function getIgnoreSpaceCharacter()
    {
        return $this->_ignoreSpaceCharacter;
    }

    /**
     * Proxy method that forwards the call to a {@link SetaPDF_Extractor_Filter_FilterInterface filter} instance if
     * available.
     *
     * This strategy filters space characters automatically if specified (see {@link setIgnoreSpaceCharacter()}.
     *
     * @param SetaPDF_Extractor_TextItem $textItem
     * @return bool|string
     * @throws SetaPDF_Core_Exception
     * @throws SetaPDF_Extractor_Exception
     * @see setFilter()
     * @see setIgnoreSpaceCharacter()
     */
    protected function _accept(SetaPDF_Extractor_TextItem $textItem)
    {
        if ($this->_ignoreSpaceCharacter) {
            // ignore spaces
            $string = $textItem->getString();
            if (
                $string === "\x20"             // SPACE
                || $string === "\xc2\xa0"      // NO-BREAK SPACE
                || $string === "\xE2\x80\x8b"  // ZERO WIDTH SPACE
                || ord($string[0]) < 32        // control characters
            ) {
                return false;
            }
        }

        return parent::_accept($textItem);
    }

    /**
     * Get an instance of the same strategy for processing an other stream (e.g. a Form XObject stream).
     *
     * @param SetaPDF_Core_Canvas_GraphicState $gs
     * @return static
     */
    protected function _getSubInstance(SetaPDF_Core_Canvas_GraphicState $gs)
    {
        $strategy = parent::_getSubInstance($gs);
        $strategy->_ignoreSpaceCharacter = $this->_ignoreSpaceCharacter;

        return $strategy;
    }
}
