<?php
/**
 * This file is part of the SetaPDF-Core Component
 *
 * @copyright  Copyright (c) 2024 Setasign GmbH & Co. KG (https://www.setasign.com)
 * @category   SetaPDF
 * @package    SetaPDF_Core
 * @subpackage Type
 * @license    https://www.setasign.com/ Commercial
 * @version    $Id: ObjectStream.php 1909 2024-02-08 09:55:57Z jan.slabon $
 */

/**
 * Class representing an object stream object.
 *
 * @copyright  Copyright (c) 2024 Setasign GmbH & Co. KG (https://www.setasign.com)
 * @category   SetaPDF
 * @package    SetaPDF_Core
 * @subpackage Type
 * @license    https://www.setasign.com/ Commercial
 */
class SetaPDF_Core_Type_ObjectStream extends SetaPDF_Core_Type_Stream
implements SetaPDF_Core_Type_Owner
{
    /**
     * The stream parser instance.
     *
     * @var SetaPDF_Core_Parser_Pdf
     */
    protected $_parser;

    /**
     * An array of object offsets in the stream keyed by object ids.
     *
     * @var array
     */
    protected $_objectOffsets = [];

    /**
     * The document instance to which this object stream belongs to.
     *
     * @var SetaPDF_Core_Type_Owner
     */
    protected $_owner;

    /**
     * Defines if a inner object had triggered a change to invalid the state of this object stream.
     *
     * @var bool
     */
    protected $_valid = true;

    /**
     * The unfiltered stream content
     *
     * @var string
     */
    protected $_unfilteredStream = null;

    /**
     * Ensures that the passed value is a SetaPDF_Core_Type_ObjectStream instance.
     *
     * @param mixed $stream
     * @return self
     * @throws SetaPDF_Core_Type_Exception
     */
    public static function ensureType($stream)
    {
        return self::_ensureType(self::class, $stream, 'ObjectStream value expected.');
    }

    /**
     * Release memory/cycled references.
     */
    public function cleanUp()
    {
        if ($this->_parser !== null) {
            $this->_parser->cleanUp();
            $this->_parser = null;
        }
        parent::cleanUp();
    }

    /**
     * Set the owner instance.
     *
     * @param SetaPDF_Core_Type_Owner $owner
     */
    public function setOwner(SetaPDF_Core_Type_Owner $owner)
    {
        $this->_owner = $owner;
    }

    /**
     * Get the owner instance.
     *
     * @return SetaPDF_Core_Type_Owner
     */
    public function getOwner()
    {
        return $this->_owner;
    }

    /**
     * Get the document instance.
     *
     * @return SetaPDF_Core_Document
     */
    public function getOwnerPdfDocument()
    {
        return $this->_owner->getOwnerPdfDocument();
    }

    /**
     * A proxy method to hold a copy of the unfiltered stream.
     *
     * @return string
     * @throws SetaPDF_Exception
     * @throws SetaPDF_Exception_NotImplemented
     */
    protected function _getUnfilteredStream()
    {
        if ($this->_unfilteredStream === null) {
            $this->_unfilteredStream = $this->getStream();
        }

        return $this->_unfilteredStream;
    }
    /**
     * Get the stream parser.
     *
     * @return SetaPDF_Core_Parser_Pdf
     * @throws SetaPDF_Core_Reader_Exception
     * @throws SetaPDF_Core_Type_Exception
     * @throws SetaPDF_Exception
     * @throws SetaPDF_Exception_NotImplemented
     */
    protected function _getParser()
    {
        if ($this->_parser === null) {
            $dict = $this->getValue();
            $firstPos = SetaPDF_Core_Type_Numeric::ensureType(
                SetaPDF_Core_Type_Dictionary_Helper::getValue($dict, 'First')
            )->getValue();
            $this->_parser = new SetaPDF_Core_Parser_Pdf(
                new SetaPDF_Core_Reader_String(substr($this->_getUnfilteredStream(), $firstPos))
            );

            // reset unfiltered stream if not needed anymore: Just check if offsets are also resolved
            if (count($this->_objectOffsets) !== 0) {
                $this->_unfilteredStream = null;
            }

            $this->_parser->setOwner($this);
        }

        return $this->_parser;
    }

    /**
     * Get the offset value for a specific object id.
     *
     * @param integer $objectId
     * @return integer
     * @throws SetaPDF_Core_Document_ObjectNotFoundException
     * @throws SetaPDF_Core_Type_Exception
     * @throws SetaPDF_Exception
     * @throws SetaPDF_Exception_NotImplemented
     */
    protected function _getObjectOffset($objectId)
    {
        $offsets = $this->getOffsets();

        if (!isset($offsets[$objectId])) {
            throw new SetaPDF_Core_Document_ObjectNotFoundException(
                sprintf('Position of object (%s) not found.', $objectId)
            );
        }

        return $offsets[$objectId];
    }

    /**
     * Get the offsets of all objects in this object stream.
     *
     * @return array
     * @throws SetaPDF_Core_Type_Exception
     * @throws SetaPDF_Exception
     * @throws SetaPDF_Exception_NotImplemented
     */
    public function getOffsets()
    {
        if (count($this->_objectOffsets) === 0) {
            $dict = $this->getValue();
            $firstPos = SetaPDF_Core_Type_Numeric::ensureType(
                SetaPDF_Core_Type_Dictionary_Helper::getValue($dict, 'First')
            )->getValue();
            $objectCount = SetaPDF_Core_Type_Numeric::ensureType(
                SetaPDF_Core_Type_Dictionary_Helper::getValue($dict, 'N')
            )->getValue();

            $stream = $this->_getUnfilteredStream();
            // reset unfiltered stream if not needed anymore: Just check if a parser instance exists
            if ($this->_parser !== null) {
                $this->_unfilteredStream = null;
            }

            $pairs = rtrim(substr($stream, 0, $firstPos));
            $pairs = preg_split('/[\x00\x09\x0A\x0C\x0D\x20]/', $pairs, $objectCount * 2, PREG_SPLIT_NO_EMPTY);
            if (!is_array($pairs)) {
                throw new SetaPDF_Core_Type_Exception(
                    'Offset pairs not found in object stream.'
                );
            }
            $pairCount = count($pairs);
            if ($pairCount % 2 !== 0) {
                throw new SetaPDF_Core_Type_Exception(
                    'Found offset pairs in object stream are not correctly aligned.'
                );
            }

            $this->_objectOffsets = [];
            for ($i = 0; $i < $pairCount; $i += 2) {
                $this->_objectOffsets[(int)$pairs[$i]] = (int)$pairs[$i + 1];
            }
        }

        return $this->_objectOffsets;
    }

    /**
     * Resolves an indirect object in this object stream.
     *
     * @param integer $objectId
     * @return SetaPDF_Core_Type_IndirectObject
     * @throws SetaPDF_Core_Document_ObjectNotFoundException
     * @throws SetaPDF_Core_Exception
     * @throws SetaPDF_Core_Parser_Pdf_InvalidTokenException
     * @throws SetaPDF_Core_Reader_Exception
     * @throws SetaPDF_Core_Type_Exception
     * @throws SetaPDF_Exception
     * @throws SetaPDF_Exception_NotImplemented
     */
    public function resolveIndirectObject($objectId)
    {
        $objectId = (int)$objectId;
        $parser = $this->_getParser();
        $parser->reset($this->_getObjectOffset($objectId));
        $value = $parser->readValue();

        return new SetaPDF_Core_Type_IndirectObject($value, $this, $objectId, 0);
    }

    /**
     * Triggered if a value of this object is changed. Forward this to the document in that case.
     *
     * A stream can only be observed by an indirect object.
     *
     * So let's check the observers for this type and forward it to its owning document instance
     * until we manage creation of object streams.
     *
     * @param SplSubject $SplSubject
     */
    #[\ReturnTypeWillChange]
    public function update(SplSubject $SplSubject)
    {
        if (!isset($this->_observed)) {
            return;
        }

        if ($SplSubject instanceof SetaPDF_Core_Type_IndirectObject && $SplSubject->getOwner() === $this) {
            $this->_valid = false;
            $observers = $this->_observers;
            foreach ($observers AS $observer) {
                if ($observer instanceof SetaPDF_Core_Type_IndirectObject) {
                    $observer->getOwner()->update($SplSubject);
                } /* should never be reached
                else {
                    $observer->update($this);
                }*/
            }
        } else {
            $this->notify();
        }
    }

    /**
     * Checks whether an object of this objects stream was changed or not.
     *
     * @return bool
     */
    public function isValid()
    {
        return $this->_valid;
    }
}
