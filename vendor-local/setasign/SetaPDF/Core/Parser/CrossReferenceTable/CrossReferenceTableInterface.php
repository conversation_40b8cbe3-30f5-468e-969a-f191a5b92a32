<?php
/**
 * This file is part of the SetaPDF-Core Component
 *
 * @copyright  Copyright (c) 2024 Setasign GmbH & Co. KG (https://www.setasign.com)
 * @category   SetaPDF
 * @package    SetaPDF_Core
 * @subpackage Parser
 * @license    https://www.setasign.com/ Commercial
 * @version    $Id: CrossReferenceTableInterface.php 1926 2024-03-13 15:33:09Z jan.slabon $
 */

/**
 * Cross-reference table interface
 *
 * @copyright  Copyright (c) 2024 Setasign GmbH & Co. KG (https://www.setasign.com)
 * @category   SetaPDF
 * @package    SetaPDF_Core
 * @subpackage Parser
 * @license    https://www.setasign.com/ Commercial
 */
interface SetaPDF_Core_Parser_CrossReferenceTable_CrossReferenceTableInterface
{
    /**
     * Check if the xref table uses compressed xref streams.
     *
     * @return boolean
     */
    public function isCompressed();

    /**
     * Get all defined object ids.
     *
     * This method returns an array of all objects which are noticed in any cross-reference table.
     * The appearance of an object id in this list is not an evidence of existence of the desired object.
     *
     * @return array
     */
    public function getDefinedObjectIds();

    /**
     * Get the generation number by an object id.
     *
     * @param integer $objectId
     * @return false|array
     * @throws SetaPDF_Core_Type_Exception
     */
    public function getGenerationNumberByObjectId($objectId);

    /**
     * Returns the offset position for a specific object.
     *
     * @param int $objectId
     * @param int|null $generation
     * @param integer $objectGeneration The final generation number, resolved if no generation number was given.
     * @return bool|int
     */
    public function getParserOffsetFor($objectId, $generation = null, &$objectGeneration = null);

    /**
     * Returns the trailer dictionary.
     *
     * @return SetaPDF_Core_Type_Dictionary
     */
    public function getTrailer();
}
