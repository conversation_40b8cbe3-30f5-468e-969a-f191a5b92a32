<?php

use App\Enums\ConnectionName;

$defaultRedisConf = [
    'driver' => 'redis',
    'connection' => 'default',
    'queue' => 'default',
    'after_commit' => true,
];

return [

    /*
    |--------------------------------------------------------------------------
    | Default Queue Driver
    |--------------------------------------------------------------------------
    |
    | The Laravel queue API supports a variety of back-ends via an unified
    | API, giving you convenient access to each back-end using the same
    | syntax for each one. Here you may set the default queue driver.
    |
    | Supported: "null", "sync", "database", "beanstalkd",
    |            "sqs", "iron", "redis"
    |
    */

    'default' => env('QUEUE_DRIVER', ConnectionName::REDIS),

    /*
    |--------------------------------------------------------------------------
    | Queue Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure the connection information for each server that
    | is used by your application. A default configuration has been added
    | for each back-end shipped with Laravel. You are free to add more.
    |
    */

    'connections' => [
        ConnectionName::SYNC => [
            'driver' => 'sync',
            'after_commit' => true,
        ],

        ConnectionName::DATABASE => [
            'driver' => 'database',
            'table' => 'jobs',
            'queue' => 'default',
            'expire' => 60,
            'retry_after' => 660,
            'after_commit' => true,
        ],

        // Kept the default redis configuration in case anything still uses this.
        ConnectionName::REDIS => $defaultRedisConf,

        // Below you have all connections for each retry_after period needed by the horizon queues.
        ConnectionName::REDIS_15 => [...$defaultRedisConf, 'retry_after' => 15],
        ConnectionName::REDIS_35 => [...$defaultRedisConf, 'retry_after' => 35],
        ConnectionName::REDIS_70 => [...$defaultRedisConf, 'retry_after' => 70],
        ConnectionName::REDIS_100 => [...$defaultRedisConf, 'retry_after' => 100],
        ConnectionName::REDIS_330 => [...$defaultRedisConf, 'retry_after' => 330],
        ConnectionName::REDIS_660 => [...$defaultRedisConf, 'retry_after' => 660],
        ConnectionName::REDIS_1000 => [...$defaultRedisConf, 'retry_after' => 1000],
        ConnectionName::REDIS_1300 => [...$defaultRedisConf, 'retry_after' => 1300],
        ConnectionName::REDIS_1900 => [...$defaultRedisConf, 'retry_after' => 1900],
    ],

    /*
    |--------------------------------------------------------------------------
    | Failed Queue Jobs
    |--------------------------------------------------------------------------
    |
    | These options configure the behavior of failed queue job logging so you
    | can control which database and table are used to store the jobs that
    | have failed. You may change them to any database / table you wish.
    |
    */

    'failed' => [
        'database' => env('QUEUE_FAILED_JOBS_DB', 'mysql'),
        'table' => 'failed_jobs',
        'driver' => 'database-uuids',
    ],
];
