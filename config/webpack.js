'use strict';

const path = require('path');
const DefinePlugin = require('webpack/lib/DefinePlugin');
const CaseSensitivePathsPlugin = require('case-sensitive-paths-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const ExtReloader  = require('./webpack-extension-reloader.js');

const buildConfig = (env) => {
    const projectDir = path.resolve(__dirname, '..');
    const isDev = env === 'development';
    const isInPipeline = process.env.IN_PIPELINE === "true";

    return {
        context: projectDir,
        mode: env,
        entry: {
            "background": [
                path.join(projectDir, 'src/index-background.js'),
            ],
            "dom-fondler": [
                path.join(projectDir, 'src/index-fondler.js'),
            ],
            "form-detector": [
                path.join(projectDir, 'src/index-detector.js'),
            ],
            "popup": [
                path.join(projectDir, 'src/popup/main.js'),
            ],
            "tax-loket": [
                path.join(projectDir, 'src/scripts/tax/inject/loket/LoketTaxScraper.js'),
            ],
            "tax-visionplanner": [
                path.join(projectDir, 'src/scripts/tax/inject/visionplanner/VisionplannerTaxScraper.js'),
            ],
            "openquestions-client": [
                path.join(projectDir, 'src/scripts/open_questions/inject/OpenQuestionsClient.js'),
            ],
            "openquestions-twinfield": [
                path.join(projectDir, 'src/scripts/open_questions/inject/Twinfield.js'),
            ],
            "openquestions-twinfield-angular": [
                path.join(projectDir, 'src/scripts/open_questions/inject/twinfield/BankAssignmentsInvestigate.js'),
            ],
            "openquestions-exact": [
                path.join(projectDir, 'src/scripts/open_questions/inject/Exact.js'),
            ],
            "openquestions-basecone": [
                path.join(projectDir, 'src/scripts/open_questions/inject/Basecone.js'),
            ],
            "openquestions-nmbrs": [
                path.join(projectDir, 'src/scripts/open_questions/inject/Nmbrs.js'),
            ],
            "openquestions-snelstart": [
                path.join(projectDir, 'src/scripts/open_questions/inject/Snelstart.js'),
            ],
            "openquestions-loket": [
                path.join(projectDir, 'src/scripts/open_questions/inject/Loket.js'),
            ],
            "openquestions-nextens": [
                path.join(projectDir, 'src/scripts/open_questions/inject/Nextens.js'),
            ],
            "openquestions-fiscaal-gemak": [
                path.join(projectDir, 'src/scripts/open_questions/inject/FiscaalGemak.js'),
            ],
            "openquestions-xero": [
                path.join(projectDir, 'src/scripts/open_questions/inject/Xero.js'),
            ],
            "openquestions-boekhoud-gemak": [
                path.join(projectDir, 'src/scripts/open_questions/inject/BoekhoudGemak.js'),
            ],
            "openquestions-minox": [
                path.join(projectDir, 'src/scripts/open_questions/inject/Minox.js'),
            ],
            "openquestions-moneybird": [
                path.join(projectDir, 'src/scripts/open_questions/inject/Moneybird.js'),
            ],
            "openquestions-eboekhouden": [
                path.join(projectDir, 'src/scripts/open_questions/inject/EBoekhouden.js'),
            ],
            "openquestions-mfo": [
                path.join(projectDir, 'src/scripts/open_questions/inject/Mfo.js'),
            ],
            "openquestions-zenvoices": [
                path.join(projectDir, 'src/scripts/open_questions/inject/Zenvoices.js'),
            ],
            "custom-sso": [
                path.join(projectDir, 'src/scripts/custom_sso/inject/CustomSso.js'),
            ],
            "utils": [
                path.join(projectDir, 'src/index-utils.js'),
            ],
            "belastingtool-exact": [
                path.join(projectDir, 'src/scripts/belastingtool/inject/ExactBelastingtool.js'),
            ],
        },
        output: {
            path: path.join(projectDir, 'dist/build'),
            publicPath: '/build/',
            filename: '[name].js',
            chunkFilename: '[id].chunk.js',
        },
        module: {
            rules: [
                // CSS files
                {
                    test: /\.css$/,
                    use: [
                        {
                            loader: 'style-loader',
                            options: {
                                injectType: 'styleTag'
                            },
                        },
                        'css-loader',
                    ],
                },
                // Web fonts
                {
                    test: /\.(woff(2)?|ttf|eot|svg)(\?v=\d+\.\d+\.\d+)?$/,
                    loader: 'file-loader',
                    options: {
                        name: 'fonts/[name].[ext]',
                        // Do not emit fonts as they will be inlined to avoid Content Security Policy errors
                        emitFile: false,
                    },
                },
            ],
        },
        plugins: [
            // Add support for environment variables under `process.env`
            new DefinePlugin({
                'process.env.NODE_ENV': `"${env}"`,
                'process.env.DATADOG_ENABLED': `"${!isDev && isInPipeline}"`,
                 DATA_VERSIONS: JSON.stringify(require('../package.json').dataVersions),
            }),
            // Alleviate cases where developers working on OSX, which does not follow strict path case sensitivity
            new CaseSensitivePathsPlugin(),
            isDev && new ExtReloader()
        ].filter(Boolean),
        devtool: 'source-map',
        optimization: {
            minimize: !isDev,
            minimizer: [
                new TerserPlugin({
                    extractComments: true,
                    parallel: true,
                    terserOptions: {
                        sourceMap: true,
                        mangle: true,
                        compress: {
                            warnings: false, // Mute warnings
                            /* eslint-disable camelcase */
                            drop_console: false, // Drop console.* statements
                            drop_debugger: true, // Drop debugger statements
                            /* eslint-enable camelcase */
                        }
                    }
                })
            ],
        },
        performance: {
            hints: false,
        },
    };
}

module.exports = buildConfig;