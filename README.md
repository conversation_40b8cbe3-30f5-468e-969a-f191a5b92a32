# README for Hix Browser Extension

The Hix Browser extension uses WebExtensions and is compatible with Mozilla Firefox and Google Chrome.

# Build instructions Firefox

---
## System requirements
- Recommended OS is Ubuntu 20.04 LTS
- Npm needs to be installed
- Node 1.16.x needs to be installed

##Build steps
- Run the `npm archive:firefox` to create the firefox binary
- Now you can find the binary in the bin/firefox with the filename hix_browser_ext-x.x.x-ff.xpi the version is defined in the package.json

#Internal information

---
## Repo Structure

- `template` holds static resources to be used on all targets. The `manifest` file works with loadash templates and is customizable by target browser.
- `src` holds browser independent scripts which will be processed by webpack to produce the distributable scripts
- `dist` holds the compilation result
- `bin` holds previously generated zip binaries

## Versioning

The version number is imported the `package.json` and must follow semver constraints.
To enable debug mode, add a `-0` prefix to the dotted notation version number (e.g. `1.2.3-0`).

## Key commands

- `npm run build` - builds the extension for all target browsers
- `npm run start:chrome` - builds and enables hot module reload for chrome
- `npm run start:firefox` - builds and enables hot module reload for firefox
- `npm run archive` - builds and creates extension binaries for all target browsers
- `npm run archive:firefox` - builds and creates extension binaries for firefox target browsers
- `npm run archive:chrome` - builds and creates extension binaries for firefox target browsers

## Developing for Mozilla Firefox

You can edit the files in the `src` folder and have the changes applied immediately by executing the build in watch mode `npm run start:firefox`.

1. Open the page about:debugging
2. Check "Enable add-on debugging"
3. Click "Load Temporary Add-on..."
4. Select manifest.json file in firefox folder.
5. Click "Debug" to open debugger. It should display "HixExtension loaded."

More information: https://developer.mozilla.org/en-US/Add-ons/WebExtensions

## Developing for Google Chrome

You can edit the files in the `src` folder and have the changes applied immediately by executing the build in watch mode `npm run start:chrome`.

1. Open the page chrome://extensions
2. Turn on developer mode (top right corner)
3. Choose "Load unpacked"
4. Select "chrome directory"
5. Click "background page" to get the debugger. 

Use the option "Pack extension" to create a packaged version with a private key file.


## Binaries creation

Run `npm run archive` to create the zip archives for firefox and chrome
Run `npm run archive:chrome` to create the zip archives for firefox
Run `npm run archive:firefox` to create the zip archives for chrome

## Browser differences

- Chrome requires the path for the urls where the response headers can be changed (for all urls in our case)
- Firefox requires the `application` property in the manifest. Chrome gives a warning when present.
- We use a Polyfill to make the Javascript for Chrome work like Firefox


# Publishing

1. Rename manifest files accordingly.
2. Run build script
3. Publish to Chrome webstore: https://chrome.google.com/webstore/developer/dashboard
4. Submit for Firefox: https://addons.mozilla.org/en-US/developers/addon/securelogin-browser-extension/versions/submit/
5. Add Firefox file to SL2 repository in folder /public/ext/dist
6. Update the file updates.json; create a SHA256 hash for the file using the command `shasum -a 256 file.xpi`" (MacOS)


# Updating extension version
Update the extension version in the following places:
1. ~/template/rules.json
2. ~/package.json
3. ~/src/index-background.js 
